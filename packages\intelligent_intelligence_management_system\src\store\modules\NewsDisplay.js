export default {
  namespaced: true,
  state: {
    hasPdfList: false,
    nowInfo: "",
    tranIndex: 2,
    //优先级
    tranYxj: false,
    tranType: false, //true代表翻译,flase代表是原文
    //翻译树
    translatedTree: [],
    tmpDataDetail: {},
    hanlp_server: "",
    analysis: true,
    NewsDet: {
      _source: {
        content_pdf: [],
        url: "",
      },
    },
    tranNewsList: {
      _source: {
        content_pdf: [],
        url: "",
      },
    },
    originalNewsList: {
      _source: {
        content_pdf: [],
        url: "",
      },
    },
    imgoriNewsList: {
      _source: {
        content_pdf: [],
        url: "",
      },
    },
    NewsTitle: "",
    oriContent: "",
    tranContent: "",
    imgoriContent: "",
  },
  mutations: {
    /**文章加入已读tags */
    toAddRead(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            msg: {
              type: "username",
              table: "already_read",
              prefix: v.newId,
              data: {
                data: {
                  newId: v.newId,
                },
              },
            },
          },
        ],
        "NewsDisplay/setAddRead"
      );
    },
    setAddRead(state, data) {},
    sethasPdfList(state, data) {
      state.hasPdfList = data;
    },
    settranType(state, data) {
      state.tranYxj = data;
      state.tranType = data;
    },
    getTranslatedIndex(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: 100,
            },
            msg: {
              type: "public",
              table: "parse_path",
              prefix: "",
              relation: "/" + v.index + "/_doc/" + v.id,
            },
          },
        ],
        (data) => {
          console.log("getTranslatedIndex", data);

          let info = data[0].row.split(";")[3].split("/");
          let translatedTree = state.translatedTree;
          if (translatedTree.length == data.length) {
            state.translatedTree.forEach((e) => {
              let panduan1 = true;
              if (e.label == v.type) {
                e.children.every((element) => {
                  if (element.label == "译文") {
                    panduan1 = false;
                  }
                });
                if (panduan1) {
                  e.children.push({
                    label: "译文",
                    type: 1,
                    oriInfo: e.children[0].oriInfo,
                    tranInfo: {
                      index: info[1],
                      tranId: info[3],
                    },
                  });
                }
              }
            });
          }
          if (translatedTree.length != data.length) {
            let info = data[0].row.split(";")[3].split("/");
            for (let index = 0; index < data.length - 1; index++) {
              const e = state.translatedTree[index];
              if (e.label == v.type) {
                let panduan = true;
                e.children.every((element) => {
                  if (element.label == "译文") {
                    panduan = false;
                  }
                });
                if (panduan) {
                  e.children.push({
                    label: "译文",
                    type: 1,
                    oriInfo: e.children[0].oriInfo,
                    tranInfo: {
                      index: data[1].row.split("/")[4],
                      tranId: data[1].row.split("/")[6],
                    },
                  });
                }
              }
            }
            state.translatedTree.push({
              label: "图片",
              id: 3,
              children: [
                {
                  label: "原文",
                  id: 5,
                  oriInfo: {
                    index: info[1],
                    tranId: info[3],
                  },
                  type: 0,
                },
              ],
            });
            window.main.$store.commit("NewsDisplay/getTranslatedIndex", {
              id: data[1].row.split("/")[6],
              index: data[1].row.split("/")[4],
              type: "图片",
            });
          }
        }
      );
    },
    setOricontent(state, data) {
      state.oriContent = data;
    },
    setTrancontent(state, data) {
      state.tranContent = data;
    },
    setimgOricontent(state, data) {
      state.imgoriContent = data;
    },

    setTmpDataDetail(state, tmpObj) {
      state.tmpDataDetail[tmpObj["key"]] = tmpObj["value"];
    },
    async sendGetDataDetailBaseData(state, data) {
      let tmpObj =
        state.tmpDataDetail["d"] || state.tmpDataDetail["elasticsearch_data"];
      await window.main.$main_socket.sendData(
        "Api.Search.DataDetail.BaseData",
        [
          {
            head: {
              row_key: [data.index + "/_doc/" + data.id],
            },
          },
        ],
        "NewsDisplay/setDataDetailBaseData"
      );
    },
    async searchListQuery(state, v) {
      await window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            control: {
              query_type: "public",
              condition: {
                time_range_begin: 0,
                time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        _id: v.id,
                      },
                    },
                  ],
                },
              },
            },
            head: {
              from: 0,
              size: 50,
            },
            msg: {
              data_range_index_name: v.index,
            },
          },
        ],
        (data) => {
          console.log("searchListQuery", data);
          if (data.hits.hits[0]._source.type == "translated") {
            state.tranNewsList = data.hits.hits[0];
            state.tranContent = data.hits.hits[0]._source.content_article;
            if (state.tranYxj) {
              state.tranType = state.tranYxj;
            } else {
              state.tranType = true;
            }

            let info = data.hits.hits[0]._source.parse_path.split("/");
            window.main.$store.commit("NewsDisplay/orisearchListQuery", {
              id: info[3],
              index: info[1],
              changetree: v.changetree,
              top: true,
            });
            state.tranContent = data.hits.hits[0]._source.content_article;
            state.nowInfo = {
              id: v.id,
              index: v.index,
            };
          } else {
            //判断来源是图片还是原文什么
            let type = "";
            if (data.hits.hits[0]._source.parse_path) {
              if (
                data.hits.hits[0]._source.parse_path.indexOf("content_image") !=
                -1
              ) {
                type = "图片";
                let info = data.hits.hits[0]._source.parse_path.split("/");
                //changetree只有第一次进来的时候需要改变翻译树
                //changeText如果是提取出来的原文就不需要再改变原文了
                window.main.$store.commit("NewsDisplay/orisearchListQuery", {
                  id: info[3],
                  index: info[1],
                  changetree: v.changetree,
                  changeText: true,
                });
              } else if (
                data.hits.hits[0]._source.parse_path.indexOf(
                  "auto_translation"
                ) != -1
              ) {
                type = "正文";
              }
            } else {
              type = "正文";
              // window.main.$store.commit("NewsDisplay/getTranslatedIndex", { id: v.id, index: v.index, type });
              state.NewsDet._source = data.hits.hits[0]._source;
              state.NewsDet.index = data.hits.hits[0]._index;
              state.NewsDet.id = data.hits.hits[0]._id;
            }
            if (v.changetree) {
              let tranObj = {
                label: type,
                id: 3,
                children: [
                  {
                    label: "原文",
                    id: 1,
                    oriInfo: {
                      index: v.index,
                      tranId: v.id,
                    },
                    type: 0,
                  },
                ],
              };
              state.translatedTree.push(tranObj);
              window.main.$store.commit("NewsDisplay/getTranslatedIndex", {
                id: v.id,
                index: v.index,
                type,
              });
            }

            state.originalNewsList = data.hits.hits[0];
            if (state.tranYxj) {
              state.tranType = state.tranYxj;
            } else {
              state.tranType = false;
            }
            state.oriContent = data.hits.hits[0]._source.content_article;
            //当通过原文点进来时候,去寻找翻译

            // window.main.$store.commit("NewsDisplay/orisearchListQuery", { id: info[3], index: info[1], changetree: v.changetree });
          }
          // state.NewsDet = data.hits.hits[0]
        }
      );
    },
    // setNewsDetail(state, data) {

    // },
    //当检索到是译文的时候,进来获取原文
    async orisearchListQuery(state, v) {
      await window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            control: {
              query_type: "public",
              condition: {
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                query_mode: "match",
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        _id: v.id,
                      },
                    },
                  ],
                },
              },
              highlight_fields: ["content_article", "virtual_file_path"],
            },
            head: {
              from: 0,
              size: 50,
            },
            msg: {
              data_range_index_name: v.index,
            },
          },
        ],
        (data) => {
          if (data.hits.hits[0]._source.type == "translated") {
            state.tranNewsList = data.hits.hits[0];
            state.tranContent = data.hits.hits[0]._source.content_article;
            // state.tranType = true
          } else {
            state.originalNewsList = data.hits.hits[0];
            // state.tranType = false
            if (v.changetree) {
              let type = "";
              if (data.hits.hits[0]._source.parse_path) {
                let info = data.hits.hits[0]._source.parse_path.split("/");
                window.main.$store.commit("NewsDisplay/orisearchListQuery", {
                  id: info[3],
                  index: info[1],
                  changetree: true,
                });

                if (
                  data.hits.hits[0]._source.parse_path.indexOf(
                    "content_image"
                  ) != -1
                ) {
                  type = "图片";
                  //图片文字翻译
                  let translatedTree = state.translatedTree;
                  let tranObj = {
                    label: type,
                    id: 3,
                    children: [
                      {
                        label: "译文",
                        id: 1,
                        type: 1,
                        tranInfo: {
                          index: state.nowInfo.index,
                          tranId: state.nowInfo.id,
                        },
                        oriInfo: {
                          index: v.index,
                          tranId: v.id,
                        },
                      },
                      {
                        label: "原文",
                        id: 2,
                        oriInfo: {
                          index: v.index,
                          tranId: v.id,
                        },
                        // tranInfo: {
                        // 	index: v.index,
                        // 	tranId: v.id,
                        // },
                        type: 0,
                      },
                    ],
                  };
                  translatedTree.push(tranObj);
                  state.translatedTree = translatedTree;
                  // state.NewsDet._source = data.hits.hits[0]._source
                }
              } else {
                type = "正文";
                let translatedTree = state.translatedTree;
                let tranObj = "";
                if (v.top) {
                  tranObj = {
                    label: type,
                    id: 4,
                    children: [
                      {
                        label: "译文",
                        id: 1,
                        type: 1,
                        tranInfo: {
                          index: state.nowInfo.index,
                          tranId: state.nowInfo.id,
                        },
                        oriInfo: {
                          index: v.index,
                          tranId: v.id,
                        },
                      },
                      {
                        label: "原文",
                        id: 6,
                        type: 0,
                        oriInfo: {
                          index: v.index,
                          tranId: v.id,
                        },
                      },
                    ],
                  };
                } else {
                  tranObj = {
                    label: type,
                    id: 4,
                    children: [
                      {
                        label: "原文",
                        id: 6,
                        type: 0,
                        oriInfo: {
                          index: v.index,
                          tranId: v.id,
                        },
                      },
                    ],
                  };
                }
                translatedTree.push(tranObj);
                state.translatedTree = translatedTree;
                window.main.$store.commit("NewsDisplay/getTranslatedIndex", {
                  id: v.id,
                  index: v.index,
                  type,
                });
                state.NewsDet._source = data.hits.hits[0]._source;
                state.NewsDet.index = data.hits.hits[0]._index;
                state.NewsDet.id = data.hits.hits[0]._id;
                if (!v.changeText) {
                  state.oriContent = data.hits.hits[0]._source.content_article;
                }
              }
            }
          }
        }
      );
    },

    async setTransearchListQuery(state, data) {
      await window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            control: {
              query_type: "public",
              condition: {
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                query_mode: "match",
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        _id: data.id,
                      },
                    },
                  ],
                },
              },
              highlight_fields: ["content_article", "virtual_file_path"],
            },
            head: {
              from: 0,
              size: 50,
            },
            msg: {
              data_range_index_name: data.index,
            },
          },
        ],
        "NewsDisplay/setTranNewsDetail"
      );
    },
    setTranNewsDetail(state, data) {
      state.tranNewsList = data.hits.hits[0];
      state.tranContent = data.hits.hits[0]._source.content_article;
    },
    setDataDetailBaseData(state, data) {
      console.log("setDataDetailBaseData", data);
      state.tmpDataDetail = data;
      if (state.tmpDataDetail.columnValues.nlp == null) {
        state.analysis = false;
        return;
      }
      if (state.tmpDataDetail.columnValues.nlp.hanlp_server == null) {
        state.analysis = false;
        return;
      }
      if (
        state.tmpDataDetail.columnValues.nlp.hanlp_server.analysis_doc !=
          null &&
        state.tmpDataDetail.columnValues.nlp.hanlp_server.analysis_doc.length ==
          2
      ) {
        state.analysis = true;
        window.main.$nextTick(() => {
          // 基于准备好的dom，初始化echarts实例
          let myChart = window.main.$echarts.init(
            document.getElementById("myChart")
          );
          let option = {
            grid: {
              left: "10%",
              top: "19%",
              right: "5%",
              bottom: "16%",
            },
            toolbox: {
              show: true,
              feature: {
                mark: { show: true },
                saveAsImage: {
                  show: true,
                  backgroundColor: "rgba(0, 0, 0, 0.75)",
                },
              },
            },
            xAxis: {
              type: "category",
              data: state.tmpDataDetail.columnValues.nlp.hanlp_server
                .analysis_doc[0],
              axisLabel: {
                interval: 0, //强制显示文字
                rotate: 20,
                fontSize: "14",
              },
              axisLine: {
                lineStyle: {
                  color: "#000",
                },
              },
            },
            yAxis: {
              name: "词频/次",
              type: "value",
              axisLine: {
                lineStyle: {
                  color: "#000",
                },
              },
            },
            series: [
              {
                label: {
                  show: true,
                  formatter: function (data) {
                    return data.value;
                  },
                },
                data: state.tmpDataDetail.columnValues.nlp.hanlp_server
                  .analysis_doc[1],
                type: "bar",
                barWidth: 20,
              },
            ],
          };
          myChart.setOption(option);
        });
      }
    },
  },
  actions: {},
};
