.loginContainer {
  height: 100%;
  width: 100%;
  .loginVideo {
    position: absolute;
    top:0px;
    left:0px;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  .loginBox {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background:  rgb(22 72 121 / 15%);
    border-radius: 4px;
    // padding: 30px;
    width: 20%;
    height: 30%;
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 40%;
  }

  .titleBox {
    height: 60%;
  }

  // .title {
  //   text-align: center;
  //   font-size: 26px;
  //   color: #ffffffc0;
  //   margin-top: 10%;
  // }
  .title{
    position: relative;
    margin-left: 23%;
    margin-top:5%;
    font-size: 2rem;
    word-spacing: 0.2em;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    color: transparent;
    background-color: rgb(106, 172, 238);
    background-clip: text;
  }

  .title::after {
    content: attr(data-text);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    background-image: linear-gradient(
      200deg, 
      transparent 0%, 
      transparent 1rem, 
      white 11rem, 
      transparent 11.15rem, 
      transparent 15rem, 
      rgba(255, 255, 255, 0.3) 20rem, 
      transparent 25rem, transparent 27rem, 
      rgba(255, 255, 255, 0.6) 32rem, 
      white 33rem, 
      rgba(255, 255, 255, 0.3) 33.15rem, 
      transparent 38rem, 
      transparent 40rem, 
      rgba(255, 255, 255, 0.3) 45rem, 
      transparent 50rem, 
      transparent 100%
    );
    background-clip: text;
    background-size: 150% 100%;
    background-repeat: no-repeat;
    animation: shine 3s infinite linear;
  }

  @keyframes shine {
    0% {
        background-position: 50% 0;
    }
    100% {
        background-position: -190% 0;
    }
}

  .textBox {
    height: 20%;
    margin-bottom: 1%;
  }

  .loadingText{
    margin-left: 32%;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.849);
    mask: radial-gradient(
        circle at 0 50%,
        #000,
        transparent 10%,
        transparent 0
    );
    mask-size: 100%;
    
    animation: scale 5s infinite;
  }

  @keyframes scale {
    50%,
    100% {
        mask-size: 2000%;
    }
  }

  .loadingText::after {
    content: " ";
    animation: dot 1s infinite steps(3, start);
    line-height: 24px;
  }

  @keyframes dot {
    33.33% {
      content: ".";
    }
    66.67% {
      content: "..";
    }
    100% {
      content: "...";
    }
  }
}