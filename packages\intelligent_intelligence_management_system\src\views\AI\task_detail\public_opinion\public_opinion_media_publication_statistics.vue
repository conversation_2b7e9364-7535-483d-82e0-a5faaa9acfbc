<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #ffedd5;
          line-height: 42px;
          font-weight: bold;
          color: #9a3412;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-pie-chart"></i
          ><span style="margin-left: 10px">媒体发文统计</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div>
        <div
          ref="MediaStatistics"
          id="MediaStatistics"
          style="height: 400px"
        ></div>
        <div
          v-if="myDataObj"
          style="padding: 10px; color: #7d7878; background-color: #eee"
        >
          {{ myDataObj }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import html2canvas from "html2canvas";

export default {
  data() {
    return {
      myDataObj: null,
    };
  },
  mounted() {
    if (this.dataObj) {
      for (let i = 0; i < this.dataObj.reports.length; i++) {
        for (let str in this.dataObj.reports[i]) {
          if (str === "pie_chart_data") {
            this.myDataObj = this.dataObj.reports[i][str];
          }
        }
      }
      this.MediaStatistics(this.dataObj.pie_chart_data);
    }
  },
  computed: {
    ...mapState({
      dataObj: (state) => state.aiTaskQueue.taskDetail.public_opinion,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "pie_chart_data") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
          this.MediaStatistics(newVal.pie_chart_data);
        }
      },

      deep: true,
    },
  },
  methods: {
    MediaStatistics(v) {
      const blueColors = [
        "#5470c6",
        "#73c0de",
        "#91cc75",
        "#fac858",
        "#ee6666",
      ];

      // 转换函数
      function convertToEChartsFormat(dataObj) {
        return Object.keys(dataObj).map((key) => ({
          value: dataObj[key],
          name: key,
        }));
      }

      // 转换并输出
      const echartsData = convertToEChartsFormat(v);
      console.log("echartsData", echartsData);
      let myChart = window.main.$echarts.init(
        document.getElementById("MediaStatistics")
      );
      myChart.setOption({
        // toolbox: {
        //   show: true,
        //   feature: {
        //     mark: {show: true},
        //     saveAsImage: {show: true},
        //   },
        // },
        title: {
          text: "媒体统计",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "bottom",
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            data: echartsData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });
      this.$bus.$MediaQuery = myChart;

      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    saveDivAsImage() {
      const captureElement = this.$refs.MediaStatistics;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "媒体发文统计.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.content_title {
  width: 90%;
  padding-left: 10px;
  font-size: 18px;
  color: #2440b3;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  min-height: 20px;
}

.content_title:hover {
  text-decoration: underline;
}

/* 或直接修改圆点颜色 */
.content_title::first-letter {
}
</style>
