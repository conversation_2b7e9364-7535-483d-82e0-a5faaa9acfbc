<template>
  <div style="height: 100%; display: flex; flex-direction: column">
    <div
      v-show="dataList.length"
      style="
        height: 45px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        justify-content: space-between;
      "
    >
      <div style="margin: 0 10px; display: inline-block">
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
        >
          <span v-if="!checkedCities.length">全选</span>
          <el-button v-else size="mini" @click="toDelCheckCol">
            删除
          </el-button>
        </el-checkbox>
        <el-button
          v-show="checkedCities.length"
          type="primary"
          size="mini"
          @click="toDocx"
          >导出报告</el-button
        >
      </div>
      <el-button
        size="mini"
        @click="showPublicOpinionReportDialog()"
        style="margin-right: 10px"
        ><i class="custom-icon"></i> 生成舆情简报</el-button
      >
    </div>

    <div style="flex: 1; overflow-y: auto">
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="collectOpinion"
          v-infinite-scroll="loadMoreCollect"
          infinite-scroll-immediate="false"
        >
          <div v-for="(val, index) in dataList" :key="index" class="newContent">
            <div style="display: flex">
              <el-checkbox
                :label="val"
                :key="index"
                style="display: flex; align-items: end; margin-bottom: 5px"
                >{{ " " }}
              </el-checkbox>
              <div
                class="content_title"
                v-html="
                  val._source.title && val._source.title.trim()
                    ? val._source.title
                    : '无标题'
                "
                @click="NewsDisplay(val)"
              ></div>
            </div>

            <div class="content_body" @click="NewsDisplay(val)">
              <!-- <img alt="" style="margin-right: 10px; background: #ccc;" v-if="val._source.content_img" :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                    val._source.content_img-[0].sha512_hash" /> -->
              <el-image
                style="margin-right: 10px; width: 100px; height: 75px"
                v-if="val._source.content_img"
                :src="
                  'filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                  val._source.content_img[0].sha512_hash
                "
              />
              <div class="content_details">
                <div class="timeAndast">
                  <span style="color: #9a95a3; padding-right: 10px">{{
                    $tools.timestampToTime(val._source.timestamp * 1000)
                  }}</span>
                  <a
                    :href="val._source.url"
                    style="text-decoration: underline"
                    target="_blank"
                    >原文链接</a
                  >
                </div>
                <span
                  class="collectContent"
                  v-html="
                    val._source.content_article
                      ? val._source.content_article
                      : val._source.content
                  "
                ></span>
              </div>
            </div>
            <div class="content_link">
              <span style="padding-right: 10px" v-if="val._source.type"
                >来自：{{ val._source.type }}</span
              >
              <span style="padding-right: 10px" v-if="val._source.author_id">
                作者：{{ $tools.longText(val._source.author_id, 12) }}</span
              >
              <span
                style="float: right; cursor: pointer; color: red"
                @click.stop="delFileData(val)"
                >删除</span
              >
            </div>
          </div>
        </div>
      </el-checkbox-group>
      <!-- <div style="width: 100%;height: 100%" v-show="!dataList.length">
                <el-empty :image-size="300" description="暂无收藏"></el-empty>
            </div> -->
    </div>

    <el-dialog
      title="生成舆情简报"
      :visible.sync="createIntell"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="intellForm"
        :rules="intellRules"
        ref="intellForm"
        label-width="100px"
      >
        <el-form-item label="情报标题" prop="title">
          <el-input
            v-model="intellForm.title"
            placeholder="请输入情报标题"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="情报模板" prop="template">
          <el-select
            v-model="intellForm.template"
            filterable
            placeholder="请选择情报模板"
            style="width: 100%"
          >
            <el-option
              v-for="(item, index) in aiAnalyzeTemplateList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cleareData">取 消</el-button>
        <el-button type="primary" @click="submitIntellForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import ElTableInfiniteScroll from "el-table-infinite-scroll";
import { Document, Packer, Paragraph, TextRun } from "docx";
import { saveAs } from "file-saver";
import { create_timestamp, update_timestamp } from "@/i18n/zh/analysis";
export default {
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  name: "public_opinion",
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      checkedCities: [],
      createIntell: false,
      intellForm: {
        title: "",
        template: "",
      },
      intellRules: {
        title: [
          { required: true, message: "请输入情报标题", trigger: "blur" },
          { max: 50, message: "标题长度不能超过50个字符", trigger: "blur" },
        ],
        // template: [
        //   { required: true, message: "请选择情报模板", trigger: "change" },
        // ],
      },
    };
  },
  computed: {
    ...mapState({
      dataList: (state) => state.collect.dataList,
      nowCollect: (state) => state.collect.nowCollect,
    }),
    aiAnalyzeTemplateList: {
      get() {
        const templateList = this.$store.state.aiAnalyze.aiAnalyzeTemplateList;
        return templateList.map((item) => ({
          label: item._source.ai_template_name,
          value: item._source,
        }));
      },
    },
  },
  mounted() {
    this.$store.commit("collect/clearDataList");
    setTimeout(() => {
      console.log("nowCollect", this.nowCollect);
      if (this.nowCollect) {
        this.$store.commit("collect/getOpinionCollect");
      }
    }, 500);
  },
  methods: {
    cleareData() {
      this.createIntell = false;
      this.intellForm.title = "";
      this.intellForm.template = "";
    },

    showPublicOpinionReportDialog() {
      console.log(
        "<showPublicOpinionReportDialog> nowCollect:",
        this.nowCollect
      );

      this.createIntell = true;
    },

    submitIntellForm() {
      this.$refs.intellForm.validate((valid) => {
        if (valid) {
          window.main.$main_socket.sendData(
            "Api.DataAnalysisTask.AddSimpleTask",
            [
              {
                msg: {
                  task_authority: "username",
                  task_type: "ai_workflow_task",
                  method: "generate_public_opinion_report_from_collection",
                  title: this.intellForm.title,
                  parms: {
                    inputs: {
                      task_id: "",
                      identifier: this.nowCollect.id + ";public_opinion",
                      hbase_table_name: "favorites_data",
                      authority:
                        window.main.$store.state.userInfo.userinfo.authority,
                      username:
                        window.main.$store.state.userInfo.userinfo.username,
                      sqlite_table_name: "TEST",
                    },
                    create_timestamp: new Date().getTime(),
                    update_timestamp: 0,
                  },
                },
              },
            ],
            (res) => {
              console.log("res", res);
              if (res?.status == "ok") {
                this.$message.success("情报创建成功");
              }
            }
          );
          this.createIntell = false;
        } else {
          console.log("表单验证失败");
        }
      });
    },

    toDelCheckCol() {
      console.log("checkedCities", this.checkedCities);
      if (this.checkedCities.length) {
        this.$confirm("是否取消选中的收藏列表?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let row = [];
            this.checkedCities.forEach((element) => {
              row.push(element.row);
            });
            this.$store.commit("collect/sendDelFileData", {
              row,
              collCache: this.collCache,
            });
            this.handleCheckedCitiesChange([]);
          })
          .catch((err) => {
            console.log(err);
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      } else {
        this.$message.warning("未选择收藏内容");
      }
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.dataList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.dataList.length;
    },
    // 全导出新闻全选
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.dataList : [];
      this.isIndeterminate = false;
    },
    toDocx() {
      let html = "";
      this.checkedCities.forEach((e, index_) => {
        html +=
          "头" +
          window.main.$tools.changeNumToHan(index_ + 1) +
          "." +
          (e._source.title && e._source.title.trim()
            ? e._source.title.trim()
            : "无标题") +
          "<br>";
        if (e._source && e._source.nlp_parse_flag) {
          if (e._source.sentence_list) {
            let sentence_list = e._source.sentence_list;
            sentence_list.forEach((element, index) => {
              html += "    " + (index + 1) + "." + element + "<br>";
            });
          }
        }
        if (e._source) {
          html +=
            "    [来源]" +
            e._source.type +
            "<br>" +
            "    [时间]" +
            this.$tools.timestampToTime(e._source.timestamp * 1000) +
            "<br>" +
            "    [链接]" +
            decodeURIComponent(e._source.url) +
            "<br>";
        }
        html += " <br>";
      });
      this.html = html;
      this.docx();
    },
    docx() {
      console.log("nowCollect");

      //生成段落 根据options进行配置
      const generateParagraph = (options) => {
        let {
          text = "",
          size = 26,
          margin = {
            left: 30,
            right: 30,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              font: {
                name: "宋体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      const generateParagraphHead = (options) => {
        let {
          text = "",
          bold = true,
          size = 32,
          margin = {
            left: 50,
            right: 50,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              bold,
              font: {
                name: "黑体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      let test = this.html.replace(/<[^>]*>/g, "<br>").split("<br>");
      let paragraphList = test.map((e) => {
        if (e.slice(0, 1) == "头") {
          let opt = {
            text: e.slice(1),
          };

          return generateParagraphHead(opt);
        } else {
          let opt = {
            text: e,
          };
          return generateParagraph(opt);
        }
      });
      //按照数据填充生成文档 内容放置于sections
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: paragraphList,
          },
        ],
      });

      //保存导出为word
      Packer.toBlob(doc).then((blob) => {
        saveAs(
          blob,
          this.nowCollect.label ? this.nowCollect.label : "舆情简报.docx"
        );
      });
    },
    loadMoreCollect() {
      this.$store.commit("collect/getOpinionCollect");
    },
    NewsDisplay(val) {
      const routeData = this.$router.resolve({
        name: "NewsDisplay",
        query: {
          id: val.id ? val.id : val._id,
          index: val.index ? val.index : val._index,
        },
      });
      window.open(routeData.href, "_blank");
    },
    // 删除收藏的数据
    delFileData(item) {
      this.$confirm("是否取消收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("collect/sendDelFileData", {
            row: [item.row],
            collCache: this.collCache,
            id: item.id,
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.custom-icon {
  display: inline-block;
  width: 24px;
  height: 14px;
  background-image: url("../../../assets/images/AI.png");
  background-size: cover;
}
.collectOpinion {
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-between;
  width: 100%;
  height: 99%;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, 32%);
  grid-auto-rows: 140px;
  gap: 10px;
  padding-bottom: 15px;
}

.newContent {
  padding: 3px 10px 5px 5px;
  border: 1px solid #ccc;

  .content_title {
    width: 90%;
    padding-left: 10px;
    font-size: 18px;
    color: #2440b3;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    min-height: 20px;
  }

  .content_title:hover {
    text-decoration: underline;
  }

  .content_body {
    padding-left: 10px;
    display: flex;

    img {
      width: 100px;
      height: 75px;
      object-fit: fill;
    }

    .content_details {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      line-clamp: 6;
      -webkit-line-clamp: 6;
      overflow: hidden;
      font-size: 14px;
      line-height: 19px;
    }
  }

  .content_link {
    padding-left: 10px;
    padding-top: 5px;
    color: #626675;
    font-size: 14px;
  }
}

.collectListBox {
  height: 87vh;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: space-between;
}

.collectContent {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  cursor: pointer;
}

.timeAndast {
  display: flex;
  justify-content: space-between;
}

.content_body img {
  width: 100px;
}
</style>
