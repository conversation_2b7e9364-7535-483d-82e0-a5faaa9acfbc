import telegramBaseData from "./telegram-data-detail/telegram-base-data.js";
import telegramGroupData from "./telegram-data-detail/telegram-group-data.js";
import telegramGroupMemberData from "./telegram-data-detail/telegram-group-member-data.js";
import telegramGroupContentData from "./telegram-data-detail/telegram-group-content-data.js";
import telegramHbaseGroup from "./telegram-data-detail/telegram-hbase-group.js";

export default {
  namespaced: true,
  state: {
    dialogVisibleB: false,
    dialogVisible: false,
    tmpDataDetail: {},
    group_content_data: [],
    group_member_data: [],
    Pdf: false,
  },
  mutations: {
    setPdf(state, v) {
      state.Pdf = v;
    },
    clearDataDetail(state) {
      state.dialogVisible = false;
      state.tmpDataDetail = {};
      state.group_content_data = [];
      state.group_member_data = [];
    },
    clearDataDetailB(state) {
      state.dialogVisibleB = false;
      state.tmpDataDetail.elasticsearch_dataB = {};
    },
    setDialogVisible(state, v) {
      state.dialogVisible = v;
    },
    setDialogVisibleB(state, v) {
      state.dialogVisibleB = v;
    },
    setTmpDataDetail(state, tmpObj) {
      state.tmpDataDetail[tmpObj["key"]] = tmpObj["value"];
    },
    setdialogVisible(state, v) {
      state.dialogVisible = v;
    },
    setdialogVisibleB(state, v) {
      state.dialogVisibleB = v;
    },
  },
  actions: {
    // 判断此消息是正文还是译文
    getTranslated({ state }) {
      const msgData = state.tmpDataDetail.d;
      if (msgData._source.type == "translated") {
        let parsePath = msgData._source.parse_path.split("/");
        window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/getorinalText", {
          id: parsePath[3],
          index: parsePath[1],
        });
        state.tmpDataDetail.d._source.translation_text =
          msgData._source.content_article;
      } else {
        state.tmpDataDetail.d._source.original_text =
          msgData._source.content_article;
      }
    },
    // 获取原文
    getorinalText({ state }, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            control: {
              query_type: "public",
              condition: {
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                query_mode: "match",
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        _id: v.id,
                      },
                    },
                  ],
                },
              },
              highlight_fields: ["content_article", "virtual_file_path"],
            },
            head: {
              from: 0,
              size: 50,
            },
            msg: {
              data_range_index_name: v.index,
            },
          },
        ],
        (data) => {
          const msgItem = data.hits.hits[0];
          state.tmpDataDetail.d._source.original_text =
            msgItem._source.content_article;
          state.tmpDataDetail.d._source.type = msgItem._source.type;
          window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/getTranslatedText", {
            id: v.id,
            index: v.index,
          });
        }
      );
    },
    // 获取所有译文
    getTranslatedText({ state }, v) {
      console.log("getTranslatedIndex", v, window.main.$main_socket);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: 100,
            },
            msg: {
              type: "public",
              table: "parse_path",
              prefix: "",
              relation: "/" + v.index + "/_doc/" + v.id,
            },
          },
        ],
        (data) => {
          console.log("getTranslatedIndex:all", data);
          let info = data[0].row.split(";")[3].split("/");
          let translatedTree = state.translatedTree;

          if (translatedTree.length == data.length) {
            state.translatedTree.forEach((e) => {
              let panduan1 = true;
              if (e.label == v.type) {
                e.children.every((element) => {
                  if (element.label == "译文") {
                    panduan1 = false;
                  }
                });
                if (panduan1) {
                  e.children.push({
                    label: "译文",
                    type: 1,
                    oriInfo: e.children[0].oriInfo,
                    tranInfo: {
                      index: info[1],
                      tranId: info[3],
                    },
                  });
                }
              }
            });
          }
          if (translatedTree.length != data.length) {
            let info = data[0].row.split(";")[3].split("/");
            for (let index = 0; index < data.length - 1; index++) {
              const e = state.translatedTree[index];
              if (e.label == v.type) {
                let panduan = true;
                console.log("translatedTree:译文", e);
                e.children.every((element) => {
                  if (element.label == "译文") {
                    panduan = false;
                  }
                });
                if (panduan) {
                  e.children.push({
                    label: "译文",
                    type: 1,
                    oriInfo: e.children[0].oriInfo,
                    tranInfo: {
                      index: data[1].row.split("/")[4],
                      tranId: data[1].row.split("/")[6],
                    },
                  });
                }
              }
            }
            window.main.$store.commit("NewsDisplay/getTranslatedIndex", {
              id: data[1].row.split("/")[6],
              index: data[1].row.split("/")[4],
              type: "图片",
            });
          }
          console.log("translatedTree:all", state.translatedTree);
        }
      );
    },
  },
  modules: {
    telegramBaseData,
    telegramGroupData,
    telegramHbaseGroup,
    telegramGroupMemberData,
    telegramGroupContentData
  },
};
