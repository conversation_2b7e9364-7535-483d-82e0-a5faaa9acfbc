export default {
  namespaced: true,
  state: {
    userinfo: {},
    toPath: "",
    superdog_random_key: null,
    loginType: 0,
    projectDialogVisible: false,
  },
  mutations: {
    setprojectDialogVisible(state, v) {
      state.projectDialogVisible = v;
    },
    logout() {
      if (window.main.$route.path !== "/") {
        console.log("登录失效，请重新登录!");
        window.main.$notify.error("登录失效，请重新登录!");
        window.main.$router.push("/");
      }
    },
    setToPath(state, v) {
      state.toPath = v;
    },
    setUserInfo(state, v) {
      if (v) {
        state.userinfo = v;
        this.commit("setAuthorityFlag", false);
        if (v.hasOwnProperty("authority")) {
          window.main.$cookies.set("authority", v.authority);
        }
        if (window.main.$route.path === "/") {
          window.main.$router.push({ name: "ai<PERSON>each" });
        }
      } else {
        return;
      }
    },
    login(state) {
      try {
        window.main.$pki_socket.sendData(
          "Api.User.Info",
          [{}],
          "userInfo/setUserInfo"
        );
      } catch (error) {
        setTimeout(() => {
          window.main.$pki_socket.sendData(
            "Api.User.Info",
            [{}],
            "userInfo/setUserInfo"
          );
        }, 500);
      }
    },
    setSuperDogRandomKey(state, data) {
      try {
        state.superdog_random_key = data.session_id;
      } catch (err) {
        state.superdog_random_key = "";
      }
    },
    resetSuperDogRandomKey(state, data) {
      state.superdog_random_key = null;
    },
    refresh(state, data) {
      try {
        window.main.$pki_socket.sendData(
          "Api.User.Info",
          [],
          "userInfo/setUserInfo"
        );
        if (data.hasOwnProperty("authority")) {
          window.main.$cookies.set("authority", data.authority);
        }
        if (state.toPath) {
          window.main.$router.push(state.toPath);
        }
      } catch (err) {}
    },
  },
  actions: {
    getUserinfo({ commit }, data) {
      commit("setUserInfo", data);
    },
  },
};
