// 中文映射，简单的翻译功能
export default function (enStr) {
  const map = {
    _: '数据唯一键',
    control_target: '群内目标人',
    end_time: '分析结束时间',
    group_id: '群ID',
    group_member_num: '群成员数',
    group_name: '群名称',
    start_time: '分析开始时间',
    timestamp: '入库时间',
    group_hash: '群组哈希值',
    is_creator: '是否群创建者',
    is_fake: '是否虚假群',
    is_left: '是否退群',
    is_scam: '是否诈骗群',
    join_timestamp: '加群时间',
    last_msg: '最新消息',
    last_msg_timestamp: '最新消息时间',
    member_count: '成员总数',
    msg_max_id: '消息总数',
    type: '类型',
    content_article: '消息内容',
    is_deleted: '是否删除',
    post_author: '发送人',
    last_edit_date: '最新编辑时间',
    msg_id: '消息ID',
    is_out: '是否退出',
    is_from_scheduled: '是否预设',
    is_noforwards: '是否传递',
    group_nickname: '群昵称',
    location: '归属地',
    nickname: '昵称',
    telephone: '手机号',
    user_id: '用户ID',
    username: '用户名',
    name: '名称',
    other: '关键词',
    parms: '原始数据',
    params: '原始数据',
    icon: '图标',
    custom: '环境变量',
    default_cpu: 'Cpu默认值',
    default_memory: '内存默认值',
    describe: '镜像描述',
    detail_name: '名称',
    iconSha512: '图标',
    image_tag: '版本号',
    label: '标签',
    value: '参数值',
    images: '镜像名',
    max_cpu: 'Cpu最大值',
    max_memory: '内存最大值',
    min_cpu: 'Cpu最小值',
    min_memory: '内存最小值',
    networks: '网络',
    web: '页面',
    volume_slot: '文件系统',
    authority: '权限',
    buttons: '按钮组',
    case_id: '案件ID',
    method: '方法',
    project_name: '项目名称',
    row: '数据ID',
    status: '状态',
    tag: '标签',
    task_authority: '任务权限',
    task_detail: '任务描述',
    task_type: '任务类型',
    title: '标题',
    vm: '镜像表',
    command: '命令',
    port_slot: '端口信息',
    msg_month_change: '消息每月数量',
    msg_number: '消息数量',
    keyword_list: '关键词',
    msgInfo: '消息变化',
    phone: '手机号',
    qq: 'QQ号',
    twitter: '推特号',
    date: '日期',
    path: '数据类型',
    country: '国家'
  }

  return map[enStr] || enStr
}
