export default {
  namespaced: true,
  state: {
    selectOrganization: null,
    identity: null,
    email: null,
    phoneIds: [],
    telegramIds: [],
    twitterIds: [],
    facebookIds: [],
    linkedinIds: [],
    personRelationIds: [],
    organizationRelationIds: [],
    // 搜索结果
    twitterSearchList: [],
    telegramSearchList: [],
    facebookSearchList: [],
    linkedinSearchList: [],
    personRelationSearchList: [],
    organizationRelationSearchList: [],
    // 路径
    socailPath: "/socail_data",
    telegramPath: "/instant_msg/telegram/user_id",
    twitterPath: "/social_platform/twitter/relation",
    facebookPath: "/social_platform/facebook",
    linkedinPath: "/social_platform/linkedin",
  },
  mutations: {
    // 重置所有数据
    resetAllData(state) {
      state.selectOrganization = null;
      state.identity = null;
      state.email = null;
      state.phoneIds = [];
      state.twitterIds = [];
      state.telegramIds = [];
      state.facebookIds = [];
      state.linkedinIds = [];
      state.twitterSearchList = [];
      state.telegramSearchList = [];
      state.facebookSearchList = [];
      state.linkedinSearchList = [];
      state.personRelationIds = [];
      state.organizationRelationIds = [];
      state.personRelationSearchList = [];
      state.organizationRelationSearchList = [];
    },

    // 设置选中的目标组织以及相关信息
    setOrganization(state, organization) {
      state.selectOrganization = organization;
      console.log("organization", organization);
      // 处理基础信息
      const params = organization._source.params;
      state.identity = params.basic?.identity || null;
      state.email = params.basic?.email || null;
      state.phoneIds = params.phoneNumbers || [];
      state.twitterIds = params.twitterIds || [];
      state.telegramIds = params.telegramIds || [];
      state.facebookIds = params.facebookIds || [];
      state.linkedinIds = params.linkedInIds || [];
      // 处理关系数据
      state.personRelationIds = [];
      state.organizationRelationIds = [];
      if (params.relation && Array.isArray(params.relation)) {
        params.relation.forEach((relationStr) => {
          try {
            const parsed =
              typeof relationStr === "string"
                ? JSON.parse(relationStr)
                : relationStr;
            if (parsed.person && Array.isArray(parsed.person)) {
              state.personRelationIds = [
                ...state.personRelationIds,
                ...parsed.person,
              ];
            }
            if (parsed.organization && Array.isArray(parsed.organization)) {
              state.organizationRelationIds = [
                ...state.organizationRelationIds,
                ...parsed.organization,
              ];
            }
          } catch (e) {
            console.warn("解析组织关系数据失败:", relationStr, e);
          }
        });
      }
      // 遍历人员关系数据
      if (state.personRelationIds && state.personRelationIds.length > 0) {
        window.main.$store.commit(
            "organizationTopologyMap/searchPersonRelation",
            state.personRelationIds
          );;
      }      
      // 遍历组织关系数据
      if (
        state.organizationRelationIds &&
        state.organizationRelationIds.length > 0
      ) {
        window.main.$store.commit(
            "organizationTopologyMap/searchOrganizationRelation",
            state.organizationRelationIds
          );
      }
      // 遍历twitter号码然后发送请求
      if (state.twitterIds && state.twitterIds.length > 0) {
        state.twitterIds.forEach((item) => {
          window.main.$store.commit(
            "organizationTopologyMap/searchTwitter",
            item
          );
        });
      }
      // 遍历telegram号码然后发送请求
      if (state.telegramIds && state.telegramIds.length > 0) {
        state.telegramIds.forEach((item) => {
          window.main.$store.commit(
            "organizationTopologyMap/searchTelegram",
            item
          );
        });
      }
      // 遍历facebook号码然后发送请求
      // if (state.facebookIds && state.facebookIds.length > 0) {
      //   state.facebookIds.forEach((item) => {
      //     window.main.$store.commit(
      //       "organizationTopologyMap/searchFacebook",
      //       item
      //     );
      //   });
      // }
      // 遍历linkedin号码然后发送请求
      if (state.linkedinIds && state.linkedinIds.length > 0) {
        state.linkedinIds.forEach((item) => {
          window.main.$store.commit(
            "organizationTopologyMap/searchLinkedin",
            item
          );
        });
      }
    },

    // 通过人员关系id进行搜索ES
    searchPersonRelation(state, personRelationId) {
      let add_es_query_conditions = {
        bool: {
          should: personRelationId.map(account => ({
            term: { _id: account}
          }))
        },
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { from: 0, size: 200 },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: { data_range_index_name: "key_person" },
          },
        ],
        (res) => {
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach((item) => {
              let personData = buildPersonData(item._source.params);
              state.personRelationSearchList.push(personData);
          });
        }
      );
      function buildPersonData(params) {
        if (typeof params === "object" && !Array.isArray(params)) return params;
        if (Array.isArray(params)) {
          const paramsObj = {};
          params.forEach((param) => {
            paramsObj[param.k] = param.v;
          });
          const standardFields = [
            "avatar",
            "name",
            "remark",
            "sex",
            "age",
            "phoneNumbers",
            "identity",
            "email",
            "dateBirth",
            "relation",
            "twitterIds",
            "facebookIds",
            "linkedInIds",
            "telegramIds",
            "basic",
            "socialize",
            "media",
            "customFields",
          ];
          const basicObj = {
            id: paramsObj.id
              ? Array.isArray(paramsObj.id)
                ? paramsObj.id[0]
                : paramsObj.id
              : "",
            name: Array.isArray(paramsObj.name)
              ? paramsObj.name[0]
              : paramsObj.name || "",
            remark: Array.isArray(paramsObj.remark)
              ? paramsObj.remark[0]
              : paramsObj.remark || "",
            sex: Array.isArray(paramsObj.sex)
              ? paramsObj.sex[0]
              : paramsObj.sex || "",
            age: paramsObj.age
              ? parseInt(
                  Array.isArray(paramsObj.age)
                    ? paramsObj.age[0]
                    : paramsObj.age
                )
              : 0,
            phone: Array.isArray(paramsObj.phoneNumbers)
              ? paramsObj.phoneNumbers[0]
              : paramsObj.phoneNumbers || "",
            identity: Array.isArray(paramsObj.identity)
              ? paramsObj.identity[0]
              : paramsObj.identity || "",
            email: Array.isArray(paramsObj.email)
              ? paramsObj.email[0]
              : paramsObj.email || "",
            avatar: Array.isArray(paramsObj.avatar)
              ? paramsObj.avatar[0]
              : paramsObj.avatar || "",
            dateBirth: Array.isArray(paramsObj.dateBirth)
              ? paramsObj.dateBirth[0]
              : paramsObj.dateBirth || "",
          };
          const customFields = {};
          Object.keys(paramsObj).forEach((key) => {
            if (!standardFields.includes(key)) {
              customFields[key] = Array.isArray(paramsObj[key])
                ? paramsObj[key][0]
                : paramsObj[key];
            }
          });
          const buildMedia = (p) => {
            const platforms = [
              { key: "telegram", idKey: "telegramIds" },
              { key: "twitter", idKey: "twitterIds" },
              { key: "facebook", idKey: "facebookIds" },
              { key: "linkedin", idKey: "linkedInIds" },
            ];
            const media = {};
            platforms.forEach(({ key, idKey }) => {
              const arr = Array.isArray(p[idKey])
                ? p[idKey]
                : p[idKey]
                ? [p[idKey]]
                : [];
              media[key] = arr.map((id) => ({ idNum: id, name: id }));
            });
            return media;
          };
          let relationData = { person: [], organization: [] };
          if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
            paramsObj.relation.forEach((relationStr) => {
              try {
                const parsed = JSON.parse(relationStr);
                if (parsed.person && Array.isArray(parsed.person)) {
                  relationData.person = [
                    ...relationData.person,
                    ...parsed.person,
                  ];
                }
                if (parsed.organization && Array.isArray(parsed.organization)) {
                  relationData.organization = [
                    ...relationData.organization,
                    ...parsed.organization,
                  ];
                }
              } catch (e) {}
            });
          }
          return {
            ...paramsObj,
            basic: basicObj,
            customFields,
            media: buildMedia(paramsObj),
            relation: relationData,
          };
        }
        return params;
      }
    },
    // 通过组织关系id进行搜索ES
    searchOrganizationRelation(state, organizationRelationId) {
      let add_es_query_conditions = {
        bool: {
          should: organizationRelationId.map(account => ({
            term: { _id: account}
          }))
        },
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { from: 0, size: 200 },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: { data_range_index_name: "key_organization" },
          },
        ],
        (res) => {
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach((item) => {
            let organizationData = buildOrganizationData(item._source.params);
            state.organizationRelationSearchList.push(organizationData);
          });
        }
      );
      function buildOrganizationData(params) {
        console.log("buildOrganizationData", params);
        
        // 如果params已经是对象格式，直接返回
        if (typeof params === 'object' && !Array.isArray(params)) {
          return params;
        }
        
        // 处理params为数组的情况
        if (Array.isArray(params)) {
          const paramsObj = {};
          params.forEach((param) => {
            paramsObj[param.k] = param.v;
          });
          
          // 定义一个辅助函数来获取数组中的第一个元素或默认值
          const getFirstOrDefault = (arr, defaultVal = '') => {
            return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
          };
          
          // 创建basic对象以兼容前端视图
          const basicObj = {
            id: getFirstOrDefault(paramsObj.id),
            name: getFirstOrDefault(paramsObj.name),
            remark: getFirstOrDefault(paramsObj.remark),
            createTime: getFirstOrDefault(paramsObj.createTime),
            belong: getFirstOrDefault(paramsObj.belong),
            desi: getFirstOrDefault(paramsObj.desi),
            avatar: getFirstOrDefault(paramsObj.avatar),
          };
          
          // 组装customFields
          const standardFields = [
            "id", "name", "remark", "createTime", "belong", "desi", "avatar", "relation", "customFields"
          ];
          const customFields = {};
          Object.keys(paramsObj).forEach(key => {
            if (!standardFields.includes(key)) {
              customFields[key] = getFirstOrDefault(paramsObj[key]);
            }
          });
          
          // 处理关系数据
          let relationData = { person: [], organization: [] };
          if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
            paramsObj.relation.forEach(relationStr => {
              try {
                const parsed = JSON.parse(relationStr);
                if (parsed.person && Array.isArray(parsed.person)) {
                  relationData.person = [...relationData.person, ...parsed.person];
                }
                if (parsed.organization && Array.isArray(parsed.organization)) {
                  relationData.organization = [...relationData.organization, ...parsed.organization];
                }
              } catch (e) {
                console.warn("解析组织关系数据失败:", relationStr, e);
              }
            });
          }
          
          // 组装最终params对象
          const result = {
            ...paramsObj,
            basic: basicObj,
            customFields,
            relation: relationData
          };
          
          console.log("构建后的组织数据:", result);
          return result;
        }
        
        // 如果params格式不识别，返回原始数据
        console.warn("无法识别的组织params格式:", params);
        return params;
      }
    },
    // 更新Twitter用户的followers数据
    updateTwitterFollowers(state, { userId, followers }) {
      const userIndex = state.twitterSearchList.findIndex(item => item.user_id === userId);
      if (userIndex !== -1) {
        if (window.main && window.main.$set) {
          window.main.$set(state.twitterSearchList[userIndex], 'followers', followers);
        } else {
          state.twitterSearchList[userIndex].followers = followers;
          state.twitterSearchList = [...state.twitterSearchList];
        }
      }
    },
    // 更新Twitter用户的following数据
    updateTwitterFollowing(state, { userId, following }) {
      const userIndex = state.twitterSearchList.findIndex(item => item.user_id === userId);
      if (userIndex !== -1) {
        if (window.main && window.main.$set) {
          window.main.$set(state.twitterSearchList[userIndex], 'following', following);
        } else {
          state.twitterSearchList[userIndex].following = following;
          state.twitterSearchList = [...state.twitterSearchList];
        }
      }
    },
    // 通过twitter号码进行搜索
    searchTwitter(state, twitter) {
      let row =
        window.main.$tools.sha512("p;" + state.twitterPath) +
        ";p;" +
        state.twitterPath +
        ";" +
        twitter;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: { row_key: [row], size: 200 },
            msg: {
              type: "public",
              path: state.twitterPath,
              prefix: twitter,
              relation: "",
            },
          },
        ],
        (data) => {
          if (data?.length > 0 && data[0].row != '') {
            data.forEach((item) => {
              state.twitterSearchList.push(item.columnValues.d);
            });
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefix.Query",
              [
                {
                  head: { row_key: [], size: 200 },
                  msg: {
                    type: "public",
                    path: state.twitterPath,
                    prefix: "",
                    relation: twitter + ";followers",
                  },
                },
              ],
              (res) => {
                if (!res?.length) return;
                console.log("res-twitter-followers", res);
                window.main.$store.commit("organizationTopologyMap/updateTwitterFollowers", { userId: twitter, followers: res });
              }
            );
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefix.Query",
              [
                {
                  head: { row_key: [], size: 200 },
                  msg: {
                    type: "public",
                    path: state.twitterPath,
                    prefix: "",
                    relation: twitter + ";following",
                  },
                },
              ],
              (res) => {
                if (!res?.length) return;
                console.log("res-twitter-following", res);
                window.main.$store.commit("organizationTopologyMap/updateTwitterFollowing", { userId: twitter, following: res });
              }
            );
          }
        }
      );
    },
    // 通过telegram号码进行搜索
    searchTelegram(state, telegram) {
      let row =
        window.main.$tools.sha512("p;" + state.telegramPath) +
        ";p;" +
        state.telegramPath +
        ";" +
        telegram;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: { row_key: [row], size: 200 },
            msg: {
              type: "public",
              path: state.telegramPath,
              prefix: telegram,
              relation: "",
            },
          },
        ],
        (data) => {
          if (data?.length && data[0].row != '') {
            data.forEach((item) => {
              state.telegramSearchList.push(item.columnValues.d);
            });
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefix.Query",
              [
                {
                  head: { row_key: [], size: 20 },
                  msg: {
                    type: "public",
                    path: state.telegramPath,
                    prefix: "",
                    relation: telegram + ";followers",
                  },
                },
              ],
              (res) => {
                if (!res?.length) return;
                console.log("res-telegram-followers", res);
                state.telegramSearchList.forEach((item) => {
                  if (item.user_id === telegram) {
                    item["followers"] = res;
                  }
                });
              }
            );
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefix.Query",
              [
                {
                  head: { row_key: [], size: 20 },
                  msg: {
                    type: "public",
                    path: state.telegramPath,
                    prefix: "",
                    relation: telegram + ";following",
            },
          },
        ],
        (res) => {
                if (!res?.length) return;
                console.log("res-telegram-following", res);
                state.telegramSearchList.forEach((item) => {
                  if (item.user_id === telegram) {
                    item["following"] = res;
                  }
                });
              }
            );
          }
        }
      );
    },
    // 通过facebook号码进行搜索
    searchFacebook(state, facebook) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: { family: ["r"], row_key: [], size: 20 },
            msg: {
              type: "public",
              path: state.facebookPath,
              prefixe: facebook,
              relation: "",
            },
          },
        ],
        (res) => {
          // 可根据需要处理 facebook 查询结果
        }
      );
    },
    // 通过linkedin号码进行搜索
    searchLinkedin(state, linkedin) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { from: 0, size: 200 },
            control: {
              query_type: "public",
              query_string: linkedin,
              condition: {
                query_mode: "match_phrase",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
              },
            },
            msg: {
              data_range_index_name:
                "social_platform_information_prefix_linkedin__",
            },
          },
        ],
        (res) => {
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach((item) => {
            state.linkedinSearchList.push(item);
          });
        }
      );
    },
    // 通过电话号码进行搜索
    searchPhone(state, phone) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: { family: ["r"], row_key: [], size: 20 },
            msg: {
              type: "public",
              path: state.phoneInfoPath,
              prefix: phone,
              relation: "",
            },
          },
        ],
        (res) => {
          // 可根据需要处理电话号码查询结果
        }
      );
    },
    // 通过身份证号码进行搜索
    searchIdentity(state, identity) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: { family: ["r"], row_key: [], size: 20 },
            msg: {
              type: "public",
              path: state.identityInfoPath,
              prefixe: identity,
              relation: "",
            },
          },
        ],
        (res) => {
          // 可根据需要处理身份证号码查询结果
        }
      );
    },
  },
};
