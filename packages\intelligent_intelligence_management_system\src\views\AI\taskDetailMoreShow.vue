<template>
  <div>
    <div style="width: 60vw; margin: 0 auto">
      <h2 style="margin: 15px 0px">{{ $route.params.title }}的任务报告</h2>
      <div
        style="
          margin-bottom: 15px;
          overflow-x: auto;
          white-space: nowrap;
          padding-bottom: 5px;
        "
      >
        <el-button
          type="danger"
          @click="tabFn('logs')"
          size="mini"
          :plain="showTab != 'logs' ? true : false"
          >日志</el-button
        >

        <el-button
          v-if="tabList.length > 0"
          type="primary"
          @click="tabFn('bulletin', item)"
          size="mini"
          :plain="nowTimeNum != item ? true : false"
          v-for="item in tabList"
          :key="item"
        >
          {{ $tools.timestampToTimeH(item) }}报告</el-button
        >
        <!-- <el-button
          type="success"
          @click="tabFn('files')"
          size="mini"
          :plain="showTab != 'files' ? true : false"
          >文件</el-button
        > -->
      </div>
    </div>

    <div style="overflow-y: auto" class="MDinput">
      <div v-if="showTab == 'bulletin'" style="height: 90%; overflow: auto">
        <div v-if="$route.params.method === 'data_analysis_search_task'">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="舆情" name="public_opinion"></el-tab-pane>
            <el-tab-pane label="Telegram" name="telegram"></el-tab-pane>
            <el-tab-pane label="Twitter" name="twitter"></el-tab-pane>
            <el-tab-pane label="Facebook" name="facebook"></el-tab-pane>
            <el-tab-pane label="Linkedin" name="linkedin"></el-tab-pane>
            <el-tab-pane
              label="社工库"
              name="social_work_library"
            ></el-tab-pane>
            <el-tab-pane label="分析报告" name="analysis_report"></el-tab-pane>
          </el-tabs>

          <div v-if="activeName === 'public_opinion'">
            <component :is="'public_opinion_article_list'"></component>
            <component :is="'public_opinion_search_results_trend'"></component>
            <component
              :is="'public_opinion_media_publication_statistics'"
            ></component>
            <component :is="'public_opinion_word_cloud'"></component>
            <component :is="'public_opinion_keyword_evolution'"></component>
            <component :is="'public_opinion_country'"></component>
          </div>
          <div v-if="activeName === 'analysis_report'">
            <component :is="'bulletin'" @child-event="saveBulletin"></component>
          </div>
        </div>
        <!-- <component
          :is="detail_key"
          v-for="(detail_val, detail_key) in taskDetail"
          :key="detail_key"
        ></component> -->
        <component
          :is="'article_list'"
          v-if="taskDetail.hasOwnProperty('article_list')"
        ></component>
        <component
          :is="'article_category'"
          v-if="taskDetail.hasOwnProperty('article_category')"
        ></component>
        <component
          :is="'bulletin'"
          @child-event="saveBulletin"
          v-if="
            taskDetail.hasOwnProperty('bulletin') &
            ($route.params.method != 'data_analysis_search_task')
          "
        ></component>
      </div>
      <!-- <div v-if="showTab === 'files'">下载文件</div> -->
      <div
        style="height: 90%; overflow: auto"
        v-if="showTab == 'logs'"
        ref="scroll"
        @scroll="handleScroll"
        class="hitsLayscroll"
        v-loading="logList.length > 0 ? false : true"
      >
        <!-- 其他滚动内容 -->
        <div class="chat-content" ref="chatContent">
          <li
            v-for="(item, index) in logList"
            :key="index"
            style="list-style: none"
          >
            <window-session-content
              :role="item.role"
              :content-show-type="windowData.contentShowType"
              :item-data="item"
            ></window-session-content>
          </li>
        </div>
      </div>
    </div>
    <el-dialog
      title="选择节点并保存"
      :visible.sync="dialogVisibleSave"
      width="50%"
      height="600px"
      append-to-body
      :modal-append-to-body="false"
    >
      <div style="display: flex">
        <div class="caseDirLay">
          <tree-box
            :key="componentKey"
            :data-node="rootNode"
            sub-component="mail-manage-tree"
          ></tree-box>
          <p class="bottom_tip">没有数据了，已经到底了!</p>
        </div>
        <div style="padding-left: 20px">
          <el-form
            ref="saveForm"
            :model="saveForm"
            label-width="80px"
            :rules="saveFormRules"
          >
            <el-form-item label="情报名" prop="name">
              <el-input v-model="saveForm.name"></el-input>
            </el-form-item>
            <el-form-item label="情报类型" prop="type">
              <el-input v-model="saveForm.type"></el-input>
            </el-form-item>
            <el-form-item label="情报节点" prop="node">
              <el-input v-model="saveForm.node" disabled></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSave = false">取 消</el-button>
        <el-button type="primary" @click="saveBtn('saveForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import ContentShowType from "@/common/constants/ContentShowType";
import WindowSessionContent from "@/components/session/window/chat/WindowSessionContent";
import { type } from "@/i18n/zh/list";
const windowData = {
  contentShowType: ContentShowType.Markdown,
};
export default {
  name: "taskDetailMoreShow",
  data() {
    return {
      activeName: "public_opinion",
      showTab: "bulletin",
      windowData,
      nowTimeNum: 0,
      dialogVisibleSave: false,
      saveForm: {
        name: "",
        type: "",
        node: "",
        content: null,
      },
      saveFormRules: {
        name: [{ required: true, message: "请输入情报名称", trigger: "blur" }],
        type: [{ required: true, message: "请输入情报类型", trigger: "blur" }],
        node: [
          { required: true, message: "请在左侧选择节点", trigger: "blur" },
        ],
      },
      tabList: [],
    };
  },

  computed: {
    ...mapState({
      caseDetail: (state) => state.intellManageTree.caseDetail,
      componentKey: (state) => state.intellManageTree.componentKey,
      rootNode: (state) => state.intellManageTree.rootNode,
      taskDetail: (state) => state.aiTaskQueue.taskDetail,
      logList: (state) => state.aiTaskQueue.logList,
    }),
    taskMethodDetail: {
      get() {
        return this.$store.state.aiTaskQueue.taskMethodDetail;
      },
      set(val) {},
    },
    caseDetail: {
      get() {
        return this.$store.state.intellManageTree.caseDetail;
      },
      set(val) {},
    },
  },
  watch: {
    taskMethodDetail: {
      handler(newVal) {
        if (newVal && newVal.columnValues) {
          console.log("案件发生变化", newVal);
          this.saveForm.type = newVal.columnValues.info.method;
        } else {
          this.saveForm.type = null;
        }
      },
      immediate: true,
      deep: true,
    },
    caseDetail: {
      handler(newVal) {
        if (newVal && newVal.columnValues && newVal.columnValues.i) {
          console.log("案件发生变化", newVal.columnValues.i.case_name);
          this.saveForm.node = newVal.columnValues.i.case_name;
        } else {
          this.saveForm.node = null;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.$store.commit("aiTaskQueue/setClearLogTemporaryList");
    this.$store.commit("aiTaskQueue/setClearLogList");
    this.$store.commit("aiTaskQueue/setClearLastRowKey");

    this.$store.commit("aiTaskQueue/sendGetTaskLog", this.$route.params.id);
    this.$store.commit(
      "aiTaskQueue/setSearchConditions",
      this.$route.params.id
    );
    window.main.$main_socket.sendData(
      "Api.DataAnalysisTask.ListDatas",
      [
        {
          head: {
            size: 200,
          },
          msg: {
            task_authority: "username",
            task_id: this.$route.params.id,
            task_type: "ai_workflow_task",
            relation: "timelist",
          },
        },
      ],
      (res) => {
        for (let i = 0; i < res.length; i++) {
          this.tabList.push(res[i].columnValues.d.data[0]);
        }
        this.tabList.sort((a, b) => b - a);
        this.nowTimeNum = this.tabList[0];
        this.getTaskDetail(this.tabList[0]);
      }
    );
  },

  mounted() {
    this.getTaskMethod();
    this.getCase();
    this.$store.commit("intellManageTree/nowChooseCase", "dir");
  },
  components: {
    WindowSessionContent,
    bulletin: () => import("./task_detail/bulletin.vue"),
    article_category: () => import("./task_detail/article_category.vue"),
    article_list: () => import("./task_detail/article_list.vue"),
    "tree-box": () => import("@/components/caseTree/tree_box.vue"),
    public_opinion_article_list: () =>
      import("./task_detail/public_opinion/public_opinion_article_list.vue"),
    public_opinion_search_results_trend: () =>
      import(
        "./task_detail/public_opinion/public_opinion_search_results_trend.vue"
      ),
    public_opinion_media_publication_statistics: () =>
      import(
        "./task_detail/public_opinion/public_opinion_media_publication_statistics.vue"
      ),
    public_opinion_word_cloud: () =>
      import("./task_detail/public_opinion/public_opinion_word_cloud.vue"),
    public_opinion_keyword_evolution: () =>
      import(
        "./task_detail/public_opinion/public_opinion_keyword_evolution.vue"
      ),
    public_opinion_country: () =>
      import("./task_detail/public_opinion/public_opinion_country.vue"),
  },

  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
      this.activeName = tab.name;
    },
    //获取任务按时间分类的任务报告
    getTaskDetail(timeNum) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListDatas",
        [
          {
            head: {
              size: 200,
            },
            msg: {
              task_authority: "username",
              task_id: this.$route.params.id,
              task_type: "ai_workflow_task",
              relation: String(timeNum),
            },
          },
        ],
        (res) => {
          let objData = new Object();
          let a = 0;
          for (let i = 0; i < res.length; i++) {
            Object.defineProperty(
              objData,
              res[i].columnValues.d["_"].split(";")[
                res[i].columnValues.d["_"].split(";").length - 1
              ],
              {
                value: res[i].columnValues.d.data,
                writable: true, // 允许修改
                enumerable: true, // 可枚举（for...in 或 Object.keys() 可见）
                configurable: true, // 允许删除或重新定义
              }
            );
            a++;
          }
          if (a === res.length) {
            this.$store.commit("aiTaskQueue/setTaskDetail", objData);
          }
        }
      );
    },
    //获取任务的method
    getTaskMethod() {
      this.$store.dispatch("aiTaskQueue/getTaskMethod", this.$route.params.id);
    },
    //获取情报树
    getCase() {
      if (window.main.$pki_socket) {
        this.$store.commit("intellManageTree/resetCaseDirTree");
        this.$store.commit("intellManageTree/setCaseDirFatherListObj", []);
        this.$store.commit("intellManageTree/setCaseDirFatherList", []);
        this.$store.commit("intellManageTree/setCaseDirFather", "");
        this.$store.commit("intellManageTree/sendCaseDirFatherArr");
      }
    },
    tabFn(tab, timeNum) {
      this.nowTimeNum = 0;
      this.showTab = tab;
      if (tab === "bulletin") {
        this.nowTimeNum = timeNum;
        this.getTaskDetail(timeNum);
      }
    },
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        /*  this.loadData(); */
      }
    },
    saveBulletin(v) {
      this.$store.commit("intellManageTree/setClearcaseId");
      this.saveForm.content = v;
      this.$store.commit("intellManageTree/setCaseDetail", {});
      this.saveForm.name = "";
      this.dialogVisibleSave = true;
    },
    saveBtn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          /* this.$store.commit("aiTaskQueue/addIntelligence", {
            name: this.saveForm.name,
            type: this.saveForm.type,
            content: this.saveForm.content,
          }); */
          //使用当前时间戳与随机数结合当作预警词的唯一id
          function reduceNumber() {
            let soleValue = Math.round(new Date().getTime() / 1000).toString();
            let random = new Array(
              "a",
              "b",
              "c",
              "d",
              "e",
              "f",
              "g",
              "h",
              "i",
              "j",
              "k",
              "l",
              "m",
              "n"
            );
            for (let i = 0; i < 6; i++) {
              let index = Math.floor(Math.random() * 13);
              soleValue += random[index];
            }
            return soleValue;
          }

          let intelligenceId = reduceNumber();
          window.main.$main_socket.sendData(
            "Api.Search.SearchList.AddOne",
            [
              {
                head: {},
                control: {
                  query_type: "case",
                  index: "key_intelligence",

                  id: intelligenceId,
                },
                msg: {
                  type: "case",
                  case_id:
                    window.main.$store.state.intellManageTree.caseDetail.row,
                  content_article: this.saveForm.content,
                  title: this.saveForm.name,
                  params: [
                    { k: "type", v: [this.saveForm.type] },
                    { k: "id", v: [intelligenceId] },
                  ],
                },
              },
            ],
            (res) => {
              if (res.status == "ok") {
                window.main.$message.success("添加情报成功");
                this.dialogVisibleSave = false;
                /*   window.main.$store.commit("organization/getOrgani"); */
              }
            }
          );
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.caseDirLay {
  width: 25%;
  height: 50vh;
  border: 1px solid #ebeef5;
  overflow: auto;
  padding: 0 0 0 10px;
  margin-left: 5px;
}
.intellRignt {
  width: 75%;
  height: 100%;
}
.MDinput {
  width: 60vw;
  height: 85vh;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}
</style>

<style scoped>
::v-deep .el-dialog__wrapper {
  z-index: 2000 !important;
}
::v-deep .v-modal {
  z-index: 1999 !important;
}
</style>
