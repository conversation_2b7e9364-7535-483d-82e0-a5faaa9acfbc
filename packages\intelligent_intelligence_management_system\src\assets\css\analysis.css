html, body, #app, .el-container {
  height: 100%;
  font-family: "Microsoft YaHei";
}

.model_lay {
  height: 100%;
  background: #fff;
  overflow: hidden;
}

.omitted {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model_lay {
  position: relative;
  width: 100%;
  height: 100%;
}

.model_lay .analysis_list_lay {
  width: 100%;
  height: 95%;
}

.model_lay .del {
  margin-top: 10px;
  margin-left: 10px;
  color: #f78989;
  font-size: 13px;
}

.model_lay .del:hover {
  cursor: pointer;
}

.model_lay .analysis-title {
  margin: 0;
  padding: 0;
  height: 50px;
  font-size: 24px;
  line-height: 24px;
  border-bottom: 1px solid #eee;
  color: #909399;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.model_lay .analysis-title span {
  margin-top: 13px;
  margin-left: 10px;
}

.model_lay .select {
  height: 40px;
  float: left;
  line-height: 40px;
  margin-left: 10px;
}

.model_lay .myprogress {
  width: 100%;
  height: 15px;
  background: #ebeef5;
  border-radius: 8px;
}

.model_lay .myprogressreceived {
  height: 15px;
  background: #67c23a;
  border-radius: 8px;
  line-height: 15px;
  font-size: 12px;
  color: #fff;
  position: relative;
}

.model_lay .textspan {
  position: absolute;
  top: 0px;
  right: 0px;
}

.model_lay .myprogressreceivedasync {
  position: absolute;
  height: 15px;
  background: rgba(64, 158, 255, 0.8);
  border-radius: 8px;
  line-height: 15px;
  font-size: 12px;
  color: #fff;
  left: 9px;
  top: 19px;
  text-align: right;
}
/*# sourceMappingURL=analysis.css.map */