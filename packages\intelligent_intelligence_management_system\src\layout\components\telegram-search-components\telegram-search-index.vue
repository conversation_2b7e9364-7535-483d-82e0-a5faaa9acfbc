<template>
  <div class="telegram-main" id="closeESsearch">
    <div class="select-search-type">
      <el-radio-group v-model="tgSearchType" @change="changeSearchType">
        <el-radio v-if="isShowRadio" label="group_data_prefix_telegram">群数据</el-radio>
        <el-radio v-if="isShowRadio" label="group_member_data_telegram"
          >群成员数据</el-radio
        >
        <el-radio label="group_content_data_prefix_telegram"
          >消息数据</el-radio
        >
      </el-radio-group>
      <div class="import_collect_btn">
        <el-button
          plain
          size="small"
          v-show="tgSearchType == 'group_content_data_prefix_telegram'"
          @click="handleImportData"
          title="导出"
          >导出</el-button
        >
        <el-button
          plain
          size="small"
          v-show="tgSearchType == 'group_content_data_prefix_telegram'"
          @click="handleCollect"
          title="收藏"
          >收藏</el-button
        >
      </div>
    </div>
    <div class="data_content">
      <!-- 左侧内容 -->
      <div
        class="content_left"
        ref="scrollContainer"
        v-loading="tgLoading && (!queryEnd || !listTrueEnd)"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <div
          v-if="activeNamePrefix !== 'group_data_prefix_telegram'"
          class="group_content_box"
        >
          <el-table
            :data="dataList"
            style="width: 100%"
            :height="'100%'"
            :infinite-scroll-immediate="false"
            infinite-scroll-distance="'5px'"
            v-el-table-infinite-scroll="loadMoreEsSeasrch"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="left_box">
                  <div
                    v-if="scope.$index % 20 === 0 && scope.$index !== 0"
                    class="index-marker"
                  >
                    第 {{ scope.$index / 20 }} 页
                  </div>
                  <div class="left_item">
                    <div class="item_info">
                      <div class="user_info">
                        <img
                          v-if="
                            memberIcon.hasOwnProperty(
                              scope.row._source.group_member
                            ) && memberIcon[scope.row._source.group_member].icon
                          "
                          :src="
                            '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                            memberIcon[scope.row._source.group_member].icon +
                            '?session_id=' +
                            userInfo.session_id
                          "
                        />
                        <img
                          v-else
                          :src="require('@/assets/images/user.png')"
                        />
                        <div
                          class="perc"
                          v-if="
                            memberIcon.hasOwnProperty(
                              scope.row._source.group_member
                            )
                          "
                        >
                          <p
                            v-if="
                              memberIcon[scope.row._source.group_member]
                                .nickname
                            "
                          >
                            <b>昵称：</b
                            >{{
                              memberIcon[scope.row._source.group_member]
                                .nickname
                            }}
                          </p>
                          <p
                            v-if="
                              memberIcon[scope.row._source.group_member]
                                .username
                            "
                          >
                            <b>用户名：</b
                            >{{
                              memberIcon[scope.row._source.group_member]
                                .username
                            }}
                          </p>
                          <p
                            v-if="
                              memberIcon[scope.row._source.group_member]
                                .telephone
                            "
                          >
                            <b>手机号：</b
                            >{{
                              memberIcon[scope.row._source.group_member]
                                .telephone
                            }}
                          </p>
                          <p
                            v-if="
                              memberIcon[scope.row._source.group_member]
                                .location
                            "
                          >
                            <b>归属地：</b
                            >{{
                              memberIcon[scope.row._source.group_member]
                                .location
                            }}
                          </p>
                        </div>
                      </div>
                      <div class="msg_info">
                        <p
                          v-if="
                            scope.row._source && scope.row._source.group_member
                          "
                        >
                          <b>发言成员（ID）：</b
                          >{{ scope.row._source.group_member }}
                        </p>
                        <p
                          v-if="scope.row._source && scope.row._source.group_id"
                        >
                          <b>发言群组（ID）：</b
                          >{{ scope.row._source.group_id }}
                        </p>
                        <p
                          v-if="
                            scope.row._source && scope.row._source.timestamp
                          "
                        >
                          <b>发言时间：</b
                          >{{
                            $tools.timestampToTime(scope.row._source.timestamp)
                          }}
                        </p>
                        <p
                          v-if="
                            scope.row._source && scope.row._source['@timestamp']
                          "
                        >
                          <b>入库时间：</b
                          >{{
                            $tools.timestampToTime(
                              scope.row._source["@timestamp"]
                            )
                          }}
                        </p>
                        <p v-if="scope.row._source">
                          <b>语义分析：</b
                          >{{
                            scope.row._source.nlp_parse_flag
                              ? "已分析"
                              : "未分析"
                          }}
                        </p>
                        <p v-if="scope.row._score">
                          <b>搜索评分：</b>{{ scope.row._score }}
                        </p>
                      </div>
                    </div>
                    <div class="item_body">
                      <div class="body_btn">
                        <b>消息内容：</b>
                        <div v-if="!(scope.row == null)">
                          <!-- <el-button
                            type="primary"
                            icon="el-icon-menu"
                            size="mini"
                            v-if="val._source.hasOwnProperty('content_article')"
                            @click.native="addTemporary(val)"
                            circle
                            title="添加分组"
                          ></el-button> -->
                          <el-button
                            type="primary"
                            icon="el-icon-more"
                            size="mini"
                            @click.native="morefn(scope.row)"
                            circle
                            title="详情"
                          ></el-button>
                        </div>
                      </div>
                      <div class="body_text">
                        <p
                          v-if="
                            scope.row['_source'].hasOwnProperty(
                              'content_article'
                            ) && !scope.row.hasOwnProperty('highlight')
                          "
                        >
                          {{ scope.row["_source"].content_article }}
                        </p>
                        <p
                          v-html="
                            scope.row['highlight'].content.join(
                              '&l t ;' + 'b r /' + '&gt;'
                            )
                          "
                          v-if="scope.row.hasOwnProperty('highlight')"
                        ></p>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          class="left_member"
          v-if="activeNamePrefix === 'group_data_prefix_telegram'"
          ref="groupDataContainer"
          v-loading="tgLoading && (!queryEnd || !listTrueEnd)"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          style="margin-top: 0"
          @scroll="handleGroupDataScroll"
        >
          <div
            class="member_item"
            v-for="(val, index) in dataList"
            :key="index"
          >
            <div class="member_btn">
              <div class="info">
                <img
                  v-if="
                    val._source &&
                    val._source.icon &&
                    val._source.icon[0].sha512_hash
                  "
                  :src="
                    '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                    val._source.icon[0].sha512_hash +
                    '?session_id=' +
                    userInfo.session_id
                  "
                />
                <img v-else :src="require('@/assets/images/user.png')" />
                <div style="width: 80%">
                  <p v-if="val._source && val._source.user_id">
                    <b>成员ID：</b>{{ val._source.user_id }}
                  </p>
                  <template v-if="val._source && val._source.nickname">
                    <template v-for="(item, index) in val._source.nickname">
                      <p v-if="item" :key="item">
                        <b>成员昵称{{ index + 1 }}：</b>
                        <span :title="item">{{ item }}</span>
                      </p>
                    </template>
                  </template>
                  <template v-if="val._source && val._source.username">
                    <template v-for="(item, index) in val._source.username">
                      <p v-if="item" :key="item">
                        <b>成员用户名{{ index + 1 }}：</b>
                        <span :title="item">{{ item }}</span>
                      </p>
                    </template>
                  </template>
                </div>
              </div>
              <div v-if="!(val == null)">
                <el-button
                  type="primary"
                  icon="el-icon-more"
                  size="mini"
                  @click.native="morefn(val)"
                  circle
                  title="详情"
                ></el-button>
              </div>
            </div>
            <div class="member_info">
              <div>
                <p v-if="val._score"><b>搜索命中评分：</b>{{ val._score }}</p>

                <p v-if="val._source && val._source.group_id">
                  <b>来自群ID：</b>{{ val._source.group_id }}
                </p>
                <p
                  v-if="val._source && val._source.group_name"
                  :title="val._source.group_name"
                >
                  <b>来自群名称：</b>{{ val._source.group_name }}
                </p>
              </div>
              <div>
                <p v-if="val._source && val._source['@timestamp']">
                  <b>入库时间：</b
                  >{{ $tools.timestampToTime(val._source["@timestamp"]) }}
                </p>
                <p v-if="val._source">
                  <b>语义分析：</b
                  >{{ val._source.nlp_parse_flag ? "已分析" : "未分析" }}
                </p>
                <p v-if="val._source && val._source.type">
                  <b>来自平台：</b>{{ val._source.type }}
                </p>
              </div>
            </div>
          </div>
          <div
            v-if="dataList.length > 0 && !queryEnd && !listTrueEnd"
            class="loading-more-indicator"
          >
            <i class="el-icon-loading"></i> 加载更多...
          </div>
          <div
            v-if="dataList.length > 0 && (queryEnd || listTrueEnd)"
            class="end-of-list"
          >
            — 已经到底啦 —
          </div>
        </div>
      </div>
      <!-- 右侧内容 -->
      <!-- <div class="content_right">
        <div id="dataTimeDistributeLineChat"></div>
        <div id="typeChart"></div>
        <div id="personnelChart"></div>
        <div id="groupChart"></div>
      </div> -->
    </div>
    <el-dialog
      title="详情"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      :before-close="handleClose"
      width="80%"
      style="margin-top: -10vh !important"
      append-to-body
    >
      <el-tabs
        style="height: 700px"
        type="border-card"
        @tab-click="handleClick"
        v-model="tabactiveName"
      >
        <div class="list_title">
          {{ bian }}<span>{{ tmpDataDetail.d["_index"] }}</span>
          <br />
          数据编号：<span>{{ tmpDataDetail.d["_id"] }}</span>
        </div>
        <el-tab-pane v-if="tmpDataDetail.d" name="baseData">
          <span slot="label">{{ $t("common." + "base_data") }}</span>
          <div>
            <el-collapse
              v-model="activeName"
              accordion
              style="margin-top: 20px"
            >
              <el-collapse-item name="1">
                <template slot="title"> 基础信息 </template>
                <div class="morInfor">
                  <div
                    class="morInfor_l"
                    v-if="
                      !(tmpDataDetail.d == null) &&
                      tmpDataDetail.d.hasOwnProperty('_source') &&
                      tmpDataDetail.d._source.hasOwnProperty('icon')
                    "
                  >
                    <img
                      :src="
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                        tmpDataDetail.d._source.icon[0].sha512_hash +
                        '?session_id=' +
                        userInfo.session_id
                      "
                      :type="tmpDataDetail.d._source.icon[0].icon_type"
                      :onerror="defaultImg"
                    />
                  </div>
                  <div class="morInfor_r">
                    <ul>
                      <li
                        class="morInfor_r_li"
                        v-for="(lival, liname, liindex) in tmpDataDetail.d[
                          '_source'
                        ]"
                        :key="liindex"
                        v-show="
                          liname != 'analysis_doc' &&
                          liname != 'content' &&
                          liname != 'content_article' &&
                          liname != 'content_img' &&
                          liname != 'content_video' &&
                          liname != 'content_voice' &&
                          liname != 'content_file' &&
                          liname != 'translation_text' &&
                          liname != 'original_text' &&
                          liname != 'image_text'
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list." + liname) }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            liname == "timestamp" ||
                            liname == "@timestamp" ||
                            liname == "birthday" ||
                            liname == "father_birthday" ||
                            liname == "mather_birthday"
                              ? $tools.timestampToTime(lival)
                              : lival
                          }}
                        </div>
                      </li>
                      <li
                        class="morInfor_r_li"
                        v-if="
                          tmpDataDetail.base_data.columnValues &&
                          tmpDataDetail.base_data.columnValues.nlp &&
                          tmpDataDetail.base_data.columnValues.nlp
                            .hanlp_server &&
                          tmpDataDetail.base_data.columnValues.nlp.hanlp_server
                            .keyword_list
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list.keyword_list") }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            tmpDataDetail.base_data.columnValues.nlp
                              .hanlp_server.keyword_list
                          }}
                        </div>
                      </li>
                      <li
                        class="morInfor_r_li"
                        v-if="
                          tmpDataDetail.base_data.columnValues &&
                          tmpDataDetail.base_data.columnValues.nlp &&
                          tmpDataDetail.base_data.columnValues.nlp
                            .hanlp_server &&
                          tmpDataDetail.base_data.columnValues.nlp.hanlp_server
                            .sentence_list
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list.sentence_list") }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            tmpDataDetail.base_data.columnValues.nlp
                              .hanlp_server.sentence_list
                          }}
                        </div>
                      </li>
                    </ul>
                    <div
                      v-if="
                        tmpDataDetail.base_data.columnValues &&
                        tmpDataDetail.base_data.columnValues.nlp &&
                        tmpDataDetail.base_data.columnValues.nlp.hanlp_server &&
                        tmpDataDetail.base_data.columnValues.nlp.hanlp_server
                          .analysis_doc
                      "
                    >
                      <div id="myChart" :style="{ height: '400px' }"></div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="消息原文"
                name="2"
                v-if="tmpDataDetail.d._source.hasOwnProperty('original_text')"
              >
                <div style="white-space: pre-line; line-height: 20px">
                  {{ tmpDataDetail.d._source.original_text }}
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="消息译文"
                name="9"
                v-if="
                  tmpDataDetail.d._source.hasOwnProperty('translation_text')
                "
              >
                <div style="white-space: pre-line; line-height: 20px">
                  {{ tmpDataDetail.d._source.translation_text }}
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="图片"
                name="3"
                v-if="tmpDataDetail.d['_source'].hasOwnProperty('content_img')"
              >
                <div class="imgAllBox">
                  <div
                    v-for="(imgItem, imgindex) in tmpDataDetail.d['_source'][
                      'content_img'
                    ]"
                    :key="imgindex"
                    style="width: 300px; margin-top: 20px"
                  >
                    <el-image
                      style="width: 300px; height: 300px"
                      :fit="'cover'"
                      :src="
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        userInfo.session_id
                      "
                      :preview-src-list="[
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                          imgItem.sha512_hash +
                          '?session_id=' +
                          userInfo.session_id,
                      ]"
                    >
                    </el-image>
                    <p style="font-weight: bold">{{ imgItem.file_name }}</p>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="视频"
                name="4"
                v-if="
                  tmpDataDetail.d['_source'].hasOwnProperty('content_video')
                "
              >
                <div
                  v-for="(imgItem, imgindex) in tmpDataDetail.d['_source'][
                    'content_video'
                  ]"
                  :key="imgindex"
                >
                  <span v-if="!imgItem">链接为空</span>
                  <video
                    controls="controls"
                    preload="auto"
                    muted
                    loop
                    :ref="'videoB' + imgindex"
                    :id="'videoB' + imgindex"
                  >
                    <source
                      :src="
                        '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        userInfo.session_id
                      "
                      :type="imgItem.content_video_type"
                      :alt="imgItem ? imgItem : '链接为空'"
                    />
                  </video>
                  <p>
                    选择播放速率：<select
                      ref="selRate"
                      @change="videoFn('videoB' + imgindex)"
                    >
                      <option value="0.5">0.5</option>
                      <option value="1" selected>1.0</option>
                      <option value="1.25">1.25</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2.0</option>
                      <option value="2">3.0</option>
                      <option value="2">4.0</option>
                    </select>
                  </p>
                  <p>{{ imgItem.file_name }}</p>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="音频"
                name="5"
                v-if="
                  tmpDataDetail.d['_source'].hasOwnProperty('content_voice')
                "
              >
                <div
                  v-for="(imgItem, imgindex) in tmpDataDetail.d['_source'][
                    'content_voice'
                  ]"
                  :key="imgindex"
                >
                  <video
                    controls="controls"
                    preload="auto"
                    muted
                    loop
                    autoplay
                    :type="tmpDataDetail.d['_source'].content_voice_type"
                    :ref="'videoB' + imgindex"
                    :id="'videoB' + imgindex"
                    :src="
                      '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_voice/' +
                      imgItem.sha512_hash +
                      '?session_id=' +
                      userInfo.session_id
                    "
                    :alt="imgItem ? imgItem : '链接为空'"
                  >
                    {{ imgItem.file_name }}
                  </video>
                  <p>
                    选择播放速率：<select
                      ref="selRate"
                      @change="videoFn('videoB' + imgindex)"
                    >
                      <option value="0.5">0.5</option>
                      <option value="1" selected>1.0</option>
                      <option value="1.25">1.25</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2.0</option>
                      <option value="2">3.0</option>
                      <option value="2">4.0</option>
                    </select>
                  </p>
                  <p>{{ imgItem.file_name }}</p>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="附件"
                name="6"
                v-if="tmpDataDetail.d['_source'].hasOwnProperty('content_file')"
              >
                <a
                  style="color: blue; display: block; line-height: 50px"
                  title="点击下载"
                  v-for="(imgItem, imgindex) in tmpDataDetail.d['_source'][
                    'content_file'
                  ]"
                  :key="imgindex"
                  :href="
                    '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_file/' +
                    imgItem.sha512_hash +
                    '?session_id=' +
                    userInfo.session_id
                  "
                  :type="imgItem.content_file_type"
                  :download="imgItem['file_name']"
                  >{{ imgItem["file_name"] }}</a
                >
              </el-collapse-item>
              <el-collapse-item
                title="pdf预览"
                name="8"
                v-if="tmpDataDetail.d['_source'].hasOwnProperty('content_pdf')"
              >
                <div
                  id="pdfBox"
                  v-for="(imgItem, imgindex) in tmpDataDetail.d['_source'][
                    'content_pdf'
                  ]"
                  :key="imgindex"
                  style="overflow: auto; height: 600px"
                >
                  <p
                    style="
                      margin-top: 10px;
                      font-weight: bold;
                      text-align: center;
                      margin-bottom: 10px;
                      font-size: 16px;
                    "
                  >
                    {{ imgItem.file_name }}
                  </p>
                  <p
                    @click="pdfFn"
                    v-if="!$store.state.search.dataDetail.Pdf"
                    style="text-align: center; cursor: pointer; color: #409eff"
                  >
                    PDF走丢了ㄒoㄒㄒoㄒ，点击一下把他找回来吧~~~~！
                  </p>
                  <div v-if="pdferror" style="text-align: center">
                    <b>not found</b>
                  </div>
                  <div>
                    <a
                      :href="
                        '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        userInfo.session_id
                      "
                      target="_blank"
                    >
                      <pdf
                        v-for="i in numPages[imgindex]"
                        ref="pdf"
                        :key="i"
                        :src="
                          '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                          imgItem.sha512_hash +
                          '?session_id=' +
                          userInfo.session_id
                        "
                        :page="i"
                      ></pdf>
                    </a>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
        <el-tab-pane name="groupContentData">
          <span slot="label">{{ $t("common." + "group_content_data") }}</span>
          <div
            class="group_lay"
            style="height: 570px; overflow: auto"
            @scroll.passive="loadGroupContentData"
          >
            <message-single
              v-for="(item, iconIndex) in groupMsgList"
              :item="item.columnValues"
              :key="iconIndex"
              :msgKey="item.row"
              :msgIcon="msgIconList[iconIndex]"
              :ref="item.row"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane name="groupData">
          <span slot="label">{{ $t("common." + "group_data") }}</span>
          <div class="group_data" v-if="groupDetailed[0]">
            <div
              class="morInfor"
              style="margin-top: 20px; border-bottom: 1px solid #ccc"
            >
              <div
                class="morInfor_l"
                v-if="groupDetailed[0].columnValues.d.hasOwnProperty('icon')"
              >
                <img
                  @error="errorFn($event)"
                  :src="
                    '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                    groupDetailed[0].columnValues.d.icon.icon.sha512_hash +
                    '?session_id=' +
                    userInfo.session_id
                  "
                />
              </div>
              <div class="morInfor_r">
                <ul>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.group_id"
                  >
                    <div class="morInfor_r_li_h">群组ID:</div>
                    <div class="morInfor_r_li_c">
                      {{ groupDetailed[0].columnValues.d.group_id.group_id }}
                    </div>
                  </li>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.group_name"
                  >
                    <div class="morInfor_r_li_h">群组名:</div>
                    <div class="morInfor_r_li_c">
                      {{
                        groupDetailed[0].columnValues.d.group_name.group_name
                      }}
                    </div>
                  </li>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.last_msg"
                  >
                    <div class="morInfor_r_li_h">最新消息:</div>
                    <div class="morInfor_r_li_c">
                      {{ groupDetailed[0].columnValues.d.last_msg.last_msg }}
                    </div>
                  </li>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.last_msg_timestamp"
                  >
                    <div class="morInfor_r_li_h">最新消息时间:</div>
                    <div class="morInfor_r_li_c">
                      {{
                        $tools.timestampToTime(
                          groupDetailed[0].columnValues.d.last_msg_timestamp
                            .last_msg_timestamp
                        )
                      }}
                    </div>
                  </li>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.msg_max_id"
                  >
                    <div class="morInfor_r_li_h">消息总数:</div>
                    <div class="morInfor_r_li_c">
                      {{
                        groupDetailed[0].columnValues.d.msg_max_id.msg_max_id
                      }}
                    </div>
                  </li>
                  <li
                    class="morInfor_r_li"
                    v-if="groupDetailed[0].columnValues.d.type"
                  >
                    <div class="morInfor_r_li_h">类型:</div>
                    <div class="morInfor_r_li_c">
                      {{ groupDetailed[0].columnValues.d.type.type }}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane name="groupMemberData">
          <span slot="label">{{ $t("common." + "group_member_data") }}</span>
          <div v-if="groupMember">
            <scrolldetailc
              @scroll-to-botton="loadGroupMemberData(false)"
              :realMinHeight="100"
            >
              <div class="group_member_data">
                <member-single
                  v-for="(item, index) in groupMember"
                  :key="index"
                  :item="item.columnValues.d"
                />
              </div>
            </scrolldetailc>
          </div>
        </el-tab-pane>
        <el-tab-pane
          name="article_count"
          id="closearticle_count"
          v-if="
            tmpDataDetail.d._source &&
            tmpDataDetail.d._source.type === 'twitter' &&
            tmpDataDetail.d._source.user_id != null &&
            tmpDataDetail.d._source.article_count != null &&
            tmpDataDetail.d._source.article_count != 0
          "
        >
          <span slot="label">{{ $t("common." + "article_count") }}</span>
          <div style="height: 800px">
            <component
              v-bind:is="'chartList'"
              :myname="'article_count'"
              :myId="'article_count'"
            >
            </component>
          </div>
        </el-tab-pane>
        <el-tab-pane
          id="closelikes_count"
          name="likes_count"
          v-if="
            tmpDataDetail.d._source &&
            tmpDataDetail.d._source.type === 'twitter' &&
            tmpDataDetail.d._source.user_id != null &&
            tmpDataDetail.d._source.likes_count != null &&
            tmpDataDetail.d._source.likes_count != 0
          "
        >
          <span slot="label">{{ $t("common." + "likes_count") }}</span>
          <div style="height: 800px">
            <component
              v-bind:is="'chartList'"
              :myname="'likes_count'"
              :myId="'likes_count'"
            >
            </component>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <!-- 人员详情弹框 -->
    <el-dialog title="详情信息" :visible.sync="dialogMoreVisible" width="30%">
      <el-descriptions class="margin-top" title="" :column="1" size="" border>
        <el-descriptions-item>
          <div class="imgStyle">
            <img
              v-if="
                moreIdData &&
                moreIdData.hasOwnProperty('icon') &&
                moreIdData['icon']
              "
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                moreIdData['icon'] +
                '?session_id=' +
                userInfo.session_id
              "
            />
            <img
              class="imgStyle"
              v-else
              :src="require('@/assets/images/user.png')"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item
          v-for="(vitem, kitem, index) in moreIdData"
          :key="index"
        >
          <template slot="label" v-if="kitem != 'icon'">
            {{ $t("list." + kitem) }}
          </template>
          <span v-if="kitem != 'icon'">{{
            kitem === "last_msg_timestamp"
              ? $tools.timestampToTime(vitem)
              : vitem
          }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <!-- 收藏目标对话框 -->
    <el-dialog
      title="选取目录"
      top="10px"
      :visible.sync="collectDialog"
      width="40%"
      append-to-body
    >
      <div style="width: 95%">
        <collectTree
          :listType="'username'"
          :tableName="'favorites_data'"
          :getCollect="getCollect"
        ></collectTree>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapState } from "vuex";
import pdf from "vue-pdf";
import * as echarts from "echarts";
import ElTableInfiniteScroll from "el-table-infinite-scroll";
import { collectTree } from "..";

export default {
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  data() {
    return {
      isShowRadio: true,
      dialogMoreVisible: false,
      moreIdData: null,
      tabactiveName: "baseData",
      activeName: "1",
      pdferror: false,
      numPages: [],
      defaultImg: 'this.src="' + require("@/assets/images/winter.jpg") + '"',
      onGroupContentScroll: null,
      orientation: true,
      checkGroupDetails: null,
      bian: "",
      dataType: "",
      tgSearchType: "group_data_prefix_telegram",
      canLoadMore: false,
      selectTGData: [],
      collectDialog: false,
      collectNum: 0,
      scrollDebounceTimer: null,
    };
  },
  computed: {
    ...mapState({
      tgLoading: (state) => state.telegramSearch.telegramSearchList.tgLoading,
      listTrueEnd: (state) =>
        state.telegramSearch.telegramSearchList.listTrueEnd,
      queryEnd: (state) => state.telegramSearch.telegramSearchList.queryEnd,
      userInfo: (state) => state.userInfo.userinfo,
      dataList: (state) => state.telegramSearch.telegramSearchList.dataList,
      pathArr: (state) =>
        state.telegramSearch.telegramSearchSelectRange.pathArr,
      activeNamePrefix: (state) =>
        state.telegramSearch.telegramSearchList.activeNamePrefix,
      memberIcon: (state) => state.telegramSearch.telegramSearchList.memberIcon,
      groupIcon: (state) => state.telegramSearch.telegramSearchList.groupIcon,
      dialogVisible: (state) =>
        state.telegramSearch.telegramSearchDataDetail.dialogVisible,
      tmpDataDetail: (state) =>
        state.telegramSearch.telegramSearchDataDetail.tmpDataDetail,
      Pdf: (state) => state.telegramSearch.telegramSearchDataDetail.Pdf,
      groupMsgList: (state) =>
        state.telegramSearch.telegramSearchDataDetail.telegramHbaseGroup
          .groupMsgList,
      msgIconList: (state) =>
        state.telegramSearch.telegramSearchDataDetail.telegramHbaseGroup
          .msgIconList,
      groupDetailed: (state) =>
        state.telegramSearch.telegramSearchDataDetail.telegramHbaseGroup
          .groupDetailed,
      groupMember: (state) =>
        state.telegramSearch.telegramSearchDataDetail.telegramHbaseGroup
          .groupMember,
      conditionsData: (state) => state.search.conditions.conditionsData,
      telegram: (state) => state.person.telegram,
      personName: (state) => state.person.personName,
      identity: (state) => state.person.identity,
      phone: (state) => state.person.phone,
      mail: (state) => state.person.mail,
    }),
  },
  watch: {
    conditionsData: {
      handler(newVal, oldVal) {
        this.searchByTelegram();
      },
      deep: true,
    },
  },
  components: {
    chartList: () => import("./telegram-use-components/comList.vue"),
    scroll: () => import("./telegram-use-components/scroll.vue"),
    scrolldetail: () => import("./telegram-use-components/scrolldetail.vue"),
    scrolldetailc: () => import("./telegram-use-components/scrolldetailc.vue"),
    scrolldetaild: () => import("./telegram-use-components/scrolldetaild.vue"),
    scrolldetaile: () => import("./telegram-use-components/scrolldetaile.vue"),
    "tree-box": () => import("./telegram-use-components/tree_box.vue"),
    "message-single": () =>
      import("./telegram-use-components/messageSingle.vue"),
    "member-single": () => import("./telegram-use-components/memberSingle.vue"),
    pdf,
    collectTree: () => import("@/layout/components/collectTree.vue"),
  },
  created() {
    this.setPdf(false);
    this.onGroupContentScroll = this.throttle(this.loadGroupContentData, 1000);
    this.loadMoreEsSeasrch = this.throttle(this.loadMoreEs, 300);
    if (window.main.$route.name == "personDetails" || window.main.$route.name == "oriDetails") {
      this.isShowRadio = false;
      this.setConditionsData();
      this.changeSearchType("group_content_data_prefix_telegram");
      let add_es_query_conditions = {
        bool: {
          should: this.telegram.map(account => ({
            match_phrase: { user_id: account }
          }))
        },
      };
      console.log("add_es_query_conditions:", add_es_query_conditions);

      this.setAddEsQueryConditions(add_es_query_conditions);
    }
  },
  mounted() {
    this.searchByTelegram();
    setTimeout(() => {
      this.canLoadMore = true;
    }, 1000);
  },
  methods: {
    ...mapMutations({
      setAddEsQueryConditions: "telegramSearch/telegramSearchList/setAddEsQueryConditions",
      setConditionsData: "search/conditions/setConditionsData",
      getListTrue: "telegramSearch/telegramSearchList/getListTrue",
      setSearchPrefixFatherPath:
        "telegramSearch/telegramSearchList/setSearchPrefixFatherPath",
      setClearStatisticalList:
        "telegramSearch/telegramSearchList/setClearStatisticalList",
      clearSearchList: "telegramSearch/telegramSearchList/clearSearchList",
      setQueryString: "telegramSearch/telegramSearchList/setQueryString",
      clearLoadingLayer: "telegramSearch/telegramSearchList/clearLoadingLayer",
      setLoadingLayer: "telegramSearch/telegramSearchList/setLoadingLayer",
      setSearch: "telegramSearch/telegramSearchList/setSearch",
      setChartOn: "telegramSearch/telegramSearchDataRange/setChartOn",
      setMaxInit: "telegramSearch/telegramSearchDataRange/setMaxInit",
      clearDataRangeTree:
        "telegramSearch/telegramSearchDataRange/clearDataRangeTree",
      setDataRangeGetter:
        "telegramSearch/telegramSearchDataRange/setDataRangeGetter",
      setdialogVisible:
        "telegramSearch/telegramSearchDataDetail/setdialogVisible",
      clearGroupMsg:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/clearGroupMsg",
      setTimeMsg:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setTimeMsg",
      clearDataDetail:
        "telegramSearch/telegramSearchDataDetail/clearDataDetail",
      setTmpDataDetail:
        "telegramSearch/telegramSearchDataDetail/setTmpDataDetail",
      clearGroupMember:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/clearGroupMember",
      sendGetDataDetailBaseData:
        "telegramSearch/telegramSearchDataDetail/telegramBaseData/sendGetDataDetailBaseData",
      setPdf: "telegramSearch/telegramSearchDataDetail/setPdf",
      setTimeRange: "telegramSearch/telegramSearchConditions/setTimeRange",
      setQueryMode: "telegramSearch/telegramSearchConditions/setQueryMode",
      setCollectionTimeRange:
        "telegramSearch/telegramSearchConditions/setCollectionTimeRange",
      setStoreMyname: "telegramSearch/telegramSearchChartData/setStoreMyname",
      initDataMore: "telegramSearch/telegramSearchChartData/initDataMore",
      chartOncklickClear:
        "telegramSearch/telegramSearchChartData/chartOncklickClear",
      initData: "telegramSearch/telegramSearchChartRange/initData",
    }),
    ...mapActions({
      sendGroupMsg:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroupMsg",
      sendGroupTopMsg:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroupTopMsg",
      sendGroup:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroup",
      sendGroupMember:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroupMember",
      getTranslated: "telegramSearch/telegramSearchDataDetail/getTranslated",
      sendGroup:
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroup",
    }),

    //使用当前时间戳与随机数结合当作预警词的唯一id
    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = new Array(
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n"
      );
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },

    // 导出数据
    handleImportData() {
      if (!this.selectTGData) {
        this.$message({
          message: "请先选择需要导出的数据",
          type: "warning",
        });
        return;
      }
      let msgArr = [];
      this.selectTGData?.forEach((item) => {
        msgArr.push([
          item._source.group_id,
          this.groupIcon[item._source.group_id]?.group_name,
          item._source.group_member,
          this.memberIcon[item._source.group_member]?.username,
          item._source?.group_member
            ? this.memberIcon[item._source.group_member]?.nickname || ""
            : "",
          item._source?.content_article
            ? item._source.content_article.replace(/\n/g, "")
            : "",
        ]);
      });
      let csvContent =
        "群ID,群名称,发言人ID,发言人用户名,发言人昵称,发言内容," + "\r\n";
      msgArr.forEach((item) => {
        let row = item.join();
        csvContent += row + "\r\n";
      });
      let blob = new Blob(["\ufeff" + csvContent], {
        type: "text/plain;charset=utf-8",
      });
      let link = document.createElement("a");
      link.download = `(${this.queryString})搜索结果.csv`;
      link.href = window.URL.createObjectURL(blob);
      link.click();
      link.remove();
    },

    // 点击收藏按钮，判断是否选中数据，如果选中数据弹出收藏列表组件
    handleCollect() {
      if (!this.selectTGData) {
        this.$message({
          message: "请先选择需要收藏的数据",
          type: "warning",
        });
        return;
      }
      this.collectDialog = true;
    },
    // 进行收藏
    getCollect(data) {
      console.log("collect", data);
      this.$confirm("确定选择此目录收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.selectTGData.forEach((element) => {
            let prefix =
              1e13 -
              Math.round(new Date().getTime() / 1000) +
              data.id +
              this.reduceNumber();
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefixTable.AddData",
              [
                {
                  msg: {
                    type: "username",
                    authority:
                      window.main.$store.state.userInfo.userinfo.authority,
                    username:
                      window.main.$store.state.userInfo.userinfo.username,
                    table: "favorites_data",
                    prefix,
                    relation: data.id + ";telegram",
                    data: {
                      data: {
                        file_data: element,
                      },
                    },
                  },
                },
              ],
              (res) => {
                console.log("收藏", res);
                if (res.status === "ok") {
                  this.collectNum++;
                  if (this.collectNum === this.selectTGData.length) {
                    this.$message.success("收藏成功!");
                    this.collectNum = 0;
                    this.selectTGData = [];
                    this.collectDialog = false;
                  }
                }
              }
            );
          });
        })
        .catch((err) => {
          this.$message({
            message: "取消收藏",
            type: "info",
          });
        });
    },

    // 选择需要导出、收藏的数据
    handleSelectionChange(val) {
      console.log("handleSelectionChange:", val);
      this.selectTGData = val;
    },

    // 父组件调用该方法，初始化搜索条件
    searchByTelegram() {
      this.setClearStatisticalList();
      this.setChartOn(false);
      this.setMaxInit();
      this.clearDataRangeTree();
      this.clearSearchList();
      this.setQueryString(this.conditionsData.queryString);
      this.setQueryMode(this.conditionsData.ppqueryMode);
      this.setTimeRange({
        timeRange: this.conditionsData.timeRange,
        timeRangeBegin: this.conditionsData.time_range_begin,
        timeRangeEnd: this.conditionsData.time_range_end,
      });
      this.setSearch(true);
      this.searchTelegramData();
      window.main.$message.success("正在搜索......");
    },

    // 搜索数据
    searchTelegramData() {
      if (this.tgLoading) return;
      this.getListTrue();
    },

    // 切换搜索数据类型
    changeSearchType(dataType) {
      console.log("changeSearchType:", dataType);
      this.tgSearchType = dataType;
      let search_prefix = {
        index_prefix: "",
        father_path: "",
      };
      switch (dataType) {
        case "group_data_prefix_telegram":
          search_prefix.index_prefix = dataType;
          search_prefix.father_path = "/group_data/telegram/group_data_prefix";
          break;
        case "group_member_data_telegram":
          search_prefix.index_prefix = dataType;
          search_prefix.father_path = "/group_data/telegram/group_member_data";
          break;
        case "group_content_data_prefix_telegram":
          search_prefix.index_prefix = dataType;
          search_prefix.father_path =
            "/group_data/telegram/group_content_data_prefix";
          break;
        default:
          break;
      }
      this.setSearchPrefixFatherPath(search_prefix);
      this.searchByTelegram();
    },

    // 滚动加载搜索内容
    handleloadMoreEsSeasrch(e) {
      console.log("loadMoreEsSeasrch:", e);
      const { scrollTop, clientHeight, scrollHeight } = e.srcElement;
      let isGo = Math.abs(scrollHeight - (scrollTop + clientHeight));
      console.log("isGo:", isGo);
      if (isGo < 5) {
        this.loadMoreEs();
      }
    },

    // 处理群组数据滚动加载
    handleGroupDataScroll() {
      if (!this.$refs.groupDataContainer) return;
      const container = this.$refs.groupDataContainer;
      const scrollPosition = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      // 当滚动到底部附近时加载更多数据
      if (scrollHeight - scrollPosition - clientHeight < 100) {
        console.log("Scroll near bottom, loading more data...");
        this.loadMoreEs();
      }
    },

    // 加载es数据
    async loadMoreEs() {
      console.log(
        "loadMoreEs:",
        this.canLoadMore,
        this.tgLoading,
        this.queryEnd,
        this.listTrueEnd
      );
      // if (!this.canLoadMore || this.tgLoading || this.queryEnd || this.listTrueEnd) return;
      try {
        // 直接调用搜索方法，不需要修改页码参数，由后端维护
        await this.searchTelegramData();
      } catch (error) {
        console.error("加载更多数据时出错:", error);
      } finally {
        this.$store.commit(
          "telegramSearch/telegramSearchList/setTGLoading",
          false
        );
      }
    },

    // loadMoreEsSeasrch方法用于表格的无限滚动
    // loadMoreEsSeasrch() {
    //   if (this.activeNamePrefix !== 'group_data_prefix_telegram') {
    //     this.loadMoreEs();
    //   }
    // },

    // 加载群组成员内容
    loadGroupMemberData() {
      this.sendGroupMember();
    },

    // 加载群组内容数据
    loadGroupContentData(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target;
      let isGo = Math.abs(scrollHeight - (scrollTop + clientHeight));
      if (isGo < 5) {
        this.sendGroupMsg();
        this.orientation = false;
      }
      if (scrollTop <= 0) {
        this.sendGroupTopMsg();
        this.orientation = false;
      }
    },

    // 节流函数
    throttle(func, delay) {
      let time = null;
      return function () {
        let args = Array.from(arguments);
        if (time === null) {
          time = setTimeout(() => {
            func(...args);
            clearTimeout(time);
            time = null;
          }, delay);
        }
      };
    },

    // 打开详情页
    morefn(v) {
      this.checkGroupDetails = v;
      this.bian = "";
      this.dataType = v._source.type;
      this.tabactiveName = "baseData";
      this.setStoreMyname("baseData");
      this.clearDataDetail();
      this.setTmpDataDetail({ key: "d", value: v });
      this.sendGetDataDetailBaseData();
      this.getTranslated();
      this.initDataMore("likes_count");
      this.initData("likes_count");
      this.initDataMore("article_count");
      this.initData("article_count");
      this.initDataMore("groupMemberData");
      this.initData("groupMemberData");
      this.initDataMore("groupData");
      this.initData("groupData");
      this.initDataMore("groupContentData");
      this.initData("groupContentData");
      let tmpThis = this;
      setTimeout(() => {
        tmpThis.bian = "数据库";
      }, 1000);
    },
    // 关闭详情弹窗
    handleClose() {
      this.activeName = "1";
      this.setdialogVisible(false);
    },
    //切换弹窗中的tabs
    handleClick(tab, event) {
      let tmpThis = this;
      if (tab.name !== "baseData") {
        let data_range_path = "";
        let data_range_index_prefix = "";
        let Obj = {};
        if (tab.name === "groupContentData") {
          this.clearGroupMsg();
          this.setTimeMsg(this.checkGroupDetails);
          this.orientation = true;
        }
        if (tab.name === "groupData") {
          this.sendGroup(this.checkGroupDetails);
        }
        if (tab.name === "groupMemberData") {
          this.clearGroupMember();
          this.sendGroupMember(this.checkGroupDetails);
        }
        if (tab.name === "likes_count") {
          data_range_index_prefix = "social_platform_timeline_prefix_twitter";
          data_range_path = "/social_platform/twitter/timeline";
          Obj = {
            dataRangeList: [
              {
                data_range_index_prefix: data_range_index_prefix,
                data_range_path: data_range_path,
                data_range_type: true,
                likes_count: {
                  user_id: this.tmpDataDetail.d._source.user_id,
                },
                moreData: "moreData",
              },
            ],
            name: tab.name,
            moreData: "moreData",
          };
          tmpThis.chartOncklickClear();
          tmpThis.clearDataRangeTree(tab.name); //清除之前的查询数据范围
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/clearSearchList",
            tab.name
          ); //清除之前的查询结果数据
          tmpThis.setStoreMyname(tab.name);
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/setusePublic",
            {
              data: tmpThis.usePublic,
              name: tab.name,
            }
          );
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/setSearch",
            {
              data: true,
              name: tab.name,
            }
          );
          window.main.$store.commit(
            `telegramSearch/telegramSearchChartRange/setDataRangeGetter`,
            Obj
          );
        }
        if (tab.name === "article_count") {
          data_range_index_prefix = "social_platform_timeline_prefix_twitter";
          data_range_path = "/social_platform/twitter/timeline";
          Obj = {
            dataRangeList: [
              {
                data_range_index_prefix: data_range_index_prefix,
                data_range_path: data_range_path,
                data_range_type: true,
                article_count: {
                  user_id: this.tmpDataDetail.d._source.user_id,
                },
                moreData: "moreData",
              },
            ],
            name: tab.name,
            moreData: "moreData",
          };
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/chartOncklickClear"
          );
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartRange/clearDataRangeTree",
            tab.name
          ); //清除之前的查询数据范围
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/clearSearchList",
            tab.name
          ); //清除之前的查询结果数据
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/setStoreMyname",
            tab.name
          );
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/setusePublic",
            {
              data: tmpThis.usePublic,
              name: tab.name,
            }
          );
          tmpThis.$store.commit(
            "telegramSearch/telegramSearchChartData/setSearch",
            {
              data: true,
              name: tab.name,
            }
          );
          window.main.$store.commit(
            `telegramSearch/telegramSearchChartRange/setDataRangeGetter`,
            Obj
          );
        }
        if (tab.name === "followers_count") {
          this.$store.commit("socialPlatformUser/setFollowerslistarr", []);
          this.$store.commit(
            "socialPlatformUser/setFollowersListQualifier",
            ""
          );
          this.$store.commit("socialPlatformUser/setnowRowKey", "");
          this.$store.commit("socialPlatformUser/setMsgConfigB", {
            obj: {
              type: "twitter",
              user_id: this.tmpDataDetail.d._source.user_id,
              query_type: tmpThis.usePublic,
            },
            relation: "followers",
          });
          this.$store.commit("socialPlatformUser/sendFollowersDatalist");
        }
        if (tab.name === "following_count") {
          this.$store.commit("socialPlatformUser/setFollowingDatalistarr", []);
          this.$store.commit(
            "socialPlatformUser/setFollowingListQualifier",
            "following"
          );
          this.$store.commit("socialPlatformUser/setnowRowKey", "");
          this.$store.commit("socialPlatformUser/setMsgConfigB", {
            obj: {
              type: "twitter",
              user_id: this.tmpDataDetail.d._source.user_id,
              query_type: tmpThis.usePublic,
            },
            relation: "following",
          });
          this.$store.commit("socialPlatformUser/sendFollowingsDatalist");
        }
      }
    },
    // 视频播放
    videoFn(v) {
      document.getElementById(v).playbackRate = event.target.value;
    },
    // pdf函数
    pdfFn() {
      this.setPdf(true);
      document.getElementById("pdfBox").style.cssText =
        "height:600px;overflow: auto;";
      this.pdferror = false;
      this.numPages = [];
      if (this.tmpDataDetail.d._source.hasOwnProperty("content_pdf")) {
        this.tmpDataDetail.d._source.content_pdf.forEach((element, index) => {
          let loadingTask = pdf.createLoadingTask(
            "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/" +
              element.sha512_hash +
              "?session_id=" +
              this.$store.state.userInfo.session_id
          );
          loadingTask.promise
            .then((pdf) => {
              this.numPages.push(pdf.numPages);
            })
            .catch((err) => {
              document.getElementById("pdfBox").style.cssText =
                "height:40px;overflow: auto;";
              this.pdferror = true;
            });
        });
      }
    },

    // 渲染时间折线图
    applyDataTimeLineChat() {
      let dataY = [];
      let dataX = [];
      let timeObj = this.statisticalList?.time;
      for (const key in timeObj) {
        dataX.push(key);
      }
      dataX.sort((a, b) => new Date(a) - new Date(b));
      dataX.forEach((item) => {
        dataY.push(timeObj[item]);
      });
      this.$nextTick(() => {
        const timeLineChart = echarts.init(
          document.getElementById("dataTimeDistributeLineChat")
        );
        timeLineChart.setOption({
          legend: {
            data: ["数据时间线统计"],
          },
          tooltip: {
            trigger: "axis",
            formatter: "数据量 : <br/>{b} : {c}条",
          },
          grid: {
            left: 40,
          },
          xAxis: {
            type: "category",
            data: dataX,
            axisLine: {
              onZero: false,
              lineStyle: {
                width: 2,
              },
            },
            boundaryGap: false,
          },
          yAxis: {
            type: "value",
            axisLine: {
              lineStyle: {
                width: 2,
              },
            },
            axisLabel: {
              formatter: "{value} 条",
            },
          },
          series: [
            {
              name: "数据时间线统计",
              type: "line",
              symbolSize: 10,
              lineStyle: {
                width: 2,
                color: "#157eea",
              },
              label: {
                show: true,
                position: "top",
                textStyle: {
                  color: "black",
                  fontSize: 14,
                },
              },
              data: dataY,
            },
          ],
        });
      });
    },
    // 渲染消息类型饼图
    applyTypeChart() {
      let data = [];
      let fileTypeObj = this.statisticalList?.fileType;
      for (const key in fileTypeObj) {
        data.push({
          value: fileTypeObj[key],
          name: key,
        });
      }
      this.$nextTick(() => {
        const fileTypeChart = echarts.init(
          document.getElementById("typeChart")
        );
        fileTypeChart.setOption({
          tooltip: {
            trigger: "item",
          },
          series: [
            {
              name: "数据类型统计图",
              type: "pie",
              radius: "50%",
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        });
      });
    },
    // 渲染人员统计图
    applyPersonnelChart() {
      let that = this;
      let dataX = [];
      let dataY = [];
      let sendObj = this.statisticalList?.send;
      for (const key in sendObj) {
        dataX.push(key + "");
        dataY.push(sendObj[key]);
      }
      this.$nextTick(() => {
        const personnelChart = echarts.init(
          document.getElementById("personnelChart")
        );
        personnelChart.setOption({
          legend: {
            data: ["人员数据统计图"],
          },
          tooltip: {
            show: true,
            trigger: "axis",
            formatter: "人员ID : {b}<br/>消息数 : {c}条",
          },
          xAxis: {
            type: "category",
            data: dataX,
            axisLine: {
              lineStyle: {
                width: 2,
              },
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              lineStyle: {
                width: 2,
              },
            },
          },
          series: [
            {
              name: "人员数据统计图",
              data: dataY,
              type: "bar",
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              label: {
                show: true,
                position: "top",
                textStyle: {
                  color: "black",
                  fontSize: 14,
                },
              },
              itemStyle: {
                color: "#157eea",
              },
            },
          ],
        });
        personnelChart.on("click", (params) => {
          that.idMoreFn("userId", params.name);
        });
      });
    },
    // 渲染群统计图
    applyGroupChart() {
      let that = this;
      let dataX = [];
      let dataY = [];
      let receptionObj = this.statisticalList?.reception;
      for (const key in receptionObj) {
        dataX.push(key + "");
        dataY.push(receptionObj[key]);
      }
      this.$nextTick(() => {
        const groupChart = echarts.init(document.getElementById("groupChart"));
        groupChart.setOption({
          legend: {
            data: ["群组数据统计图"],
          },
          tooltip: {
            show: true,
            trigger: "axis",
            formatter: "群组ID : {b}<br/>消息数 : {c}条",
          },
          xAxis: {
            type: "category",
            data: dataX,
            axisLine: {
              lineStyle: {
                width: 2,
              },
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              lineStyle: {
                width: 2,
              },
            },
          },
          series: [
            {
              name: "群组数据统计图",
              data: dataY,
              type: "bar",
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              label: {
                show: true,
                position: "top",
                textStyle: {
                  color: "black",
                  fontSize: 14,
                },
              },
              itemStyle: {
                color: "#157eea",
              },
            },
          ],
        });
        groupChart.on("click", (params) => {
          that.idMoreFn("groupId", params.name);
        });
      });
    },
    idMoreFn(nowType, v) {
      if (nowType === "groupId") {
        if (this.groupIcon.hasOwnProperty(v)) {
          this.moreIdData = this.groupIcon[v];
        } else {
          this.moreIdData = { data: "无" };
        }
      }
      if (nowType === "userId") {
        if (this.memberIcon.hasOwnProperty(v)) {
          this.moreIdData = this.memberIcon[v];
        } else {
          this.moreIdData = { data: "无" };
        }
      }
      this.dialogMoreVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.telegram-main,
#closeESsearch {
  width: 100%;
  height: 100%;
  .select-search-type {
    display: flex;
    justify-content: space-between;
    .import_collect_btn {
      margin-right: 0.625rem;
    }
  }
  .data_content {
    width: 100%;
    height: 43.75rem;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .content_left {
      width: 100%;
      height: 100%;
      overflow: auto;
      .group_content_box {
        width: 100%;
        height: 100%;
        .left_box {
          .index-marker {
            text-align: center;
            line-height: 48px;
            background-color: #eee;
          }
          .left_item {
            width: 100%;
            border: 0.0625rem solid #ccc;
            border-radius: 0.1875rem;
            margin-bottom: 0.625rem;
            display: flex;
            .item_info {
              width: 25%;
              background: #b6c5f8;
              border-right: 0.0625rem solid #ccc;
              p {
                padding-bottom: 0.3125rem;
              }
              .user_info {
                display: flex;
                .perc {
                  padding: 0.625rem;
                  font-size: 0.75rem;
                }
              }
              img {
                margin: 0.625rem;
                width: 3.75rem;
                height: 3.75rem;
                border-radius: 50%;
                border: 0.125rem solid #409eff;
              }
            }
            .msg_info {
              padding: 0.625rem;
              font-size: 0.875rem;
            }
            .item_body {
              width: 75%;
              background: #a0add8;
              .body_btn {
                padding: 0.625rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 0.0625rem solid #ccc;
              }
              .body_text {
                white-space: pre-line;
                word-break: break-all;
                padding: 0.625rem;
              }
            }
          }
        }
      }
      .left_member {
        margin-top: 0.625rem;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        height: 100%;
        overflow-y: auto;
        padding-bottom: 1.875rem;
        position: relative;

        .member_item {
          margin-right: 0.9375rem;
          margin-bottom: 0.9375rem;
          width: 30%;
          height: 12.5rem;
          padding: 0.625rem;
          background: #b6c5f8;
          border-radius: 0.1875rem;
          .member_btn {
            display: flex;
            justify-content: space-between;
            border-bottom: 0.0625rem solid #867f7f;
            padding-bottom: 0.3125rem;
            .info {
              width: 80%;
              display: flex;
              font-size: 0.875rem;
              p {
                padding-bottom: 0.3125rem;
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              img {
                margin-right: 1.25rem;
                width: 3.75rem;
                height: 3.75rem;
                border: 0.125rem solid #409eff;
                border-radius: 50%;
              }
            }
          }
          .member_info {
            padding: 0.3125rem 0.625rem 0.625rem;
            font-size: 0.875rem;
            display: flex;
            div {
              width: 50%;
            }
            p {
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              padding-bottom: 0.3125rem;
            }
          }
        }

        .loading-more-indicator {
          width: 100%;
          text-align: center;
          padding: 1rem 0;
          color: #606266;
          font-size: 0.875rem;

          .el-icon-loading {
            margin-right: 0.5rem;
            font-size: 1rem;
          }
        }

        .end-of-list {
          width: 100%;
          text-align: center;
          padding: 1rem 0;
          color: #909399;
          font-size: 0.875rem;
        }
      }
    }
    .content_right {
      width: 40%;
      height: 69vh;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      #dataTimeDistributeLineChat,
      #personnelChart,
      #groupChart,
      #typeChart {
        width: 50%;
        height: 320px;
      }
    }
  }
}

.omitted {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list_title {
  .cartLink {
    float: right;
    margin-right: 15px;
    position: relative;
    background: #f56c6c;
    color: #fff;
    border-radius: 12px;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;
    text-align: center;
  }
  span {
    font-size: 14px;
    color: rgb(255, 0, 0);
  }
  b {
    font-size: 14px;
    margin-left: 20px;
  }
}

.list_lay {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  .unit {
    display: flex;
    justify-content: space-between;
    .unit_l {
      border: 1px solid #ddd;
      border-right: 0px;
      width: 114px;
      /* display: flex; */
      justify-content: center;
      padding: 10px 0 0;
      text-align: center;
      img {
        background: #eee;
        border: 2px solid #e6a23c;
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }
    }
    .unit_m {
      flex: 1;
      border: 0.0625rem solid #ddd;
      border-right: 0;
      .unit_m_table {
        display: flex;
        flex-wrap: wrap;
        .list_table {
          width: 10.625rem;
          line-height: 1.875rem;
          padding: 0.3125rem;
          border-bottom: 0.0625rem solid #ccc;
          font-size: 0.875rem;
        }
      }
      .unit_m_marka {
        word-break: break-all;
        background: #dbe3fd;
        border-top: 0.0625rem solid #ccc;
        padding: 0.625rem;
      }
    }
  }
}

.morInfor {
  display: flex;
  .morInfor_l {
    width: 6.25rem;
    padding: 0.625rem;
    display: flex;
    justify-content: center;
    img {
      background: #eee;
      border: 0.125rem solid #e6a23c;
      width: 3.125rem;
      height: 3.125rem;
      border-radius: 50%;
    }
  }
  .morInfor_r_li_h {
    color: #969696;
  }
  .morInfor_r_li_c {
    margin-left: 0.9375rem;
  }
}

.group_lay {
  max-height: 43.75rem;
  margin-top: 1.25rem;
  li {
    display: flex;
    justify-content: space-between;
    background: #eee;
    border-bottom: 0.125rem solid #fff;
    padding-right: 0.625rem;
    padding-bottom: 0.625rem;
    .group_l_img {
      padding: 0.625rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      p {
        margin-top: 0.625rem;
        width: 8.75rem;
        display: block;
        text-align: center;
        color: #e6a23c;
        font-weight: bold;
      }
      img {
        background: #eee;
        border: 0.125rem solid #e6a23c;
        width: 3.125rem;
        height: 3.125rem;
        border-radius: 50%;
      }
    }
    .group_r {
      flex: 1;
      padding-top: 0.625rem;
      .group_r_timestamp {
        color: #bbb;
        text-align: right;
      }
    }
  }
}

$pdf-viewer-asset-base-path: "/node_modules/@certifaction/pdfjs/dist/";
/* @import "@certifaction/vue-pdf-viewer/src/style/index"; */

.treeItem {
  color: #666;
  padding: 20px;

  cursor: pointer;
  font-size: 16px;
  position: relative;

  .btns {
    position: absolute;
    right: 20px;
    top: 20px;

    span {
      margin-left: 10px;
    }
  }
}

.treeItem:hover > .case_dir_name,
.treeItem:hover > .btns {
  color: #409eff;
}

.triangle {
  margin-right: 4px;
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-left: 7px solid #c0c4cc;
  border-bottom: 5px solid transparent;
}

.bottomsty {
  margin-right: 4px;
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 7px solid #c0c4cc;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

.treePage {
  text-align: center;

  a {
    margin: 0 10px;
    border: 1px solid #f5f5f5;
    background: #f2f2f2;
    padding: 0 2px;

    &:hover {
      color: #409eff;
    }
  }
}

.el-icon-circle-close {
  color: white;
}

.tagsSty {
  margin-right: 10px;
  background: #52a04f;
  padding: 2px 3px;
  cursor: pointer;
  color: #fff;
}

.el-collapse-item__header {
  font-weight: bold !important;
  background: #eee;
  text-indent: 10px;
  border-bottom: 1px solid #fff;
}

.imgStyle img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.morInfor_l {
  border-right: 1px solid #ccc;
  border-top: 1px solid #ccc;
  width: 100px;
  padding: 10px;
  display: flex;
  justify-content: center;
}

.morInfor_l img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.tree_box {
  padding-left: 20px;
}

.relatedRow {
  margin-left: 15px;
  margin-right: 15px;
  margin-bottom: 15px;
  display: flex;
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;

  .rowItem {
    flex: 1 1;
    border-right: 1px solid #ddd;

    .rowItemT {
      padding: 10px;
      text-align: center;
      font-weight: bold;
      border-top: 1px solid #ddd;
    }

    .rowItemB {
      padding: 10px;
      text-align: center;
      word-break: break-all;
      border-top: 1px solid #ddd;
    }
  }

  .operation {
    border-right: 1px solid #ddd;
    border-top: 1px solid #ddd;
  }

  .operation {
    padding: 0 10px;
    display: flex;
    align-items: center;
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}

.el-dialog {
  width: 85%;
}

.dialogTit {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
  margin-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
}

.group-data-scroll-container {
  height: 600px;
  overflow-y: auto;
  position: relative;
}

.loading-indicator,
.end-of-list {
  text-align: center;
  padding: 15px 0;
  color: #909399;
  font-size: 14px;
}

.loading-indicator i {
  margin-right: 5px;
}
</style>
