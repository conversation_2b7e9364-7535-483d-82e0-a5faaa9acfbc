import Layout from "@/layout";

export default [
  {
    path: "/",
    name: "login",
    component: () => import("@/views/login"),
  },

  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },
  {
    path: "/collect",
    component: Layout,
    redirect: "/collect",
    children: [
      {
        path: "/collect",
        name: "collect",
        component: () => import("@/views/collect"),
        meta: { title: "数据收藏", icon: "el-icon-s-home" },
      },
    ],
  },
  {
    path: "/aiSeach",
    component: Layout,
    redirect: "/aiSeach",
    children: [
      {
        path: "/aiSeach",
        name: "aiSeach",
        component: () => import("@/views/AiSearch"),
        meta: { title: "AI搜索", icon: "el-icon-s-home" },
      },
    ],
  },
  {
    path: "/NewsDisplay",
    component: Layout,
    children: [
      {
        path: "/NewsDisplay",
        name: "NewsDisplay",
        component: () => import("@/views/NewsDisplay"),
        meta: { title: "新闻详情", icon: "el-icon-s-home" },
      },
    ],
  },
  {
    path: "/search",
    component: Layout,
    redirect: "/search",
    meta: { title: "数据搜索", icon: "el-icon-s-data" },
    children: [
      {
        path: "",
        name: "search",
        component: () => import("@/views/search"),
        meta: { title: "数据搜索", icon: "el-icon-search" },
      },
      {
        path: "searchList",
        name: "searchList",
        component: () => import("@/views/search/searchList"),
        meta: { title: "搜索结果", icon: "el-icon-search" },
      },
    ],
  },
  {
    path: "/archives",
    component: Layout,
    redirect: "/archives",
    meta: { title: "目标管理", icon: "el-icon-s-data" },
    children: [
      {
        path: "/personArchives",
        name: "personArchives",
        component: () => import("@/views/archives/personArchives"),
        meta: { title: "目标人" },
        hidden: true,
        children: [],
      },
      {
        path: "/personDetails",
        name: "personDetails",
        component: () => import("@/layout/components/archives/PersonDetails"),
        meta: { title: "目标人详情" },
      },
      {
        path: "/oriDetails",
        name: "oriDetails",
        component: () =>
          import("@/layout/components/archives/OrganizationDetails"),
        meta: { title: "目标组织详情" },
      },
      {
        path: "/origaniArchives",
        name: "origaniArchives",
        component: () => import("@/views/archives/origaniArchives"),
        meta: { title: "目标组织" },
        hidden: true,
      },
      {
        path: "/intellManage",
        name: "intellManage",
        component: () => import("@/views/archives/intellManage"),
        meta: { title: "相关情报" },
        hidden: true,
      },
    ],
  },
  {
    path: "/AI",
    component: Layout,
    redirect: "/AI",
    meta: { title: "AI任务", icon: "el-icon-s-data" },
    children: [
      {
        path: "/AIplanTask",
        name: "AIplanTask",
        component: () => import("@/views/AI/AIplanTask"),
        meta: { title: "计划任务" },
        hidden: false,
      },
      {
        path: "/analyze",
        name: "analyze",
        component: () => import("@/views/AI/analyze"),
        meta: { title: "解析模板" },
        hidden: true,
      },
      {
        path: "/taskQueue",
        name: "taskQueue",
        component: () => import("@/views/AI/taskQueue"),
        meta: { title: "任务队列" },
        hidden: true,
      },
      {
        path: "/taskDetail/:id/:title/:method",
        name: "taskDetail",
        component: () => import("@/views/AI/taskDetailMoreShow.vue"),
        meta: { title: "任务详情" },
        hidden: true,
      },
    ],
  },
  {
    path: "/deployment",
    component: Layout,
    redirect: "/deployment",
    meta: { title: "布控展示", icon: "el-icon-s-data" },
    children: [
      {
        path: "/opinionWeb",
        name: "opinionWeb",
        component: () => import("@/views/deployment/opinionWeb"),
        meta: { title: "舆情网站" },
        hidden: true,
      },
      {
        path: "/TwitterAccount",
        name: "TwitterAccount",
        component: () => import("@/views/deployment/TwitterAccount"),
        meta: { title: "Twitter账号" },
        hidden: true,
        children: [],
      },
      {
        path: "TwitterDetail/:id",
        name: "TwitterDetail",
        component: () =>
          import("@/views/deployment/TwitterAccount/TwitterDetail"),
      },
      {
        path: "/FacebookAccount",
        name: "FacebookAccount",
        component: () => import("@/views/deployment/FacebookAccount"),
        meta: { title: "Facebook账号" },
        hidden: true,
      },
      {
        path: "/LinkedinAccount",
        name: "LinkedinAccount",
        component: () => import("@/views/deployment/LinkedinAccount"),
        meta: { title: "Linkedin账号" },
        hidden: true,
      },
      {
        path: "LinkedinDetail/:id",
        name: "LinkedinDetail",
        component: () =>
          import("@/views/deployment/LinkedinAccount/LinkedinDetail"),
      },
      {
        path: "/TelegramAccount",
        name: "TelegramAccount",
        component: () => import("@/views/deployment/TelegramAccount"),
        meta: { title: "Telegram账号" },
        hidden: true,
      },
    ],
  },
  {
    path: "/warning",
    component: Layout,
    redirect: "/warning",
    children: [
      {
        path: "/warning",
        name: "warning",
        component: () => import("@/views/warning"),
        meta: { title: "预警管理", icon: "el-icon-warning-outline" },
      },
    ],
  },
  {
    path: "/dataImport",
    component: Layout,
    redirect: "/dataImport",
    children: [
      {
        path: "/dataImport",
        name: "dataImport",
        component: () => import("@/views/dataImport"),
        meta: { title: "数据导入", icon: "el-icon-warning-outline" },
      },
    ],
  },
  {
    path: "/mailbox",
    component: Layout,
    redirect: "/mailbox",
    children: [
      {
        path: "mailbox",
        name: "mailbox",
        component: () => import("@/views/mailbox"),
        meta: { title: "站内邮箱", icon: "el-icon-message" },
      },
    ],
  },
  // 404页面
  { path: "*", redirect: "/404", hidden: true },
];
