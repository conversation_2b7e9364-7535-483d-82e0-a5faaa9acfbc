<template>
  <div class="intellManage">
    <div class="container">
      <div
        class="id-card"
        v-for="(item, index) in personRelationSearchList"
        :key="index"
        @click="viewPersonDetail(item)"
      >
        <!-- 身份证正面 -->
        <div class="id-card-front">
          <div
            v-if="item._source.type === 'secondary_key_person'"
            class="secondary-badge"
          >
            次要
          </div>
          <div class="id-card-content">
            <div class="id-card-photo">
              <el-avatar
                :size="120"
                :src="getAvatarSrc(item._source.params.basic.avatar)"
                @error="handleAvatarError"
              >
              </el-avatar>
            </div>
  
            <div class="id-card-info">
              <div class="info-row">
                <span class="label">姓名:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.name"
                  @click.stop
                  >{{ item._source.params.basic.name }}</span
                >
              </div>
              <div class="info-row remark">
                <span class="label">备注:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.remark || '暂无备注'"
                  @click.stop
                  >{{ item._source.params.basic.remark || "暂无备注" }}</span
                >
              </div>
              <div class="info-row">
                <span class="label">性别:</span>
                <span class="value" :title="item._source.params.basic.sex">{{
                  item._source.params.basic.sex || "未知"
                }}</span>
                <span class="label" style="margin-left: 10px">年龄:</span>
                <span class="value" :title="item._source.params.basic.age">{{
                  isNaN(item._source.params.basic.age) ||
                  !item._source.params.basic.age
                    ? "未知"
                    : item._source.params.basic.age
                }}</span>
              </div>
              <div class="info-row">
                <span class="label">手机号:</span>
                <span class="value" :title="item._source.params.basic.phone">{{
                  item._source.params.basic.phone || "未知"
                }}</span>
              </div>
              <div class="info-row">
                <span class="label">信息完整度:</span>
                <span class="value">{{
                  getCompleteness(item._source.params.basic)
                }}</span>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 卡片悬停效果 -->
        <div class="id-card-hover"></div>
      </div>
  
      <!-- 空状态展示 -->
      <el-empty
        v-if="personRelationSearchList.length === 0"
        description="暂无人员数据"
        class="empty-state"
      >
        <el-button type="primary" @click="refreshData">重新加载</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

export default {
  name: "RelevantPerson",
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapState({
      personRelationSearchList: (state) =>
        state.relevantPerson.relevantPersonList,
    }),
  },

  created() {
    this.resetData();
    this.getPerson();
  },

  methods: {
    ...mapMutations({
      getPerson: "relevantPerson/getPerson",
      resetData: "relevantPerson/resetData",
    }),

    refreshData() {
      this.resetData();
      this.getPerson();
    },

    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require("@/assets/images/user.png");
    },

    handleAvatarError() {
      return true; // 使用默认头像
    },

    // 信息完整度处理函数
    getCompleteness(basicInfo) {
      const totalFields = 15; // 假设有5个基本信息字段
      const filledFields = Object.values(basicInfo).filter(value => value).length;
      return `${filledFields} / ${totalFields}`;
    },

    getPersonType(type) {
      const typeMap = {
        key_person: "目标人",
        secondary_key_person: "次要目标人",
      };
      return typeMap[type] || "未知类型";
    },

    viewPersonDetail(data) {
      let person = JSON.stringify(data);
      const routeData = this.$router.resolve({
        name: 'personDetails',
        query: {
          data: person,
        }
      })
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.intellManage {
  padding: 10px;
  height: 100%;

  .container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    padding: 8px;
    overflow-y: auto;
    height: 100%;
  
    .id-card {
      position: relative;
      width: 80%;
      height: 170px;
      perspective: 1000px;
      cursor: pointer;
  
      .id-card-front {
        position: relative;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
        transition: all 0.3s ease;
        border: 1px solid #e8e8e8;
        overflow: hidden;
  
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 6px;
          background: linear-gradient(90deg, #1890ff, #36cfc9);
        }
  
        .id-card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
  
          .id-card-title {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
            letter-spacing: 1px;
          }
  
          .id-card-type {
            font-size: 14px;
            color: #666;
            background: #e6f7ff;
            padding: 2px 8px;
            border-radius: 4px;
          }
        }
  
        .id-card-content {
          display: flex;
          gap: 20px;
  
          .id-card-photo {
            width: 100px;
            height: 120px;
            display: flex;
            flex: 0 0 100px;
            min-width: 100px;
            flex-shrink: 0;
            align-items: center;
            justify-content: center;
            :deep(.el-avatar) {
              width: 100% !important;
              height: 100% !important;
              border-radius: 12px !important;
              object-fit: cover;
              box-shadow: 0 2px 8px rgba(24, 143, 255, 0.24);
            }
          }
  
          .id-card-info {
            flex: 1 1 auto;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
  
            .info-row {
              display: flex;
              height: 25px;
              line-height: 25px;
              .label {
                width: auto;
                color: #666;
                font-size: 14px;
              }
  
              .value {
                margin-left: 5px;
                flex: 1;
                color: #333;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: default;
                max-width: 12.5rem;
                min-width: 0;
              }
  
              &.remark {
                .value {
                  color: #666;
                  font-size: 13px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }
            }
          }
        }
  
        .id-card-footer {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 12px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: rgba(0, 0, 0, 0.02);
          border-top: 1px dashed #e8e8e8;
  
          .id-card-qrcode {
            color: #1890ff;
            font-size: 20px;
          }
  
          .id-card-number {
            font-size: 12px;
            color: #999;
            font-family: monospace;
          }
        }
      }
  
      .id-card-hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 12px;
  
        i {
          font-size: 32px;
          margin-bottom: 8px;
        }
  
        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
  
      &:hover {
        .id-card-front {
          transform: translateY(-4px);
          box-shadow: 5px 5px 5px rgba(24, 143, 255, 0.24);
        }
  
        .id-card-hover {
          opacity: 1;
        }
      }
    }
  
    .empty-state {
      grid-column: 1 / -1;
      margin-top: 120px;
    }
}
}

.secondary-badge {
  position: absolute;
  top: 5px;
  right: 10px;
  color: #E6A23C;
  font-size: 13px;
  z-index: 2;
}
</style>
