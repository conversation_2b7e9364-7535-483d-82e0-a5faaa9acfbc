<template>
  <div class="task-queue">
    <!-- 顶部筛选区域 -->
    <div class="filter-area">
      <el-button type="danger" size="mini" @click="handleDeleteSelected"
        >删除</el-button
      >

      <div class="task-status">
        <span>任务状态：</span>
        <el-radio-group v-model="state_value">
          <el-radio
            v-for="item in analysisState"
            :key="item.label"
            :label="item.value"
            @change="handleRadioChanges(item)"
          >
            {{ item.label }}
          </el-radio>
          <el-radio
            :label="'计划任务'"
            @change="handleRadioChangesPlan('planTask')"
          ></el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 任务列表 -->
    <el-table
      ref="myTable"
      :data="pageList"
      style="width: 100%"
      :height="'80%'"
      :row-key="(row) => row.row"
      @expand-change="handleExpandChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <div class="expand_layout">
            <div class="expand_layout_title">
              <div>{{ props.row.columnValues.info.title }}的计划任务</div>
              <el-button
                style="margin-left: 10px"
                type="primary"
                size="mini"
                @click="showAddTaskDialog(props.row)"
                >添加计划任务</el-button
              >
            </div>
            <div class="cardLayout">
              <div
                class="cardItem"
                v-for="item in planTaskListObj[props.row.row]"
                :key="item.row"
              >
                <div>
                  <span>{{
                    item.metadata.annotations.week
                      ? item.metadata.annotations.week
                      : "每天"
                  }}</span>
                  -
                  <span>
                    {{ item.metadata.annotations.time }}
                  </span>
                </div>
                <div>
                  <i
                    @click="handleEditTask(props.row, item)"
                    class="el-icon-edit-outline"
                    style="color: #409eff; cursor: pointer"
                  ></i>
                  <i
                    @click="handleDeleteTask(props.row, item)"
                    class="el-icon-delete"
                    style="color: #f56c6c; cursor: pointer; margin-left: 10px"
                  ></i>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        prop="columnValues.info.title"
        label="任务名"
        width="400"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="columnValues.info.method"
        label="任务类型"
        width="300"
        align="center"
        :formatter="formatTaskMethod"
      >
      </el-table-column>
      <el-table-column
        prop="columnValues.info.status"
        label="任务状态"
        width="300"
        align="center"
        :formatter="formatTaskStatus"
      ></el-table-column>
      <!-- <el-table-column
        prop="columnValues.parm.template.ai_template_name"
        label="模板名称"
        align="center"
      >
      </el-table-column> -->
      <!-- <el-table-column label="任务进度" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.columnValues.info.all_num" style="color: #ccc"
            >无</span
          >
          <div class="myprogress" v-if="scope.row.columnValues.info.all_num">
            <div
              class="myprogressreceived"
              :style="{
                width:
                  Number(
                    ((Number(scope.row.columnValues.info.all_num_received) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100)
                      | mytoFixed
                  ) + '%',
              }"
            >
              <div class="textspan">
                {{
                  Number(
                    ((Number(scope.row.columnValues.info.all_num_received) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100)
                      | mytoFixed
                  ) + "%"
                }}
              </div>
            </div>
            <div
              class="myprogressreceivedasync"
              v-if="scope.row.columnValues.info.all_num_received_sync"
              :style="{
                width:
                  Number(
                    ((Number(
                      scope.row.columnValues.info.all_num_received_sync
                    ) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100 -
                      3)
                      | mytoFixed
                  ) + '%',
              }"
            >
              {{
                Number(
                  ((Number(scope.row.columnValues.info.all_num_received_sync) /
                    Number(scope.row.columnValues.info.all_num)) *
                    100)
                    | mytoFixed
                ) + "%"
              }}
            </div>
            <div
              class="myprogressreceivedasync"
              v-if="scope.row.columnValues.info.all_num"
              style="background: none; left: 15px"
            >
              {{ scope.row.columnValues.info.all_num_received }}
            </div>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <!-- <el-button
              size="mini"
              type="primary"
              v-if="
                scope.row.columnValues.info.status == 'parse_end' &&
                Number(
                  ((Number(scope.row.columnValues.info.all_num_received) /
                    Number(scope.row.columnValues.info.all_num)) *
                    100) |
                    mytoFixed
                ) === 100
              "
              @click="handleView(scope.row)"
            >
              查看
            </el-button> -->
          <el-button
            size="mini"
            type="success"
            v-if="
              scope.row.columnValues.info.status == 'parse_end' &&
              Number(
                ((Number(scope.row.columnValues.info.all_num_received) /
                  Number(scope.row.columnValues.info.all_num)) *
                  100) |
                  mytoFixed
              ) === 100
            "
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
          <!-- <el-button
              size="mini"
              type="danger"
              @click="handleLogs(scope.row.row)"
            >
              日志
            </el-button> -->
          <el-button
            size="mini"
            plain
            @click="handleMarkdownView(scope.row.row, scope.row)"
          >
            查看
          </el-button>
          <el-button @click="showPlayTask(scope.row)" size="mini" type="primary"
            >查看计划任务
            <!-- {{
                $refs.myTable?.store?.states.expandRows?.includes(scope.row)
                  ? "收起"
                  : "展开"
              }} -->
          </el-button>

          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="page-box" v-if="taskNum > 0">
      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="taskNum"
      >
      </el-pagination>
    </div>
    <!-- 日志对话框 -->
    <!-- <el-dialog
      :visible.sync="logsDialogVisible"
      append-to-body
      width="80%"
      top="20px"
      title="日志列表"
    >
      <div
        ref="scroll"
        @scroll="handleScroll"
        class="hitsLayscroll"
        v-loading="logList.length > 0 ? false : true"
      >
        
        <div class="chat-content" ref="chatContent">
          <li v-for="(item, index) in logList" :key="index">
            <window-session-content
              :role="item.role"
              :content-show-type="windowData.contentShowType"
              :item-data="item"
            ></window-session-content>
          </li>
        </div>
      </div>
    </el-dialog> -->
    <!-- markdown展示 -->
    <el-dialog
      :visible.sync="markdownDialogVisible"
      append-to-body
      width="80%"
      top="20px"
      title="任务详情"
    >
      <div>
        <div class="MDbutton">
          <div style="margin-right: 15px">
            <el-button type="primary" @click="exportMD">导出</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveMD">保存</el-button>
          </div>
        </div>
        <div class="MDinput" style="overflow-y: auto">
          <component
            :is="detail_key"
            v-for="(detail_val, detail_key) in taskDetail"
            :key="detail_key"
          ></component>

          <!-- <mavon-editor
            v-model="markdownData"
            :externalLink="externalLink"
            style="height: 100%"
            :editable="false"
            :subfield="false"
            :toolbarsFlag="false"
            :defaultOpen="'preview'"
          /> -->
          <!-- <MarkdownView
            style="height: 100%"
            ref="mv"
            :content="markdownData"
          ></MarkdownView> -->
        </div>
      </div>
    </el-dialog>
    <!-- markdown编辑 -->
    <el-dialog
      :visible.sync="buildBri"
      append-to-body
      width="80%"
      top="20px"
      title="任务详情"
    >
      <div>
        <div class="MDbutton">
          <div style="margin-right: 15px">
            <el-button type="primary" @click="exportMD">导出</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveMD">保存</el-button>
          </div>
        </div>
        <div class="MDinput">
          <mavon-editor
            v-model="MDcontent"
            :externalLink="externalLink"
            style="height: 100%"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 保存情报对话框 -->
    <el-dialog
      title="保存情报"
      :visible.sync="favoriteDataVisible"
      @close="clearFavoriteData"
      width="40%"
      append-to-body
    >
      <el-form>
        <el-form-item label="情报标题：">
          <el-input
            style="width: 80%"
            placeholder="请输入情报标题"
            v-model="taskTitle"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务名称：">
          <!-- <p class="fileName">{{ currentTask?.columnValues?.info?.title || '无标题任务' }}</p> -->
        </el-form-item>
        <el-form-item label="类型选择：">
          <el-select v-model="taskCategory" placeholder="请选择类型">
            <el-option
              v-for="item in taskCategories"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="clearFavoriteData">取 消</el-button>
        <el-button type="primary" @click="saveTaskData">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加/编辑任务对话框 -->
    <el-dialog
      :title="dialogStatus === 'add' ? '添加计划任务' : '编辑计划任务'"
      :visible.sync="dialogVisible"
      width="40%"
      append-to-body
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        :model="taskForm"
        :rules="rules"
        ref="taskForm"
        label-width="100px"
      >
        <!-- <el-form-item label="任务名" prop="taskName">
          <el-input
            v-model="taskForm.taskName"
            placeholder="请输入任务名称"
            style="width: 100%"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="频次" prop="frequency">
          <el-radio-group
            v-model="taskForm.frequency"
            @change="handleFrequencyChange"
          >
            <el-radio label="每天">每天</el-radio>
            <el-radio label="每周">每周</el-radio>
            <!-- <el-radio label="一次" @click.native.prevent="handleOnceClick"
              >一次</el-radio
            > -->
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item
          label="日期范围"
          prop="dateRange"
          v-if="taskForm.frequency !== '一次'"
        >
          <el-date-picker
            v-model="taskForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="日期"
          prop="singleDate"
          v-if="taskForm.frequency === '一次'"
        >
          <el-date-picker
            v-model="taskForm.singleDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
          >
          </el-date-picker>
        </el-form-item> -->

        <el-form-item label="时间" prop="timeValue">
          <el-select
            v-model="taskForm.weekValue"
            placeholder="请选择"
            v-if="taskForm.frequency === '每周'"
          >
            <el-option
              v-for="item in weeksOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-time-picker
            v-model="taskForm.timeValue"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            @change="calculateCronExpression"
          >
          </el-time-picker>
        </el-form-item>
        <!-- <el-form-item label="Cron表达式">
          <el-input
            v-model="cronExpression"
            readonly
            placeholder="Cron表达式将根据频次和时间自动生成"
          >
            <template slot="append">
              <el-tooltip content="Cron表达式用于定时任务调度" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item> -->
        <el-form-item label="任务模板" prop="templateNumber">
          <el-select
            v-model="taskForm.templateNumber"
            placeholder="请选择任务模板"
            style="width: 100%"
            :disabled="dialogStatus === 'edit'"
          >
            <el-option
              v-for="item in templateList"
              :key="item.description"
              :label="item.description"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="描述信息" prop="describe">
          <el-input
            type="textarea"
            v-model="taskForm.describe"
            :rows="3"
            placeholder="请输入任务描述信息"
          ></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="环境变量" prop="environment">
          <el-select
            v-model="environmentSelectValue"
            placeholder="请选择环境变量"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
            @change="handleEnvironmentChange"
          >
            <el-option
              v-for="item in taskQueueList"
              :key="item.name"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          v-if="dialogStatus === 'add'"
          @click="submitTaskForm"
          >确认</el-button
        >
        <el-button
          type="primary"
          v-if="dialogStatus === 'edit'"
          @click="updateTaskForm"
          >确认编辑</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MarkdownView from "@/components/MarkdownView/index";
import { mapState, mapMutations } from "vuex";
import ContentShowType from "@/common/constants/ContentShowType";
import WindowSessionContent from "@/components/session/window/chat/WindowSessionContent";
const windowData = {
  contentShowType: ContentShowType.Markdown,
};
export default {
  name: "TaskQueue",
  data() {
    // 动态生成日期验证规则
    const validateDateInput = (rule, value, callback) => {
      if (this.taskForm.frequency === "一次") {
        if (!this.taskForm.singleDate) {
          return callback(new Error("请选择日期"));
        }
      } else {
        if (!this.taskForm.dateRange || this.taskForm.dateRange.length !== 2) {
          return callback(new Error("请选择日期范围"));
        }
      }
      callback();
    };
    return {
      loopGetTaskListInterval: null,
      cronjob_name: "",
      myplanTaskListObj: {},
      dialogVisible: false,
      dialogStatus: "add",
      cronExpression: "",
      weeksOptions: [
        {
          value: "周一",
          label: "周一",
        },
        {
          value: "周二",
          label: "周二",
        },
        {
          value: "周三",
          label: "周三",
        },
        {
          value: "周四",
          label: "周四",
        },
        {
          value: "周五",
          label: "周五",
        },
        {
          value: "周六",
          label: "周六",
        },
        {
          value: "周日",
          label: "周日",
        },
      ],
      taskForm: {
        frequency: "每天",
        dateRange: [],
        singleDate: "",
        weekValue: "",
        timeValue: "",
        templateNumber: "",
        describe: "",
        environment: [],
        cronExpression: "",
        taskName: "",
        taskId: "",
        taskType: "",
      },
      rules: {
        frequency: [
          { required: true, message: "请选择任务频次", trigger: "change" },
        ],
        // dateRange: [{ validator: validateDateInput, trigger: "change" }],
        singleDate: [{ validator: validateDateInput, trigger: "change" }],
        timeValue: [
          { required: true, message: "请选择执行时间", trigger: "change" },
        ],
        templateNumber: [
          { required: true, message: "请选择任务模板", trigger: "change" },
        ],
        describe: [
          { required: true, message: "请输入任务描述", trigger: "blur" },
        ],
        taskName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
      },
      taskQueueList: [],
      markdownData: "",
      markdownDialogVisible: false,
      windowData,
      selectedTasks: [],
      state_value: "all",
      currentPage: 1,
      pageSize: 20,
      buildBri: false, // 编辑markdown对话框显示状态
      MDcontent: "", // markdown内容
      currentTask: null, // 当前查看的任务
      favoriteDataVisible: false, // 保存情报对话框显示状态
      taskTitle: "", // 情报标题
      taskCategory: "report", // 默认情报类型
      taskCategories: [
        { label: "简报", value: "report" },
        { label: "分析", value: "analysis" },
        { label: "情报", value: "intelligence" },
      ],
      method: [
        {
          value: "generate_public_opinion_report_from_collection",
          label: "生成舆情简报",
        },
        {
          value: "data_analysis_search_task",
          label: "Ai查询分析任务简报",
        },
      ],
      analysisState: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "parse_end",
          label: "已完成",
        },
        {
          value: "parsing",
          label: "进行中",
        },

        {
          value: "error",
          label: "失败",
        },
      ],
      analysisAllState: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "parse_end",
          label: "已完成",
        },
        {
          value: "parsing",
          label: "进行中",
        },
        {
          value: "job_parsing",
          label: "进行中",
        },
        {
          value: "error",
          label: "失败",
        },
        {
          value: "parse_ready",
          label: "待解析",
        },
      ],
      externalLink: {
        markdown_css: function () {
          return "/mavon-editor/markdown/github-markdown.min.css";
        },
        hljs_js: function () {
          return "/mavon-editor/highlightjs/highlight.min.js";
        },
        hljs_css: function (css) {
          return "/mavon-editor/highlightjs/styles/" + css + ".min.css";
        },
        hljs_lang: function (lang) {
          return "/mavon-editor/highlightjs/languages/" + lang + ".min.js";
        },
        katex_css: function () {
          return "/mavon-editor/katex/katex.min.css";
        },
        katex_js: function () {
          return "/mavon-editor/katex/katex.min.js";
        },
      },
      refreshTimer: null,
    };
  },
  components: {
    WindowSessionContent,
    MarkdownView,
    bulletin: () => import("./task_detail/bulletin.vue"),
    article_category: () => import("./task_detail/article_category.vue"),
    article_list: () => import("./task_detail/article_list.vue"),
  },
  computed: {
    ...mapState({
      pageList: (state) => state.aiTaskQueue.pageList,
      taskNum: (state) => state.aiTaskQueue.taskNum,
      planTaskListObj: (state) => state.aiPlanTask.planTaskListObj,
      planTaskList: (state) => state.aiPlanTask.planTaskList,
      templateList: (state) => state.aiPlanTask.templateList,
      taskLoading: (state) => state.aiTaskQueue.taskLoading,
      showTaskList: (state) => state.aiTaskQueue.showTaskList,
      from: (state) => state.aiTaskQueue.from,
      allTaskListCount: (state) => state.aiTaskQueue.allTaskListCount,
      parseReadyTaskListCount: (state) =>
        state.aiTaskQueue.parseReadyTaskListCount,
      parseEndTaskListCount: (state) => state.aiTaskQueue.parseEndTaskListCount,
      parseErrorTaskListCount: (state) =>
        state.aiTaskQueue.parseErrorTaskListCount,
      logList: (state) => state.aiTaskQueue.logList,
      taskDetail: (state) => state.aiTaskQueue.taskDetail,
      allTaskQueueList: (state) => state.aiTaskQueue.allTaskList,
    }),
    // 环境变量选择器的值
    environmentSelectValue: {
      get() {
        // 从 taskForm.environment 数组中获取第一个元素的 value
        return this.taskForm.environment && this.taskForm.environment.length > 0
          ? this.taskForm.environment[0].value
          : "";
      },
      set(value) {
        // 这个 setter 不会被直接调用，因为我们使用 @change 事件
        console.log("environmentSelectValue setter:", value);
      },
    },
    mytoFixed: function (num) {
      num *= Math.pow(10, 2);
      num = Math.round(num);
      return num / Math.pow(10, 2);
    },
    getTotalCount: {
      get(val) {
        return this.$store.state.aiTaskQueue.showTaskdatasCount;
      },
      set(nval) {},
    },
    logsDialogVisible: {
      get(val) {
        return this.$store.state.aiTaskQueue.logsDialogVisible;
      },
      set(nval) {
        this.$store.commit("aiTaskQueue/setLogsDialogVisible", nval);
      },
    },
  },
  filters: {
    mytoFixed: function (num) {
      num *= Math.pow(10, 2);
      num = Math.round(num);
      return num / Math.pow(10, 2);
    },
  },
  created() {
    this.$store.commit("aiTaskQueue/clearTaskList", "all");
    this.resetTaskState();
    this.sendListAnalysisList();
    this.getPlanTaskTemplateList();
  },
  destroyed() {
    if (this.loopGetTaskListInterval) {
      clearInterval(this.loopGetTaskListInterval);
      this.loopGetTaskListInterval = null;
    }
  },
  watch: {
    allTaskQueueList: {
      handler(newVal, oldVal) {
        newVal.forEach((item) => {
          this.taskQueueList.push({
            name: item.columnValues.info.title,
            value: item.row,
            type: item.columnValues.info.task_type,
          });
        });
      },
    },
    planTaskListObj: {
      handler(newVal, oldVal) {
        // 注意：在嵌套对象中，oldVal 和 newVal 可能是相同的
        console.log("newVal", newVal);
        this.myplanTaskListObj = newVal;
      },
      deep: true, // 深度监听
    },
  },
  methods: {
    ...mapMutations({
      updatePlanTask: "aiPlanTask/updatePlanTask",
      getPlanTaskTemplateList: "aiPlanTask/getPlanTaskTemplateList",
      resetPlanTaskData: "aiPlanTask/resetPlanTaskData",
      getPlanTaskList: "aiPlanTask/getPlanTaskList",
      getAllPlanTaskList: "aiTaskQueue/getAllPlanTaskList",
      sendListAnalysisList: "aiTaskQueue/sendListAnalysisList",
      resetTaskState: "aiTaskQueue/resetTaskState",
      setTaskStatus: "aiTaskQueue/setTaskStatus",
      setPageAndData: "aiTaskQueue/setPageAndData",
      sendDelTask: "aiTaskQueue/sendDelTask",
      createPlanTask: "aiPlanTask/createPlanTask",
      deletePlanTask: "aiPlanTask/deletePlanTask",
    }),
    handleExpandChange(row, expandedRows) {
      this.resetPlanTaskData();
      // row: 当前操作的行数据
      // expandedRows: 当前所有展开的行数组
      this.taskForm.taskId = row.row;
      this.taskForm.taskType = row.columnValues.info.task_type;
      console.log("触发展开/折叠事件", row);
      if (expandedRows.some((r) => r.row === row.row)) {
        console.log("当前行已展开，可以在这里调用你的函数");
        this.getPlanTaskList(row);
      }
    },

    showPlayTask(v) {
      // this.taskForm.taskId = v.row;
      this.$refs.myTable.toggleRowExpansion(v);
      // this.getPlanTaskList(v);
    },
    // 重置任务表单
    resetTaskForm() {
      this.taskForm = {
        frequency: "每天",
        dateRange: [],
        singleDate: "",
        timeValue: "",
        weekValue: "",
        templateNumber: "",
        describe: "",
        environment: [],
        cronExpression: "",
        taskName: "",
        taskId: "",
        taskType: "",
      };
      this.cronExpression = "";
      if (this.$refs.taskForm) {
        this.$refs.taskForm.resetFields();
      }
    },
    // 处理删除计划任务
    handleDeleteTask(row, item) {
      this.$confirm("确认删除该任务吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log("删除任务", row, item);
          this.deletePlanTask({ row: row, item: item });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    //编辑计划任务
    updateTaskForm() {
      this.updatePlanTask({
        fromData: this.taskForm,
        cronjob_name: this.cronjob_name,
      });
      this.dialogVisible = false;
    },
    // 提交任务表单
    submitTaskForm() {
      this.$refs.taskForm.validate((valid) => {
        if (valid) {
          // 构建任务数据对象
          const taskData = {
            cronjob_template_number: this.taskForm.templateNumber,
            schedule: this.cronExpression,
            describe: this.taskForm.describe,
            environment: this.taskForm.environment,
            task_name: this.taskForm.taskName,
            task_id: this.taskForm.taskId,
            frequency: this.taskForm.frequency,
            time: this.taskForm.timeValue,
            week: this.taskForm.weekValue,
            task_type: this.taskForm.taskType,
          };

          // 创建任务
          console.log("submitTaskForm", taskData);
          this.createPlanTask(taskData);
          this.dialogVisible = false;
        } else {
          return false;
        }
      });
    },
    // 处理频次变化
    handleFrequencyChange(value) {
      // 切换频次时重置日期相关字段
      if (value === "一次") {
        this.taskForm.dateRange = [];
        this.taskForm.singleDate = "";
      } else {
        this.taskForm.singleDate = "";
        this.taskForm.dateRange = [];
      }
      // 重新计算cron表达式
      this.calculateCronExpression();
    },
    // 处理"一次"选项点击
    handleOnceClick() {
      this.$message({
        message: "功能正在开发中...",
        type: "info",
      });
    },
    // 处理环境变量变化
    handleEnvironmentChange(value) {
      // 根据选中的 value 找到对应的 item
      const selectedItem = this.taskQueueList.find(
        (item) => item.value === value
      );

      if (selectedItem) {
        // 设置为正确的格式：[{name: item.name, value: item.value}]
        this.taskForm.environment = [
          {
            name: selectedItem.name,
            value: selectedItem.value,
            type: selectedItem.type,
          },
        ];
      } else {
        // 如果没有找到对应的 item，可能是用户手动输入的
        this.taskForm.environment = [
          {
            name: value,
            value: value,
            type: "ai_workflow_task",
          },
        ];
      }

      console.log(
        "环境变量变化:",
        value,
        "设置结果:",
        this.taskForm.environment
      );
    },
    // 计算Cron表达式
    calculateCronExpression() {
      if (!this.taskForm.timeValue) {
        this.cronExpression = "";
        return;
      }

      const [hours, minutes] = this.taskForm.timeValue.split(":").map(Number);

      switch (this.taskForm.frequency) {
        case "每天":
          this.cronExpression = `${minutes} ${hours} * * *`;
          break;
        case "每周":
          if (this.taskForm.weekValue === "周一") {
            this.cronExpression = `${minutes} ${hours} * * 1`;
          } else if (this.taskForm.weekValue === "周二") {
            this.cronExpression = `${minutes} ${hours} * * 2`;
          } else if (this.taskForm.weekValue === "周三") {
            this.cronExpression = `${minutes} ${hours} * * 3`;
          } else if (this.taskForm.weekValue === "周四") {
            this.cronExpression = `${minutes} ${hours} * * 4`;
          } else if (this.taskForm.weekValue === "周五") {
            this.cronExpression = `${minutes} ${hours} * * 5`;
          } else if (this.taskForm.weekValue === "周六") {
          } else if (this.taskForm.weekValue === "周日") {
            this.cronExpression = `${minutes} ${hours} * * 7`;
          }

          break;
        default:
          this.cronExpression = "";
      }
    },
    handleEditTask(row, task) {
      console.log("handleEditTask", row, task);
      this.dialogStatus = "edit";
      // 设置基础表单数据
      this.taskForm = {
        taskId: row.row,
        frequency: task.metadata.annotations.week ? "每周" : "每天",
        timeValue: task.metadata.annotations.time,
        weekValue: task.metadata.annotations.week,
        cronExpression: task.spec.schedule,
        templateNumber: task.metadata.labels.cronjobTemplateNumber,
        taskType: row.columnValues.info.task_type,
      };
      this.cronjob_name = task.metadata.name;
      console.log("this.taskForm", this.taskForm);
      //
      // 根据频次类型设置日期字段
      /* if (task.frequency === "一次") {
        this.taskForm.singleDate = task.startDate;
        this.taskForm.dateRange = [];
      } else {
        this.taskForm.dateRange = [task.startDate, task.endDate];
        this.taskForm.singleDate = "";
      } */
      this.dialogVisible = true;
    },
    // 显示添加任务对话框
    showAddTaskDialog(row) {
      this.dialogStatus = "add";
      this.resetTaskForm();
      this.taskForm.taskId = row.row;
      this.taskForm.taskType = row.columnValues.info.task_type;
      this.dialogVisible = true;
    },
    //简报handleSummary
    handleMarkdownView(v1, v2) {
      console.log(v1, v2);

      const routeData = window.main.$router.resolve({
        path: `/taskDetail/${v1}/${v2.columnValues.info.title}/${v2.columnValues.info.method}`,
      });
      window.open(routeData.href, "_blank");
      /* window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListDatas",
        [
          {
            head: {
              // row_key: state.logLastRowKey,
              size: 200,
              //qualifier:state.logQualifier,
            },
            msg: {
              task_authority: "username",
              task_id: v,
              task_type: "ai_workflow_task",
            },
            //msg:{task_type:['social_platform_task'], familys:['logs']}
          },
        ],
        (res) => {
          let objData = new Object();
          let a = 0;
          for (let i = 0; i < res.length; i++) {
            Object.defineProperty(objData, res[i].columnValues.d["_"], {
              value: res[i].columnValues.d.data,
              writable: true, // 允许修改
              enumerable: true, // 可枚举（for...in 或 Object.keys() 可见）
              configurable: true, // 允许删除或重新定义
            });
            a++;
          }
          if (a === res.length) {
            this.$store.commit("aiTaskQueue/setTaskDetail", objData);
            this.markdownDialogVisible = true;
          }


        }
      ); */
    },
    //自动刷新
    loopGetTaskList() {
      this.loopGetTaskListInterval = setInterval(() => {
        this.$store.commit("aiTaskQueue/clearTaskList", "parsing");
        this.sendListAnalysisList();
      }, 1000);
    },
    // 当前页变化
    handleCurrentChange(val) {
      let test = null;
      this.currentPage = val;
      this.selectedTasks = [];
      this.$store.commit("aiTaskQueue/setPageAndData", val);
    },

    // 切换任务状态
    handleRadioChanges(item) {
      this.state_value = item.value;
      this.currentPage = 1;
      this.$store.commit("aiTaskQueue/clearTaskList", item.value);
      if (item.value == "parsing") {
        this.$message({
          message: "刷新已开启",
          type: "success",
        });
        this.loopGetTaskList();
      } else {
        if (this.loopGetTaskListInterval) {
          clearInterval(this.loopGetTaskListInterval);
          this.loopGetTaskListInterval = null;
        }
        this.sendListAnalysisList();
      }
    },
    //获取含有计划任务的任务列表
    handleRadioChangesPlan() {
      this.currentPage = 1;
      this.$store.commit("aiTaskQueue/clearTaskList", "all");
      if (this.loopGetTaskListInterval) {
        clearInterval(this.loopGetTaskListInterval);
        this.loopGetTaskListInterval = null;
      }
      //获取所有计划任务
      this.getAllPlanTaskList();
    },

    // 删除选中的任务
    handleDeleteSelected() {
      if (this.selectedTasks.length === 0) {
        this.$message.warning("请先选择要删除的任务");
        return;
      }
      this.$confirm("确认删除选中的任务?", "提示", {
        type: "warning",
      })
        .then(() => {
          const taskIds = this.selectedTasks.map((task) => task.id);
          this.sendDelTask(taskIds);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 选中任务
    handleSelectionChange(val) {
      this.selectedTasks = val;
    },

    // 操作任务---查看任务
    handleView(task) {
      console.log("查看任务", task);
      this.currentTask = task;
      this.MDcontent = task.columnValues?.info?.content || "暂无内容";
      this.buildBri = true;
    },

    // 操作任务---下载任务
    handleDownload(task) {
      this.$message.success(`开始下载任务: ${task.name}`);
    },
    // 操作任务---查看日志
    handleLogs(task) {
      this.$store.commit("aiTaskQueue/setClearLogTemporaryList");
      this.$store.commit("aiTaskQueue/setClearLogList");
      this.$store.commit("aiTaskQueue/setClearLastRowKey");
      this.$store.commit("aiTaskQueue/sendGetTaskLog", task);
    },
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        /*  this.loadData(); */
      }
    },
    // 操作任务---删除任务
    handleDelete(task) {
      console.log("删除任务", task);
      this.$confirm(`确认删除任务"${task.columnValues.info.title}"?`, "提示", {
        type: "warning",
      })
        .then(() => {
          this.sendDelTask([task.row]);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 导出markdown文件
    exportMD() {
      const blob = new Blob([this.MDcontent], { type: "text/markdown" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `任务_${
        this.currentTask?.columnValues?.info?.title || "untitled"
      }_${Date.now()}.md`;
      link.click();
      URL.revokeObjectURL(link.href);
    },

    // 保存markdown内容
    saveMD() {
      this.taskTitle =
        this.currentTask?.columnValues?.info?.title || "无标题任务";
      this.favoriteDataVisible = true;
    },

    // 取消保存操作
    clearFavoriteData() {
      this.favoriteDataVisible = false;
      this.taskTitle = "";
    },

    // 保存任务数据
    saveTaskData() {
      if (!this.taskTitle.trim()) {
        this.$message.warning("请输入情报标题");
        return;
      }

      // 构建要保存的数据
      const prefix =
        1e13 -
        Math.round(new Date().getTime() / 1000) +
        (this.currentTask?.row || Date.now());

      // 发送数据到服务器
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            msg: {
              type: "task",
              task_id: this.currentTask?.row || "",
              table: "ai_task_intelligence",
              prefix,
              data: {
                data: {
                  file_data: {
                    title: this.taskTitle,
                    content: this.MDcontent,
                    category: this.taskCategory,
                    createTime: Date.now(),
                    taskInfo: this.currentTask?.columnValues?.info || {},
                  },
                },
              },
            },
          },
        ],
        (res) => {
          console.log(res);
          if (res.status === "ok") {
            this.$message({
              type: "success",
              message: "保存成功",
            });
            this.favoriteDataVisible = false;
            this.buildBri = false;
          } else {
            this.$message.error("保存失败：" + (res.message || "未知错误"));
          }
        }
      );
    },

    formatTaskStatus(row) {
      const status = row.columnValues?.info?.status;
      const state = this.analysisAllState.find((item) => item.value === status);
      return state ? state.label : "未知";
    },

    formatTaskMethod(row) {
      const method = row.columnValues?.info?.method;
      const zh = this.method.find((item) => item.value === method);
      return zh ? zh.label : "未知任务类型";
    },
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.expand_layout {
  margin-top: -13px;
  padding-left: 50px;
  border: 1px solid #eee;
  padding-bottom: 10px;
  padding-top: 10px;
}
.expand_layout_title {
  display: flex;
}
.cardLayout {
  display: flex;
  flex-wrap: wrap;
  .cardItem {
    display: flex;
    justify-content: space-between;
    border-radius: 20px;
    margin-top: 20px;
    margin-left: 20px;
    padding: 10px 10px;
    width: 200px;
    height: 50px;
    box-shadow: 0px 1px 6px 1px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
  }
}
.hitsLayscroll {
  height: 700px;
  overflow-y: scroll;

  .chat-content {
    width: 100%;
    padding: 8px 0 14px 0;
    box-sizing: border-box;
    flex-grow: 1;
    .chat-main-content {
      width: 100%;
      display: flex;
    }

    li {
      list-style: none;
      height: auto;
      width: 1020px;
      margin: 0 auto;
      display: flex;
    }
  }
}
.task-queue {
  padding: 20px;
  height: 100%;
}

.page-box {
  width: 50%;
  margin-left: 30%;
}

.filter-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  height: 5%;
}

.task-status {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.status-completed {
  color: #67c23a;
}

.status-processing {
  color: #409eff;
}

.status-failed {
  color: #f56c6c;
}

.MDbutton {
  display: flex;
  height: 6vh;
  justify-content: end;
  align-items: center;
  padding-right: 20px;
}

.MDinput {
  border: 1px solid #ccc;
  height: 77vh;
  display: flex;
  flex-direction: column;
}

.file {
  display: flex;
  align-items: center;
  margin-right: 15px;
  position: relative;

  span {
    display: inline-block;
    padding: 7px 15px;
    background-color: #409eff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  }

  input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}

.myprogress {
  width: 100%;
  height: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.myprogressreceived {
  height: 100%;
  background: #409eff;
  text-align: center;
  font-size: 12px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;

  .textspan {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.myprogressreceivedasync {
  height: 100%;
  background: #67c23a;
  position: absolute;
  top: 0;
  font-size: 12px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fileName {
  margin: 0;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 对话框样式 */
::v-deep .el-form-item__label {
  font-weight: bold;
}

/* 隐藏 mavon-editor 滚动条 */
::v-deep .mavon-editor {
  .v-note-wrapper {
    .v-note-panel {
      .v-note-show {
        .v-note-show-content {
          .v-note-show-content-wrapper {
            .v-note-show-content-wrapper-content {
              &::-webkit-scrollbar {
                display: none;
              }
              -ms-overflow-style: none;
              scrollbar-width: none;
            }
          }
        }
      }
    }
  }
}

/* 隐藏 mavon-editor 编辑区域的滚动条 */
::v-deep .mavon-editor .v-note-wrapper .v-note-panel .v-note-edit {
  .v-note-edit-wrapper {
    .v-note-edit-wrapper-content {
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }
}

/* 隐藏 mavon-editor 预览区域的滚动条 */
::v-deep .mavon-editor .v-note-wrapper .v-note-panel .v-note-show {
  .v-note-show-content {
    .v-note-show-content-wrapper {
      .v-note-show-content-wrapper-content {
        &::-webkit-scrollbar {
          display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    }
  }
}
</style>
