/**
 * 邮件解析工具
 * 用于解析MIME格式的邮件内容，并提取HTML等信息
 */

import emailjsMimeParser from 'emailjs-mime-parser';

/**
 * 解析邮件内容
 * @param {String} emailContent - 原始邮件内容
 * @returns {Object} 解析后的邮件对象，包含各种格式的内容
 */
export function parseEmail(emailContent) {
  try {
    // 使用emailjs-mime-parser解析邮件
    const parsed = emailjsMimeParser(emailContent);
    
    // 提取基本信息
    const info = {
      from: parsed.headers.from ? parsed.headers.from[0].value[0].address : '',
      to: parsed.headers.to ? parsed.headers.to[0].value[0].address : '',
      subject: parsed.headers.subject ? parsed.headers.subject[0].value : '',
      date: parsed.headers.date ? parsed.headers.date[0].value : '',
      htmlContent: '',
      textContent: '',
      jsonContent: null
    };
    
    // 遍历子节点查找内容
    processChildNodes(parsed, info);
    
    return info;
  } catch (error) {
    console.error('邮件解析失败:', error);
    return {
      from: '',
      to: '',
      subject: '',
      date: '',
      htmlContent: '',
      textContent: emailContent,
      jsonContent: null
    };
  }
}

/**
 * 递归处理MIME节点
 * @param {Object} node - MIME节点
 * @param {Object} info - 存储解析结果的对象
 */
function processChildNodes(node, info) {
  // 如果是文本节点
  if (node.contentType && node.contentType.value === 'text/plain') {
    // 提取纯文本内容
    const content = node.content || '';
    
    // 尝试解析JSON
    try {
      const jsonData = JSON.parse(content);
      info.jsonContent = jsonData;
      
      // 如果JSON中有content字段，提取为文本内容
      if (jsonData.content) {
        info.textContent += jsonData.content;
      } else {
        info.textContent += content;
      }
    } catch (e) {
      // 不是JSON格式，作为纯文本处理
      info.textContent += content;
    }
  }
  
  // 如果是HTML节点
  if (node.contentType && node.contentType.value === 'text/html') {
    info.htmlContent += node.content || '';
  }
  
  // 递归处理子节点
  if (node.childNodes && node.childNodes.length) {
    node.childNodes.forEach(child => {
      processChildNodes(child, info);
    });
  }
}

/**
 * 格式化邮件为HTML显示
 * @param {String} emailContent - 原始邮件内容
 * @returns {String} 格式化后的HTML
 */
export function formatEmailToHtml(emailContent) {
  const parsedEmail = parseEmail(emailContent);
  
  // 如果有解析到HTML内容，优先使用
  if (parsedEmail.htmlContent) {
    return parsedEmail.htmlContent;
  }
  
  // 如果有JSON内容且包含消息，则格式化JSON
  if (parsedEmail.jsonContent) {
    let content = '';
    
    // 处理JSON内容
    if (parsedEmail.jsonContent.content) {
      content = parsedEmail.jsonContent.content;
    } else {
      // 格式化展示整个JSON
      content = `<pre>${JSON.stringify(parsedEmail.jsonContent, null, 2)}</pre>`;
    }
    
    // 构建HTML
    return `
      <div class="email-container">
        <div class="email-header">
          ${parsedEmail.from ? `<div><strong>发件人:</strong> ${parsedEmail.from}</div>` : ''}
          ${parsedEmail.to ? `<div><strong>收件人:</strong> ${parsedEmail.to}</div>` : ''}
          ${parsedEmail.date ? `<div><strong>日期:</strong> ${parsedEmail.date}</div>` : ''}
        </div>
        <div class="email-content">
          ${content}
        </div>
      </div>
    `;
  }
  
  // 退回到纯文本
  return `
    <div class="email-container">
      <div class="email-header">
        ${parsedEmail.from ? `<div><strong>发件人:</strong> ${parsedEmail.from}</div>` : ''}
        ${parsedEmail.to ? `<div><strong>收件人:</strong> ${parsedEmail.to}</div>` : ''}
        ${parsedEmail.date ? `<div><strong>日期:</strong> ${parsedEmail.date}</div>` : ''}
      </div>
      <div class="email-content">
        <pre>${parsedEmail.textContent}</pre>
      </div>
    </div>
  `;
} 