<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #dbeafe;
          line-height: 42px;
          font-weight: bold;
          color: #2244b0;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-s-order"></i
          ><span style="margin-left: 10px">文章列表</span>
        </div>
      </div>
      <div style="padding: 10px; line-height: 42px">
        <div
          v-for="(val, index) in listData"
          :key="index"
          class="newContent with-dot"
        >
          <div
            class="content_title"
            v-html="
              val.columnValues.d.file_data._source.title &&
              val.columnValues.d.file_data._source.title.trim()
                ? '• ' + val.columnValues.d.file_data._source.title
                : '• 无标题'
            "
            @click="NewsDisplay(val.columnValues.d.file_data)"
          ></div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "article_list",
  data() {
    return {
      listData: [],
    };
  },
  computed: {
    ...mapState({
      article_list: (state) => state.aiTaskQueue.taskDetail.article_list,
    }),
  },
  created() {
    window.main.$main_socket.sendData(
      "Api.Search.SearchPrefixTable.Query",
      [
        {
          head: {
            size: 2000,
            from: 0,
          },
          msg: {
            table: "favorites_data",
            prefix: "",
            type: "username",
            relation: this.article_list[0],
          },
        },
      ],
      (res) => {
        if (res) {
          this.listData = res;
        }
      }
    );
  },
  methods: {
    NewsDisplay(val) {
      const routeData = this.$router.resolve({
        name: "NewsDisplay",
        query: {
          id: val.id ? val.id : val._id,
          index: val.index ? val.index : val._index,
        },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.content_title {
  width: 90%;
  padding-left: 10px;
  font-size: 18px;
  color: #2440b3;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  min-height: 20px;
}

.content_title:hover {
  text-decoration: underline;
}
/* 或直接修改圆点颜色 */
.content_title::first-letter {
}
</style>
