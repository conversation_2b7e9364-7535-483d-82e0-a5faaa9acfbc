<template>
  <div class="intellManage">
    <div v-if="dataLoading" class="loading">
      <i class="el-icon-loading"></i>加载中...
    </div>
    <div v-if="twitterList.length === 0" class="no-more">暂无数据</div>
    <el-card
      class="box-card"
      v-for="(item, index) in twitterList"
      shadow="hover"
    >
      <div class="box-row" @click="moreFn(item)">
        <div>
          <img
            v-if="item.columnValues.d.icon"
            :onerror="defaultImg"
            :src="
              '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
              item.columnValues.d.icon.sha512_hash +
              '?session_id=' +
              $store.state.userInfo.session_id
            "
          />
          <img v-else :onerror="defaultImg" :src="imga" />
          <div style="margin-right: 15px; color: #999; margin-left: -10px">
            <span
              class="account_bage"
              :style="
                noStatus({ status: { status: item.columnValues.d.status } })
              "
            ></span>
            <span style="margin-left: -4px; font-size: 11px">{{
              item.columnValues.d.status === "monitor" ? "监控" : "未监控"
            }}</span>
          </div>
        </div>
        <div
          style="
            margin-left: 10px;
            height: 90px;
            justify-content: space-between;
            flex-direction: column;
            display: flex;
          "
        >
          <div v-if="item.columnValues.d.nickname">
            <b>{{ item.columnValues.d.nickname }}</b>
          </div>
          <div
            v-if="item.columnValues.d.user_id || item.columnValues.d._"
            style=""
          >
            {{ item.columnValues.d.user_id || item.columnValues.d._._ }}
          </div>
          <div>
            <span
              v-if="item.columnValues.d.article_count"
              style="margin-right: 20px"
              title="发文"
              ><i
                class="el-icon-document"
                style="color: #e6a23c; margin-right: 5px"
              ></i
              >{{ item.columnValues.d.article_count | capitalize }}</span
            >
            <span
              v-if="item.columnValues.d.followers_count"
              style="margin-right: 20px"
              title="粉丝"
              ><i
                class="icon iconfont"
                style="color: #e6a23c; margin-right: 5px"
                >&#xe619;</i
              >{{ item.columnValues.d.followers_count | capitalize }}</span
            >
            <span
              v-if="item.columnValues.d.following_count"
              style="margin-right: 20px"
              title="关注"
              ><i
                class="el-icon-view"
                style="color: #e6a23c; margin-right: 5px"
              ></i
              >{{ item.columnValues.d.following_count | capitalize }}</span
            >
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      defaultImg: 'this.src="' + require("@/assets/images/winter.jpg") + '"',
    };
  },
  watch: {},
  computed: {
    ...mapState({
      twitterList: (state) => state.deployment.twitter.twitterList,
      dataLoading: (state) => state.deployment.twitter.loading,
    }),
  },
  created() {
    this.loadData();
  },
  filters: {
    capitalize(value) {
      switch (true) {
        case Number(value) >= 10000 && Number(value) <= 100000000:
          return (Number(value) / 10000).toFixed(2) + "万";
          break;
        case 100000000 <= Number(value):
          return (Number(value) / 100000000).toFixed(2) + "亿";
          break;
        default:
          return Number(value);
      }
    },
  },
  mounted() {},
  methods: {
    ...mapMutations({ initialData: "search/twLinFacSearch/initialData" }),
    // 加载数据
    async loadData() {
      try {
        const newData = await this.$store.dispatch(
          "deployment/twitter/getTwitterList"
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    noStatus(data) {
      console.log("data", data.status.status);
      if (data?.status?.status) {
        switch (data?.status?.status) {
          case "monitor":
            this.accountState = "监控";
            console.log("jiankong");
            return "background-color: green";
          case "spider_ready":
            this.accountState = "未监控";
            return "background-color: gray";

          default:
            "background-color: gray";
            break;
        }
      } else {
        this.accountState = "未监控";
        return "background-color: gray";
      }
    },
    async moreFn(v) {
      this.initialData();

      this.$store.commit("deployment/twitter/setRow", v.row);
      this.$store.commit(
        "deployment/twitter/setUserId",
        v.columnValues.d.user_id
      );
      this.$store.commit("search/twLinFacSearch/setAddEsQueryConditions", {
        bool: {
          must: [
            {
              term: {
                type: "twitter",
              },
            },
            {
              term: {
                relation: "reply",
              },
            },
            {
              term: {
                user_id: v.columnValues.d.user_id,
              },
            },
          ],
        },
      });
      try {
        const newData = await this.$store.dispatch(
          "deployment/twitter/getTwitterDetail",
          v.row
        );
        this.loadTwitterListData();
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    // 加载详情里的推文列表数据
    async loadTwitterListData() {
      if (
        this.$store.state.search.twLinFacSearch.loading ||
        this.$store.state.search.twLinFacSearch.noMore
      )
        return;
      this.$store.commit("search/twLinFacSearch/setLoading", true);
      let obj = {};
      obj = {
        data_range_father_path: "/social_platform/twitter/timeline",
        data_range_index_prefix: "social_platform_timeline_prefix_twitter",
      };
      this.$store.commit("search/twLinFacSearch/setSearchType", obj);
      try {
        const newData = await this.$store.dispatch(
          "search/twLinFacSearch/getListTrue",
          obj
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.loading,
.no-more {
  margin-top: 20px;
  font-size: 24px;
  width: 100%;
  text-align: center;
  padding: 10px;
  color: #999;
}
.intellManage {
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
}
.remark {
  text-decoration: underline;
  color: #999;
  cursor: pointer;
  margin-left: 20px;
}

.active {
  font-size: 12px;
  padding: 5px 0;
  border: none;
  background-color: rgba(0, 0, 0, 0);
}

.remark:hover {
  color: red;
}

.remarkTitle {
  color: red;
  font-weight: bold;
  margin-right: 10px;
}

.remarkInput {
  width: 60%;
}
.box-row {
  cursor: pointer;
  img {
    display: block;
    width: 48px;
    height: 48px;
    border: 1px solid #e6a23c;
    border-radius: 50%;
  }
  display: flex;
}
.box-card {
  padding: 20px;
  height: 200px;
  width: 23.5%;
  margin-bottom: 20px;
  margin-right: 23px;
}
.account_bage {
  display: inline-block;
  margin: 0 5px 0 10px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: flicker 1500ms infinite;
}

@keyframes flicker {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  to {
    opacity: 1;
  }
}
</style>
