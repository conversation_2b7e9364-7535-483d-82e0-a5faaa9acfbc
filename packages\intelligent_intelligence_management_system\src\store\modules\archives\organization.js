// 数据构造函数
function buildData(data) {
  console.log("buildData", data);
  // 根据setPersonList函数功能对传进来的数据进行构造，并返回一个对象
  const newItem = { ...data };
  // 标准字段
  const standardFields = [
    "type","avatar", "name", "remark", "sex", "age", "phoneNumbers", "identity", "email", "dateBirth",
    "relation", "twitterIds", "facebookIds", "linkedInIds", "telegramIds", "basic", "socialize", "media", "customFields",
  ];
  
  // 组装media对象
  const buildMedia = (p) => {
    const platforms = [
      { key: 'telegram', idKey: 'telegramIds' },
      { key: 'twitter', idKey: 'twitterIds' },
      { key: 'facebook', idKey: 'facebookIds' },
      { key: 'linkedin', idKey: 'linkedInIds' },
    ];
    const media = {};
    platforms.forEach(({ key, idKey }) => {
      const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
      media[key] = arr.map(id => ({
        idNum: id,
        name: id,
      }));
    });
    return media;
  };
  
  // 1. 处理params为对象的情况
  if (newItem._source && typeof newItem._source.params === 'object' && !Array.isArray(newItem._source.params)) {
    const p = newItem._source.params || {};
    // 组装basic对象
    const basicObj = {};
    standardFields.forEach(field => {
      if (field === 'basic') return;
      if (Array.isArray(p[field])) {
        basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field][0] || '';
      } else if (typeof p[field] === 'string' || typeof p[field] === 'number') {
        basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field];
      }
    });
    // 合并basic字段
    if (p.basic && typeof p.basic === 'object') {
      Object.assign(basicObj, p.basic);
    }
    // 组装customFields
    const customFields = {};
    Object.keys(p).forEach(key => {
      if (!standardFields.includes(key)) {
        customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
      }
    });
    // 处理关系数据
    let relationData = { person: [], organization: [] };
    if (p.relation && Array.isArray(p.relation)) {
      p.relation.forEach(relationStr => {
        try {
          const parsed = JSON.parse(relationStr);
          if (parsed.person && Array.isArray(parsed.person)) {
            relationData.person = [...relationData.person, ...parsed.person];
          }
          if (parsed.organization && Array.isArray(parsed.organization)) {
            relationData.organization = [...relationData.organization, ...parsed.organization];
          }
        } catch (e) {
          console.warn("解析关系数据失败:", relationStr, e);
        }
      });
    }
    
    // 组装params
    newItem._source.params = {
      ...p,
      basic: basicObj,
      customFields,
      media: buildMedia(p),
      relation: relationData
    };
  }
  // 2. 处理params为数组的情况（兼容旧数据）
  else if (newItem._source && Array.isArray(newItem._source.params)) {
    const paramsObj = {};
    newItem._source.params.forEach((param) => {
      paramsObj[param.k] = param.v;
    });
    const basicObj = {
      id: newItem._id,
      type: Array.isArray(paramsObj.type) ? paramsObj.type[0] : (paramsObj.type || 'key_person'),
      name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
      remark: Array.isArray(paramsObj.remark) ? paramsObj.remark[0] : (paramsObj.remark || ''),
      sex: Array.isArray(paramsObj.sex) ? paramsObj.sex[0] : (paramsObj.sex || ''),
      age: paramsObj.age ? parseInt(Array.isArray(paramsObj.age) ? paramsObj.age[0] : paramsObj.age) : 0,
      phone: Array.isArray(paramsObj.phoneNumbers) ? paramsObj.phoneNumbers[0] : (paramsObj.phoneNumbers || ''),
      identity: Array.isArray(paramsObj.identity) ? paramsObj.identity[0] : (paramsObj.identity || ''),
      email: Array.isArray(paramsObj.email) ? paramsObj.email[0] : (paramsObj.email || ''),
      avatar: Array.isArray(paramsObj.avatar) ? paramsObj.avatar[0] : (paramsObj.avatar || ''),
      dateBirth: Array.isArray(paramsObj.dateBirth) ? paramsObj.dateBirth[0] : (paramsObj.dateBirth || '')
    };
    // 组装customFields
    const customFields = {};
    Object.keys(paramsObj).forEach(key => {
      if (!standardFields.includes(key)) {
        customFields[key] = Array.isArray(paramsObj[key]) ? paramsObj[key][0] : paramsObj[key];
      }
    });
    // 处理关系数据（兼容旧数据格式）
    let relationData = { person: [], organization: [] };
    if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
      paramsObj.relation.forEach(relationStr => {
        try {
          const parsed = JSON.parse(relationStr);
          if (parsed.person && Array.isArray(parsed.person)) {
            relationData.person = [...relationData.person, ...parsed.person];
          }
          if (parsed.organization && Array.isArray(parsed.organization)) {
            relationData.organization = [...relationData.organization, ...parsed.organization];
          }
        } catch (e) {
          console.warn("解析关系数据失败:", relationStr, e);
        }
      });
    }
    
    newItem._source.params = {
      ...paramsObj,
      basic: basicObj,
      customFields,
      media: buildMedia(paramsObj),
      relation: relationData
    };
  }
  return newItem;
}

export default {
  namespaced: true,
  state: {
    from: 0,
    size: 12,
    total: 0,
    organiList: [], // 组织数据
    organiBar: [], // 组织功能项
    selectOrgani: null, // 选中的组织
    organizationDetailLoading: false, // 组织详情加载中
    telegram: [],
    twitter: [],
    facebook: [],
    linkedin: [],
    processedPersonData: null,
    processedOrganizationData: null,
    searchKeyword: ''
  },
  mutations: {
    // 组织详情刷新后数据重置
    resetOrganizationDetailData(state) {
      state.selectOrgani = null;
      state.organizationDetailLoading = false;
      state.processedPersonData = null;
      state.processedOrganizationData = null;
    },

    // 设置分页 (与person模块一致)
    setPage(state, data) {
      state.from = (data - 1) * state.size;
      state.organiList = [];
    },

    // 重置数据
    resetAllData(state) {
      state.from = 0;
      state.total = 0;
      state.searchKeyword = '';
      state.organiList = [];
    },
    
    // 重置部分数据 (与person模块一致)
    resetData(state) {
      state.from = 0;
      state.total = 0;
      state.organiList = [];
    },

    // 设置搜索关键词
    setSearchKeyword(state, data) {
      state.searchKeyword = data;
    },

    // 获取组织功能项
    sendOrganizationBar(state) {
      state.organiBar = [];
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { from: 0, size: 20 },
            control: {
              query_string: "",
              query_type: "username",
              condition: { query_mode: "match", time_range: "无", time_range_begin: 0, time_range_end: 0, collection_time_range: "无", collection_time_range_begin: 0, collection_time_range_end: 0 },
              add_es_query_conditions: { bool: { must: [] } },
            },
            msg: { data_range_index_name: "intelligen_intelligence_management_project_management" },
          },
        ],
        "organization/setOrganiBar"
      );
    },
    setOrganiBar(state, data) {
      console.log("data:",data);
      
      state.organiBar = [
        { key: "关系拓扑", value: "关系拓扑" },
        { key: "情报", value: "intelligence" },
        {key: "关系人", value:"relevantPerson"},
        {key: "关系组织", value:"relevantOriganization"}
      ]
      if (data?.hits?.hits?.length) {
        // 1. 过滤掉 value 为 "social_work_library" 的项
        const filtered = data.hits.hits.filter(item => {
          const content = item._source.project_content;
          // 兼容字符串和对象
          const value = typeof content === 'string' ? JSON.parse(content).value : content.value;
          return value !== "social_work_library";
        });

        // 2. 按 index 升序排序
        filtered.sort((a, b) => {
          const aContent = typeof a._source.project_content === 'string' ? JSON.parse(a._source.project_content) : a._source.project_content;
          const bContent = typeof b._source.project_content === 'string' ? JSON.parse(b._source.project_content) : b._source.project_content;
          return aContent.index - bContent.index;
        });

        // 3. push 到 personBar
        filtered.forEach(item => {
          const content = typeof item._source.project_content === 'string' ? JSON.parse(item._source.project_content) : item._source.project_content;
          state.organiBar.push({
            key: content.label,
            value: content.value,
          });
        });
      }
    },

    // 获取组织列表
    getOrgani(state) {
      let add_es_query_conditions = {
          bool: {
            must: [
              {
                term: {
                  "type": "key_organization",
                },
              },  
            ],
          },
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { 
              from: state.from, 
              size: state.size 
            },
            control: { 
              query_type: "public", 
              query_string: state.searchKeyword,
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: { data_range_index_name: "key_organization" },
          },
        ],
        "organization/setOrganiList"
      );
    },
    // 设置组织列表 (已按person模块重构)
    setOrganiList(state, data) {
      console.log("获取组织数据", data);
      if (data?.hits?.hits?.length) {
        state.organiList = data.hits.hits.map(item => {
          const newItem = { ...item };
          // 定义一个辅助函数来获取数组中的第一个元素或默认值
          const getFirstOrDefault = (arr, defaultVal = '') => {
            return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
          };
          if (newItem._source && newItem._source.params) {
            let paramsObj = {};
            newItem._source.params.forEach((param) => {
              if (param.v && param.v.length === 1 && param.v[0] === '[]') {
                paramsObj[param.k] = [];
              } else {
                paramsObj[param.k] = param.v;
              }
            });

            // 创建basic对象以兼容前端视图
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
              remark: getFirstOrDefault(paramsObj.remark),
              createTime: getFirstOrDefault(paramsObj.createTime),
              belong: getFirstOrDefault(paramsObj.belong),
              desi: getFirstOrDefault(paramsObj.desi),
              avatar: getFirstOrDefault(paramsObj.avatar),
            };
            
            newItem._source.params = { ...paramsObj, basic: basicObj };
          } else if (newItem._source && !Array.isArray(newItem._source.params)) {
            const p = newItem._source.params || {};
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
              remark: getFirstOrDefault(paramsObj.remark),
              createTime: getFirstOrDefault(paramsObj.createTime),
              belong: getFirstOrDefault(paramsObj.belong),
              desi: getFirstOrDefault(paramsObj.desi),
              avatar: getFirstOrDefault(paramsObj.avatar),
            };
            newItem._source.params = {
              ...p,
              basic: basicObj
            };
          }
          return newItem;
        });
        console.log("处理后的 organiList:", state.organiList);
      } else {
        state.organiList = [];
      }
      // 更新智能管理树
      window.main.$store.commit("intellManageTree/setIntellList", { data: state.organiList, index: 0 });
      state.total = data?.hits?.total?.value;
    },

    // 添加组织 (参考person模块重构)
    addOrgani(state, data) {
      console.log("添加组织", data);
      let params = [];
      let personRelation = [];
      let organizationRelation = [];
      if (data) {
        Object.entries(data).forEach(([key, value]) => {
          if (key === 'customFields') {
            if (typeof value === 'object' && value !== null) {
              Object.entries(value).forEach(([customKey, customValue]) => {
                params.push({
                  k: customKey,
                  v: [String(customValue)]
                });
              });
            }
          } else if (key === 'relation' && value.length > 0) {
            value.forEach(item => {
              if (item.intellValue[0] === '目标人' || item.intellValue[0] === '次要目标人') {
                personRelation.push(item.personData._id);
              }
              if (item.intellValue[0] === '目标组织') {
                organizationRelation.push(item.organizationData._id);
              }
            });
            params.push({
              k: 'relation',
              v: [JSON.stringify({ 'person': personRelation }), JSON.stringify({ 'organization': organizationRelation })]
            });
          }
          else {
            let formattedValue;
            if (Array.isArray(value)) {
              formattedValue = value.map(String);
            } 
            else {
              formattedValue = [String(value)];
            }
            params.push({
              k: key,
              v: formattedValue
            });
          }
        });
      }
      console.log("params", params);

      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "public",
              index: "key_organization",
              id: data.id,
            },
            msg: {
              type: "key_organization",
              name: [data.name],
              params: params,
            },
          },
        ],
        (res) => {
          console.log("添加组织", res);
          if (res?.status == "ok") {
            window.main.$message.success("添加组织成功,请刷新页面");
            state.relationFlag = true;
            window.main.$store.commit("organization/resetAllData");
            window.main.$store.commit("organization/getOrgani");
            if (data.basic.relation.length > 0) {
              window.main.$store.commit("organization/addOrganiRelationFn", data);
            }
          }
        }
      );
    },

    // 添加次要目标人 (参考person模块重构)
    addSecondaryPerson(state, data) {
      console.log("添加次要目标人", data);
      let params = [];
      let personRelation = [];
      let organizationRelation = [];
      if (data) {
        Object.entries(data).forEach(([key, value]) => {
          if (key === 'customFields') {
            if (typeof value === 'object' && value !== null) {
              Object.entries(value).forEach(([customKey, customValue]) => {
                params.push({
                  k: customKey,
                  v: [String(customValue)]
                });
              });
            }
          } else if (key === 'relation' && value.length > 0) {
            value.forEach(item => {
              if (item.intellValue[0] === '目标人' || item.intellValue[0] === '次要目标人') {
                personRelation.push(item.personData._id);
              }
              if (item.intellValue[0] === '目标组织') {
                organizationRelation.push(item.organizationData._id);
              }
            });
            params.push({
              k: 'relation',
              v: [JSON.stringify({ 'person': personRelation }), JSON.stringify({ 'organization': organizationRelation })]
            });
          }
          else {
            let formattedValue;
            if (Array.isArray(value)) {
              formattedValue = value.map(String);
            } 
            else {
              formattedValue = [String(value)];
            }
            params.push({
              k: key,
              v: formattedValue
            });
          }
        });
      }
      console.log("params", params);
      
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "public",
              index: "key_person",
              id: data.id,
            },
            msg: {
              type: data.type,
              name: [data.name],
              params: params,
            },
          },
        ],
        (res) => {
          console.log("添加次要目标人", res);
          if (res?.status == "ok") {
            window.main.$message.success("添加目标人成功，请刷新页面");
            state.relationFlag = true;
            if (data.basic.relation.length > 0) {
              window.main.$store.commit("organization/addSecondaryPersonRelationFn", data);
            }
          }
        }
      );

      // 遍历数组中的每个对象
      // data.basic.forEach((person, index) => {
      //   let params = [];
        
      //   // 处理每个对象的属性
      //   Object.entries(person).forEach(([key, value]) => {
      //     if (key === 'customFields') {
      //       if (typeof value === 'object' && value !== null) {
      //         Object.entries(value).forEach(([customKey, customValue]) => {
      //           params.push({
      //             k: customKey,
      //             v: [String(customValue)]
      //           });
      //         });
      //       }
      //     } 
      //     else if (key === 'id') {
      //       // 跳过id字段，因为会在后面单独添加
      //       return;
      //     }
      //     else {
      //       let formattedValue;
      //       if (Array.isArray(value)) {
      //         formattedValue = value.map(String);
      //       } 
      //       else {
      //         formattedValue = [String(value)];
      //       }
      //       params.push({
      //         k: key,
      //         v: formattedValue
      //       });
      //     }
      //   });
        
      //   // 添加对象的唯一ID
      //   params.push({ k: "id", v: [person.id] });
      //   console.log("params", params);
        
      //   // 发送API请求
      //   window.main.$main_socket.sendData(
      //     "Api.Search.SearchList.AddOne",
      //     [
      //       {
      //         head: {},
      //         control: {
      //           query_type: "public",
      //           index: "key_person",
      //           id: person.id,
      //         },
      //         msg: {
      //           type: "secondary_key_person",
      //           name: [person.name],
      //           params: params,
      //         },
      //       },
      //     ],
      //     (res) => {
      //       console.log(`添加次要目标人 ${person.name}`, res);
      //       if (res?.status == "ok") {
      //         window.main.$message.success(`添加次要目标人 ${person.name} 成功`);
      //         state.relationFlag = true;
      //         if (person.relation && person.relation.length > 0) {
      //           // 为关系处理创建临时数据结构
      //           const tempData = {
      //             basic: person,
      //             id: person.id
      //           };
      //           window.main.$store.commit("organization/addSecondaryPersonRelationFn", tempData);
      //         }
      //       } else {
      //         window.main.$message.error(`添加次要目标人 ${person.name} 失败`);
      //       }
      //     }
      //   );
      // });
    },

    // 封装函数 循环处理添加组织关系的数据
    addOrganiRelationFn(state, data) {
      data.basic.relation.forEach(item => {
        if (item.intellValue[0] === '目标人') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_organization',
            name: item.organizationRelation,
            targetID: item.personData._id, 
            relation: 'key_person', 
            desc: item.organizationRelation
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.personData._id, 
          //   table: 'key_person',
          //   name: item.reverseOrganizationRelation,
          //   person: item.personData,
          //   targetID: data.id,
          //   relation: 'key_organization', 
          //   desc: item.reverseOrganizationRelation
          // });
        } else if (item.intellValue[0] === '次要目标人') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_organization',
            name: item.organizationRelation,
            targetID: item.personData._id, 
            relation: 'key_person', 
            desc: item.organizationRelation
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.personData._id, 
          //   table: 'key_person',
          //   name: item.reverseOrganizationRelation,
          //   person: item.personData,
          //   targetID: data.id,
          //   relation: 'key_organization', 
          //   desc: item.reverseOrganizationRelation
          // });
        } else if (item.intellValue[0] === '目标组织') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_organization',
            name: item.organizationRelation,
            targetID: item.organizationData._id, 
            relation: 'key_organization', 
            desc: item.organizationRelation
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.organizationData._id, 
          //   table: 'key_organization',
          //   name: item.reverseOrganizationRelation,
          //   organization: item.organizationData,
          //   targetID: data.id,
          //   relation: 'key_organization',
          //   desc: item.reverseOrganizationRelation
          // });
        }
      });
    },

    // 封装函数 循环处理添加次要目标人关系的数据
    addSecondaryPersonRelationFn(state, data) {
      data.basic.relation.forEach(item => {
        if (item.intellValue[0] === '目标人') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.personRelation,
            targetID: item.personData._id, 
            relation: 'key_person',
            desc: item.personRelation, 
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.personData._id, 
          //   table: 'key_person',
          //   name: item.reversePersonRelation,
          //   person: item.personData,
          //   targetID: data.id,
          //   relation: 'key_person', 
          //   desc: item.reversePersonRelation
          // });
        } else if (item.intellValue[0] === '次要目标人') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.personRelation,
            targetID: item.personData._id, 
            relation: 'key_person', 
            desc: item.personRelation
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.personData._id, 
          //   table: 'key_person',
          //   name: item.reversePersonRelation,
          //   person: item.personData,
          //   targetID: data.id,
          //   relation: 'key_person', 
          //   desc: item.reversePersonRelation
          // });
        } else if (item.intellValue[0] === '目标组织') {
          window.main.$store.commit("organization/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.organizationRelation,
            targetID: item.organizationData._id, 
            relation: 'key_organization', 
            desc: item.organizationRelation
          });
          // window.main.$store.commit("organization/addReverseRelation", {
          //   id: item.organizationData._id, 
          //   table: 'key_organization',
          //   name: item.reverseOrganizationRelation,
          //   organization: item.organizationData,
          //   targetID: data.id,
          //   relation: 'key_person',
          //   desc: item.reverseOrganizationRelation
          // });
        }
      });
    },

    // 正向关系
    addForwardRelation(state, data) {
      console.log("添加正向关系", data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {},
            msg: {
              type: 'public',
              table: data.table,
              prefix: data.id,
              data: {
                relation: data.relation,
                id: data.targetID,
                desc: data.desc
              }
            },
          },
        ],
        (res) => {
          console.log("添加正向关系1", res);
        }
      );
      // window.main.$main_socket.sendData(
      //   "Api.Search.SearchPrefixTable.AddData",
      //   [
      //     {
      //       head: {},
      //       msg: {
      //         type: 'public',
      //         table: data.table,
      //         relation: data.id + ';' + data.relation,
      //         prefix: data.targetID,
      //         data: {
      //           data: {
      //             relation: data.desc,
      //             id: data.targetID,
      //           }
      //         }
      //       }
      //     }
      //   ],
      //   (res) => {
      //     console.log("添加正向关系2", res);
      //   }
      // );
    },

    // 反向关系
    addReverseRelation(state, data) {
      console.log("添加反向关系", data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {},
            msg: {
              type: 'public',
              table: data.table,
              prefix: data.id,
              data: {
                relation: {
                  [data.relation]: { name: data.name}
                }
              }
            }
          }
        ],
        (res) => {
          console.log("添加反向关系1", res);
        }
      );
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {},
            msg: {
              type: 'public',
              table: data.table,
              relation: data.id + ';' + data.relation,
              prefix: data.targetID,
              data: {
                data: {
                  relation: data.desc,
                  id: data.targetID,
                }
              }
            }
          }
        ],
        (res) => {
          console.log("添加反向关系2", res);
        } 
      );
    },

    // 编辑组织（参考 person.js 的 editPerson 实现方式）
    editOrganization(state, data) {
      // 1. 取出 params
      const params = data._source.params;
      // 2. 组装 params 数组
      const paramsArr = [];
      // 3. 为basic中的每个字段创建单独的params项
      if (params.basic) {
        Object.keys(params.basic).forEach(key => {
          if (key !== 'relation' && key !== 'other' && key !== 'customFields') {
            paramsArr.push({ k: key, v: params.basic[key] });
          }
        });
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "public",
              index: "key_organization",
              id: data._id,
            },
            msg: {
              type: "public",
              params: paramsArr,
            },
          },
        ],
        "organization/editCallBack"
      );
    },
    editCallBack(state, data) {
      if (data.status === "ok") {
        window.main.$message.success("修改成功");
      }
    },

    // 删除组织
    deleteOri(state, data) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Del",
        [
          {
            head: {},
            msg: { query_type: "public", index: "key_organization", id: data },
          },
        ],
        "organization/delCall"
      );
    },
    delCall(state, data) {
      console.log("删除回调", data);
      if (data?.status == "ok") {
        window.main.$message.success("删除成功,即将关闭窗口,请刷新页面");
        setTimeout(() => {
          window.close();
        }, 1200);
      }
    },

    // 搜索组织 (已按person模块重构)
    searchOri(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: { size: 200 },
            control: { 
              query_type: "public", 
              query_string: v,
            },
            msg: { data_range_index_name: "key_organization" },
          },
        ],
        "organization/setOrganiList" // 复用setOrganiList作为回调
      );
    },

    // 选择的组织信息
    selectOrgani(state, data) {
      console.log("选择的组织信息", data);
      // 兼容新数据格式
      let p = data._source.params;
      const standardFields = [
        "avatar", "name", "remark", "createTime", "belong", "desi", "relation", "twitterIds", 
        "facebookIds", "linkedInIds", "telegramIds", "basic", "socialize", "media", "customFields"
      ];
      // 组装basic对象
      const basicObj = {};
      const socialMediaFields = ['twitterIds', 'facebookIds', 'linkedInIds', 'telegramIds'];
      standardFields.forEach(field => {
        if (field === 'basic') return;
        if (socialMediaFields.includes(field)) {
          // 保证为数组格式
          if (Array.isArray(p[field])) {
            basicObj[field] = p[field];
          } else if (typeof p[field] !== 'undefined') {
            basicObj[field] = [p[field]];
          } else {
            basicObj[field] = [];
          }
        } else if (Array.isArray(p[field])) {
          basicObj[field] = p[field][0] || '';
        } else if (typeof p[field] === 'string' || typeof p[field] === 'number') {
          basicObj[field] = p[field];
        }
      });
      if (p.basic && typeof p.basic === 'object') {
        Object.assign(basicObj, p.basic);
      }
      // 组装customFields
      const customFields = {};
      Object.keys(p).forEach(key => {
        if (!standardFields.includes(key)) {
          customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
        }
      });
      // 组装media对象
      const buildMedia = (p) => {
        const platforms = [
          { key: 'telegram', idKey: 'telegramIds' },
          { key: 'twitter', idKey: 'twitterIds' },
          { key: 'facebook', idKey: 'facebookIds' },
          { key: 'linkedin', idKey: 'linkedInIds' },
        ];
        const media = {};
        platforms.forEach(({ key, idKey }) => {
          const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
          media[key] = arr.map(id => ({
            idNum: id,
            name: id,
          }));
        });
        return media;
      };
      data._source.params = {
        ...p,
        basic: basicObj,
        customFields,
        media: buildMedia(p)
      };
      state.selectOrgani = data;
      // 遍历社交媒体数据，提取 name
      const socialPlatforms = ["telegram", "twitter", "facebook", "linkedin"];
      socialPlatforms.forEach((platform) => {
        state[platform] = [];
        if (
          data._source.params.media &&
          data._source.params.media[platform] &&
          Array.isArray(data._source.params.media[platform])
        ) {
          data._source.params.media[platform].forEach((item) => {
            if (item.name) {
              state[platform].push(item.name);
            }
          });
        }
      });
      window.main.$store.commit("organization/getRelation", data._id);
    },

    // 获取组织关系数据
    getRelation(state, id) {
      state.organizationDetailLoading = true;
      // 查询组织与目标人关系
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_organization",
              type: "public",
              relation: id + ';key_person',
            },
          },
        ],
        (res) => {
          console.log("获取组织-人员关系数据", res);
          res.forEach(item => {
            window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [
                {
                  head: {
                    from: 0,
                    size: 200,
                  },
                  control: {
                    query_type: "public",
                    query_string: "",
                    add_es_query_conditions: {
                      bool: {
                        must: [
                          {
                            term: {
                              "_id": item.columnValues.d.id,
                            },
                          },
                        ],
                      },
                    },
                  },
                  msg: {
                    data_range_index_name: "key_person",
                  },
                }
              ],
              (data) => {
                console.log("获取组织-人员详细数据", data);
                if (data.hits.hits.length > 0) {
                  window.main.$store.commit("organization/buildPersonRelation", data.hits.hits);
                }
              }
            );
          })
        }
      );
      // 查询组织与组织关系
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_organization",
              type: "public",
              relation: id + ';key_organization',
            },
          },
        ],
        (res) => {
          console.log("获取组织-组织关系数据", res);
          res.forEach(item => {
            window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [
                {
                  head: {
                    from: 0,
                    size: 200,
                  },
                  control: {
                    query_type: "public",
                    query_string: "",
                    add_es_query_conditions: {
                      bool: {
                        must: [
                          {
                            term: {
                              "_id": item.columnValues.d.id,
                            },
                          },
                        ],
                      },
                    },
                  },
                  msg: {
                    data_range_index_name: "key_organization",
                  },
                }
              ],
              (data) => {
                console.log("获取组织-组织详细数据", data);
                if (data.hits.hits.length > 0) {
                  window.main.$store.commit("organization/buildOrganizationRelation", data.hits.hits);
                }
              }
            )
          })
        }
      );
      setTimeout(() => {
        state.organizationDetailLoading = false;
      }, 3000);
    },

    // 构造组织-人员关系
    buildPersonRelation(state, data) {
      if (!state.selectOrgani._source.params.socialize) {
        state.selectOrgani._source.params.socialize = {};
      }
      if (!state.selectOrgani._source.params.socialize.relation) {
        state.selectOrgani._source.params.socialize.relation = [];
      }
      data.forEach(item => {
        const processedData = buildData(item);
        console.log("processedData11111", processedData);
        if (processedData) {
          const relationData = {
            relationName: "人员关系",
            relationInfo: {
              name: processedData._source.params.basic.name,
              remark: processedData._source.params.basic.remark,
              sex: processedData._source.params.basic.sex,
              age: processedData._source.params.basic.age,
              dateBirth: processedData._source.params.basic.dateBirth,
              identity: processedData._source.params.basic.identity,
              phone: processedData._source.params.basic.phone,
            }
          };
          state.selectOrgani._source.params.socialize.relation.push(relationData);
        }
      });
      localStorage.setItem('intelligence_organi', JSON.stringify(state.selectOrgani));
    },

    // 构造组织-组织关系
    buildOrganizationRelation(state, data) {
      if (!state.selectOrgani._source.params.socialize) {
        state.selectOrgani._source.params.socialize = {};
      }
      if (!state.selectOrgani._source.params.socialize.relation) {
        state.selectOrgani._source.params.socialize.relation = [];
      }
      // 不清空，叠加到人员关系后面
      data.forEach(item => {
        const processedData = buildData(item);
        console.log("processedData22222", processedData);
        if (processedData) {
        const relationData = {
          relationName: "组织关系",
          relationInfo: {
              name: processedData._source.params.basic.name,
              remark: processedData._source.params.basic.remark,
              createTime: processedData._source.params.createTime[0],
              belong: processedData._source.params.belong[0],
              desi: processedData._source.params.desi[0],
              avatar: processedData._source.params.avatar[0],
          }
        };
          state.selectOrgani._source.params.socialize.relation.push(relationData);
        }
      });
      localStorage.setItem('intelligence_organi', JSON.stringify(state.selectOrgani));
    },

  },
  actions: {},
};
