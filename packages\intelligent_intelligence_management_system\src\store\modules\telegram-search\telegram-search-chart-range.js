export default {
  namespaced: true,
  state: {
    pauseFlag: false,
    chartOn: false,
    tmpDataList: {},
  },
  mutations: {
    initData(state, v) {
      state.tmpDataList = Object.assign({}, state.tmpDataList, {
        [v]: {
          from: 0,
          size: 20,
          dataRangeGetter: {},
          dataRangeDetail: {},
          searchedTableTotal: 0,
          searchedDataTotal: 0,
          searchedDataTotalSize: 0,
          dataRangeQueryIndexMaxList: [],
          dataRangeQueryIndexMaxCount: 0,
        },
      });
    },
    setChartOn(state, v) {
      state.chartOn = v;
    },
    setDataRangeGetter(state, data) {
      let dataRangeList = data.dataRangeList;
      function* getter(dataRangeList) {
        if (dataRangeList == null || dataRangeList.length == 0) {
          return;
        }
        for (let index = 0; index < dataRangeList.length; index++) {
          let dataRangeOne = dataRangeList[index];
          let from = 0;
          while (true) {
            state.tmpDataList[data.name].dataRangeDetail.data_range_name =
              dataRangeOne.data_range_name;
            if (!dataRangeOne.data_range_type) {
              state.tmpDataList[data.name].searchedTableTotal += 1;
              state.tmpDataList[data.name].searchedDataTotal += Number(
                dataRangeOne.total
              );
              state.tmpDataList[data.name].searchedDataTotalSize += Number(
                dataRangeOne.total_size
              );
              state.tmpDataList[data.name].dataRangeQueryIndexMaxList.push(
                dataRangeOne.data_range_index_name
              );
              state.tmpDataList[data.name].dataRangeQueryIndexMaxCount +=
                Number(dataRangeOne.total_size);
              if (
                state.tmpDataList[data.name].dataRangeQueryIndexMaxList.length <
                  state.tmpDataList[data.name].size &&
                state.tmpDataList[data.name].dataRangeQueryIndexMaxCount <
                  50 * 1024 * 1024 * 1024
              ) {
                if (index >= dataRangeList.length - 1) {
                  let tmpDataRangeDetail = {};
                  tmpDataRangeDetail.data_range_index_name =
                    state.tmpDataList[
                      data.name
                    ].dataRangeQueryIndexMaxList.join(",");
                  window.main.$store.commit("telegramSearch/telegramSearchChartData/setDataListGetter", {
                    data: tmpDataRangeDetail,
                    name: data.name,
                  });
                  yield { res: dataRangeOne };
                  state.dataRangeQueryIndexMaxList = [];
                  state.dataRangeQueryIndexMaxCount = 0;
                  break;
                } else {
                  break;
                }
              } else {
                let tmpDataRangeDetail = {};
                tmpDataRangeDetail.data_range_index_name =
                  state.tmpDataList[data.name].dataRangeQueryIndexMaxList.join(
                    ","
                  );
                window.main.$store.commit("telegramSearch/telegramSearchChartData/setDataListGetter", {
                  data: tmpDataRangeDetail,
                  name: data.name,
                });
                yield { res: dataRangeOne };
                state.tmpDataList[data.name].dataRangeQueryIndexMaxList = [];
                state.tmpDataList[data.name].dataRangeQueryIndexMaxCount = 0;
                break;
              }
            }
            let dataRangeDetail;
            if (dataRangeOne.hasOwnProperty("add_es_query_conditions_data")) {
              window.main.$store.commit("telegramSearch/telegramSearchChartData/setAddEsQueryConditions", {
                bool: {
                  must: [
                    {
                      [dataRangeOne.add_es_query_conditions_data.mode]: {
                        [dataRangeOne.add_es_query_conditions_data.field]:
                          dataRangeOne.add_es_query_conditions_data.key,
                      },
                    },
                  ],
                },
              });
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                  add_es_query_conditions: {
                    bool: {
                      must: [
                        {
                          [dataRangeOne.add_es_query_conditions_data.mode]: {
                            [dataRangeOne.add_es_query_conditions_data.field]:
                              dataRangeOne.add_es_query_conditions_data.key,
                          },
                        },
                      ],
                    },
                  },
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
            } else if (dataRangeOne.hasOwnProperty("article_count")) {
              window.main.$store.commit("telegramSearch/telegramSearchChartData/setAddEsQueryConditions", {
                bool: {
                  must: [
                    {
                      term: {
                        type: "twitter",
                      },
                    },
                    {
                      term: {
                        user_id: data.dataRangeList[0].article_count.user_id,
                      },
                    },
                  ],
                  must_not: [
                    {
                      term: {
                        relation: "likes",
                      },
                    },
                  ],
                },
              });
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  order: "desc",
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                  add_es_query_conditions: {
                    bool: {
                      must: [
                        {
                          term: {
                            type: "twitter",
                          },
                        },
                        {
                          term: {
                            user_id: data.user_id,
                          },
                        },
                      ],
                      must_not: [
                        {
                          term: {
                            relation: "likes",
                          },
                        },
                      ],
                    },
                  },
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
            } else if (dataRangeOne.hasOwnProperty("likes_count")) {
              window.main.$store.commit("telegramSearch/telegramSearchChartData/setAddEsQueryConditions", {
                bool: {
                  must: [
                    {
                      term: {
                        type: "twitter",
                      },
                    },
                    {
                      term: {
                        user_id: data.dataRangeList[0].likes_count.user_id,
                      },
                    },
                    {
                      term: {
                        relation: "likes",
                      },
                    },
                  ],
                },
              });
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  order: "desc",
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                  add_es_query_conditions: {
                    bool: {
                      must: [
                        {
                          term: {
                            type: "twitter",
                          },
                        },
                        {
                          term: {
                            user_id: data.dataRangeList[0].likes_count.user_id,
                          },
                        },
                        {
                          term: {
                            relation: "likes",
                          },
                        },
                      ],
                    },
                  },
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
            } else if (dataRangeOne.hasOwnProperty("reply_count")) {
              window.main.$store.commit("telegramSearch/telegramSearchChartData/setAddEsQueryConditions", {
                bool: {
                  must: [
                    {
                      term: {
                        type: "twitter",
                      },
                    },
                    {
                      term: {
                        content_article_father_id:
                          data.dataRangeList[0].reply_count.content_article_id,
                      },
                    },
                    {
                      term: {
                        relation: "reply",
                      },
                    },
                  ],
                },
              });
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  order: "desc",
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                  add_es_query_conditions: {
                    bool: {
                      must: [
                        {
                          term: {
                            type: "twitter",
                          },
                        },
                        {
                          term: {
                            content_article_father_id:
                              data.dataRangeList[0].reply_count
                                .content_article_id,
                          },
                        },
                        {
                          term: {
                            relation: "reply",
                          },
                        },
                      ],
                    },
                  },
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
            } else if (dataRangeOne.hasOwnProperty("moreData")) {
              window.main.$store.commit("telegramSearch/telegramSearchChartData/setAddEsQueryConditions", {
                bool: {
                  must: [
                    {
                      term: {
                        type: window.main.$store.state.telegramSearch.telegramSearchDataDetail
                          .tmpDataDetail.d._source.type,
                      },
                    },
                    {
                      term: {
                        group_id:
                          window.main.$store.state.telegramSearch.telegramSearchDataDetail
                            .tmpDataDetail.d._source.group_id,
                      },
                    },
                  ],
                },
              });
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                  add_es_query_conditions: {
                    bool: {
                      must: [
                        {
                          term: {
                            type: window.main.$store.state.telegramSearch.telegramSearchDataDetail
                              .tmpDataDetail.d._source.type,
                          },
                        },
                        {
                          term: {
                            group_name:
                              window.main.$store.state.telegramSearch.telegramSearchDataDetail
                                .tmpDataDetail.d._source.group_name,
                          },
                        },
                      ],
                    },
                  },
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
              if (data.name === "groupContentData") {
                dataRangeDetail.control.order = "desc";
              }
            } else {
              dataRangeDetail = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: from,
                  size: state.tmpDataList[data.name].size,
                },
                control: {
                  query_type:
                    window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[data.name]
                      .use_public,
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: dataRangeOne.data_range_index_prefix,
                },
              };
            }
            state.tmpDataList[
              data.name
            ].dataRangeDetail.data_range_father_path =
              dataRangeOne.data_range_path;
            window.main.$store.commit("telegramSearch/telegramSearchChartData/clearLoadingBun");
            window.main.$store.commit("telegramSearch/telegramSearchChartData/setLoadingLayer");

            let nextValue = yield window.main.$main_socket.sendData(
              "Api.Search.DataRange.ListTrue",
              [dataRangeDetail],
              `telegramSearch/telegramSearchChartData/set${data.name}DataRangeTree`
            );
            if (nextValue == "nextDataRangePage") {
              from = from + state.tmpDataList[data.name].size;
              continue;
            }

            let tmpRes = yield* getter(nextValue);
            if (
              nextValue == null ||
              nextValue.length < state.tmpDataList[data.name].size
            ) {
              from = 0;
              break;
            } else {
              from = from + state.tmpDataList[data.name].size;
            }
          }
        }
        return;
      }

      state.tmpDataList[data.name].dataRangeGetter = getter(dataRangeList);
      let nextRes =
        state.tmpDataList[data.name].dataRangeGetter.next(dataRangeList);
      if (nextRes.value === undefined && nextRes.done) {
        window.main.$store.commit("telegramSearch/telegramSearchChartData/clearLoadingLayer");
      }
    },
    detailLockfn(state, v) {
      state.detailLock = v;
    },
    clearDataRangeTree(state, name) {
      if (state.tmpDataList[name]) {
        state.tmpDataList[name].searchedTableTotal = 0;
        state.tmpDataList[name].searchedDataTotal = 0;
        state.tmpDataList[name].searchedDataTotalSize = 0;
        state.tmpDataList[name].from = 0;
        state.tmpDataList[name].dataRangeGetter = {};
      }
    },

    async setcollision_key_count_chartDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.collision_key_count_chart.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.collision_key_count_chart.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setcollision_key_score_chartDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.collision_key_score_chart.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.collision_key_score_chart.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setsearch_task_count_chartDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.search_task_count_chart.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.search_task_count_chart.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setsearch_task_score_chartDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.search_task_score_chart.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.search_task_score_chart.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setarticle_countDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.article_count.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.article_count.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setlikes_countDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.likes_count.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.likes_count.dataRangeGetter.next(
            dataRangeList
          );
      }

      return;
    },
    async setreply_countDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.reply_count.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.reply_count.dataRangeGetter.next(
            dataRangeList
          );
      }

      return;
    },
    async setgroupContentDataDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupContentData.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupContentData.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setgroupDataDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupData.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupData.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
    async setgroupMemberDataDataRangeTree(state, dataRangeList) {
      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupMemberData.dataRangeGetter.next(
            []
          );
      } else {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList.groupMemberData.dataRangeGetter.next(
            dataRangeList
          );
      }
      return;
    },
  },
};
