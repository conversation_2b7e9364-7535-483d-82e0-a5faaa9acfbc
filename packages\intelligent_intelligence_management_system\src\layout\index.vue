<template>
  <div class="app-wrapper">
    <header
      class="main__header"
      style="display: flex; align-items: center; height: 5vh; padding: 0 2%"
    >
      <div
        class="login_logo"
        style="
          display: flex;
          align-items: center;
          position: absolute;
          z-index: 1000;
          font-size: 22px;
        "
      >
        <img
          src="../assets/images/AI_logo.png"
          style="
            width: 35px;
            height: 35px;
            margin-right: 10px;
            border-radius: 50%;
          "
        />
        智能情报管理系统
      </div>
      <div class="header-button-box">
        <div class="option-container">
          <div class="header-container">
            <div class="user-container" id="userContainer">
              <div class="user-arrow">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="项目管理"
                  placement="bottom"
                >
                  <i
                    class="el-icon-menu"
                    style="font-size: 20px; margin-right: 10px; color: #000"
                    @click="handleProjectLink"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="消息系统"
                  placement="bottom"
                >
                  <i
                    class="el-icon-message-solid"
                    style="font-size: 20px; margin-right: 10px; color: #000"
                    @click="handleMessageLink"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="邮件系统"
                  placement="bottom"
                >
                  <i
                    class="el-icon-s-promotion"
                    style="font-size: 20px; color: #000; margin-right: 10px"
                    @click="handleMailLink"
                  ></i>
                </el-tooltip>
                <el-dropdown
                  trigger="click"
                  @command="handleClickUserOption"
                  handleClickUserOption
                >
                  <span class="user-solid"
                    ><i
                      class="el-icon-user-solid"
                      style="font-size: 20px; color: #000"
                    ></i>
                    <span style="color: #aaa">
                      {{ userInfo.username }}
                    </span></span
                  >
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="index"
                      v-for="(item, index) in userOptions"
                      :key="index"
                      >{{ item.name }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
          <!-- <div class="option-list-box">
            <div :class="showUserList ? 'option-list' : 'option-list-hide'">
              <div class="option-item-container linear-trans" v-for="(item, index) in userOptions" :key="index"
                @click="handleClickUserOption(index)">
                <div class="top-line" v-if="item.showTopLine"></div>
                <div class="option-icon"><i :class="item.icon" /></div>
                <div class="option-name">{{ item.name }}</div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </header>
    <section class="main__body" style="display: flex">
      <section class="nav__bar__container" id="navBar">
        <div
          v-for="(navItem, index) in navConfig"
          :key="index"
          class="linear-trans-fast"
          :class="
            'nav__btn__box' + (navItem.isSelect === true ? ' btn__select' : '')
          "
        >
          <div
            class="btn__side__bar bar__top linear-trans-fast"
            :class="navItem.isSelect === true ? 'bar__select' : ''"
          ></div>
          <div
            class="btn__label linear-trans-fast"
            :class="navItem.isSelect === true ? 'label__select' : ''"
          >
            <el-dropdown
              v-if="navItem.children"
              @command="dropClickNavBtn"
              trigger="click"
            >
              <span
                class="el-dropdown-link"
                style="
                  color: #000;
                  font-size: 1.8vh;
                  display: flex;
                  align-items: center;
                "
                >{{ navItem.label
                }}<i
                  class="el-icon-arrow-down"
                  style="font-size: 1.5vh; margin-left: 2px; color: #909699"
                ></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(childrenItem, childrenIndex) in navItem.children"
                  :key="childrenIndex"
                  :command="childrenItem.route"
                  >{{ childrenItem.label }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
            <div v-else @click="handleClickNavBtn(index)">
              {{ navItem.label }}
            </div>
          </div>
        </div>
      </section>
      <div class="main-container">
        <app-main />
      </div>
    </section>
    <message_system
      ref="messageRef"
      :mainSocket="this.$main_socket"
      :messageSocket="this.$emlmsgs_socket"
      v-if="messageDialogVisible"
      :getContacts="true"
    />
    <el-dialog
      title="项目管理"
      :visible.sync="projectDialogVisible"
      width="80%"
    >
      <projectManageVue :projectList="projectList"></projectManageVue>
    </el-dialog>
    <el-dialog title="用户信息" :visible.sync="personDialogVisible" width="30%">
      <div class="dialog-body-box">
        <div class="info-box" v-for="(item, index) in propConfig" :key="index">
          <div class="prop-name">{{ item.cnName }}</div>
          <div
            class="prop-value"
            :title="propValue(item.propName)"
            style="max-width: 200px; overflow: hidden; text-overflow: ellipsis"
          >
            {{ propValue(item.propName) }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import projectManageVue from "@/components/projectManage.vue";
import Vue from "vue";
import { AppMain } from "./components";
// import { Sidebar, AppMain } from "./components";
import { mapState, mapMutations } from "vuex";

export default {
  name: "Layout",
  data() {
    return {
      projectList: [],
      propConfig: [
        {
          propName: "username",
          cnName: "用户名",
        },
        {
          propName: "user_id",
          cnName: "用户ID",
        },
        {
          propName: "nickname",
          cnName: "昵称",
        },
        {
          propName: "authority",
          cnName: "用户权限",
        },
        {
          propName: "website",
          cnName: "网站",
        },
        {
          propName: "email",
          cnName: "邮箱",
        },
        {
          propName: "create_time",
          cnName: "创建时间",
        },
      ],
      personDialogVisible: false,
      messageDialogVisible: false,

      routeParam: {
        newRoute: null,
        preRoute: null,
        needUpdate: false,
      },
      navConfig: [
        {
          label: "Ai搜索",
          route: "aiSeach",
          isSelect: true,
        },
        {
          label: "目标管理",
          isSelect: false,
          children: [
            {
              label: "目标人",
              route: "personArchives",
              isSelect: true,
            },
            {
              label: "目标组织",
              route: "origaniArchives",
              isSelect: true,
            },
            {
              label: "相关情报",
              route: "intellManage",
              isSelect: true,
            },
          ],
        },
        {
          label: "数据搜索",
          route: "search",
          isSelect: true,
        },

        {
          label: "数据收藏",
          route: "collect",
          isSelect: false,
        },

        {
          label: "AI任务",
          route: "taskQueue",
          isSelect: true,
          /* children: [
            {
              label: "计划任务",
              route: "AIplanTask",
              isSelect: true,
            },
            {
              label: "解析模板",
              route: "analyze",
              isSelect: true,
            },
            {
              label: "任务队列",
              route: "taskQueue",
              isSelect: true,
            },
          ], */
        },
        {
          label: "布控展示",
          isSelect: false,
          children: [
            {
              label: "舆情网站",
              route: "opinionWeb",
              isSelect: true,
            },
            {
              label: "Twitter账号",
              route: "TwitterAccount",
              isSelect: true,
            },
            {
              label: "Facebook账号",
              route: "FacebookAccount",
              isSelect: true,
            },
            {
              label: "Linkedin账号",
              route: "LinkedinAccount",
              isSelect: true,
            },
            {
              label: "Telegram账号",
              route: "TelegramAccount",
              isSelect: true,
            },
          ],
        },
        {
          label: "预警管理",
          route: "warning",
          isSelect: false,
        },
        {
          label: "数据导入",
          route: "dataImport",
          isSelect: false,
        },
      ],
      showUserList: false,
      userName: "无名氏",
      userOptions: [
        {
          name: "用户信息",
          icon: "iconfont  user",
          showTopLine: false,
        },
        {
          name: "切换用户",
          icon: "iconfont  tuichudenglu",
          showTopLine: true,
        },
      ],
    };
  },
  watch: {
    "$route.path": {
      handler(newVal, oldVal) {
        this.routeParam.newRoute = newVal;
        this.routeParam.preRoute = oldVal;
        this.routeParam.needUpdate =
          this.routeParam.newRoute !== this.routeParam.preRoute;
        this.checkCrtRoute();
      },

      immediate: true,
    },
  },
  mounted() {
    this.msgInterval = setInterval(() => {
      if (this.$emlmsgs_socket) {
        window.main.$pki_socket.sendData(
          "Api.User.Info",
          [{}],
          "userInfo/setUserInfo"
        );
        this.messageDialogVisible = true;
        clearInterval(this.msgInterval);
        this.$nextTick(() => {
          // window.main.$emlmsgs_socket.registerMethod(
          //   "Api.Msg.Broadcast.Authority",
          //   this.$refs.messageRef.sendMessageCallBack
          // );
          // window.main.$emlmsgs_socket.registerMethod(
          //   "Api.Msg.Broadcast.Public",
          //   this.$refs.messageRef.sendMessageCallBack
          // );
          // window.main.$emlmsgs_socket.registerMethod(
          //   "Api.Msg.Broadcast.System",
          //   this.$refs.messageRef.sendMessageCallBack
          // );
          // window.main.$emlmsgs_socket.registerMethod(
          //   "Api.Msg.Broadcast.Username",
          //   this.$refs.messageRef.sendMessageCallBack
          // );
        });
      }
    }, 1000);
    // window.addEventListener("mouseover", (e) => {
    //   this.handleMouseOverUserArea(e);
    // });
  },
  created() {
    this.importData();
    this.getProjectTemplate();
    this.$store.dispatch("chat/getToolsList");
    this.$store.dispatch("chat/getCallMcpTool");
  },
  beforeDestroy() {
    // window.removeEventListener("mouseover", (e) => {
    //   this.handleMouseOverUserArea(e);
    // });
    Vue.prototype.$bus = this;
  },
  components: {
    AppMain,
    projectManageVue,
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo.userinfo,
    }),
    projectDialogVisible: {
      get: (val) => {
        return window.main.$store.state.userInfo.projectDialogVisible;
      },
      set: (nval) => {
        window.main.$store.commit("userInfo/setprojectDialogVisible", nval);
      },
    },
  },
  methods: {
    ...mapMutations("projectManage", ["getProjectTemplate"]),
    importData() {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs":
                "",
            },
          },
        ],
        (res) => {
          let arr = [];
          let data =
            res[
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
            ].tabs;
          for (let i = 0; i < data.length; i++) {
            switch (data[i].value) {
              case "public_opinion":
                arr.push({
                  src: require("@/assets/images/dataImport/yuqing.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              case "telegram":
                arr.push({
                  src: require("@/assets/images/dataImport/telegram.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              case "twitter":
                arr.push({
                  src: require("@/assets/images/dataImport/twitter.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              case "facebook":
                arr.push({
                  src: require("@/assets/images/dataImport/facebook.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              case "linkedin":
                arr.push({
                  src: require("@/assets/images/dataImport/Linkedin.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              case "social_work_library":
                arr.push({
                  src: require("@/assets/images/dataImport/社工库.png"),
                  label: data[i].label,
                  value: data[i].value,
                  index: data[i].index,
                });
                break;
              default:
                console.log("子系统", res);
            }
          }
          this.projectList = arr;
        }
      );
    },
    //项目管理
    handleProjectLink() {
      this.$store.commit("userInfo/setprojectDialogVisible", true);
    },
    //过滤用户信息展示
    propValue(propName) {
      if (
        window.main.$store.state.userInfo.userinfo[propName] &&
        window.main.$store.state.userInfo.userinfo[propName] !== ""
      ) {
        if (propName == "create_time") {
          return window.main.$tools.timestampToTime(
            window.main.$store.state.userInfo.userinfo[propName]
          );
        } else {
          return window.main.$store.state.userInfo.userinfo[propName];
        }
      }
      return "--";
    },
    getCase() {
      if (window.main.$constant_socket) {
      } else {
        setTimeout(() => {
          this.getCase();
        }, 1000);
      }
    },
    //跳转舆情配置界面
    createRoot(item) {
      let host = window.location.host.split(":");
      return `https://${host[0]}:8880?#/CrawlerManagement`;
    },
    //跳转邮件系统
    handleMailLink() {
      this.$router.push({ name: "mailbox" });
    },
    //点击用户头像列表
    handleClickUserOption(tIndex) {
      this.showUserList = false;
      switch (tIndex) {
        case 0:
          this.personDialogVisible = true;
          break;
        case 1:
          this.logout();
          break;
      }
    },
    //点击头部导航列表
    handleClickNavBtn(t_index) {
      this.$router.push({ name: this.navConfig[t_index].route });
    },
    //点击导航下拉框
    dropClickNavBtn(name) {
      this.$router.push({ name });
    },
    //判断导航选中index
    checkCrtRoute() {
      if (this.routeParam.needUpdate === false) return;
      if (!this.navConfig) return;

      let t_route = this.routeParam.newRoute.split("/")[1];

      let t_nav_index = 0;
      let navHas = false;

      this.navConfig.forEach((item, index) => {
        if (item.children) {
          const childrenIndex = item.children.findIndex(
            (child) => child.route === t_route
          );
          if (childrenIndex !== -1) {
            t_nav_index = index;
            navHas = true;
          }
        } else {
          if (item.route === t_route) {
            t_nav_index = index;
            navHas = true;
          }
        }
      });

      // if (t_route !== "index") {
      //   var t_index = this.navConfig.findIndex((item) => {
      //     return item.route.indexOf(t_route) >= 0;
      //   });
      //   t_nav_index = t_index >= 0 ? t_index : 9;
      // }
      // if (t_route == "report") {
      //   t_nav_index = 3;
      // }
      // if (t_route == "searchList") {
      //   t_nav_index = 2;
      // }
      this.navConfig.forEach((item) => {
        if (item.isSelect) {
          item.isSelect = false;
        }
      });
      console.log("t_nav_index", t_nav_index);
      if (t_nav_index != "9" && navHas) {
        this.navConfig[t_nav_index].isSelect = true;
      }
    },
    //hover展示用户信息列表
    handleMouseOverUserArea(e) {
      let obj_1 = document.getElementsByClassName("user-solid")[0];

      let obj_2 = document.getElementsByClassName("option-list-box")[0];
      this.showUserList =
        (obj_1 && obj_1.contains(e.target)) ||
        (obj_2 && obj_2.contains(e.target));
    },
    //打开消息系统
    handleMessageLink() {
      this.$refs.messageRef.showMessageSystem();
    },
    //切换用户
    logout() {
      this.$confirm(
        "切换用户将重新安装证书, 页面也会重新加载, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          if ("serviceWorker" in navigator) {
            window.main.$store.state.userInfo.userinfo = {};
            window.main.$router.push("/");

            navigator.serviceWorker.getRegistrations().then((registrations) => {
              registrations.forEach((sw) => sw.unregister());
            });

            setTimeout(() => {
              window.postMessage(
                {
                  type: "SNTL_FROM_NEW_PAGE",
                  host: window.location.href,
                  text: {
                    url: window.location.href,
                    method: "ChangeUser",
                  },
                },
                "*"
              );
            }, 1000);
          } else {
            alert("切换用户失败!");
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.nav__bar__container {
  z-index: 105;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-around;
}

.nav__bar__container > :last-child {
  margin-right: 250px;
}

$color-primary-1: rgba(15, 253, 253, 1);
$color-primary-2: rgba(0, 54, 54, 1);
$color-font-1: rgba(250, 250, 250, 1);

.nav__btn__box {
  width: 15vh;
  height: 2.3vh;
  clip-path: polygon(0% 0%, 100% 0%, 100% 75%, 90% 100%, 0% 100%);
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  cursor: pointer;
  margin-left: 10px;
}

.btn__side__bar {
  width: 5.5%;
  background-color: rgba($color-primary-1, 0.15);
}

.btn__label {
  color: #000;
  width: 100%;
  line-height: 2.5vh;
  text-align: center;
  font-family: "黑体";
  font-size: 1.8vh;
  font-style: italic;
  // text-shadow: 1px 1px 1px rgb($color-primary-2, 0.5), 0 0 5px rgba($color-primary-1, 0.5);
  letter-spacing: 2px;
  display: flex;
  justify-content: center;
}

.btn__select {
  background-color: rgba($color-primary-1, 0.4);
}

.bar__top {
  background-color: rgba($color-primary-1, 0.4);
  height: 0;
  position: absolute;
  left: 0;
}

.bar__select {
  height: 100%;
}

.nav__btn__box:hover {
  background-color: rgba($color-primary-1, 0.4);

  .btn__side__bar {
    background-color: rgba($color-primary-1, 0.4);
    height: 100%;
  }
}

.linear-trans-fast {
  transition: all 0.15s linear;
}

.dialog-body-box {
  position: relative;
  width: 98.5%;
  margin: 10px auto;
}

.info-box {
  color: #000;
  margin-top: 12px;
  padding-left: 10px;
  padding-right: 10px;
  display: flex;
  flex-wrap: nowrap;
  text-align: left;
  font-size: 13px;
}

.prop-value {
  white-space: nowrap;
  line-height: 30px;
  margin-left: 10px;
  font-size: 14px;
}

.prop-name {
  width: 80px;
  white-space: nowrap;
  line-height: 30px;
  font-size: 15px;
}

.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }

  //background: #f1f4f9;
}
</style>
<style lang="scss" scoped>
.main-container {
  width: 100%;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
</style>
<style src="../assets/scss/index.scss" lang="scss"></style>
<style>
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: rgba(15, 253, 253, 0.4) !important;
  color: #000 !important;
}
</style>
