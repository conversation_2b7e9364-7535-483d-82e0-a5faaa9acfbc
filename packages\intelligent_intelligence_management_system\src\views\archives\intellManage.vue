<template>
  <div class="collect" style="display: flex; height: 100%">
    <div class="caseDirLay">
      <div class="intel_title">
        <span>情报列表</span>
      </div>
      <tree-box
        :key="componentKey"
        :data-node="rootNode"
        sub-component="mail-manage-tree"
      ></tree-box>
      <p class="bottom_tip">没有数据了，已经到底了!</p>
    </div>
    <div class="intellRignt">
      <div class="case_dir_header">
        <div class="case_dir_btn" v-if="nowChooseCase === 'dir'">
          <el-button size="mini" type="primary" @click="showSharedDirectory"
            >分享目录</el-button
          >
          <el-button size="mini" type="danger" @click="deleteDirectory"
            >删除目录</el-button
          >
          <el-button size="mini" type="success" @click="showAddDirectory"
            >添加目录</el-button
          >
          <el-button size="mini" type="success" @click="showAddCase"
            >添加节点</el-button
          >
        </div>
        <div class="case_dir_btn" v-else-if="nowChooseCase === 'case'">
          <el-button size="mini" type="success" @click="createMD"
            >创建</el-button
          >
          <el-button size="mini" type="primary" @click="importBulletin()"
            >导入</el-button
          >
          <el-button size="mini" type="danger" @click="deleteNode()"
            >删除节点</el-button
          >
        </div>
      </div>
      <div v-if="nowChooseCase === 'dir'">
        <div
          class="case_dir_body"
          v-if="
            caseDirDetail &&
            caseDirDetail.columnValues &&
            caseDirDetail.columnValues.i
          "
        >
          <el-descriptions
            :column="2"
            border
            :labelStyle="labelCs"
            :contentStyle="contenCs"
          >
            <el-descriptions-item label="父节点">{{
              caseDirDetail.columnValues.i.case_dir_father_path
            }}</el-descriptions-item>
            <el-descriptions-item label="创建用户">{{
              caseDirDetail.columnValues.i.create_username
            }}</el-descriptions-item>
            <el-descriptions-item label="创建权限">{{
              caseDirDetail.columnValues.i.create_authority
            }}</el-descriptions-item>
            <el-descriptions-item label="创建时间"
              ><span v-if="caseDirDetail.columnValues.i.create_timestamp">{{
                $tools.timestampToTime(
                  caseDirDetail.columnValues.i.create_timestamp
                )
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="情报目录名">{{
              caseDirDetail.columnValues.i.case_dir_name
            }}</el-descriptions-item>
            <el-descriptions-item label="情报目录描述">
              <template v-if="!showEditInput">
                {{
                  caseDirDetail.columnValues.p
                    ? caseDirDetail.columnValues.p.summary
                    : ""
                }}
              </template>
              <el-input
                v-else
                v-model="case_dir_summary"
                size="mini"
              ></el-input>
            </el-descriptions-item>
            <!-- <el-descriptions-item label="翻译系统显示" v-if="caseDirDetail.columnValues.p">
                        <el-switch v-if="caseDirDetail.columnValues.p.hasOwnProperty('showTranslate')"
                            @change="changeShowTranslate" v-model="caseDirDetail.columnValues.p.showTranslate"
                            active-color="#13ce66" inactive-color="#ff4949" active-value='true' inactive-value='false'>
                        </el-switch>
                        <el-switch v-else @change="changeShowTranslate" v-model="showTranslates" active-color="#13ce66"
                            inactive-color="#ff4949">
                        </el-switch></el-descriptions-item> -->
            <el-descriptions-item
              label="修改情报目录"
              v-if="caseDirDetail.columnValues.i.case_dir_father_path"
            >
              <el-button
                v-if="!showEditInput"
                size="mini"
                type="primary"
                @click="editDirectory"
                >修改目录</el-button
              >
              <div v-else>
                <el-button size="mini" type="success" @click="sendEditDirectory"
                  >确认修改</el-button
                >
                <el-button
                  size="mini"
                  type="warning"
                  @click="showEditInput = !showEditInput"
                  >取消修改</el-button
                >
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div class="markDownBox" v-else-if="nowChooseCase === 'case'">
        <div class="session_box">
          <div
            class="session_list"
            ref="scrollContainer"
            v-show="bulletinList.length"
          >
            <el-card
              shadow="hover"
              class="session_card"
              v-for="item in bulletinList"
              :key="item._id"
              @click.native="handleSelectMDcontent(item)"
            >
              <div class="item_header">
                <p class="item_title">{{ item._source.title }}</p>
                <div class="item_btn_list">
                  <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-edit"
                    circle
                    @click.stop="redactBulletin(item)"
                  ></el-button>
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-download"
                    circle
                    @click.stop="downloadBulletin(item)"
                  ></el-button>
                  <el-button
                    type="info"
                    size="mini"
                    icon="el-icon-paperclip"
                    circle
                    @click.stop="shareBulletin(item)"
                  ></el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    circle
                    @click.stop="deleteBulletin(item)"
                  ></el-button>
                </div>
              </div>
              <div class="item_content">
                <span v-html="item._source.content_article"></span>
              </div>
              <div class="item_time">
                <span>{{
                  $tools.timestampToTime(item._source["@timestamp"])
                }}</span>
              </div>
            </el-card>
          </div>
          <div style="width: 100%; height: 100%" v-show="!bulletinList.length">
            <el-empty :image-size="300" description="暂无情报"></el-empty>
          </div>
          <div class="session_page_box">
            <el-pagination
              layout="prev, pager, next"
              :current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="session_preview">
          <div class="preview_box" v-if="MDcontent">
            <mavon-editor
              v-model="MDcontent"
              :externalLink="externalLink"
              style="height: 100%"
              :editable="false"
              :subfield="false"
              :toolbarsFlag="false"
              :defaultOpen="'preview'"
            />
          </div>
          <div class="preview_no_data" v-if="!MDcontent">
            <span>暂无内容</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 分享情报 -->
    <el-dialog
      title="分享情报"
      :visible.sync="showShareDialog"
      :close-on-click-modal="false"
      width="60%"
      top="20px"
      append-to-body
    >
      <div class="authority_tree">
        <el-tree
          :key="treeKey"
          :data="treeData"
          show-checkbox
          node-key="authority_id"
          ref="tree"
          highlight-current
        >
          <span class="custom-tree-node" slot-scope="{ data }">
            <span>{{
              data.authority_detail + "(" + data.authority + ")"
            }}</span>
          </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showShareDialog = false">取 消</el-button>
        <el-button type="primary" @click="sharedDirectory">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加情报目录 -->
    <el-dialog
      title="添加情报目录"
      :visible.sync="dialogAddCaseDir"
      :close-on-click-modal="false"
      width="30%"
      append-to-body
    >
      <el-form
        :model="addCaseDirForm"
        @submit.native.prevent
        :rules="addCaseDirFormRules"
        ref="addCaseDirForm"
      >
        <el-form-item label="目录名称" label-width="100px" prop="case_dir_name">
          <el-input
            v-model="addCaseDirForm.case_dir_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="目录描述"
          label-width="100px"
          prop="case_dir_summary"
        >
          <el-input
            v-model="addCaseDirForm.case_dir_summary"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="resetAddDirectoryForm('addCaseDirForm')"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="addDirectory('addCaseDirForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 添加情报 -->
    <el-dialog
      title="添加情报"
      :visible.sync="dialogAddCase"
      :close-on-click-modal="false"
      width="30%"
      append-to-body
    >
      <el-form
        :model="addCaseForm"
        @submit.native.prevent
        :rules="addCaseFormRulesaaa"
        ref="addCaseForm"
      >
        <el-form-item label="情报名称" label-width="100px" prop="case_name">
          <el-input
            v-model="addCaseForm.case_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="情报描述" label-width="100px" prop="summary">
          <el-input v-model="addCaseForm.summary" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAddCaseForm('addCaseForm')">取 消</el-button>
        <el-button
          type="primary"
          @click="addCase('addCaseForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- markdown编辑 -->
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="buildBri"
      append-to-body
      width="80%"
      top="20px"
    >
      <div
        v-loading="savingIntelligence"
        element-loading-text="正在保存情报..."
        element-loading-spinner="el-icon-loading"
      >
        <div class="MDbutton">
          <div class="file">
            <span>上传文件</span>
            <input type="file" accept=".md" @change="jaince($event)" />
          </div>
          <div style="margin-right: 15px">
            <el-button type="primary" @click="MDout()">导出情报</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveDataVisible = true"
              >保存情报</el-button
            >
          </div>
        </div>
        <div class="MDinput">
          <mavon-editor
            ref="md"
            v-model="MDcontent"
            :externalLink="externalLink"
            style="height: 100%"
          />
        </div>
      </div>
    </el-dialog>
    <!-- 保存情报 -->
    <el-dialog
      title="保存情报"
      :visible.sync="saveDataVisible"
      @close="saveDataVisible = false"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form>
        <el-form-item label="情报标题：">
          <el-input
            style="width: 80%"
            placeholder="请输入情报标题"
            v-model="bulletinTitle"
          ></el-input>
        </el-form-item>
        <el-form-item label="案件名：">
          <p class="fileName">
            {{
              caseDetail.columnValues ? caseDetail.columnValues.i.case_name : ""
            }}
          </p>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="saveDataVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveMD(false)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 导入情报 -->
    <el-dialog
      title="导入情报"
      :visible.sync="importBulletinVisible"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelImportSession()"
      width="40%"
    >
      <el-form>
        <el-form-item label="情报标题：">
          <el-input
            style="width: 80%"
            placeholder="请输入情报标题"
            v-model="bulletinTitle"
          ></el-input>
        </el-form-item>
        <el-form-item label="情报文件：">
          <input type="file" accept=".md" @change="jaince($event)" />
        </el-form-item>
        <el-form-item label="案件名：">
          <p class="fileName">
            {{
              caseDetail.columnValues ? caseDetail.columnValues.i.case_name : ""
            }}
          </p>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="cancelImportSession()">取 消</el-button>
        <el-button type="primary" @click="saveMD(true)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 分享情报 -->
    <el-dialog
      title="分享情报"
      :visible.sync="favoriteDataVisible"
      :close-on-click-modal="false"
      @close="clearexfavoriteData"
      width="40%"
      append-to-body
    >
      <el-form>
        <el-form-item label="关联档案：">
          <el-radio-group v-model="databaseRadio">
            <el-radio
              v-for="item in databaseList"
              :label="item.name"
              :key="item.id"
              border
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="档案列表：">
          <div v-show="databaseRadio == 'username'">
            <el-select
              v-model="peopleValue"
              filterable=""
              placeholder="请选择"
              popper-class="long-option-popper"
            >
              <el-option
                v-for="(item, index) in organiList"
                :key="index"
                :value="item._source.params.basic.name"
                @click.native="handleChangeIntell(item)"
              >
                <span
                  class="option-label"
                  :title="item._source.params.basic.name"
                >
                  {{ item._source.params.basic.name }}
                </span>
              </el-option>
            </el-select>
          </div>
          <div v-show="databaseRadio == 'public'">
            <el-select
              v-model="peopleValue"
              filterable=""
              placeholder="请选择"
              popper-class="long-option-popper"
            >
              <el-option
                v-for="(item, index) in personList"
                :key="index"
                :value="item._source.params.basic.name"
                @click.native="handleChangeIntell(item)"
              >
                <span
                  class="option-label"
                  :title="item._source.params.basic.name"
                >
                  {{ item._source.params.basic.name }}
                </span>
              </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="clearexfavoriteData">取 消</el-button>
        <el-button type="primary" @click="sendShareIntelligence"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { Document, Packer, Paragraph, TextRun } from "docx";
import { saveAs } from "file-saver";
export default {
  data() {
    return {
      shareIntel: null, // 临时存储要分享的情报
      selectedItem: "", //保存情报选择关联的重点人或者重点组织
      peopleValue: "",
      databaseRadio: "public",
      databaseList: [
        { label: "个人档案", name: "public", id: 0 },
        { label: "组织档案", name: "username", id: 1 },
      ],
      favoriteDataVisible: false, //保存情报弹窗
      buildBri: false, //编辑markdown
      importBulletinVisible: false, //导入
      bulletinTitle: "",
      nowAddFileName: "",
      externalLink: {
        markdown_css: function () {
          return "/mavon-editor/markdown/github-markdown.min.css";
        },
        hljs_js: function () {
          return "/mavon-editor/highlightjs/highlight.min.js";
        },
        hljs_css: function (css) {
          return "/mavon-editor/highlightjs/styles/" + css + ".min.css";
        },
        hljs_lang: function (lang) {
          return "/mavon-editor/highlightjs/languages/" + lang + ".min.js";
        },
        katex_css: function () {
          return "/mavon-editor/katex/katex.min.css";
        },
        katex_js: function () {
          return "/mavon-editor/katex/katex.min.js";
        },
      },

      labelCs: {
        width: "240px",
        height: "55px",
      },
      contenCs: {
        width: "425px",
      },
      showEditInput: false,

      showShareDialog: false, // 分享情报
      treeLoading: true,
      treeKey: 0,
      // 添加情报目录
      dialogAddCaseDir: false,
      addCaseDirForm: {
        case_dir_name: "",
        case_dir_summary: "",
        case_dir_father_path: "",
      },
      addCaseDirFormRules: {
        case_dir_summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_dir_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      // 添加情报
      dialogAddCase: false,
      addCaseForm: {
        case_name: "",
        summary: "",
        case_dir_father_path: "",
      },
      addCaseFormRulesaaa: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      showEditInput: false,
      case_dir_summary: "",
      MDcontent: "",
      saveDataVisible: false,
      bulletinID: "", //编辑状态下，临时存储当前情报id
      savingIntelligence: false, // 保存情报的loading状态
      saveTimeout: null, // 保存超时计时器
    };
  },
  mounted() {
    this.getCase();
    this.$store.commit("intellManageTree/nowChooseCase", "dir");
    this.$store.commit("person/getPerson");
    this.$store.commit("organization/getOrgani");

    // 监听保存成功事件
    this.$root.$on(
      "saveIntelligenceSuccess",
      this.handleSaveIntelligenceSuccess
    );
  },

  beforeDestroy() {
    // 清理超时计时器
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    // 移除事件监听
    this.$root.$off(
      "saveIntelligenceSuccess",
      this.handleSaveIntelligenceSuccess
    );
  },
  components: {
    "tree-box": () => import("@/components/caseTree/tree_box.vue"),
  },
  watch: {
    treeDataChange: {
      handler(newVal, oldVal) {
        console.log("treeDataChange", newVal);

        if (this.treeDataChange) {
          this.treeKey++;
          setTimeout(() => {
            this.treeLoading = !this.treeDataChange;
          }, 500);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    ...mapState({
      currentPage: (state) => state.intellManageTree.page,
      pageSize: (state) => state.intellManageTree.size,
      total: (state) => state.intellManageTree.total,
      componentKey: (state) => state.intellManageTree.componentKey,
      rootNode: (state) => state.intellManageTree.rootNode,
      caseDirDetail: (state) => state.intellManageTree.caseDirDetail,
      caseDetail: (state) => state.intellManageTree.caseDetail,
      treeDataChange: (state) => state.intellManageTree.treeDataChange,
      nowChooseCase: (state) => state.intellManageTree.nowChooseCase,
      bulletinList: (state) => state.intellManageTree.bulletinList,
      case_name: (state) => state.intellManageTree.case_name,
      personList: (state) => state.person.personList,
      organiList: (state) => state.organization.organiList,
    }),
    treeData: {
      get() {
        return this.$store.state.intellManageTree.treeData;
      },
      set(newVal) {
        this.$store.commit("intellManageTree/setTreeData", newVal);
      },
    },
  },
  methods: {
    // 向后端发送分享情报请求
    sendShareIntelligence() {
      this.$store.commit("intellManageTree/shareIntelligence", {
        po: this.selectedItem,
        intel: this.shareIntel,
      });
      this.favoriteDataVisible = false;
    },

    // 保存临时分享的情报，展示分享对话框
    shareBulletin(item) {
      this.shareIntel = item;
      this.favoriteDataVisible = true;
    },

    // 选择情报，进行展示
    handleSelectMDcontent(item) {
      this.MDcontent = item._source.content_article;
    },

    // 翻页函数
    handleCurrentChange(page) {
      this.$store.commit("intellManageTree/clearBulletinAndSetFrom", page);
      this.$store.commit("intellManageTree/sendCaseSessionList", null);
      this.$refs.scrollContainer.scrollTop = 0;
    },

    // 导入--dialog
    importBulletin() {
      this.importBulletinVisible = true;
      this.bulletinTitle = "";
    },

    // 删除节点
    deleteNode() {
      this.$confirm("此操作将删除该节点，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("intellManageTree/sendDelCase", this.caseDetail);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    handleChangeIntell(item) {
      this.selectedItem = item;
    },
    async imgAdd(pos, file) {
      let base64 = await this.getBase64(file);
      console.log("imgAdd", pos, base64);
      this.$refs.md.$img2Url(pos, base64);
    },
    getBase64(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.readAsDataURL(blob);

        reader.onload = () => resolve(reader.result);

        reader.onerror = (error) => reject(error);
      });
    },
    // 切换文件获取简报
    handleClick(tab) {},
    // 取消收藏操作
    clearexfavoriteData() {
      this.favoriteDataVisible = false;
    },
    deleteBulletin(detail) {
      console.log(detail);

      this.$confirm("是否删除情报?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("intellManageTree/sendDelFileData", {
            row: detail._id,
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 取消导入简报
    cancelImportSession() {
      this.importBulletinVisible = false;
      (this.bulletinTitle = ""), (this.MDcontent = "");
    },

    async downloadBulletin(detail) {
      try {
        // 创建 Word 文档
        const doc = new Document({
          sections: [
            {
              properties: {},
              children: [
                // 标题
                new Paragraph({
                  children: [
                    new TextRun({
                      text: detail._source.title,
                      bold: true,
                      size: 32, // 16pt
                    }),
                  ],
                  spacing: {
                    after: 400, // 段后间距
                  },
                }),
                // 内容
                new Paragraph({
                  children: [
                    new TextRun({
                      text: detail._source.content,
                      size: 24, // 12pt
                    }),
                  ],
                  spacing: {
                    line: 360, // 行间距
                  },
                }),
              ],
            },
          ],
        });

        // 生成并下载 Word 文档
        const buffer = await Packer.toBuffer(doc);
        const blob = new Blob([buffer], {
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        });

        saveAs(blob, `${detail._source.title}.docx`);
      } catch (error) {
        console.error("生成文档失败:", error);
        this.$message.error("文档生成失败，请重试");
      }
    },
    //编辑markd情报
    redactBulletin(detail) {
      this.buildBri = true;
      this.MDcontent = detail._source.content;
      this.bulletinTitle = detail._source.title;
      this.bulletinID = detail._id;
    },
    // 保存情报
    saveMD(param) {
      if (this.bulletinTitle == "") {
        window.main.$message.warning("请填写标题!");
        return;
      }

      // 开始保存，显示遮罩层
      this.savingIntelligence = true;

      // 设置5秒超时
      this.saveTimeout = setTimeout(() => {
        this.savingIntelligence = false;
        this.$message.error("保存超时，请重试");
      }, 5000);

      this.$store.commit("intellManageTree/sendSaveIntelligence", {
        content: this.MDcontent,
        title: this.bulletinTitle,
        id: this.bulletinID,
      });
    },

    // 保存情报成功回调
    handleSaveIntelligenceSuccess() {
      // 清除超时计时器
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
        this.saveTimeout = null;
      }

      // 取消遮罩层
      this.savingIntelligence = false;

      // 显示成功消息
      this.$message.success("情报保存成功");

      // 关闭弹窗并重置数据
      this.buildBri = false;
      this.saveDataVisible = false;
      this.importBulletinVisible = false;
      this.bulletinID = "";
      this.bulletinTitle = "";
      this.MDcontent = "";
    },

    createMD() {
      this.buildBri = true;
      this.MDcontent = "";
      this.bulletinTitle = "";
    },
    jaince(e) {
      console.log(e);
      const file = e.target.files[0];

      // 检查文件是否存在
      if (!file) {
        return;
      }

      // 检查文件扩展名是否为 .md
      const fileName = file.name.toLowerCase();
      const fileExtension = fileName.substring(fileName.lastIndexOf("."));

      if (fileExtension !== ".md") {
        this.$message.error(
          "只支持导入 Markdown 格式文件（.md），请选择正确的文件格式"
        );
        // 清空文件输入框
        e.target.value = "";
        return;
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        this.MDcontent = e.target.result;
      };

      reader.onerror = () => {
        this.$message.error("文件读取失败，请重试");
      };

      reader.readAsText(file);
    },
    MDout() {
      console.log("MDcontent", this.MDcontent);
      const blob = new Blob([this.MDcontent], { type: "text/markdown" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `${this.bulletinTitle}.md`;
      link.click();
      URL.revokeObjectURL(link.href);
    },
    // 收藏数据
    collectedData(val) {
      // this.favoriteDataVisible = true;
      this.$store.commit("briefing/setFavoriteDataVisible", true);
      this.nowClickItem = val;
    },
    // 显示添加目录
    showAddDirectory() {
      this.dialogAddCaseDir = true;
      console.log("this.caseDirDetail添加目录", this.caseDirDetail);
      if (this.caseDirDetail.hasOwnProperty("sub_authority")) {
        this.addCaseDirForm.case_dir_father_path =
          this.caseDirDetail.sub_authority;
      } else {
        this.addCaseDirForm.case_dir_father_path =
          this.caseDirDetail.columnValues.i.case_dir_father_path +
          "/" +
          this.caseDirDetail.columnValues.i.case_dir_name;
      }
    },
    // 重置添加目录
    resetAddDirectoryForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogAddCaseDir = false;
    },
    // 添加目录
    addDirectory(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "intellManageTree/setCaseDirFather",
            this.addCaseDirForm.case_dir_father_path
          );
          this.$store.commit(
            "intellManageTree/sendAddCaseDir",
            this.addCaseDirForm
          );
          this.resetAddDirectoryForm(formName);
        }
      });
    },
    // 删除目录
    deleteDirectory() {
      this.$confirm("此操作将删除该目录，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit(
            "intellManageTree/setCaseDirFather",
            this.caseDirDetail.columnValues.i.case_dir_father_path
          );
          this.$store.commit(
            "intellManageTree/sendDelCaseDir",
            this.caseDirDetail
          );
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 分享目录
    sharedDirectory() {
      let tmpNode = this.$refs.tree.getCheckedNodes();
      if (tmpNode.length == 0) {
        this.$message.warning("请至少选择一个部门！");
        return;
      }
      let tmpPath;
      if (!this.caseDirDetail.hasOwnProperty("columnValues")) {
        tmpPath = "/";
      } else {
        if (this.caseDirDetail.columnValues.i.case_dir_father_path == "/") {
          tmpPath =
            this.caseDirDetail.columnValues.i.case_dir_father_path +
            this.caseDirDetail.columnValues.i.case_dir_name;
        } else {
          tmpPath =
            this.caseDirDetail.columnValues.i.case_dir_father_path +
            "/" +
            this.caseDirDetail.columnValues.i.case_dir_name;
        }
      }
      this.$confirm(
        "将为所选部门分享[ " + tmpPath + " ]情报目录权限，是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$store.commit("intellManageTree/sendSharedDirectory", tmpNode);
          this.showShareDialog = false;
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消分享目录",
          });
        });
    },
    // 选择分享权限
    showSharedDirectory() {
      this.treeData = [];
      this.$store.commit("intellManageTree/getDepartmentList");
      this.treeLoading = true;
      this.showShareDialog = true;
    },
    // 删除情报
    deleteCase() {
      this.$confirm("此操作将永久删除该情报, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit(
            "intellManageTree/setCaseDirFather",
            this.caseDetail.columnValues.i.case_dir
          );
          this.$store.commit(
            "intellManageTree/sendDelCase",
            this.caseDetail.row
          );
        })
        .catch((err) => {
          console.log(err);

          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    //获取情报树
    getCase() {
      if (window.main.$pki_socket) {
        this.$store.commit("intellManageTree/resetCaseDirTree");
        this.$store.commit("intellManageTree/setCaseDirFatherListObj", []);
        this.$store.commit("intellManageTree/setCaseDirFatherList", []);
        this.$store.commit("intellManageTree/setCaseDirFather", "");
        this.$store.commit("intellManageTree/sendCaseDirFatherArr");
      }
    },
    // 显示添加情报
    showAddCase() {
      this.dialogAddCase = true;
      if (this.caseDirDetail.hasOwnProperty("sub_authority")) {
        this.addCaseForm.case_dir_father_path =
          this.caseDirDetail.sub_authority;
      } else {
        if (this.caseDirDetail.columnValues.i.case_dir_father_path != "/") {
          this.addCaseForm.case_dir_father_path =
            this.caseDirDetail.columnValues.i.case_dir_father_path +
            "/" +
            this.caseDirDetail.columnValues.i.case_dir_name;
        } else {
          this.addCaseForm.case_dir_father_path =
            this.caseDirDetail.columnValues.i.case_dir_father_path +
            this.caseDirDetail.columnValues.i.case_dir_name;
        }
      }
    },
    // 重置添加情报
    resetAddCaseForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogAddCase = false;
    },
    // 添加情报
    addCase(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "intellManageTree/setCaseDirFather",
            this.addCaseForm.case_dir_father_path
          );
          this.$store.commit("intellManageTree/sendAddCase", this.addCaseForm);
          this.resetAddCaseForm(formName);
        }
      });
    },
    // 修改情报目录
    editDirectory() {
      this.case_dir_summary = this.caseDirDetail.columnValues?.p?.summary;
      this.showEditInput = true;
    },
    // 确认修改情报目录
    sendEditDirectory() {
      this.$store.commit("intellManageTree/sendEditDirectory", {
        summary: this.case_dir_summary,
        caseDirDetail: this.caseDirDetail,
      });
      this.showEditInput = false;
    },
  },
};
</script>
<style>
.long-option-popper {
  max-width: 400px;
}
.long-option-popper .option-label {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
<style scoped lang="scss">
.caseDirLay {
  width: 15%;
  height: 100%;
  border: 1px solid #ebeef5;
  overflow: auto;
  padding: 0 0 0 10px;
  margin-left: 5px;
  .intel_title {
    height: 5%;
    display: flex;
    align-items: center;
    text-align: left;
    background-color: #f5f7fa;
  }
}

.case_dir_header {
  height: 5%;
  background-color: #f5f7fa;
  padding: 0 15px;
  display: flex;
  justify-content: right;
  align-items: center;
  .case_dir_btn {
    display: flex;
    align-items: center;
    justify-content: right;
  }
}

.authority_tree {
  height: 700px;
  overflow: auto;
}

.intellRignt {
  width: 85%;
  height: 100%;

  .markDownBox {
    border: 1px solid #eee;
    height: 94%;
    width: 100%;
    display: flex;

    .session_box {
      width: 50%;
      height: 100%;

      .session_list {
        width: 100%;
        height: 90%;
        overflow-y: auto;

        .session_card {
          height: auto;
          min-height: 6.25rem;
          width: 100%;
          display: flex;
          margin-bottom: 0.625rem;
          border: none;
          padding: 0.1875rem;
          &:hover {
            cursor: pointer;
            background-color: #40a0ff4d;
          }

          :deep .el-card__body {
            width: 100%;
            padding: 5px;
          }

          .item_header {
            height: 2.5rem;
            width: 100%;
            display: flex;
            align-content: center;
            justify-content: space-between;
            align-items: center;

            .item_title {
              font-size: 16px;
              font-weight: bold;
              width: 60%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .item_btn_list {
              display: flex;
              gap: 5px;
            }
          }

          .item_content {
            width: 100%;
            max-height: 6.25rem;
            min-height: 3.125rem;
            padding: 3px;
            font-size: 0.875rem;
            span {
              gap: 0.0625rem;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 4;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .item_time {
            height: 2.5rem;
            padding: 0.3125rem;
            text-align: left;
            align-content: center;
            font-size: 0.875rem;
            color: #777777;
          }
        }
      }

      .session_page_box {
        width: 100%;
        height: 10%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .session_preview {
      height: 100%;
      width: 50%;

      .preview_box {
        height: 100%;
        width: 100%;

        ::v-deep .v-note-show,
        .single-show {
          overflow: hidden;
        }
      }

      .preview_no_data {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.MDbutton {
  display: flex;
  height: 6vh;
  justify-content: end;
  align-items: center;
  padding-right: 20px;
}

.MDinput {
  border: 1px solid #ccc;
  height: 77vh;
  display: flex;
  flex-direction: column;
}

// .session_list::after {
//     content: "";
//     flex: 1;
//     /* 或者flex: 1 */
// }

// ::v-deep .content-input-wrapper {
//     height: 100%;
// }
</style>
