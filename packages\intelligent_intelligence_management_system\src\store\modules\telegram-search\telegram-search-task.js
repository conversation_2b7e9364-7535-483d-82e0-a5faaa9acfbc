export default {
  namespaced: true,
  state: {
    analysisTaskList: [],
    nowActiveName: "",
    lastTaskRow: "",
    loglist: [],
    nowCheckLogRow: "",
    lastLogRow: "",
  },
  mutations: {
    clearAnalysisTaskList(state) {
      state.analysisTaskList = [];
      state.lastTaskRow = "";
    },
    getAnalysisTask(state, v) {
      if (v) {
        state.nowActiveName = v;
      }
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.List",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              from: 0,
              size: 20,
              row_key: state.lastTaskRow ? [state.lastTaskRow] : [],
            },
            msg: {
              task_authority: "username",
              tag: ["apocalypse_search"],
              task_type: "search_task",
              status: state.nowActiveName,
              familys: ["info"],
            },
          },
        ],
        "telegramSearch/telegramSearchTask/setAnalysisTask"
      );
    },
    setAnalysisTask(state, res) {
      if (res.length >= 20) {
        state.lastTaskRow = res[res.length - 1].row;
        window.main.$store.commit("telegramSearch/telegramSearchTask/getAnalysisTask");
        state.analysisTaskList.push(...res);
      } else {
        state.analysisTaskList.push(...res);
        // state.analysisTaskList.map((element) => {
        //   if (element.columnValues.info.method) {
        //     element.columnValues.info.method = window.main.$t(
        //       "analysis." + element.columnValues.info.method
        //     );
        //   }
        //   if (element.columnValues.info.status) {
        //     element.columnValues.info.status = window.main.$t(
        //       "analysis." + element.columnValues.info.status
        //     );
        //   }
        //   return element;
        // });
      }
    },
    clearLogList(state) {
      state.loglist = [];
      state.lastLogRow = "";
    },
    getTaskLog(state, v) {
      if (v) {
        state.nowCheckLogRow = v;
      }
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListLogs",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastLogRow ? [state.lastLogRow] : [],
            },
            msg: {
              task_authority: "username",
              task_type: "search_task",
              task_id: v,
            },
          },
        ],
        "telegramSearch/telegramSearchTask/setTaskLog"
      );
    },
    setTaskLog(state, res) {
      state.lastLogRow = res[res.length - 1]?.row;
      let logListarr = [];
      res.forEach((item) => {
        let obj = new Object();
        obj["info"] = item["columnValues"]["info"]["msg"];
        obj["timestamp"] = window.main.$tools.timestampToTime(
          item["columnValues"]["info"]["timestamp"].substring(0, 10)
        );
        logListarr.push(obj);
      });
      state.loglist.push(...logListarr);
    },
    sendSetStatus(state, v) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.SetStatus",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [v.row],
            },
            msg: {
              task_authority: "username",
              status: v.command,
              tag: ["apocalypse_search"],
              task_type: "search_task",
            },
          },
        ],
        "telegramSearch/telegramSearchTask/setStatus"
      );
    },
    setStatus(state, res) {
      window.main.$store.commit("telegramSearch/telegramSearchTask/clearAnalysisTaskList");
      window.main.$store.commit("telegramSearch/telegramSearchTask/getAnalysisTask");
    },
    senddel(state, v) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Del",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [v],
            },
            msg: {
              task_authority: "username",
              tag: ["apocalypse_search"],
              task_type: "search_task",
            },
          },
        ],
        "telegramSearch/telegramSearchTask/setdel"
      );
    },
    setdel(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除成功！");
        window.main.$store.commit("telegramSearch/telegramSearchTask/clearAnalysisTaskList");
        window.main.$store.commit("telegramSearch/telegramSearchTask/getAnalysisTask");
      }
    },
    // 获取更新任务
    // getUpdateTask(state, v) {
    //   if (!state.analysisTaskList.length) {
    //     return;
    //   }
    //   let arrRow = [];
    //   state.analysisTaskList.forEach((item) => {
    //     arrRow.push(item.row);
    //   });
    //   window.main.$main_socket.sendData(
    //     "Api.DataAnalysisTask.Detail",
    //     [
    //       {
    //         head: {
    //           session_id: window.main.$store.state.userInfo.session_id,
    //           row_key: arrRow,
    //         },
    //         msg: {
    //           task_authority: "username",
    //           tag: ["apocalypse_search"],
    //           task_type: "search_task",
    //           familys: ["info"],
    //         },
    //       },
    //     ],
    //     "searchTask/setUpdateTask"
    //   );
    // },
    // setUpdateTask(state, res) {
    //   state.analysisTaskList = res;
    // },
  },
};
