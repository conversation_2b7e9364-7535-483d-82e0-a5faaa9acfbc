.details {
    display: flex;
    justify-content: space-between;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e9ecef 100%);

    .el-card.box-card {
        border-radius: 18px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        border: none;
        padding: 0 0 24px 0;
        background: #fff;
        transition: box-shadow 0.2s;
    }

    .left {
        width: 350px;
        min-width: 320px;
        max-width: 480px;
        flex-shrink: 0;
        padding: 0;
        background: transparent;
        max-height: calc(100vh - 32px);
        overflow-y: auto;

        .person-card  {
            background: #f5f5f5;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            margin: 16px 12px 0 15px;
            display: flex;
            flex-direction: column;
            // align-items: stretch;

            .avatar {
                background: #cccccc1a;
                box-shadow: 0 1px 4px rgba(0,0,0,0.04);
                padding: 12px 16px;
                display: flex;
                align-items: center;
                // margin-bottom: 12px;
                min-height: 76px;

                .avatar-img {
                    margin-right: 14px;
                    width: 70px;
                    height: 100px;
                    .custom-avatar {
                        width: 70px !important;
                        height: 100px !important;
                        border-radius: 50%;
                        border: 1px solid #e0e7ef;
                    }
                }

                .avatar-info {
                    flex: 1;
                    font-size: 12px;
                    .info-row {
                        height: 30px;
                        line-height: 30px;
                        align-items: center;
                        display: flex;
                        max-width: 100%;
                        overflow: hidden;
                    }
                    .info-label {
                        color: #555;
                        font-size: 12px;
                        min-width: 40px;
                        flex-shrink: 0;
                    }
                    .info-value {
                        font-size: 12px;
                        max-width: 120px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        flex: 1;
                    }
                    .info-row.sex-age-row {
                        height: 30px;
                        line-height: 30px;
                        align-items: center;
                        span {
                            display: flex;
                            align-items: center;
                        }
                        .info-age {
                            margin-left: 30px;
                        }
                    }
                    .avatar-actions {
                        height: 40px;
                        line-height: 40px;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }

            // 操作按钮区
            .info-details[style*="space-evenly"] {
                margin-top: 8px !important;
                justify-content: flex-start !important;
                gap: 8px;
                .el-button {
                    margin: 0 2px;
                    font-size: 18px;
                }
            }
            .other {
                background: #fff;
                .info-details {
                    border-radius: 0;
                    border-bottom: 1px solid #ececec;
                    padding: 8px 12px;
                    margin-bottom: 0;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    .info-title {
                        color: #888;
                        min-width: 72px;
                    }
                    .info-info {
                        color: #444;
                        margin-left: 8px;
                    }
                }
                .info-details:last-child {
                    border-bottom: none;
                }
            }
            .relation-collapse-content {
                background: #fff;
                border-radius: 8px;
                margin-bottom: 8px;
                box-shadow: 0 1px 4px rgba(0,0,0,0.03);
                display: flex;
                flex-direction: column;
                gap: 6px;
                .relation-row {
                  display: flex;
                  align-items: center;
                  font-size: 14px;
                  .relation-label {
                    font-size: 12px;
                    color: #666;
                    font-weight: 600;
                    min-width: 72px;
                  }
                  .relation-value {
                    color: #222;
                    margin-left: 8px;
                    font-weight: 500;
                  }
                }
              }
        }


        .personOrg {
            margin: 16px 12px 0 12px;
            .friendInfo {
                margin-bottom: 12px;
                .el-collapse-item__header {
                    background: #999;
                    color: #fff;
                    border-radius: 6px 6px 0 0;
                    font-weight: 700;
                    font-size: 15px;
                    padding: 0 12px;
                    min-height: 36px;
                    display: flex;
                    align-items: center;
                }
                .el-collapse-item__wrap {
                    background: #fff;
                    border-radius: 0 0 6px 6px;
                    padding: 8px 12px;
                }
                .friend-details {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    margin-bottom: 4px;
                    .info-title {
                        color: #888;
                        min-width: 72px;
                    }
                    .info-info {
                        color: #444;
                        margin-left: 8px;
                    }
                }
            }
        }
    }

    // 操作按钮区
    .info-details[style*="space-evenly"] {
        margin-top: 18px !important;
        justify-content: flex-end !important;
        gap: 12px;
        .el-button {
            margin: 0 4px;
        }
    }

    .right {
        flex: 1;
        min-width: 0;
        width: auto;
        height: 100%;
        background-color: #f0f0f0;
        position: relative;
        border-radius: 18px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.04);
    }
}

.info-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.info-info {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.main-container {
    // background-color: #eee;
}

::v-deep .el-collapse-item__header {
    font-size: 16px;
    font-weight: 600;
    padding: 5px;
    // background-color: #ddd;
}

::v-deep .el-collapse-item__wrap {
    padding: 5px;
    // background-color: #ddd;
}

::v-deep .custom-avatar {
    /* 基础尺寸（保持1:1.4比例） */
    width: 150px;
    height: 210px;
    /* 150 * 1.4 = 210 */

    /* 容器样式 */
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    /* 超出部分隐藏 */
}

::v-deep .custom-avatar .el-avatar__image {
    /* 图片适配 */
    object-fit: cover;
    /* 保持比例填充容器 */
    width: 100%;
    height: 100%;
}



.media-collapse-content {
  background: #f8fafd;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  gap: 6px;
  .media-row {
    display: flex;
    align-items: center;
    font-size: 14px;
    height: 30px; // 固定行高
    .media-label {
      color: #666;
      font-weight: 600;
      min-width: 72px;
      flex-shrink: 0; // 防止标签被压缩
    }
    .media-value {
      color: #222;
      margin-left: 8px;
      font-weight: 500;
      flex: 1; // 占据剩余空间
      max-width: calc(100% - 80px); // 限制最大宽度，为标签留出空间
      height: 30px;
      line-height: 30px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      position: relative;

      // 鼠标悬停时显示完整内容
      &:hover {
        &::after {
          content: attr(title);
          position: absolute;
          top: 100%;
          left: 0;
          z-index: 1000;
          background: #333;
          color: #fff;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          white-space: normal;
          word-wrap: break-word;
          max-width: 300px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
          margin-top: 4px;
          opacity: 0;
          animation: fadeIn 0.2s ease-in-out forwards;
        }
      }
    }
  }
}

.info-row,
.relation-row,
.media-row {
  height: 30px;
  line-height: 30px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.info-label,
.relation-label,
.media-label {
  color: #222;
  font-weight: 600;
  min-width: 60px;
  font-size: 13px;
}

.info-value,
.relation-value,
.media-value {
  color: #888;
  font-weight: 400;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 添加淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}