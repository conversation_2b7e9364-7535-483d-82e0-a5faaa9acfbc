<template>
  <div
    class="model_lay"
    style="padding: 10px; width: 1200px; margin-left: auto; margin-right: auto"
  >
    <div v-if="twitterDetail">
      <div class="box-row">
        <div>
          <img
            v-if="twitterDetail.icon"
            :onerror="defaultImg"
            :src="
              '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
              twitterDetail.icon.sha512_hash +
              '?session_id=' +
              $store.state.userInfo.session_id
            "
          />
          <img v-else :onerror="defaultImg" :src="imga" />
        </div>
        <div
          style="
            margin-left: 10px;
            height: 90px;
            justify-content: space-between;
            flex-direction: column;
            display: flex;
          "
        >
          <div v-if="twitterDetail.nickname">
            <b>{{ twitterDetail.nickname }}</b>
          </div>
          <div style="">{{ twitterDetail.user_id }}</div>
          <div>
            <span style="margin-right: 20px" title="发文"
              ><i
                class="el-icon-document"
                style="color: #e6a23c; margin-right: 5px"
              ></i
              >{{ twitterDetail.article_count | capitalize }}</span
            >
            <span style="margin-right: 20px" title="粉丝"
              ><i
                class="icon iconfont"
                style="color: #e6a23c; margin-right: 5px"
                >&#xe619;</i
              >{{ twitterDetail.followers_count | capitalize }}</span
            >
            <span style="margin-right: 20px" title="关注"
              ><i
                class="el-icon-view"
                style="color: #e6a23c; margin-right: 5px"
              ></i
              >{{ twitterDetail.following_count | capitalize }}</span
            >
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          margin-top: 20px;
          width: 100%;
          justify-content: space-between;
        "
      >
        <div style="display: flex">
          <div style="margin-right: 25px; color: #999">
            注册时间：{{
              $tools.timestampToTime(twitterDetail.create_timestamp)
            }}
          </div>
          <div
            style="
              margin-right: 25px;
              color: #999;
              width: 150px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            "
          >
            用户：{{ twitterDetail.username[0] }}
          </div>
        </div>
        <div style="display: flex">
          <div style="margin-right: 25px; color: #999">
            <span
              class="account_bage"
              :style="noStatus({ status: { status: twitterDetail.status } })"
            ></span
            >{{ accountState }}
          </div>
        </div>
      </div>
    </div>
    <div
      style="
        line-height: 42px;
        background-color: #f5f7fa;
        padding: 0 10px;
        margin-top: 20px;
        border: 1px solid #ccc;
        border-bottom: 0px;
      "
    >
      {{
        $route.params.id.split(";")[$route.params.id.split(";").length - 1]
      }}回复的推文
    </div>
    <div ref="scroll" @scroll="handleScroll" class="scrollDiv">
      <!-- 其他滚动内容 -->

      <div v-for="(val, index) in queryData" :key="index">
        <div v-if="index % 20 === 0 && index !== 0" class="index-marker">
          第 {{ index / 20 }} 页
        </div>
        <div class="twitterlistRow">
          <div
            class="twitterlistRowLeft"
            v-if="
              !(val == null) &&
              val.hasOwnProperty('_source') &&
              val._source.hasOwnProperty('icon')
            "
          >
            <img
              :onerror="defaultImg"
              :src="
                '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
                val._source.icon[0].sha512_hash +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
              :type="val._source.icon[0].icon_type"
            />
          </div>
          <div class="twitterlistRowLeft" v-else>
            <img :src="require('@/assets/images/winter.jpg')" />
          </div>
          <div class="twitterlistRowRight">
            <div class="twitterlistRowTop">
              <div>
                <b>{{ val._source.nickname[0] }}</b>
                <span style="margin-left: 10px">{{ val._source.user_id }}</span>
                <span
                  style="margin-left: 10px; color: #999"
                  v-if="val._source.hasOwnProperty('create_timestamp')"
                  >{{
                    $tools.timestampToTime(val._source.create_timestamp)
                  }}</span
                >
                <span
                  style="margin-left: 10px; color: #999"
                  v-if="val._source.hasOwnProperty('timestamp')"
                  >{{ $tools.timestampToTime(val._source.timestamp) }}</span
                >
              </div>
            </div>
            <div class="twitterlistRowMid">
              <span v-if="val._source.hasOwnProperty('summary')">{{
                val._source.summary[0] === ""
                  ? "暂无简介"
                  : val._source.summary[0]
              }}</span>
              <span v-if="val._source.hasOwnProperty('content_article')">{{
                val._source.content_article === ""
                  ? "暂无内容"
                  : val._source.content_article
              }}</span>
            </div>
            <div class="twitterlistRowFoot">
              <div
                class="footItem"
                title="点赞"
                v-if="val._source.hasOwnProperty('likes_count')"
                @click="
                  likefn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe619;</i>点赞
                <span style="margin-left: 4px">{{
                  val._source.likes_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="粉丝"
                v-if="val._source.hasOwnProperty('followers_count')"
                @click="
                  hotFollowersListfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe60c;</i>
                <span style="margin-left: 4px">{{
                  val._source.followers_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="关注"
                v-if="val._source.hasOwnProperty('following_count')"
                @click="
                  hotFollowingListfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-star-off"></i>
                <span style="margin-left: 4px">{{
                  val._source.following_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="文章"
                v-if="val._source.hasOwnProperty('article_count')"
                @click="
                  getTuiWenDatalist({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe74b;</i>
                <span style="margin-left: 4px">{{
                  val._source.article_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="评论"
                v-if="val._source.hasOwnProperty('reply_count')"
                @click="
                  replyfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-chat-dot-round"></i>

                <span style="margin-left: 4px">{{
                  val._source.reply_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="转发"
                v-if="val._source.hasOwnProperty('forward_count')"
                @click="
                  forwardfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-share"></i>
                <span style="margin-left: 4px">{{
                  val._source.forward_count
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="loading && (!queryEnd || !listTrueEnd)" class="loading">
        加载中...
      </div>
      <div v-if="queryEnd && listTrueEnd" class="no-more">没有更多数据了</div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      defaultImg: 'this.src="' + require("@/assets/images/winter.jpg") + '"',
    };
  },
  watch: {},
  computed: {
    ...mapState({
      twitterDetail: (state) => state.deployment.twitter.twitterDetail,
      queryData: (state) => state.search.twLinFacSearch.queryData.hits,
      loading: (state) => state.search.twLinFacSearch.loading,
      listTrueEnd: (state) => state.search.twLinFacSearch.listTrueEnd,
      queryEnd: (state) => state.search.twLinFacSearch.queryEnd,
    }),
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.scroll.style.height = "90%";
      this.$refs.scroll.style.overflowY = "auto";
    });
  },
  created() {
    this.moreFn();
    //初始化数据
    this.initialData();
    //加载数据

    this.loadTwitterListData();
  },
  filters: {
    capitalize(value) {
      switch (true) {
        case Number(value) >= 10000 && Number(value) <= 100000000:
          return (Number(value) / 10000).toFixed(2) + "万";
          break;
        case 100000000 <= Number(value):
          return (Number(value) / 100000000).toFixed(2) + "亿";
          break;
        default:
          return Number(value);
      }
    },
  },
  methods: {
    ...mapMutations({ initialData: "search/twLinFacSearch/initialData" }),
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        this.loadTwitterListData();
      }
    },
    async moreFn() {
      try {
        const newData = await this.$store.dispatch(
          "deployment/twitter/getTwitterDetail"
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    noStatus(data) {
      console.log("data", data.status.status);
      if (data?.status?.status) {
        switch (data?.status?.status) {
          case "monitor":
            this.accountState = "监控";
            console.log("jiankong");
            return "background-color: green";
          case "spider_ready":
            this.accountState = "未监控";
            return "background-color: gray";

          default:
            "background-color: gray";
            break;
        }
      } else {
        this.accountState = "未监控";
        return "background-color: gray";
      }
    },
    // 加载详情里的推文列表数据
    async loadTwitterListData() {
      if (
        this.$store.state.search.twLinFacSearch.loading ||
        this.$store.state.search.twLinFacSearch.noMore
      )
        return;
      this.$store.commit("search/twLinFacSearch/setLoading", true);
      let obj = {};
      obj = {
        data_range_father_path: "/social_platform/twitter/timeline",
        data_range_index_prefix: "social_platform_timeline_prefix_twitter",
      };
      this.$store.commit("search/twLinFacSearch/setSearchType", obj);
      try {
        const newData = await this.$store.dispatch(
          "search/twLinFacSearch/getListTrue",
          obj
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.model_lay {
  height: 100%;
  display: flex;
  flex-direction: column;
  .scrollDiv {
    flex: 1;
    border: 1px solid #ccc;
    border-top: 0px;
  }
}
::-webkit-scrollbar {
  width: 7px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #afb1b4;
}
::-webkit-scrollbar-track-piece {
  width: 2px;
  background: #dfe9f7;
}
.tuiwenRowR {
  float: left;
}
.reply {
  background: #b3d7fd;
  border-radius: 4px;
  padding: 5px;
  color: #333;
  position: relative;
}
.reply::before {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 8px 8px 0;
  border-color: transparent #b3d7fd transparent transparent;
  content: "";
  position: absolute;
  left: -8px;
  top: 8px;
  border-right-color: #b3d7fd;
}

.tuiwen {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tuiwen:hover {
  color: #409eff;
}
.tuiwenRow {
  display: -webkit-box;
  //display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 40px;
  .tuiwenRowL {
    cursor: pointer;
    margin-right: 20px;
    float: left;
  }
}
.box-row {
  img {
    display: block;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: 1px solid #e6a23c;
  }
  display: flex;
}
.box-card {
  width: 480px;
}
.account_bage {
  display: inline-block;
  margin: 0 5px 0 10px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: flicker 1500ms infinite;
}
@keyframes flicker {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}

.type {
  height: 42px;
  line-height: 42px;
}
/* .item {
    padding: 10px;
    border-bottom: 1px solid #eee;
  } */
.index-marker {
  text-align: center;
  line-height: 48px;
  background-color: #eee;
}
.twitterlistRow {
  display: flex;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}
.twitterlistRowLeft {
  width: 100px;
  text-align: center;
}
.twitterlistRowLeft img {
  margin-top: 10px;
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.twitterlistRowRight {
  flex: 1 1;
}
.twitterlistRowTop {
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
}
.twitterlistRowMid {
  margin-top: 10px;
}
.twitterlistRowFoot {
  display: flex;
  margin-top: 20px;
}
.loading,
.no-more {
  text-align: center;
  padding: 10px;
  color: #999;
}

.morInfor {
  display: flex;
}
.morInfor .morInfor_l {
  width: 100px;
  padding: 10px;
  display: flex;
  justify-content: center;
}
.morInfor .morInfor_l img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.morInfor .morInfor_r {
  width: 80%;
}
.morInfor .morInfor_r .morInfor_r_li {
  display: flex;
  margin-top: 10px;
}
.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_h {
  color: #969696;
}
.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_c {
  margin-left: 15px;
}
.morInfor .morInfor_r .morInfor_r_box {
  padding-bottom: 10px;
  color: #999;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_t {
  display: block;
  margin-top: 10px;
  color: #80a8e5;
  font-weight: bold;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_m {
  padding-top: 10px;
  padding-bottom: 10px;
  color: #666;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_b .tit {
  font-weight: bold;
}
</style>
