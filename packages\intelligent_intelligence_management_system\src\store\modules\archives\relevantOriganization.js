export default {
  namespaced: true,
  state: {
    from: 0,
    size: 200,
    total: 0,
    relevantOriganizationList: [],
  },
  mutations: {
    resetData(state) {
      state.relevantOriganizationList = [];
      state.size = 200;
    },

    // 获取目标组织数据
    getOrganization(state) {
      let person_origanization = JSON.parse(window.main.$route.query.data)
      if (!person_origanization._source.params.hasOwnProperty('relation') || person_origanization._source.params.relation[1] == undefined) {
        return
      }
      let organizationRelationIds = JSON.parse(
        person_origanization._source.params.relation[1]
      ).organization;      
      let add_es_query_conditions = {
        bool: {
          should: organizationRelationIds.map((account) => ({
            term: { _id: account },
          })),
        },
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: state.from,
              size: state.size,
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: {
              data_range_index_name: "key_organization",
            },
          },
        ],
        "relevantOriganization/setOrganizationList"
      );
    },

    setOrganizationList(state, data) {
      if (data?.hits?.hits?.length) {
        state.relevantOriganizationList = data.hits.hits.map(item => {
          const newItem = { ...item };
          // 定义一个辅助函数来获取数组中的第一个元素或默认值
          const getFirstOrDefault = (arr, defaultVal = '') => {
            return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
          };
          if (newItem._source && newItem._source.params) {
            let paramsObj = {};
            newItem._source.params.forEach((param) => {
              if (param.v && param.v.length === 1 && param.v[0] === '[]') {
                paramsObj[param.k] = [];
              } else {
                paramsObj[param.k] = param.v;
              }
            });

            // 创建basic对象以兼容前端视图
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
              remark: getFirstOrDefault(paramsObj.remark),
              createTime: getFirstOrDefault(paramsObj.createTime),
              belong: getFirstOrDefault(paramsObj.belong),
              desi: getFirstOrDefault(paramsObj.desi),
              avatar: getFirstOrDefault(paramsObj.avatar),
            };
            
            newItem._source.params = { ...paramsObj, basic: basicObj };
          } else if (newItem._source && !Array.isArray(newItem._source.params)) {
            const p = newItem._source.params || {};
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
              remark: getFirstOrDefault(paramsObj.remark),
              createTime: getFirstOrDefault(paramsObj.createTime),
              belong: getFirstOrDefault(paramsObj.belong),
              desi: getFirstOrDefault(paramsObj.desi),
              avatar: getFirstOrDefault(paramsObj.avatar),
            };
            newItem._source.params = {
              ...p,
              basic: basicObj
            };
          }
          return newItem;
        });
      }
    },
  },
};
