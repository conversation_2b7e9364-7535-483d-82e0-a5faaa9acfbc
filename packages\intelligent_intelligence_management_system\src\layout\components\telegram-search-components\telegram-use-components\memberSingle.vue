<template>
  <div v-if="item" class="group_member_data">
    <div class="member_icon">
      <img
        v-if="icon.length"
        :src="
          '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
          icon[0].sha512_hash +
          '?session_id=' +
          $store.state.userInfo.session_id
        "
      />
      <img v-else src="@/assets/images/user.png" />
    </div>
    <div class="member_name">
      <p class="name" v-if="username.length">
        用户名:{{ username[0].username }}
      </p>
      <p class="name" v-if="nickname.length">昵称:{{ nickname[0].nickname }}</p>
      <p class="name" v-if="user_id">用户ID:{{ user_id }}</p>
      <p class="name" v-if="telephone.length">
        手机号:{{ telephone[0].telephone }}
      </p>
      <p class="name" v-if="location.length">
        归属地:{{ location[0].location }}
      </p>
      <p class="name" v-if="item.type && item.type.type">
        类型:{{ item.type.type }}
      </p>
    </div>
    <!-- <div style="width: 10%">
      <el-button type="primary" @click="addHbaseMemberGraph(item)"
        >图形分析</el-button
      >
    </div> -->
  </div>
</template>

<script>
export default {
  name: "member-single",
  props: {
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      user_id: "",
      group_id: [],
      nickname: [],
      username: [],
      telephone: [],
      location: [],
      icon: [],
    };
  },
  created() {
    this.getMemberInfo()
  },
  methods: {
    getMemberInfo() {
      const info = this.item;
      for (const key in info) {
        if (key.startsWith("user_id") && info[key].user_id) {
          this.user_id = info[key].user_id;
        }
        if (key.startsWith("group_id") && info[key].group_id) {
          this.group_id.push(info[key]);
        }
        if (key.startsWith("nickname") && info[key].nickname) {
          this.nickname.push(info[key]);
        }
        if (key.startsWith("username") && info[key].username) {
          this.username.push(info[key]);
        }
        if (key.startsWith("telephone") && info[key].telephone) {
          this.telephone.push(info[key]);
        }
        if (key.startsWith("location") && info[key].location) {
          this.location.push(info[key]);
        }
        if (key.startsWith("icon") && info[key].icon) {
          this.icon.push(info[key].icon);
        }
      }
    },
    		// 添加图形分析
		addHbaseMemberGraph(item){
			let nowMemberInfo = {
				iconSha512: '',
				user_id: '',
				type: '',
				user_name: ''
			}
			nowMemberInfo.iconSha512 = this.icon[0].sha512_hash
			nowMemberInfo.user_name = this.username[0].username
			nowMemberInfo.user_id = this.user_id,
			nowMemberInfo.type = this.item?.type?.type,
			window.sessionStorage.setItem(nowMemberInfo.user_id, JSON.stringify(nowMemberInfo))
			this.$message.success('加入成功，请前往图形分析页面查看！')
		}
  },
};
</script>
<style lang="scss" scoped>
.group_member_data {
  margin-top: 20px;
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
}
.member_icon{
  width: 10%;
  border-right: 1px solid #ccc;
}
.member_icon img{
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: block;
  margin: 10px auto ;
}
 .member_name{
  font-size: 16px;
  width: 80%;
  padding-left: 50px;
}
 .member_name .name{
  padding-bottom: 5px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
