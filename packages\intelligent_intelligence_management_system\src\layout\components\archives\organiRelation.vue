<template>
  <div v-show="this.showFlag" class="organi-relation-container">
    <!-- 组织人员关系 公共组件 -->
    <el-tabs tab-position="top" class="relation-tabs">
      <el-tab-pane label="目标组织">
        <div class="organi_list" v-if="organiList.length">
          <div 
            class="organi_card" 
            v-for="(organi, index) in organiList" 
            :key="'organi' + index"
            @click="selectOrgani(organi)"
            :class="{'selected': isOrganiSelected(organi)}"
          >
            <div class="card_header">
              <div class="avatar">
                <el-avatar 
                  shape="square" 
                  :size="60" 
                  :src="organi._source.params.basic.avatar || ''"
                >
                  {{ getInitial(organi._source.params.basic.name) }}
                </el-avatar>
              </div>
              <div class="info">
                <div class="name">{{ organi._source.params.basic.name }}</div>
                <div class="remark">{{ organi._source.params.basic.remark || '暂无备注' }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="empty_list" v-else>暂无组织数据</div>
      </el-tab-pane>
      <el-tab-pane label="目标人">
        <div class="person_list" v-if="personList.length">
          <div
            class="person_card"
            v-for="(person, index) in personList"
            :key="'person' + index"
            @click="selectPerson(person)"
            :class="{'selected': isPersonSelected(person)}"
          >
            <div class="card_header">
              <div class="avatar">
                <el-avatar
                  shape="square"
                  :size="60"
                  :src="person._source.params.basic.avatar || ''"
                >
                  {{ getInitial(person._source.params.basic.name) }}
                </el-avatar>
              </div>
              <div class="info">
                <div class="name">{{ person._source.params.basic.name }}</div>
                <div class="remark">{{ person._source.params.basic.remark || '暂无备注' }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="empty_list" v-else>暂无数据</div>
      </el-tab-pane>
      <el-tab-pane label="次要目标人">
        <div class="person_list" v-if="secondaryPersonList.length">
          <div
            class="person_card"
            v-for="(secondaryPerson, index) in secondaryPersonList"
            :key="'secondaryPerson' + index"
            @click="selectSecondaryPerson(secondaryPerson)"
            :class="{'selected': isSecondaryPersonSelected(secondaryPerson)}"
          >
            <div class="card_header">
              <div class="avatar">
                <el-avatar
                  shape="square"
                  :size="60"
                  :src="secondaryPerson._source.params.basic.avatar || ''"
                >
                  {{ getInitial(secondaryPerson._source.params.basic.name) }}
                </el-avatar>
              </div>
              <div class="info">
                <div class="name">{{ secondaryPerson._source.params.basic.name }}</div>
                <div class="remark">{{ secondaryPerson._source.params.basic.remark || '暂无备注' }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="empty_list" v-else>暂无数据</div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  props: {
    showFlag: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    initialSelectedOrganizations: {
      type: Array,
      default: () => []
    },
    initialSelectedPersons: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedOrganiIds: [],
      selectedOrganizations: [],
      selectedPersonIds: [],
      selectedPersons: [],
      selectedSecondaryPersonIds: [],
      selectedSecondaryPersons: [],
    };
  },
  created() {
    this.$store.commit("personOrganizationDialog/resetData");
    this.$store.commit("personOrganizationDialog/getOrganizationDialog");
    this.$store.commit("personOrganizationDialog/getPersonDialog");
    this.$store.commit("personOrganizationDialog/getSecondaryPersonDialog");
    // 初始化选中状态
    this.initializeSelectedItems();
  },
  watch: {
    // 监听showFlag变化，当面板打开时重新初始化选中状态
    showFlag(newVal) {
      if (newVal) {
        this.initializeSelectedItems();
      }
    }
  },
  computed: {
    ...mapState({
      personList: (state) => state.personOrganizationDialog.personList,
      organiList: (state) => state.personOrganizationDialog.organizationList,
      secondaryPersonList: (state) => state.personOrganizationDialog.secondaryPersonList,
    }),
  },
  methods: {
    // 新增选择目标人方法
    selectPerson(item) {
      console.log("选择的目标人", item);
      
      // 如果已经选中，则取消选择
      if (this.isPersonSelected(item)) {
        this.selectedPersonIds = this.selectedPersonIds.filter(id => id !== item._id);
        this.selectedPersons = this.selectedPersons.filter(person => person._id !== item._id);
      } else {
        this.selectedPersonIds.push(item._id);
        this.selectedPersons.push(item);
      }
      console.log("选择的成员", item, this.selectedPersons);
      // 向父组件传递选中的人员数据
      this.$emit('selectPersons', this.selectedPersons);
    },
    
    // 新增选择组织方法
    selectOrgani(item) {
      console.log("选择的组织", item, this.organiList);
      
      // 如果已经选中，则取消选择
      if (this.isOrganiSelected(item)) {
        this.selectedOrganiIds = this.selectedOrganiIds.filter(id => id !== item._id);
        this.selectedOrganizations = this.selectedOrganizations.filter(org => org._id !== item._id);
      } else {
        this.selectedOrganiIds.push(item._id);
        this.selectedOrganizations.push(item);
      }
      
      // 向父组件传递选中的组织数据
      this.$emit('selectOrganizations', this.selectedOrganizations);
    },

    // 新增选择次要目标人方法（与目标人选择完全一致）
    selectSecondaryPerson(item) {
      // 如果已经选中，则取消选择
      if (this.isSecondaryPersonSelected(item)) {
        this.selectedSecondaryPersonIds = this.selectedSecondaryPersonIds.filter(id => id !== item._id);
        this.selectedSecondaryPersons = this.selectedSecondaryPersons.filter(person => person._id !== item._id);
      } else {
        this.selectedSecondaryPersonIds.push(item._id);
        this.selectedSecondaryPersons.push(item);
      }
      // 向父组件传递选中的次要目标人数据（与目标人一致，完整对象数组）
      this.$emit('selectSecondaryPersons', this.selectedSecondaryPersons);
    },
    
    // 获取名称首字母或默认字符
    getInitial(name) {
      return name ? name.charAt(0) : 'O';
    },

    isOrganiSelected(organi) {
      return this.selectedOrganiIds.includes(organi._id);
    },

    isPersonSelected(person) {
      return this.selectedPersonIds.includes(person._id);
    },

    isSecondaryPersonSelected(person) {
      return this.selectedSecondaryPersonIds.includes(person._id);
    },
    
    // 移除选中的组织（供父组件调用）
    removeSelectedOrgani(organi) {
      if (organi && organi._source && organi._source.params && organi._id) {
        const id = organi._id; 
        if (this.selectedOrganiIds.includes(id)) {
          this.selectedOrganiIds = this.selectedOrganiIds.filter(organiId => organiId !== id);
          this.selectedOrganizations = this.selectedOrganizations.filter(org => org._id !== id);
          // 通知父组件更新
          this.$emit('selectOrganizations', this.selectedOrganizations);
        }
      }
    },
    
    // 移除选中的人员（供父组件调用）
    removeSelectedPerson(person) {
      if (person && person._source && person._source.params && person._id) {
        const id = person._id; 
        if (this.selectedPersonIds.includes(id)) {
          this.selectedPersonIds = this.selectedPersonIds.filter(personId => personId !== id);
          this.selectedPersons = this.selectedPersons.filter(p => p._id !== id);
          // 通知父组件更新
          this.$emit('selectPersons', this.selectedPersons);
        }
      }
    },
    
    // 初始化选中状态
    initializeSelectedItems() {
      // 初始化已选择的组织
      this.selectedOrganizations = Array.isArray(this.initialSelectedOrganizations) ? [...this.initialSelectedOrganizations] : [];
      this.selectedOrganiIds = this.selectedOrganizations
        .filter(org => org && org._source && org._source.params && org._id)
        .map(org => org._id);
      
      // 初始化已选择的人员
      this.selectedPersons = Array.isArray(this.initialSelectedPersons) ? [...this.initialSelectedPersons] : [];
      this.selectedPersonIds = this.selectedPersons
        .filter(person => person && person._source && person._source.params && person._id)
        .map(person => person._id);
    },
    
    // 从父组件获取已选择的数据并初始化
    initializeSelectedFromParent(selectedOrganizations, selectedPersons) {
      // 初始化已选择的组织
      if (Array.isArray(selectedOrganizations)) {
        this.selectedOrganizations = [...selectedOrganizations];
        this.selectedOrganiIds = selectedOrganizations
          .filter(org => org && org._source && org._source.params && org._id)
          .map(org => org._id);
      }
      
      // 初始化已选择的人员
      if (Array.isArray(selectedPersons)) {
        this.selectedPersons = [...selectedPersons];
        this.selectedPersonIds = selectedPersons
          .filter(person => person && person._source && person._source.params && person._id)
          .map(person => person._id);
      }
    },
    
    // 根据ID列表更新选中的组织
    updateSelectedOrganizations(orgIds) {
      if (!Array.isArray(orgIds)) return;
      
      // 更新选中的ID列表
      this.selectedOrganiIds = [...orgIds];
      
      // 根据ID列表重新构建选中的组织对象列表
      this.selectedOrganizations = this.organiList.filter(org => 
        org && 
        org._source && 
        org._source.params && 
        org._id && 
        orgIds.includes(org._id)
      );
      
      // 通知父组件更新
      this.$emit('selectOrganizations', this.selectedOrganizations);
    },
    
    // 根据ID列表更新选中的人员
    updateSelectedPersons(personIds) {
      if (!Array.isArray(personIds)) return;
      
      // 更新选中的ID列表
      this.selectedPersonIds = [...personIds];
      
      // 根据ID列表重新构建选中的人员对象列表
      this.selectedPersons = this.personList.filter(person => 
        person && 
        person._source && 
        person._source.params && 
        person._id && 
        personIds.includes(person._id)
      );
      
      // 通知父组件更新
      this.$emit('selectPersons', this.selectedPersons);
    }
  },
};
</script>
<style lang="scss" scoped>
.person_list {
  padding: 10px;

  .person_card {
    margin-bottom: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    display: flex;
    align-items: center;
    
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    
    &.selected {
      border: 1px solid #409EFF;
      background-color: #ecf5ff;
    }

    .card_header {
      flex: 1;
      padding: 10px;
      display: flex;
      align-items: center;

      .avatar {
        margin-right: 15px;

        .el-avatar {
          background-color: #409EFF;
          color: #fff;
          font-size: 24px;
          font-weight: bold;
        }
      }

      .info {
        flex: 1;
        overflow: hidden;

        .name {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .remark {
          font-size: 13px;
          color: #909399;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .card_footer {
      padding: 10px;
      text-align: right;
    }
  }
}

.organi_list {
  padding: 10px;
  
  .organi_card {
    margin-bottom: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    display: flex;
    align-items: center;
    
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    
    &.selected {
      border: 1px solid #409EFF;
      background-color: #ecf5ff;
    }

    .card_header {
      flex: 1;
      padding: 10px;
      display: flex;
      align-items: center;
      
      .avatar {
        margin-right: 15px;
        
        .el-avatar {
          background-color: #409EFF;
          color: #fff;
          font-size: 24px;
          font-weight: bold;
        }
      }
      
      .info {
        flex: 1;
        overflow: hidden;
        
        .name {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .remark {
          font-size: 13px;
          color: #909399;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    
    .card_footer {
      padding: 10px;
      text-align: right;
    }
  }
}

.empty_list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  font-size: 14px;
}

.organi-relation-container {
  padding-left: 10px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.relation-tabs {
  width: 100%;
}

::v-deep .el-tabs {
  height: 100%;
  width: 100%;
}

::v-deep .el-tabs__content {
  padding: 0;
  height: 65vh;
  overflow-y: auto;
}
</style>