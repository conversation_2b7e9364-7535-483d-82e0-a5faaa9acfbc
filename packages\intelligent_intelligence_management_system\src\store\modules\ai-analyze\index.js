export default {
  namespaced: true,
  state: {
    aiAnalyzeTemplateList: [],
    loading: false,
    from: 1,
    size: 20,
    total: null,
    selectTemplateType: "all",
  },
  mutations: {
    // 重置所有数据
    resetAllData(state) {
      state.aiAnalyzeTemplateList = [];
      state.from = 1;
      state.total = null;
      state.loading = false;
      state.selectTemplateType = "all";
    },

    // 选择模板类型时，设置数据参数
    setSelectTemplateData(state, params) {
      state.aiAnalyzeTemplateList = [];
      state.from = 1;
      state.total = null;
      state.selectTemplateType = params;
    },

    // 翻页时，数据设置必要参数
    setChangePage(state, params) {
      state.aiAnalyzeTemplateList = [];
      state.from = params.page;
    },

    // 创建模板成功之后，清除部分数据，进行重新赋值
    resetOtherData(state) {
      state.aiAnalyzeTemplateList = [];
      state.from = 1;
      state.total = null;
    },

    // 获取模板数据
    getAIAnalyzeTemplate(state) {
      state.loading = true;
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: (state.from - 1) * state.size,
              size: state.size,
            },
            control: {
              query_string: "",
              query_type: "username",
              condition: {
                query_mode: "match",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must:
                    state.selectTemplateType === "all"
                      ? []
                      : [
                          {
                            term: {
                              type: state.selectTemplateType,
                            },
                          },
                        ],
                },
              },
            },
            msg: {
              data_range_index_name:
                "intelligen_intelligence_management_ai_analysis",
            },
          },
        ],
        "aiAnalyze/setAiAnalyzeTemplate"
      );
    },
    // 设置模板数据
    setAiAnalyzeTemplate(state, data) {
      console.log("setAiAnalyzeTemplate:", data);
      state.loading = false;
      if (
        !data?.hits.hasOwnProperty("hits") ||
        data?.hits?.hits?.length === 0
      ) {
        window.main.$message.warning("没有数据！");
        return;
      }
      data?.hits?.hits?.forEach((item) => {
        item._source.ai_template_content = JSON.parse(
          item._source.ai_template_content
        );
      });
      state.aiAnalyzeTemplateList = data?.hits?.hits;
      state.total = data?.hits?.total?.value;
    },

    // 创建模板数据
    createAiAnalyzeTemplate(state, data) {
      let randomNum =
        2 ** 31 -
        new Date().getTime() / 1000 +
        Math.floor(Math.random() * 100000).toString();
      let template_content = JSON.stringify(data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_ai_analysis",
              type: "_doc",
              id: randomNum,
            },
            msg: {
              ai_template_category: data.type,
              ai_template_name: data.title,
              type: data.type,
              ai_template_content: template_content,
            },
          },
        ],
        "aiAnalyze/getCreateTemplateOssStatus"
      );
    },
    // 创建模板成功，重新获取
    getCreateTemplateOssStatus(state, data) {
      if (!data || data.status !== "ok") {
        window.main.$message.error("模板创建失败！");
        return;
      }
      window.main.$message.success("模板创建成功，正在刷新列表...");
      setTimeout(() => {
        window.main.$store.commit("aiAnalyze/resetOtherData");
        window.main.$store.commit("aiAnalyze/getAIAnalyzeTemplate");
      }, 1000);
    },

    // 编辑模板
    editAiAnalyzeTemplate(state, data) {
      let template_content = JSON.stringify(data);
      console.log("creating aiAnalyzeTemplate:", data, template_content);
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_ai_analysis",
              type: "_doc",
              id: data.id,
            },
            msg: {
              ai_template_category: data.type,
              ai_template_name: data.title,
              type: data.type,
              ai_template_content: template_content,
            },
          },
        ],
        "aiAnalyze/getCreateTemplateOssStatus"
      );
    },
    // 编辑模板成功，重新获取
    getCreateTemplateOssStatus(state, data) {
      if (!data || data.status !== "ok") {
        window.main.$message.error("模板编辑失败！");
        return;
      }
      window.main.$message.success("模板编辑成功，正在刷新列表...");
      setTimeout(() => {
        window.main.$store.commit("aiAnalyze/resetAllData");
        window.main.$store.commit("aiAnalyze/getAIAnalyzeTemplate");
      }, 1000);
    },

    // 删除模板
    deleteAiAnalyzeTemplate(state, data) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.DelOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_ai_analysis",
              type: "_doc",
              id: data,
            },
          },
        ],
        "aiAnalyze/getDeleteTemplateOssStatus"
      );
    },
    // 删除模板成功，重新获取
    getDeleteTemplateOssStatus(state, data) {
      console.log(
        "getDeleteOssStatus:",
        data.status === "ok",
        data.status !== "ok"
      );
      if (!data || data?.status !== "ok") {
        window.main.$message.error("模板删除失败！");
        return;
      }
      window.main.$message.success("模板删除成功，正在刷新列表...");
      setTimeout(() => {
        window.main.$store.commit("aiAnalyze/resetAllData");
        window.main.$store.commit("aiAnalyze/getAIAnalyzeTemplate");
      }, 1000);
    },

    // 创建OSS存储表
    createOSSTable(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.CreateOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_ai_analysis",
              type: "_doc",
              id: "_",
              field_template: {
                ai_template_category: {
                  type: "keyword",
                  index: true,
                },
                ai_template_name: {
                  type: "keyword",
                  index: true,
                },
                type: {
                  type: "keyword",
                  index: true,
                },
                ai_template_content: {
                  type: "text",
                  index: false,
                },
              },
            },
          },
        ],
        "aiAnalyze/getCreateOssStatus"
      );
    },
    // 创建OSS存储表状态
    getCreateOssStatus(state, data) {
      console.log("getCreateOssStatus:", data);
    },

    // 删除表
    deleteOSSTable(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.DropOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_ai_analysis",
              type: "_",
              id: "_",
            },
          },
        ],
        "aiAnalyze/getDeleteOssStatus"
      );
    },
    // 删除表状态
    getDeleteOssStatus(state, data) {
      console.log("getDeleteOssStatus:", data);
    },
  },
  actions: {},
  modules: {},
};
