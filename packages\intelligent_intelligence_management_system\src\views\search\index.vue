<template>
  <div class="search" style="height: 100%">
    <div class="creatTask">
      <el-button
        type="primary"
        @click="showAddAiDialog"
        size="mini"
        icon="el-icon-plus"
        >AI查询分析任务</el-button
      >
    </div>
    <!-- 舆情搜索结果组件使用示例 -->
    <!-- <newsSearchList :searchString="'美国'" :findType="'match_phrase'" :queryMode="'content_article'" :timeRange="'7天'"></newsSearchList> -->
    <div
      style="
        height: 100%;
        display: flex;
        width: 100%;
        align-items: center;
        flex-direction: column;
        justify-content: center;
      "
    >
      <div
        class="searchIco"
        style="
          font-size: 32px;
          margin-bottom: 25px;
          margin-top: -200px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        检索
      </div>
      <div class="searchoption">
        <div class="search_input">
          <div class="search_input_a" style="display: flex">
            <el-input
              v-model="queryString"
              placeholder="请输入关键字"
              @keyup.enter.native="gofn"
              ref="keyInput"
              style="color: #333"
            ></el-input>
            <el-button type="primary" style="" class="search_btn" @click="gofn"
              >搜索</el-button
            >
          </div>

          <div class="advancedLay" style="top: 40px">
            <div class="searchCondition" v-if="ppqueryMode === 'expression'">
              <el-button
                size="mini"
                v-for="(item, index) in keyWordList"
                :key="index"
                @click="addSymbol(item)"
                >{{ item }}</el-button
              >
            </div>

            <el-form
              ref="form"
              label-width="100px"
              style="padding-top: 30px; color: #fff !important"
              @submit.native.prevent
            >
              <el-form-item label="时间范围" style="color: #fff">
                <el-radio-group
                  v-model="timeRange"
                  style="display: block; margin-top: 10px"
                >
                  <el-radio
                    v-for="(item, index) in timeList.filter(
                      (item) => item !== '自定义时间'
                    )"
                    :key="index"
                    :label="item"
                    >{{ item }}</el-radio
                  >
                </el-radio-group>
                <div
                  style="
                    display: inline-flex;
                    align-items: center;
                    margin-top: 20px;
                    height: 48px;
                  "
                >
                  <el-radio v-model="timeRange" label="自定义时间"
                    >自定义时间</el-radio
                  >
                  <el-date-picker
                    v-if="timeRange === '自定义时间'"
                    v-model="customTime"
                    :picker-options="pickerOptions"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="timestamp"
                    @change="handleChange"
                    style="margin-left: 10px"
                  >
                  </el-date-picker>
                </div>
              </el-form-item>
              <!-- <el-form-item label="查询模式">
                <el-radio-group v-model="queryMode">
                  <el-radio
                    v-for="(item, index) in rangeList"
                    :key="index"
                    :label="item.type"
                    >{{ item.text }}</el-radio
                  >
                </el-radio-group>
              </el-form-item> -->
              <el-form-item label="匹配模式">
                <el-radio-group v-model="ppqueryMode">
                  <el-radio
                    v-for="(item, index) in ppList"
                    :key="index"
                    :label="item.type"
                    >{{ item.text }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="创建查询任务"
      :visible.sync="dialogAddAiVisible"
      width="80%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      custom-class="custom-dialog"
    >
      <el-form
        ref="addAiRuleForm"
        label-width="100px"
        style=""
        @submit.native.prevent
        :model="addAiRuleForm"
        :rules="addAiRules"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input
            v-model="addAiRuleForm.name"
            @keyup.enter.native="sendSearchTask"
            size="mini"
            style="width: 400px"
          ></el-input>
        </el-form-item>

        <el-form-item label="查询字符串" prop="queryString">
          <el-input
            v-model="addAiRuleForm.queryString"
            placeholder="请输入关键字"
            ref="taskKeyInput"
            style="color: #333"
          ></el-input>
          <el-button
            size="mini"
            v-for="(item, index) in keyWordList"
            :key="index"
            @click="addTaskSymbol(item)"
            >{{ item }}</el-button
          >
        </el-form-item>

        <el-form-item label="时间范围">
          <el-radio-group
            v-model="addAiRuleForm.timeRange"
            role="radiogroup"
            aria-label="时间范围选择"
            :tabindex="0"
            style="display: block; margin-top: 15px"
          >
            <el-radio
              v-for="time in [
                '无',
                '今天',
                '本月',
                '24h',
                '2天',
                '3天',
                '7天',
                '10天',
                '30天',
              ]"
              :key="time"
              :label="time"
              role="radio"
              :aria-checked="addAiRuleForm.timeRange === time"
              :tabindex="-1"
            >
              {{ time }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <div style="text-align: center">
            <el-button @click="resetForm('addAiRuleForm')">取 消</el-button>
            <el-button type="primary" @click="sendSearchTask">确 定</el-button>
          </div>
        </el-form-item>
      </el-form>
      <!--  </el-form> -->
    </el-dialog>
  </div>
</template>
<script>
import name from "@/plugins/name";

export default {
  data() {
    return {
      dialogAddAiVisible: false,
      addAiRuleForm: {
        name: "",

        timeRange: "无",
        queryMode: "match",
        queryString: "",
      },
      addAiRules: {
        name: [
          { required: true, message: "请输入内容", trigger: "blur" },
          /* { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }, */
        ],
        queryString: [
          { required: true, message: "请输入字符串", trigger: "blur" },
          /* { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }, */
        ],
      },
      checkLists: ["username", "public", "authority"],
      queryStringActiveName: "first",
      queryStringtextarea: "",
      dynamicValidateForm: {
        domains: [
          {
            value: "",
          },
        ],
      },

      pickerOptions: {
        disabledDate(time) {
          // time 表示的是面板中每一个日期值
          // >Date.now() 只能选择今天以及今天之后的时间
          return time.getTime() > Date.now();
        },
      },

      timeList: [
        "今天",
        "24h",
        "3天",
        "7天",
        "本月",
        "30天",
        "无",
        "自定义时间",
      ],
      rangeList: [
        { type: "content_article", text: "内容查询" },
        { type: "title", text: "标题查询" },
        { type: "type", text: "媒体查询" },
        { type: "author_id", text: "作者查询" },
      ],
      ppList: [
        { type: "match_phrase", text: "精确匹配" },
        { type: "match", text: "分词匹配" },
        { type: "wildcard", text: "分词模糊匹配" },
        { type: "expression", text: "表达式匹配" },
        { type: "regexp", text: "正则匹配" },
      ],
      keyWordList: ["-", "(", ")", "+", "|"],
    };
  },
  mounted() {},
  computed: {
    customTime: {
      get() {
        return this.$store.state.search.conditions.conditionsData.customTime;
      },
      set(val) {
        this.$store.commit("search/conditions/setCustomTime", [
          val[0],
          val[1] + 24 * 3600000 - 1000,
        ]);
      },
    },
    timeRange: {
      get() {
        return this.$store.state.search.conditions.conditionsData.timeRange;
      },
      set(val) {
        this.$store.commit("search/conditions/setTimeRange", val);
      },
    },
    queryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryMode", val);
      },
    },
    queryString: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryString;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryString", val);
      },
    },
    ppqueryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.ppqueryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setPPQueryMode", val);
      },
    },
  },
  methods: {
    // 点击添加符号
    addTaskSymbol(symbol) {
      const input = this.$refs.taskKeyInput.$refs.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      const currentValue = this.addAiRuleForm.queryString;

      // 在光标位置插入符号
      this.addAiRuleForm.queryString =
        currentValue.substring(0, startPos) +
        symbol +
        currentValue.substring(endPos);

      // 更新光标位置到插入符号后
      this.$nextTick(() => {
        input.setSelectionRange(
          startPos + symbol.length,
          startPos + symbol.length
        );
        input.focus();
      });
    },
    sendSearchTask() {
      this.$refs["addAiRuleForm"].validate((valid) => {
        if (!valid) {
          return false;
        }

        // 检查查询字符串是否为空
        if (
          !this.addAiRuleForm.queryString ||
          this.addAiRuleForm.queryString.length === 0 ||
          this.addAiRuleForm.queryString[0] === ""
        ) {
          this.$message.error("查询字符串不能为空");
          return false;
        }
        this.$store.dispatch("search/conditions/sendSearchTask", {
          task: this.addAiRuleForm,
        });
        this.addAiRuleForm = {
          name: "",
          timeRange: "无",
          queryMode: "match",
          queryString: "",
        };
        this.dialogAddAiVisible = false;
      });
    },
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    upCHange() {
      let tmpThis = this;
      var output = document.getElementById("fileRuls");
      console.log("dddd", document.getElementById("fileRuls"));
      console.log("this.$refs.file.files[0]", this.$refs.file.files[0]);
      var reader = new FileReader();
      reader.onload = function (e) {
        // 这个事件发生，意为着数据准备好了
        // 把它复制到页面的<div>元素中

        output.innerHTML = e.target.result.replace(
          new RegExp("\n", "gm"),
          "<br />"
        );
        let arr = [];

        output.innerHTML.split("<br>").forEach((i) => {
          arr.push(i.trim());
        });
        tmpThis.task.queryString = arr;
      };
      reader.readAsText(this.$refs.file.files[0]);
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: "",
        key: Date.now(),
      });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    showAddAiDialog() {
      this.dialogAddAiVisible = true;
    },
    handleChange() {
      this.$store.commit("search/conditions/setTimeRange", "自定义时间");
    },
    // 点击添加符号
    addSymbol(symbol) {
      const input = this.$refs.keyInput.$refs.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      const currentValue = this.queryString;

      // 在光标位置插入符号
      this.queryString =
        currentValue.substring(0, startPos) +
        symbol +
        currentValue.substring(endPos);

      // 更新光标位置到插入符号后
      this.$nextTick(() => {
        input.setSelectionRange(
          startPos + symbol.length,
          startPos + symbol.length
        );
        input.focus();
      });
    },
    //发送搜索
    gofn() {
      this.$store.commit("search/searchList/sendSearchData");
      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      window.main.$store.commit(
        "search/twLinFacSearch/setAddEsQueryConditions",
        {
          bool: {
            must: [tmpCondition],
          },
        }
      );
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: [tmpCondition],
        },
      });
      window.main.$router.push({ name: "searchList" });
    },
  },
};
</script>
<style lang="scss" scoped>
.search {
  position: relative;
  .creatTask {
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

:deep(.custom-dialog) {
  position: relative;
  z-index: 3000 !important;

  // 添加表单内容的样式
  .el-form {
    .el-input__inner,
    .el-textarea__inner,
    .el-radio__label,
    .el-checkbox__label {
      // color: #8c8c8c; // 使用较浅的灰色
    }

    // 保持label字体颜色不变
    .el-form-item__label {
      color: #606266; // 默认的label颜色
    }
  }
}

/* :deep(.v-modal) {
  position: fixed !important;
  z-index: 2999 !important;
  opacity: 0.5;
  background: #000;
}
 */
:deep(.el-dialog__wrapper) {
  position: fixed !important;
  z-index: 2998 !important;
}
</style>
