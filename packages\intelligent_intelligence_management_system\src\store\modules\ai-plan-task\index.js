import { task_id } from "@/i18n/zh/list";

export default {
  namespaced: true,
  state: {
    templateList: [], // 模板列表
    planTaskList: [], // 任务列表
    planTaskListObj: {},
    continueToken: "",
    size: 20,
  },
  mutations: {
    // 重置所有数据
    resetPlanTaskData(state) {
      state.planTaskList = [];
      state.continueToken = "";
    },

    // 获取任务模板列表
    getPlanTaskTemplateList(state) {
      window.main.$constant_socket.sendData(
        "Api.Node.ChildNodeList",
        [
          {
            msg: {
              nodePath: "/comm/web/cronjob",
            },
          },
        ],
        (res) => {
          let msg = {};
          for (let i = 0; i < res.length; i++) {
            msg = {
              ...msg,
              ["/comm/web/cronjob/" + res[i]]: "",
            };
          }
          window.main.$constant_socket.sendData(
            "Api.Node.NodeData",
            [
              {
                head: {},
                msg: msg,
              },
            ],
            (res2) => {
              console.log("res3333", res2);
              let templateList = [];
              for (let str in res2) {
                let keyname = str.split("/")[str.split("/").length - 1];
                templateList.push({ ...res2[str], value: keyname });
              }
              state.templateList = templateList;
            }
          );
        }
      );
    },

    // 获取一个任务下的计划任务列表
    getPlanTaskList(state, v) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.List",
        [
          {
            head: {
              size: state.size,
            },
            msg: {
              task_id: v.row,
              /*  limit: state.size,
              continue: state.continueToken, */
            },
          },
        ],
        (res) => {
          state.planTaskListObj = {
            ...state.planTaskListObj,
            [v.row]: res.items,
          };
        }
      );
    },

    // 创建计划任务
    createPlanTask(state, task) {
      console.log("createPlanTask", task);
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Create",
        [
          {
            head: {},
            msg: {
              cronjob_template_number: task.cronjob_template_number,
              schedule: task.schedule,
              // describe: task.describe,
              // environment: task.environment,
              task_id: task.task_id,
              task_type: task.task_type,
              annotations: {
                week: task.week,
                time: task.time,
                task_name: task.task_name,
                describe: task.describe,
                task_id: task.task_id,
              },
              /*  parameter: {
                task_name: task.task_name
              } */
            },
          },
        ],
        (res) => {
          console.log("createPlanTask", res);
          if (res?.metadata != null) {
            window.main.$message.success("创建成功");
            window.main.$store.commit("aiPlanTask/resetPlanTaskData");
            window.main.$store.commit("aiPlanTask/getPlanTaskList", {
              row: task.task_id,
            });
          } else {
            window.main.$message.error("创建失败");
          }
        }
      );
    },

    // 删除任务
    deletePlanTask(state, task) {
      console.log("deletePlanTask", task, task.item, task.item.metadata);
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Delete",
        [
          {
            head: {},
            msg: {
              labels: {
                app: task.item.metadata.labels.app,
              },
            },
          },
        ],
        (res) => {
          window.main.$message.success("删除成功");
          window.main.$store.commit("aiPlanTask/resetPlanTaskData");
          window.main.$store.commit("aiPlanTask/getPlanTaskList", {
            row: task.row.row,
          });
        }
      );
    },

    // 更新任务
    updatePlanTask(state, task) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Create",
        [
          {
            head: {},
            msg: {
              cronjob_name: task.cronjob_name,
              cronjob_template_number: task.fromData.templateNumber,
              schedule: task.fromData.cronExpression,
              task_id: task.fromData.taskId,
              task_type: task.fromData.taskType,
              annotations: {
                week: task.fromData.weekValue,
                time: task.fromData.timeValue,
                task_name: task.fromData.task_name,
                describe: task.fromData.describe,
                task_id: task.fromData.taskId,
              },
            },
          },
        ],
        (res) => {
          console.log("updatePlanTask", res);
          if (res?.metadata != null) {
            window.main.$message.success("更新成功");
            window.main.$store.commit("aiPlanTask/resetPlanTaskData");
            window.main.$store.commit("aiPlanTask/getPlanTaskList", {
              row: task.fromData.taskId,
            });
          } else {
            window.main.$message.error("更新失败");
          }
        }
      );
    },

    // 获取任务详情
    getPlanTaskDetail(state, task) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Detail",
        [
          {
            head: {},
            msg: {
              cronjob_name: task.cronjob_name,
            },
          },
        ],
        (res) => {
          console.log("getPlanTaskDetail", res);
        }
      );
    },
  },
  actions: {},
  modules: {},
};
