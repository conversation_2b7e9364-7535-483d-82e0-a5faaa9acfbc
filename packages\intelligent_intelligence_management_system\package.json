{"name": "intelligent_intelligence_management_system", "version": "1.0.0", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@certifaction/vue-pdf-viewer": "^1.7.2", "@riophae/vue-treeselect": "^0.4.0", "@vue/composition-api": "^1.7.2", "@wangeditor/editor": "^5.1.1", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.21.4", "bootstrap": "^5.3.3", "certificate-login": "workspace:*", "core-js": "^3.41.0", "crypto-js": "^4.2.0", "dc-websocket-jsonrpc": "workspace:*", "docx": "^8.5.0", "echarts": "^4.9.0", "echarts-gl": "^1.1.0", "echarts-v5": "npm:echarts@^5.2.0", "echarts-wordcloud": "^1.1.3", "el-table-infinite-scroll": "^2.0.2", "element-ui": "^2.15.13", "emailjs-mime-parser": "^2.0.7", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "mammoth": "^1.5.1", "markdown-it": "^14.1.0", "mavon-editor": "^2.10.4", "mdast-util-gfm-autolink-literal": "^1.0.0", "mdast-util-gfm-footnote": "^1.0.0", "message_system": "workspace:*", "normalize.css": "^7.0.0", "nprogress": "^0.2.0", "openai": "^3.3.0", "path-to-regexp": "^2.4.0", "quoted-printable": "^1.0.1", "sha512": "0.0.1", "utf8": "^3.0.0", "uuid": "^11.1.0", "view-design": "^4.7.0", "vue": "^2.6.10", "vue-cookies": "^1.8.6", "vue-i18n": "^8.15.3", "vue-json-pretty": "^1.9.5", "vue-json-viewer": "^2.2.22", "vue-masonry": "^0.10.5", "vue-pdf": "^4.2.0", "vue-router": "^3.2.0", "vuex": "^3.4.0", "warning_system": "workspace:*", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "^4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "^4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "copy-webpack-plugin": "6.4.1", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mdast-util-gfm": "^3.1.0", "mockjs": "1.0.1-beta3", "remark-gfm": "^4.0.1", "runjs": "4.3.2", "sass": "^1.26.8", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.10", "webpack": "4.47.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}