<template>
  <div class="task-queue">
    <!-- 顶部筛选区域 -->
    <div class="filter-area">
      <el-button type="danger" size="small" @click="handleDeleteSelected"
        >删除</el-button
      >

      <div class="task-status">
        <span>任务状态：</span>
        <el-radio-group v-model="state_value">
          <el-radio
            v-for="item in analysisState"
            :key="item.label"
            :label="item.value"
            @change="handleRadioChanges(item)"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 任务列表 -->
    <el-table
      :data="showTaskList"
      style="width: 100%"
      :height="'80%'"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        prop="columnValues.info.title"
        label="任务名"
        width="400"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="columnValues.info.method"
        label="任务类型"
        width="300"
        align="center"
        :formatter="formatTaskMethod"
      >
      </el-table-column>
      <el-table-column
        prop="columnValues.info.status"
        label="任务状态"
        width="300"
        align="center"
        :formatter="formatTaskStatus"
      ></el-table-column>
      <!-- <el-table-column
        prop="columnValues.parm.template.ai_template_name"
        label="模板名称"
        align="center"
      >
      </el-table-column> -->
      <!-- <el-table-column label="任务进度" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.columnValues.info.all_num" style="color: #ccc"
            >无</span
          >
          <div class="myprogress" v-if="scope.row.columnValues.info.all_num">
            <div
              class="myprogressreceived"
              :style="{
                width:
                  Number(
                    ((Number(scope.row.columnValues.info.all_num_received) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100)
                      | mytoFixed
                  ) + '%',
              }"
            >
              <div class="textspan">
                {{
                  Number(
                    ((Number(scope.row.columnValues.info.all_num_received) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100)
                      | mytoFixed
                  ) + "%"
                }}
              </div>
            </div>
            <div
              class="myprogressreceivedasync"
              v-if="scope.row.columnValues.info.all_num_received_sync"
              :style="{
                width:
                  Number(
                    ((Number(
                      scope.row.columnValues.info.all_num_received_sync
                    ) /
                      Number(scope.row.columnValues.info.all_num)) *
                      100 -
                      3)
                      | mytoFixed
                  ) + '%',
              }"
            >
              {{
                Number(
                  ((Number(scope.row.columnValues.info.all_num_received_sync) /
                    Number(scope.row.columnValues.info.all_num)) *
                    100)
                    | mytoFixed
                ) + "%"
              }}
            </div>
            <div
              class="myprogressreceivedasync"
              v-if="scope.row.columnValues.info.all_num"
              style="background: none; left: 15px"
            >
              {{ scope.row.columnValues.info.all_num_received }}
            </div>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button-group>
            <!-- <el-button
              size="mini"
              type="primary"
              v-if="
                scope.row.columnValues.info.status == 'parse_end' &&
                Number(
                  ((Number(scope.row.columnValues.info.all_num_received) /
                    Number(scope.row.columnValues.info.all_num)) *
                    100) |
                    mytoFixed
                ) === 100
              "
              @click="handleView(scope.row)"
            >
              查看
            </el-button> -->
            <el-button
              size="mini"
              type="success"
              v-if="
                scope.row.columnValues.info.status == 'parse_end' &&
                Number(
                  ((Number(scope.row.columnValues.info.all_num_received) /
                    Number(scope.row.columnValues.info.all_num)) *
                    100) |
                    mytoFixed
                ) === 100
              "
              @click="handleDownload(scope.row)"
            >
              下载
            </el-button>
            <!-- <el-button
              size="mini"
              type="danger"
              @click="handleLogs(scope.row.row)"
            >
              日志
            </el-button> -->
            <el-button
              size="mini"
              type="danger"
              @click="handleMarkdownView(scope.row.row, scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="page-box" v-if="getTotalCount > 0">
      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="getTotalCount"
      >
      </el-pagination>
    </div>
    <!-- 日志对话框 -->
    <!-- <el-dialog
      :visible.sync="logsDialogVisible"
      append-to-body
      width="80%"
      top="20px"
      title="日志列表"
    >
      <div
        ref="scroll"
        @scroll="handleScroll"
        class="hitsLayscroll"
        v-loading="logList.length > 0 ? false : true"
      >
        
        <div class="chat-content" ref="chatContent">
          <li v-for="(item, index) in logList" :key="index">
            <window-session-content
              :role="item.role"
              :content-show-type="windowData.contentShowType"
              :item-data="item"
            ></window-session-content>
          </li>
        </div>
      </div>
    </el-dialog> -->
    <!-- markdown展示 -->
    <el-dialog
      :visible.sync="markdownDialogVisible"
      append-to-body
      width="80%"
      top="20px"
      title="任务详情"
    >
      <div>
        <div class="MDbutton">
          <div style="margin-right: 15px">
            <el-button type="primary" @click="exportMD">导出</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveMD">保存</el-button>
          </div>
        </div>
        <div class="MDinput" style="overflow-y: auto">
          <component
            :is="detail_key"
            v-for="(detail_val, detail_key) in taskDetail"
            :key="detail_key"
          ></component>

          <!-- <mavon-editor
            v-model="markdownData"
            :externalLink="externalLink"
            style="height: 100%"
            :editable="false"
            :subfield="false"
            :toolbarsFlag="false"
            :defaultOpen="'preview'"
          /> -->
          <!-- <MarkdownView
            style="height: 100%"
            ref="mv"
            :content="markdownData"
          ></MarkdownView> -->
        </div>
      </div>
    </el-dialog>
    <!-- markdown编辑 -->
    <el-dialog
      :visible.sync="buildBri"
      append-to-body
      width="80%"
      top="20px"
      title="任务详情"
    >
      <div>
        <div class="MDbutton">
          <div style="margin-right: 15px">
            <el-button type="primary" @click="exportMD">导出</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveMD">保存</el-button>
          </div>
        </div>
        <div class="MDinput">
          <mavon-editor
            v-model="MDcontent"
            :externalLink="externalLink"
            style="height: 100%"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 保存情报对话框 -->
    <el-dialog
      title="保存情报"
      :visible.sync="favoriteDataVisible"
      @close="clearFavoriteData"
      width="40%"
      append-to-body
    >
      <el-form>
        <el-form-item label="情报标题：">
          <el-input
            style="width: 80%"
            placeholder="请输入情报标题"
            v-model="taskTitle"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务名称：">
          <!-- <p class="fileName">{{ currentTask?.columnValues?.info?.title || '无标题任务' }}</p> -->
        </el-form-item>
        <el-form-item label="类型选择：">
          <el-select v-model="taskCategory" placeholder="请选择类型">
            <el-option
              v-for="item in taskCategories"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="clearFavoriteData">取 消</el-button>
        <el-button type="primary" @click="saveTaskData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MarkdownView from "@/components/MarkdownView/index";
import { mapState, mapMutations } from "vuex";
import ContentShowType from "@/common/constants/ContentShowType";
import WindowSessionContent from "@/components/session/window/chat/WindowSessionContent";
const windowData = {
  contentShowType: ContentShowType.Markdown,
};
export default {
  name: "TaskQueue",
  data() {
    return {
      markdownData: "",
      markdownDialogVisible: false,
      windowData,
      selectedTasks: [],
      state_value: "all",
      currentPage: 1,
      pageSize: 20,
      buildBri: false, // 编辑markdown对话框显示状态
      MDcontent: "", // markdown内容
      currentTask: null, // 当前查看的任务
      favoriteDataVisible: false, // 保存情报对话框显示状态
      taskTitle: "", // 情报标题
      taskCategory: "report", // 默认情报类型
      taskCategories: [
        { label: "简报", value: "report" },
        { label: "分析", value: "analysis" },
        { label: "情报", value: "intelligence" },
      ],
      method: [
        {
          value: "generate_public_opinion_report_from_collection",
          label: "生成舆情简报",
        },
      ],
      analysisState: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "parse_end",
          label: "已完成",
        },
        {
          value: "parsing",
          label: "进行中",
        },
        {
          value: "error",
          label: "失败",
        },
      ],
      analysisAllState: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "parse_end",
          label: "已完成",
        },
        {
          value: "parsing",
          label: "进行中",
        },
        {
          value: "error",
          label: "失败",
        },
        {
          value: "parse_ready",
          label: "待解析",
        },
      ],
      externalLink: {
        markdown_css: function () {
          return "/mavon-editor/markdown/github-markdown.min.css";
        },
        hljs_js: function () {
          return "/mavon-editor/highlightjs/highlight.min.js";
        },
        hljs_css: function (css) {
          return "/mavon-editor/highlightjs/styles/" + css + ".min.css";
        },
        hljs_lang: function (lang) {
          return "/mavon-editor/highlightjs/languages/" + lang + ".min.js";
        },
        katex_css: function () {
          return "/mavon-editor/katex/katex.min.css";
        },
        katex_js: function () {
          return "/mavon-editor/katex/katex.min.js";
        },
      },
      refreshTimer: null,
    };
  },
  components: {
    WindowSessionContent,
    MarkdownView,
    bulletin: () => import("./task_detail/bulletin.vue"),
    article_category: () => import("./task_detail/article_category.vue"),
    article_list: () => import("./task_detail/article_list.vue"),
  },
  computed: {
    ...mapState({
      taskLoading: (state) => state.aiTaskQueue.taskLoading,
      showTaskList: (state) => state.aiTaskQueue.showTaskList,
      from: (state) => state.aiTaskQueue.from,
      allTaskListCount: (state) => state.aiTaskQueue.allTaskListCount,
      parseReadyTaskListCount: (state) =>
        state.aiTaskQueue.parseReadyTaskListCount,
      parseEndTaskListCount: (state) => state.aiTaskQueue.parseEndTaskListCount,
      parseErrorTaskListCount: (state) =>
        state.aiTaskQueue.parseErrorTaskListCount,
      logList: (state) => state.aiTaskQueue.logList,
      taskDetail: (state) => state.aiTaskQueue.taskDetail,
    }),
    mytoFixed: function (num) {
      num *= Math.pow(10, 2);
      num = Math.round(num);
      return num / Math.pow(10, 2);
    },
    getTotalCount() {
      switch (this.state_value) {
        case "parse_end":
          return this.parseEndTaskListCount;
        case "processing":
          return this.parseReadyTaskListCount + this.parseParsingTaskListCount;
        case "error":
          return this.parseErrorTaskListCount;
        default:
          return this.allTaskListCount;
      }
    },
    logsDialogVisible: {
      get(val) {
        return this.$store.state.aiTaskQueue.logsDialogVisible;
      },
      set(nval) {
        this.$store.commit("aiTaskQueue/setLogsDialogVisible", nval);
      },
    },
  },
  filters: {
    mytoFixed: function (num) {
      num *= Math.pow(10, 2);
      num = Math.round(num);
      return num / Math.pow(10, 2);
    },
  },
  created() {
    this.resetTaskState();
    this.sendListAnalysisList();
  },
  methods: {
    ...mapMutations({
      sendListAnalysisList: "aiTaskQueue/sendListAnalysisList",
      resetTaskState: "aiTaskQueue/resetTaskState",
      setTaskStatus: "aiTaskQueue/setTaskStatus",
      setPageAndData: "aiTaskQueue/setPageAndData",
      sendDelTask: "aiTaskQueue/sendDelTask",
    }),
    //简报handleSummary
    handleMarkdownView(v1, v2) {
      console.log(v1, v2);
      const routeData = window.main.$router.resolve({
        path: `/taskDetail/${v1}/${v2.columnValues.info.title}`,
      });
      window.open(routeData.href, "_blank");
      /* window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListDatas",
        [
          {
            head: {
              // row_key: state.logLastRowKey,
              size: 200,
              //qualifier:state.logQualifier,
            },
            msg: {
              task_authority: "username",
              task_id: v,
              task_type: "ai_workflow_task",
            },
            //msg:{task_type:['social_platform_task'], familys:['logs']}
          },
        ],
        (res) => {
          let objData = new Object();
          let a = 0;
          for (let i = 0; i < res.length; i++) {
            Object.defineProperty(objData, res[i].columnValues.d["_"], {
              value: res[i].columnValues.d.data,
              writable: true, // 允许修改
              enumerable: true, // 可枚举（for...in 或 Object.keys() 可见）
              configurable: true, // 允许删除或重新定义
            });
            a++;
          }
          if (a === res.length) {
            this.$store.commit("aiTaskQueue/setTaskDetail", objData);
            this.markdownDialogVisible = true;
          }

          
        }
      ); */
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.$store.commit("aiTaskQueue/setPageAndData", val);
    },

    // 切换任务状态
    handleRadioChanges(item) {
      this.state_value = item.value;
      this.setTaskStatus(item.value);

      // 新增：处理定时器
      if (item.value === "processing") {
        if (this.refreshTimer) clearInterval(this.refreshTimer);
        // 启动定时器，每5秒刷新
        this.refreshTimer = setInterval(() => {
          // 每次刷新前重置任务状态，防止数据叠加
          this.$store.commit("aiTaskQueue/resetTaskState");
          this.sendListAnalysisList();
        }, 5000);
        // 立即刷新一次，防止首次切换等待5秒
        this.$store.commit("aiTaskQueue/resetTaskState");
        this.sendListAnalysisList();
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        }
      }
    },

    // 删除选中的任务
    handleDeleteSelected() {
      if (this.selectedTasks.length === 0) {
        this.$message.warning("请先选择要删除的任务");
        return;
      }
      this.$confirm("确认删除选中的任务?", "提示", {
        type: "warning",
      })
        .then(() => {
          const taskIds = this.selectedTasks.map((task) => task.id);
          this.sendDelTask(taskIds);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 选中任务
    handleSelectionChange(val) {
      this.selectedTasks = val;
    },

    // 操作任务---查看任务
    handleView(task) {
      console.log("查看任务", task);
      this.currentTask = task;
      this.MDcontent = task.columnValues?.info?.content || "暂无内容";
      this.buildBri = true;
    },

    // 操作任务---下载任务
    handleDownload(task) {
      this.$message.success(`开始下载任务: ${task.name}`);
    },
    // 操作任务---查看日志
    handleLogs(task) {
      this.$store.commit("aiTaskQueue/setClearLogTemporaryList");
      this.$store.commit("aiTaskQueue/setClearLogList");
      this.$store.commit("aiTaskQueue/setClearLastRowKey");
      this.$store.commit("aiTaskQueue/sendGetTaskLog", task);
    },
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        /*  this.loadData(); */
      }
    },
    // 操作任务---删除任务
    handleDelete(task) {
      console.log("删除任务", task);
      this.$confirm(`确认删除任务"${task.columnValues.info.title}"?`, "提示", {
        type: "warning",
      })
        .then(() => {
          this.sendDelTask([task.row]);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 导出markdown文件
    exportMD() {
      const blob = new Blob([this.MDcontent], { type: "text/markdown" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `任务_${
        this.currentTask?.columnValues?.info?.title || "untitled"
      }_${Date.now()}.md`;
      link.click();
      URL.revokeObjectURL(link.href);
    },

    // 保存markdown内容
    saveMD() {
      this.taskTitle =
        this.currentTask?.columnValues?.info?.title || "无标题任务";
      this.favoriteDataVisible = true;
    },

    // 取消保存操作
    clearFavoriteData() {
      this.favoriteDataVisible = false;
      this.taskTitle = "";
    },

    // 保存任务数据
    saveTaskData() {
      if (!this.taskTitle.trim()) {
        this.$message.warning("请输入情报标题");
        return;
      }

      // 构建要保存的数据
      const prefix =
        1e13 -
        Math.round(new Date().getTime() / 1000) +
        (this.currentTask?.row || Date.now());

      // 发送数据到服务器
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            msg: {
              type: "task",
              task_id: this.currentTask?.row || "",
              table: "ai_task_intelligence",
              prefix,
              data: {
                data: {
                  file_data: {
                    title: this.taskTitle,
                    content: this.MDcontent,
                    category: this.taskCategory,
                    createTime: Date.now(),
                    taskInfo: this.currentTask?.columnValues?.info || {},
                  },
                },
              },
            },
          },
        ],
        (res) => {
          console.log(res);
          if (res.status === "ok") {
            this.$message({
              type: "success",
              message: "保存成功",
            });
            this.favoriteDataVisible = false;
            this.buildBri = false;
          } else {
            this.$message.error("保存失败：" + (res.message || "未知错误"));
          }
        }
      );
    },

    formatTaskStatus(row) {
      const status = row.columnValues?.info?.status;
      const state = this.analysisAllState.find((item) => item.value === status);
      return state ? state.label : "未知";
    },

    formatTaskMethod(row) {
      const method = row.columnValues?.info?.method;
      const zh = this.method.find((item) => item.value === method);
      return zh ? zh.label : "未知任务类型";
    },
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.hitsLayscroll {
  height: 700px;
  overflow-y: scroll;

  .chat-content {
    width: 100%;
    padding: 8px 0 14px 0;
    box-sizing: border-box;
    flex-grow: 1;
    .chat-main-content {
      width: 100%;
      display: flex;
    }

    li {
      list-style: none;
      height: auto;
      width: 1020px;
      margin: 0 auto;
      display: flex;
    }
  }
}
.task-queue {
  padding: 20px;
  height: 100%;
}

.page-box {
  width: 50%;
  margin-left: 30%;
}

.filter-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  height: 5%;
}

.task-status {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.status-completed {
  color: #67c23a;
}

.status-processing {
  color: #409eff;
}

.status-failed {
  color: #f56c6c;
}

.MDbutton {
  display: flex;
  height: 6vh;
  justify-content: end;
  align-items: center;
  padding-right: 20px;
}

.MDinput {
  border: 1px solid #ccc;
  height: 77vh;
  display: flex;
  flex-direction: column;
}

.file {
  display: flex;
  align-items: center;
  margin-right: 15px;
  position: relative;

  span {
    display: inline-block;
    padding: 7px 15px;
    background-color: #409eff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  }

  input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}

.myprogress {
  width: 100%;
  height: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.myprogressreceived {
  height: 100%;
  background: #409eff;
  text-align: center;
  font-size: 12px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;

  .textspan {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.myprogressreceivedasync {
  height: 100%;
  background: #67c23a;
  position: absolute;
  top: 0;
  font-size: 12px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fileName {
  margin: 0;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 对话框样式 */
::v-deep .el-form-item__label {
  font-weight: bold;
}

/* 隐藏 mavon-editor 滚动条 */
::v-deep .mavon-editor {
  .v-note-wrapper {
    .v-note-panel {
      .v-note-show {
        .v-note-show-content {
          .v-note-show-content-wrapper {
            .v-note-show-content-wrapper-content {
              &::-webkit-scrollbar {
                display: none;
              }
              -ms-overflow-style: none;
              scrollbar-width: none;
            }
          }
        }
      }
    }
  }
}

/* 隐藏 mavon-editor 编辑区域的滚动条 */
::v-deep .mavon-editor .v-note-wrapper .v-note-panel .v-note-edit {
  .v-note-edit-wrapper {
    .v-note-edit-wrapper-content {
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }
}

/* 隐藏 mavon-editor 预览区域的滚动条 */
::v-deep .mavon-editor .v-note-wrapper .v-note-panel .v-note-show {
  .v-note-show-content {
    .v-note-show-content-wrapper {
      .v-note-show-content-wrapper-content {
        &::-webkit-scrollbar {
          display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    }
  }
}
</style>
