let list = require("./list.js");
let common = require("./common.js");
let analysis = require("./analysis.js");
let chart = require("./chart.js");
let caseManage = require("./caseManage.js");
let overview = require("./overview.js");
let information = require("./information.js");
let AnalysisTask = require("./AnalysisTask.js");
import store from "@/store";

export default {
  zh: {
    publics: { "@timestamp": "入库时间" },
    information,
    list,
    common,
    analysis,
    chart,
    caseManage,
    overview,
    AnalysisTask,
  },
};
