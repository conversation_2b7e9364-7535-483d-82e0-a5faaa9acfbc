@import './transition.scss';
html,
body,
#app {
  height: 100%;
  font-family: "Microsoft YaHei";
  margin: 0px;
}
/* 全局修正 */
/* .v-modal {
  z-index: calc(var(--el-dialog-z-index) - 1) !important;
} */

.main__body {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 21;
  padding-top: 5.5vh;
  box-sizing: border-box;
}

.header-button-box {
  position: absolute;
  top: 0;
  right: 3%;
  height: 5vh;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row-reverse;
  z-index: 25;
}
.header-container{
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-end;
  height: 100%;
}

.option-container {
  width: 100%;
}



.user-container {
  height: 100%;

  align-items: center;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  line-height: 2.5vh;
  color: rgba(200, 200, 200, 1);
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease; /* Safari */
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.user-container:hover {
  cursor: pointer;
  color: rgba(240, 240, 240, 1);
}



.option-list,
.option-list-hide {
  z-index: 26;
  position: relative;
  width: 100%;
  pointer-events: none;
  display: inline-block;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  max-height: 450px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #ccc;
  //box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.35);
  //color: rgba(255, 255, 255, 1);
  -ms-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

.option-list-hide {
  max-height: 0;
  border: none;
}

.option-item-container {
  pointer-events: all;
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  display: flex;
  flex-wrap: nowrap;
  text-align: center;
}

.top-line {
  position: absolute;
  top: 0;
  width: 100%;
  border-top: 1px solid #eee;
}

.option-item-container:hover {
  cursor: pointer;
  background-color: #ecf8ff;
}

.option-icon {
  margin-left: 10px;
}

.option-name {
  margin-left: 10px;
}

#navBar {
  position: fixed;
  width: 100%;
  height: 5vh;
  left: 0;
  right: 0;
  margin:0 auto;
  top: 0;
  display: flex;
  align-items: center;  
  z-index: 10;
  padding: 5px;
  background: #fff;
  padding-left: 40%;
  padding-right: 1%;
  border-bottom: 0.5px solid #eee;
}
