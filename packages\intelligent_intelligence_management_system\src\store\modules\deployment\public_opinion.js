export default {
  namespaced: true,
  state: {
    nodeList: {},
    dataList: [],
  },
  mutations: {
    setNodeList(state, res) {
      if (res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          state.nodeList[`/etc/kappa/news/kappa_news_spider/${res[i]}`] = "";
        }
        this.dispatch("deployment/public_opinion/getDataList", state.nodeList);
      } else {
      }
    },
    setDataList(state, data) {
      state.nodeList = {};
      let a = [];
      for (let i in data) {
        data[i]["node"] = i;
        a.push(data[i]);
      }
      state.dataList = a;
      console.log("setNodeListres", state.dataList);
    },
  },
  actions: {
    //先查询节点列表
    getNodeList({ state, commit }, v) {
      window.main.$constant_socket.sendData(
        "Api.Node.ChildNodeList",
        [
          {
            head: {},
            msg: {
              nodePath: "/etc/kappa/news/kappa_news_spider",
            },
          },
        ],
        "deployment/public_opinion/setNodeList"
      );
    },
    getDataList({ state, commit }, data) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            head: {},
            msg: data,
          },
        ],
        "deployment/public_opinion/setDataList"
      );
    },
  },
};
