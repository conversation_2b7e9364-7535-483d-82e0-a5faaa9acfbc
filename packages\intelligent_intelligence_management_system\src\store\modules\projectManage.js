export default {
  namespaced: true,
  state: { dataList: [] },
  mutations: {
    //初始化数据
    resetAllData(state) {
      state.dataList = [];
    },
    // 获取oss模板数据
    getProjectTemplate(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 20,
            },
            control: {
              query_string: "",
              query_type: "username",
              condition: {
                query_mode: "match",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [],
                },
              },
            },
            msg: {
              data_range_index_name:
                "intelligen_intelligence_management_project_management",
            },
          },
        ],
        "projectManage/setProjectTemplate"
      );
    },
    // 设置oss模板数据
    setProjectTemplate(state, data) {
      console.log("setProjectTemplate:", data);

      if (
        !data?.hits.hasOwnProperty("hits") ||
        data?.hits?.hits?.length === 0
      ) {
        window.main.$message.warning("没有数据！");
        return;
      }
      data?.hits?.hits?.forEach((item) => {
        item._source.project_content = JSON.parse(item._source.project_content);
        state.dataList.push({
          label: item._source.project_content.label,
          value: item._source.project_content.value,
          index: item._source.project_content.value,
          id: item._id,
        });
      });
    },
    // 创建模板数据
    createTemplate(state, data) {
      let randomNum =
        2 ** 31 -
        new Date().getTime() / 1000 +
        Math.floor(Math.random() * 100000).toString();
      let template_content = JSON.stringify(data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_project_management",
              type: "_doc",
              id: randomNum,
            },
            msg: {
              project_content: template_content,
              type: "all",
            },
          },
        ],
        "projectManage/getCreateTemplateOssStatus"
      );
    },
    // 删除模板
    deleteTemplate(state, data) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.DelOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_project_management",
              type: "_doc",
              id: data.id,
            },
          },
        ],
        "projectManage/getDeleteTemplateOssStatus"
      );
    },
    // 删除模板成功，重新获取
    getDeleteTemplateOssStatus(state, data) {
      console.log(
        "getDeleteOssStatus:",
        data.status === "ok",
        data.status !== "ok"
      );
      if (!data || data?.status !== "ok") {
        window.main.$message.error("模板删除失败！");
        return;
      }
      window.main.$message.success("模板删除成功，正在刷新列表...");
      setTimeout(() => {
        window.main.$store.commit("projectManage/resetAllData");
        window.main.$store.commit("projectManage/getProjectTemplate");
      }, 1000);
    },
    getCreateTemplateOssStatus(state, data) {
      console.log("getCreateTemplateOssStatus:", data);
      if (!data || data.status !== "ok") {
        window.main.$message.error("模板创建失败！");
        return;
      }
      window.main.$message.success("模板创建成功");
      setTimeout(() => {
        window.main.$store.commit("projectManage/resetAllData");
        window.main.$store.commit("projectManage/getProjectTemplate");
      }, 1000);
    },
    // 创建OSS存储表
    createOSSTable(state) {
      console.log("createOSSTable");
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.CreateOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_project_management",
              type: "_doc",
              id: "_",
              field_template: {
                project_content: {
                  type: "text",
                  index: false,
                },
              },
            },
          },
        ],
        "projectManage/getCreateOssStatus"
      );
    },
    // 删除表
    deleteOSSTable(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.DropOss",
        [
          {
            head: {},
            control: {
              query_type: "username",
              index: "intelligen_intelligence_management_project_management",
              type: "_",
              id: "_",
            },
          },
        ],
        "aiAnalyze/getDeleteOssStatus"
      );
    },
    // OSS存储表状态
    getCreateOssStatus(state, data) {
      console.log("getCreateOssStatus:", data);
    },
  },
  actions: {},
};
