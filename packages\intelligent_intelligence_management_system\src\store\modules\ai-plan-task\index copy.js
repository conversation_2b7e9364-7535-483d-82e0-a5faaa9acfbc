export default {
  namespaced: true,
  state: {
    templateList: [], // 模板列表
    planTaskList: [], // 任务列表
    continueToken: "", 
    size: 20,
  },
  mutations: {
    // 重置所有数据
    resetPlanTaskData(state) {
      state.planTaskList = [];
      state.continueToken = "";
    },

    // 获取任务模板列表
    getPlanTaskTemplateList(state) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/comm/web/cronjob": "",
            }
          }
        ],
        (res) => {
          console.log("getPlanTaskTemplateList", res);
          let template = res["/comm/web/cronjob"];
          let templateList = [];
          for (let key in template) {
            templateList.push({
              key: key,
              value: template[key].description || "系统模板"
            });
          }
          state.templateList = templateList;
        }
      )
    },

    // 获取任务列表
    getPlanTaskList(state) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.List",
        [{
          msg: {
            limit: state.size,
            continue: state.continueToken
          }
        }],
        (res) => {
          console.log("getPlanTaskList", res);
          if (res?.items?.length) {
            state.planTaskList.push(...res.items);
            state.continueToken = res.items[res.items.length - 1].metadata.continue;
          }
        }
      );
    },
    
    // 创建任务
    createPlanTask(state, task) {
      console.log("createPlanTask", task);
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Create",
        [
          {
            head: {},
            msg: {
              cronjob_template_number: task.cronjob_template_number,
              schedule: task.schedule,
              describe: task.describe,
              environment: task.environment,
              task_id: task.environment[0].value,
              task_type: task.environment[0].type,
              parameter: {
                task_name: task.task_name
              }
            }
          },
        ],
        (res) => {
          console.log("createPlanTask", res);
          if (res?.metadata != null) {
            window.main.$message.success("创建成功");
            window.main.$store.commit("aiPlanTask/resetPlanTaskData");
            window.main.$store.commit("aiPlanTask/getPlanTaskList");
          } else {
            window.main.$message.error("创建失败");
          }
        }
      );
    },

    // 删除任务
    deletePlanTask(state, taskId) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Delete",
        [
          {
            head: {},
            msg: {
              cronjob_name: taskId
            }
          },
        ],
        (res) => {
          if (res?.status === "Success") {
            window.main.$message.success("删除成功");
            window.main.$store.commit("aiPlanTask/resetPlanTaskData");
            window.main.$store.commit("aiPlanTask/getPlanTaskList");
          } else {
            window.main.$message.error("删除失败");
          }
        }
      );
    },

    // 更新任务
    updatePlanTask(state, task) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Update",
        [
          {
            head: {},
            msg: {
              cronjob_name: task.id,
              schedule: task.cronExpression,
              describe: task.describe,
              environment: task.environment,
              task_id: task.environment[0].value,
              task_type: task.environment[0].type,
              parameter: {
                task_name: task.task_name
              }
            }
          },
        ],
        (res) => {
          console.log("updatePlanTask", res);
          if (res?.metadata?.annotations?.cronjobTemplateNumber) {
            window.main.$message.success("更新成功");
            window.main.$store.commit("aiPlanTask/resetPlanTaskData");
            window.main.$store.commit("aiPlanTask/getPlanTaskList");
          } else {
            window.main.$message.error("更新失败");
          }
        }
      );
    },

    // 获取任务详情
    getPlanTaskDetail(state, task) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.Detail",
        [
          {
            head: {},
            msg: {
              cronjob_name: task.cronjob_name
            }
          },
        ],
        (res) => {
          console.log("getPlanTaskDetail", res);
        }
      );
    },
  },
  actions: {},
  modules: {},
};
