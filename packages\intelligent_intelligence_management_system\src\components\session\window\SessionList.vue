<template>
  <div class="session-list">
    <div style="display: flex; justify-content: space-between; width: 100%">
      <!-- <div
        class="createSession pointer"
        style="flex: 0 5%; border-radius: 10px"
      >
        <div @click="handleCreateSession" style="">
          <i class="el-icon-plus" style="font-size: 18px"></i>
          <span>新的对话</span>
        </div>
      </div> -->
      <div class="newDiag" @click="handleCreateSession">
        <i class="el-icon-chat-dot-round" style="font-size: 18px"></i>新的对话
      </div>
      <el-button
        size="mini"
        type="text"
        icon="el-icon-delete"
        @click="handleClearSession"
        >清空</el-button
      >
    </div>

    <div
      class="list-box"
      style="flex: 1 1 auto; height: 100%; overflow-y: auto; margin: 10px 0"
    >
      <transition-group name="fade-list-to-right">
        <!-- 每个时间段分类 -->
        <div
          v-for="(group, title) in sessionListGroups"
          :key="title"
          style="margin-top: 20px"
        >
          <h4 v-if="group.length > 0">{{ title }}（{{ group.length }}条）</h4>
          <ul>
            <li
              class="pointer"
              v-for="item in group"
              :key="item.row"
              @click="clickSessionListItem(item)"
              @mouseenter="currentHoverId = item.row"
              @mouseleave="currentHoverId = null"
              :style="{
                background:
                  item.row.split(';')[item.row.split(';').length - 1] ===
                  $store.state.chat.sessionId
                    ? 'rgb(219, 234, 254)'
                    : '',
              }"
            >
              <div class="content-wrapper">
                <template v-if="editingId === item.row">
                  <div @click.stop>
                    <!-- 添加点击事件阻止冒泡 -->
                    <input
                      ref="editInput"
                      v-model="editContent"
                      @keyup.enter="handleSave(item)"
                      @keyup.esc="handleCancel"
                      @blur="handleSave(item)"
                      class="edit-input"
                    />
                  </div>
                </template>
                <template v-else>
                  <div class="title-text">
                    {{
                      item.columnValues.hasOwnProperty("d") &&
                      item.columnValues.d.hasOwnProperty("title")
                        ? item.columnValues.d.title
                        : "无标题"
                    }}
                  </div>
                </template>
              </div>
              <!-- 操作按钮区域 -->
              <div class="action-buttons">
                <!-- <template v-if="editingId === item.row">
                  <button @click.stop="handleSave()" class="confirm-btn">
                    保存
                  </button>
                  <button @click.stop="handleCancel" class="cancel-btn">
                    取消
                  </button>
                </template> -->
                <template v-if="editingId != item.row">
                  <button
                    v-show="currentHoverId === item.row"
                    @click.stop="handleEdit(item)"
                    class="edit-btn"
                  >
                    编辑
                  </button>

                  <button
                    v-show="currentHoverId === item.row"
                    @click.stop="handleDelete(item.row)"
                    class="delete-btn"
                  >
                    删除
                  </button>
                </template>
              </div>
            </li>
          </ul>
        </div>
      </transition-group>
    </div>
    <!-- <div class="session-bottom w100" style="flex: 0 15%">
      <div class="loading-more pointer" @click="handleLoadingMore">
        <span>加载更多</span>
      </div> 
       <div class="flex mg-top-sm bd-bottom pd-bottom-xs"></div> 
    </div> -->
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import editor from "mavon-editor";
import { mapState } from "vuex";
export default {
  name: "SessionList",
  props: {
    windowData: {
      type: Object,
      default: () => {},
    },
    sessionData: {
      type: Object,
      default: () => {},
    },
    loading: { type: Boolean, default: false },
    type: { type: String, default: null },
    clickSessionId: { type: String, default: "" },
    domainUniqueKey: { type: String, default: null },
    drawUniqueKey: { type: String, default: null },
  },
  data() {
    return {
      scrollFlag: false,
      currentHoverId: null, // 当前悬停的条目ID
      editingId: null, // 正在编辑的条目ID
      editContent: "", // 编辑中的内容
      editorItem: null,
      /* ifTourist: this.$store.getters.ifTourist === '1',
        staticUrl: this.$store.getters.resourceMain.staticWebsite,
        imgHeader: this.$store.getters.imgHeader, */
      queryParam: {
        page: 1,
        size: 10,
      },
      isLogin: !!getToken(),
      thisSessionData: {},
    };
  },
  watch: {},
  created() {},
  mounted() {
    // if (this.loading && this.isLogin) {
    //   this.getUserSessionList()
    // }
  },
  computed: {
    ...mapState({
      sessionList: (state) => state.chat.sessionList,
      sessionListGroups: (state) => state.chat.sessionListGroups,
    }),
  },
  methods: {
    /*  handleBefore() {
      if (!this.isLogin) {
        this.$message.info("登录后体验更多功能~");
        return false;
      }
      return true;
    }, */
    // 编辑处理
    handleEdit(item) {
      /*   const newContent = prompt("编辑内容", item.content); */

      this.editingId = item.row;
      this.editContent = item.columnValues.d.title;
      this.editorItem = item;
      console.log("editorItem", this.editorItem);
      this.$nextTick(() => {
        this.$refs.editInput[0].focus(); // 自动聚焦输入框
      });
    },
    handleSave(item) {
      // 获取原始标题
      const originalTitle = this.editorItem.columnValues.d.title;
      // 如果编辑内容不为空且与原始内容不同，才进行保存
      if (this.editContent.trim() && this.editContent !== originalTitle) {
        let obj = this.editorItem;
        obj.columnValues.d.title = this.editContent;
        this.$emit("handleEdit", obj);
      }
      this.cancelEdit();
    },

    handleCancel() {
      this.cancelEdit();
    },

    cancelEdit() {
      this.editingId = null;
      this.editContent = "";
    },
    // 删除处理
    handleDelete(item) {
      if (confirm("确定删除该记录？")) {
        this.$emit("handleDelete", item);
      }
    },
    initSessionList() {
      this.sessionList = [];
      this.queryParam = { page: 1, size: 10 };
    },
    getUserSessionList() {
      this.queryParam.type = this.type;
      this.queryParam.domainUniqueKey = this.domainUniqueKey;
      this.queryParam.drawUniqueKey = this.drawUniqueKey;
      this.$api
        .get("/module/session/sessioninfo/getUserSessionList", this.queryParam)
        .then((res) => {
          if (res.status) {
            if (res.data.length > 0) {
              let sessionListIds = this.sessionList.map((item) => item.id);
              res.data.forEach((item) => {
                if (!sessionListIds.includes(item.id)) {
                  this.sessionList.push(item);
                }
              });
              this.scrollFlag = false;
            }
          }
        });
    },
    handleFlushList() {
      this.initSessionList();
      this.getUserSessionList();
    },
    handleLoadingMore() {
      if (this.scrollFlag) {
        this.$message.info("没有更多项了");
        return;
      }
      this.scrollFlag = true;
      this.queryParam.page++;
      this.getUserSessionList();
    },
    clickSessionListItem(item) {
      this.thisSessionData = item;
      this.$emit("clickSessionListItem", item);
    },
    flushRecord() {
      this.$emit("clickSessionListItem", this.thisSessionData);
    },
    handleCreateSession() {
      this.$emit("handleCreateSession");
    },
    handleClearSession() {
      this.$emit("handleClearSession");
    },
  },
};
</script>

<style lang="scss" scoped>
.newDiag {
  line-height: 42px;
  background-color: rgb(219, 234, 254);
  color: #4d6bfe;
  padding: 0 35px;
  border-radius: 10px;
  cursor: pointer;
}
.newDiag:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  background-color: rgb(203, 224, 251);
  transform: scale(0.99);
}
.session-list {
  width: 100%;
  height: 100%; // 确保容器占满父元素高度
  flex: 1;
  padding: 10px 14px;
  display: flex;
  overflow: hidden;
  align-items: flex-start;
  flex-wrap: nowrap;
  flex-direction: column;
  color: var(--font-color-default);
  background: var(--session-list-background);
  border-right: 1px var(--col-border-color) solid;
  transition: padding 0.5s;
  user-select: none;
  overflow: hidden;
}
.hiddenStatusSession .session-list {
  padding: 0;
}

.createSession {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  background-color: rgb(219, 234, 254);
  color: #4d6bfe;
  cursor: pointer;
}
.createSession:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  background-color: rgb(203, 224, 251);
  transform: scale(0.99);
}
.createSession > div {
  width: 100%;
  height: 32px;
  max-height: 32px;
  line-height: 42px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border: 1px var(--session-list-create-session-border) dashed;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}
.createSession span {
  font-size: 15px;
  padding: 0 2px;
}
.createSession > div:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  transform: scale(0.99);
}

.list-box {
  position: relative;
  width: 100%;
  // 删除下面这两行
  // scrollbar-width: none;
  // -ms-overflow-style: none;
  overflow-x: hidden;
  overflow-y: auto;

  li {
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    list-style: none;
    position: relative;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    transition: background 0.3s;
    &:hover {
      background: #eee;
    }
    .content-wrapper {
      flex: 1;
      min-width: 0;
      .title-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        padding-right: 80px; // 为操作按钮预留空间
      }
    }
    .action-buttons {
      font-size: 12px;
      position: absolute;
      right: 0px;
      top: 50%;
      transform: translateY(-50%);
      /* background: linear-gradient(90deg, transparent 0%, #f8f8f8 20%); */
      padding-left: 20px;
      .edit-input {
        flex: 1;
        padding: 5px;
        border: 1px solid #ddd !important;
        border-radius: 3px;
        font-size: inherit;
        outline: none;
      }
      /*       .edit-btn {
        color: #3a3a3a99;
      } */

      .edit-input:focus {
        /* flex: 1;
        padding: 5px;
        border: 1px solid #ddd !important;
        border-radius: 3px;
        font-size: inherit;
        outline: none; */
        /* border-color: #409eff !important;
        box-shadow: 0 0 3px rgba(64, 158, 255, 0.3) !important; */
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }

      .confirm-btn {
        background: #67c23a;
        color: white;
        border-color: #67c23a;
      }

      .cancel-btn {
        background: #909399;
        color: white;
        border-color: #909399;
      }
    }
  }
  button {
    margin-left: 8px;
    /* padding: 4px 8px; */
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
  }
  button:hover {
    background: #409eff;
    color: white;
    border-color: #409eff;
    opacity: 0.9;
    transform: translateY(-1px);
  }

  li.pointer.itemActive {
    background-image: #eee;
  }

  li span {
    max-width: 75%;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 删除或注释掉这个整个样式块
/* .list-box::-webkit-scrollbar {
  width: 0;
  background: transparent;
  display: none;
} */

::v-deep.list-box li span.iconfont {
  color: var(--font-color-default);
  font-size: 20px;
  margin: 0 4px 0 8px;
}

.session-bottom {
  display: flex;
  flex-direction: column;
}

.loading-more {
  margin: 5px 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px var(--item-border-normal-color) dashed;
  transition: all 0.2s ease;

  &:hover {
    border: 1px dashed var(--session-list-create-session-border-hover);
    transform: scale(0.99);
  }

  span {
    padding: 5px 0;
  }
}

.action {
  right: 0;
  position: absolute;
  height: 100%;
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.edit-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  outline: none;

  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}
</style>
