module.exports = {
  // common
  summary: "描述",
  clue_susername: "线索所属用户",
  clue_susername_authority: "线索所属用户权限",
  create_timestamp: "创建时间",
  update_timestamp: "更新时间",
  case_name: "案件名称",
  person_susername: "关键人所属用户",
  person_susername_authority: "关键人所属用户权限",
  organization_susername: "关键组织所属用户",
  organization_susername_authority: "关键组织所属用户权限",
  organization_name: "关键组织名称",
  fullname: "关键人名称",
  nickname: "昵称",
  identity_number: "身份证号",
  birthday: "生日",
  sex: "性别",
  address: "地址",
  telephone: "电话",
  organization_website: "组织网址",
  organization_group: "组织群组",
  clue_content: "线索内容",
  case_id: "case_id",
  case_dir_id: "case_dir_id",
  tags: "tags",
  type: "类型",
  group_name: "群组名",
  group_nickname: "群昵称",
  "@authority": "权限",
  "@timestamp": "时间",
  "@username": "用户名",
  article_count: "文章数量",
  content: "正文",
  followers_count: "粉丝数",
  following_count: "关注量",
  icon: "头像",
  icon_type: "头像类型",
  icon_url: "头像链接",
  likes_count: "点赞数",
  nlp_parse_flag: "nlp_parse_flag",
  username: "用户名",
  key_clue_id: "key_clue_id",
  key_organization_id: "key_organization_id",
  timestamp: "时间",
  group_member: "群成员",
  content_article: "文章内容",
  case_dir: "父路径",
  create_authority: "创建权限",
  create_username: "创建用户",
  case_email: "创建人邮箱",
  remarks: "备注",
  target_event: "相关事件",
  target_person: "相关目标人",
  case_dir_father_path: "父路径",
  case_dir_name: "目录名称",
  rowkey_parms: "附加信息",
  name: "名字",
  // content_file_name:''
};
