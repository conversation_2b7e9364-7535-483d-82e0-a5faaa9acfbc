// @import './variables.scss';
// @import './transition.scss';
// @import './element-ui.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}
p{
  margin: 0;
  padding: 0;
}
label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

.el-card__body{
  padding: 0;
}
.el-card__header{
  color: #000;
  // font-weight: bold;
  background: #ecf8ff;
  border-left: 5px solid #50bfff;

  // background: #f5f7fa;
  // border-bottom: 1px solid #ebeef5;
}
::-webkit-scrollbar{
  width: 7px;
  height: 10px;
}
::-webkit-scrollbar-thumb{
  border-radius: 10px;
  background: #afb1b4;
}
::-webkit-scrollbar-track-piece{
  width: 2px;
  background: #dfe9f7;
}