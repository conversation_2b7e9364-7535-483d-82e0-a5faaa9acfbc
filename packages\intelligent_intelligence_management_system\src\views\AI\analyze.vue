<template>
  <div 
    class="intellManage"
    v-loading="loading"
    element-loading-text="加载中..."
    element-loading-background="rgba(0, 0, 0, 0.7)"
    elment-loading-spinner="el-icon-loading"
  >
    <!-- 顶部按钮 -->
    <div class="top-actions">
      <el-select 
        v-model="selectedType" 
        placeholder="请选择模板类型" 
        style="width: 200px;margin-right: 10px;"
        @change="fetchTemplates">
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-button type="primary" size="small" @click="openDialog">添加模板</el-button>
    </div>

    <!-- 模板卡片 -->
    <div class="template-container">
      <div class="template-card" v-for="template in currentTemplates" :key="template._id">
        <div class="template-header">
          <span class="template-title" :title="template._source.ai_template_name">{{ template._source.ai_template_name }}</span>
          <div class="template-actions">
            <el-button type="primary" circle size="mini" icon="el-icon-edit" @click="editTemplate(template)"></el-button>
            <el-button type="danger" circle size="mini" icon="el-icon-delete" @click="deleteTemplate(template._id)"></el-button>
          </div>
        </div>
        <div class="template-content">
          <div class="content-item">
            <span class="item-label">内容:</span>
            <span class="item-value">{{ template._source.ai_template_content.content }}</span>
          </div>
          <div class="content-item">
            <span class="item-label">段落:</span>
            <span class="item-value">{{ template._source.ai_template_content.paragraph }}</span>
          </div>
          <div class="content-item">
            <span class="item-label">字数:</span>
            <span class="item-value">{{ template._source.ai_template_content.wordCount }}</span>
          </div>
          <div class="content-item">
            <span class="item-label">语气:</span>
            <span class="item-value">{{ getToneLabel(template._source.ai_template_content.tone) }}</span>
          </div>
        </div>
      </div>
      <div v-if="currentTemplates.length === 0" class="no-data">
        暂无模板数据
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

     <!-- 新增模板对话框 -->
     <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible"
      width="600px"
      :modal-append-to-body="false"
      :close-on-click-modal="false" 
      :close-on-press-escape="false"
      @closed="resetForm">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="left">
        
        <el-form-item label="模板类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择模板类型"
            style="width: 100%">
            <el-option
              v-for="item in typeOptions.filter(i => i.value !== 'all')"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="模板标题" prop="title">
          <el-input 
            v-model="form.title"
            placeholder="请输入模板标题"
            maxlength="50"
            show-word-limit />
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="3"
            placeholder="请输入模板内容"
            maxlength="500"
            show-word-limit />
        </el-form-item>

        <el-form-item label="段落数" prop="paragraph">
          <el-input-number 
            v-model="form.paragraph"
            :min="1"
            :max="10"
            controls-position="right" />
        </el-form-item>

        <el-form-item label="字数要求" prop="wordCount">
          <el-input
            v-model="form.wordCount"
            placeholder="请输入字数要求"
            type="number">
            <template slot="append">字</template>
          </el-input>
        </el-form-item>

        <el-form-item label="语气风格" prop="tone">
          <el-select
            v-model="form.tone"
            placeholder="请选择语气风格"
            style="width: 100%">
            <el-option
              v-for="item in toneOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';

export default {
  name: 'AnalyzeTemplate',
  data() {
    return {
      currentPage: 1,
      templates: [],
      addTemplateDialogVisible: false,
      dialogVisible: false,
      dialogTitle: '新增模板',
      form: {
        id: '',
        type: 'publicOpinion',
        title: '',
        content: '',
        paragraph: 3,
        wordCount: 500,
        tone: ''
      },
      selectedType: 'all',
      typeOptions: [
        { value: 'all', label: '全部' },
        { value: 'publicOpinion', label: '舆情简报' },
        { value: 'intelligence', label: '情报' }
      ],
      rules: {
        title: [
          { required: true, message: '请输入模板标题', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入模板内容', trigger: 'blur' },
          { min: 10, message: '内容至少需要 10 个字符', trigger: 'blur' }
        ],
        paragraph: [
          { type: 'number', min: 1, max: 10, message: '段落数需在1-10之间', trigger: 'blur' }
        ],
        wordCount: [
          { required: true, message: '请输入字数要求', trigger: 'blur' },
          { type: 'number', min: 100, message: '字数不能少于100字', trigger: 'blur' }
        ],
        tone: [
          { required: true, message: '请选择语气风格', trigger: 'change' }
        ]
      },
      toneOptions: [
        { value: 'formal', label: '正式严谨' },
        { value: 'friendly', label: '亲切友好' },
        { value: 'professional', label: '专业客观' },
        { value: 'humorous', label: '幽默风趣' }
      ]
    }
  },
  computed: {
    ...mapState({
      currentTemplates: (state) => state.aiAnalyze.aiAnalyzeTemplateList,
      total: (state) => state.aiAnalyze.total,
      pageSize: (state) => state.aiAnalyze.size,
      loading: (state) => state.aiAnalyze.loading,
    })
  },
  created() {
    this.resetAllData()
    this.getAIAnalyzeTemplate();
  },
  methods: {
    getToneLabel(toneValue) {
      const tone = this.toneOptions.find(opt => opt.value === toneValue);
      return tone ? tone.label : '未知语气';
    },
    ...mapMutations({
      getAIAnalyzeTemplate: "aiAnalyze/getAIAnalyzeTemplate",
      resetAllData: "aiAnalyze/resetAllData",
      setSelectTemplateData: "aiAnalyze/setSelectTemplateData",
      setChangePage: "aiAnalyze/setChangePage",
      deleteAiAnalyzeTemplate: "aiAnalyze/deleteAiAnalyzeTemplate",
      createAiAnalyzeTemplate: "aiAnalyze/createAiAnalyzeTemplate",
      editAiAnalyzeTemplate: "aiAnalyze/editAiAnalyzeTemplate",
    }),
    
    // 分页切换
    handleCurrentChange(val) {
      this.currentPage = val;
      this.setChangePage({ page: val });
      this.getAIAnalyzeTemplate();
    },

    // 切换类型，请求模板数据
    fetchTemplates() {
      this.setSelectTemplateData(this.selectedType);
      this.getAIAnalyzeTemplate();
    },
    
    // 编辑模板
    editTemplate(template) {
      console.log("tem:", template);
      if (template) {
        const { id: _, ...contentData } = template._source.ai_template_content;
        this.form = {
          id: template._id,
          ...contentData
        };
        this.dialogTitle = '编辑模板';
        this.dialogVisible = true;
      }
    },
    
    // 删除模板
    deleteTemplate(id) {
      this.$confirm('确认删除该模板?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAiAnalyzeTemplate(id);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    openDialog() {
      this.form.type = this.selectedType;
      this.dialogVisible = true;
    },
    
    resetForm() {
      this.form = {
        id: '',
        type: this.selectedType,  // 使用当前选中类型
        title: '',
        content: '',
        paragraph: 3,
        wordCount: 500,
        tone: ''
      };
      this.dialogTitle = '新增模板';
      this.$refs.form.clearValidate();
    },
    
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.editAiAnalyzeTemplate(this.form);
          } else {
            this.createAiAnalyzeTemplate(this.form);
          }
          this.dialogVisible = false;
        } else {
          this.$message.warning('请填写完整表单内容');
          return false;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.intellManage {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
  z-index: 1;
}

.top-actions {
  margin-bottom: 1.25rem;
  text-align: right;
}

.template-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  margin-bottom: 1.25rem;
  height: calc(100vh - 23.75rem); /* 留出更多空间给顶部操作栏和分页 */
  overflow-y: auto;
  padding: 0.625rem 0;
  position: relative;
}

.template-card {
  width: 15.625rem;
  height: 13.75rem; /* 固定高度 */
  border: 1px solid #e0e0e0;
  border-radius: 0.3125rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.template-title {
  font-weight: bold;
  font-size: 1rem;
  max-width: 10rem; /* 限制最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1; /* 允许标题收缩 */
}

.template-actions {
  display: flex;
  gap: 0.3125rem;
}

.template-content {
  padding: 0.625rem;
  flex: 1;
  overflow-y: auto;
}

.content-item {
  margin-bottom: 0.375rem; /* 减少间距 */
  display: flex;
}

.item-label {
  font-weight: bold;
  margin-right: 0.3125rem;
  min-width: 3.125rem;
}

.item-value {
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  margin-top: auto;
  text-align: center;
  text-align: center;
  padding: 1.25rem 0;
}

.no-data {
  width: 100%;
  text-align: center;
  padding: 3.125rem 0;
  color: #999;
  font-size: 0.875rem;
}
</style>
