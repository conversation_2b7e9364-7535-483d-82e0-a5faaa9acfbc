<template>
  <div>
    <el-checkbox-group v-model="selectedItems">
      <div class="projectLay">
        <div
          class="card-content"
          v-for="(item, index) in projectList"
          :key="index"
        >
          <img :src="item.src" />
          <div>
            <el-checkbox :label="index" @change="createTemplate(item)">{{
              item.label
            }}</el-checkbox>
          </div>
        </div>
      </div>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedItems: [],
    };
  },
  props: {
    projectList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    dataList() {
      return this.$store.state.projectManage.dataList;
    },
    checkList() {
      return this.selectedItems.map((index) => this.projectList[index]);
    },
  },
  created() {
    // this.$store.commit("projectManage/createOSSTable");
    // this.$store.commit("projectManage/deleteOSSTable");
    // 初始化时查找dataList中的项目在projectList中的索引并选中
    this.projectList.forEach((item, index) => {
      if (
        this.dataList.some(
          (dataItem) =>
            dataItem.label === item.label && dataItem.value === item.value
        )
      ) {
        this.selectedItems.push(index);
      }
    });
  },
  watch: {
    selectedItems: {
      handler(newVal) {
        this.$emit("update:selected", this.checkList);
      },
      deep: true,
    },
  },
  methods: {
    createTemplate(item) {
      console.log("xuanze", item);
      let existingDataItem = null;
      const exists = this.dataList.some((dataItem) => {
        if (dataItem.label === item.label && dataItem.value === item.value) {
          existingDataItem = dataItem;
          return true;
        }
        return false;
      });

      if (exists) {
        this.$confirm(" 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$store.commit(
              "projectManage/deleteTemplate",
              existingDataItem
            );
            // 删除成功后，从selectedItems中移除对应的index
            const index = this.projectList.findIndex(
              (projectItem) =>
                projectItem.label === item.label &&
                projectItem.value === item.value
            );
            if (index !== -1) {
              this.selectedItems = this.selectedItems.filter(
                (i) => i !== index
              );
            }
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
            // 取消删除时，重新添加选中状态
            const index = this.projectList.findIndex(
              (projectItem) =>
                projectItem.label === item.label &&
                projectItem.value === item.value
            );
            if (index !== -1 && !this.selectedItems.includes(index)) {
              this.$nextTick(() => {
                this.selectedItems.push(index);
              });
            }
          });
      } else {
        this.$store.commit("projectManage/createTemplate", item);
        // 创建成功后，添加到选中状态
        const index = this.projectList.findIndex(
          (projectItem) =>
            projectItem.label === item.label && projectItem.value === item.value
        );
        if (index !== -1 && !this.selectedItems.includes(index)) {
          this.selectedItems.push(index);
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.projectLay {
  display: flex;
  justify-content: space-around;
  .card-content {
    text-align: center;
  }
}
</style>
