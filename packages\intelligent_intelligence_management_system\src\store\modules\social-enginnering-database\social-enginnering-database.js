import VueCookies from "vue-cookies";
import { Message } from "view-design"; // Vue 2 中 iView/View UI Plus 通常被称为 view-design
import tools from "@/utils/tools";

const state = {
  pathShow: "",
  searchValue: "",
  selectPathValue: [],
  selectPathData: [],
  allPathTreeData: [],
  allPathArr: [],
  allDataTypeTitle: [],
  caseTreeTimer: null,
  pathTreeTimer: null,
  selectCaseData: [],
  // 搜索所需数据
  searchData: {
    value: "",
    database: "",
    caseId: "",
    pathArr: [],
    prefixRow: [],
    dataRow: [],
    relationStr: "",
  },
  combinedSearchList: [],
  isSearchCombined: false,
  setNowsearchDataRelation: "",
  setNowsearchDataPath: "",
  prefixSearchPathIndex: 0,
  prefixSearchList: [],
  preciseSearchList: [],
  dataSearchPathIndex: 0,
  dataSearchList: [],
  dataSearchListShow: [],
  dataSizeAll: 20,
  searchDataSizeAll: 100,
  dataHaveLastObj: {},
  caseTreeLoad: true,
  pathTreeLoad: true,
  searchPrefixLoad: false,
  searchLastLoad: false,
  searchTimer: null,
  editDialog: false,
  allPathDesc: {},
  prefixSeachOver: false,
  dataSearchOver: false,
  pathCountNum: {},
  pathNumObj: {
    countRes: { num: 0, timestamp: null, indexNum: 0 },
    countSta: { num: 0, indexNum: 0, timestamp: null },
  },
  pathFilterOpen: true,
};

const mutations = {
  SET_STATE(state, { key, value }) {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value;
    }
  },
  CLEAR_PATH_TREE_DATA(state) {
    state.selectPathData = [];
    state.allPathTreeData = [];
    state.allPathArr = [];
    state.allPathDesc = {};
  },
  CLEAR_CASE_TREE_DATA(state) {
    state.selectCaseData = [];
  },
  CLEAR_DATA_SEARCH_LIST(state) {
    state.searchData.dataRow = [];
    state.dataSearchList = [];
    state.dataSearchListShow = [];
    state.searchDataSizeAll = 100;
  },
  UPDATE_DATA_SEARCH_LIST(state, newList) {
    state.dataSearchList.push(...newList);
  },
  SET_DATA_SEARCH_LIST_SHOW(state, list) {
    state.dataSearchListShow = list;
  },
  RESET_SEARCH_DATA_STATE(
    state,
    { value, database, caseId, pathArr, relationArr }
  ) {
    state.dataSearchList = [];
    state.dataSearchListShow = [];
    state.dataHaveLastObj = {};
    state.prefixSearchPathIndex = 0;
    state.dataSearchPathIndex = 0;
    state.dataSizeAll = 20;
    state.searchDataSizeAll = 100;
    state.searchData.value = value;
    state.searchData.database = database;
    state.searchData.caseId = caseId;
    state.searchData.prefixRow = [];
    state.searchData.dataRow = [];
    state.combinedSearchList = [];
    state.isSearchComplete = false;

    if (relationArr.length === 1) {
      state.searchData.relationStr = "";
    } else {
      state.searchData.relationStr = relationArr.slice(1).join(";");
    }

    let filterPathArr = [];
    if (pathArr.length) {
      filterPathArr = pathArr;
    } else {
      filterPathArr = state.allPathArr;
    }
    state.searchData.pathArr = filterPathArr.filter((path) => path !== "/");
  },
  ADD_TO_COMBINED_SEARCH_LIST(state, items) {
    const currentRows = new Set(
      state.combinedSearchList.map((item) => item.row)
    );
    const newItems = items.filter((item) => !currentRows.has(item.row));
    state.combinedSearchList.push(...newItems);
  },
  CLEAR_DATA(state) {
    state.dataSearchOver = false;
    state.combinedSearchList = [];
    state.dataSearchListShow = [];
    state.pathShow = "";
  },
};

const actions = {
  createBatchQueryTask(task) {
    window.main.$main_socket.sendData(
      "Api.DataAnalysisTask.AddSimpleTask",
      [
        {
          head: {},
          msg: {
            title: task.taskName,
            task_type: "search_task",
            method: "hbase_data_comp",
            task_authority: "username",
            parms: {
              query_string: task.queryString,
              data_type: task.dataType,
              create_timestamp: new Date().getTime(),
              data: {
                dataTypeTitle: task.dataTypeTitle,
              },
            },
          },
        },
      ],
      (res) => {
        if (res?.status === "ok") {
          Message.success({
            background: true,
            content: "批量查询任务创建成功",
          });
        } else {
          Message.error({ background: true, content: "批量查询任务创建失败!" });
        }
      }
    );
  },

  calculateDataBse({ state }, type) {
    let userinfo = window.main.$store.state.userInfo.userinfo;
    switch (type) {
      case "public":
        return type.charAt(0);
      case "authority":
        return type.charAt(0) + ";" + userinfo.authority;
      case "username":
        return (
          type.charAt(0) + ";" + userinfo.authority + ";" + userinfo.username
        );
      case "case":
        return type.charAt(0) + ";" + state.searchData.caseId;
      default:
        return;
    }
  },

  getPathTreeData({ commit, state }) {
    let index = 0;
    commit("SET_STATE", { key: "pathTreeLoad", value: true });
    const getPathTree = (treeData, path) => {
      index++;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.ListDir",
        [{ head: {}, msg: { path } }],
        (res) => {
          res.forEach((item) => {
            if (item.columnValues?.p?.sys_lock && state.pathFilterOpen) {
              return;
            }
            let treeObject = {
              title: item.columnValues?.p?.alias
                ? item.columnValues.p.alias
                : item.columnValues.i.name,
              expand: true,
              value: item.columnValues.i.path,
              selected: false,
              checked: false,
              children: [],
            };
            state.allPathDesc[item.columnValues.i.path] = item.columnValues?.p
              ?.alias
              ? item.columnValues.p.alias
              : item.columnValues.i.path;
            state.allPathArr.push(item.columnValues.i.path);
            state.allDataTypeTitle.push(item.columnValues?.p?.alias);
            if (treeData?.children) {
              treeData.children.push(treeObject);
            } else {
              treeData.push(treeObject);
            }
            getPathTree(treeObject, item.columnValues.i.path);
          });
          index--;
        },
        () => {
          index--;
        }
      );
    };

    const selectPathData = [
      {
        title: "/",
        expand: true,
        value: "/",
        selected: false,
        checked: true,
        children: [],
      },
    ];
    commit("SET_STATE", { key: "selectPathData", value: selectPathData });
    state.allPathDesc["/"] = "/";
    state.allDataTypeTitle.push("/");
    state.allPathArr.push("/");
    getPathTree(selectPathData[0], "/");
    const pathTreeTimer = setInterval(() => {
      if (index <= 0) {
        clearInterval(pathTreeTimer);
        commit("SET_STATE", {
          key: "allPathTreeData",
          value: state.selectPathData,
        });
        commit("SET_STATE", { key: "pathTreeLoad", value: false });
        state.allPathArr = [...new Set(state.allPathArr)];
        Message.success({ background: true, content: "数据类型获取完成" });
        window.localStorage.setItem(
          "selectPathData",
          JSON.stringify(state.selectPathData)
        );
        window.localStorage.setItem(
          "allPathArr",
          JSON.stringify(state.allPathArr)
        );
        window.localStorage.setItem(
          "allPathDesc",
          JSON.stringify(state.allPathDesc)
        );
        window.localStorage.setItem(
          "allDataTypeTitle",
          JSON.stringify([...new Set(state.allDataTypeTitle)])
        );
      }
    }, 1000);
    commit("SET_STATE", { key: "pathTreeTimer", value: pathTreeTimer });
  },

  clearPathTreeData({ commit }) {
    commit("CLEAR_PATH_TREE_DATA");
  },

  getCaseTreeData({ commit, state }) {
    let index = 0;
    index++;
    const authorityTimer = setInterval(() => {
      let authority = VueCookies.get("authority");
      if (!authority) {
        window.main.$pki_socket.sendData(
          "Api.User.Info",
          [],
          "userInfo/setUserInfo"
        );
      } else {
        clearInterval(authorityTimer);
        window.main.$pki_socket.sendData(
            'Api.SubAuthority.List',
            [
              {
                head: {
                  from: 0,
                  size: 1000
                },
                msg: {
                  authority: authority,
                  sub_authority_father: '/63617365'
                }
              }
            ],
            (res) => {
              index--
              getCaseTree(state.selectCaseData, res[0].sub_authority)
            }
          )
          const getCaseTree = (caseTree, casePath) => {
            index++
            window.main.$case_socket.sendData(
              'Api.CaseDir.List',
              [
                {
                  head: {
                    session_id: '',
                    from: 0,
                    size: 1000
                  },
                  msg: {
                    case_dir_father_path: casePath
                  }
                }
              ],
              (res) => {
                index--
                res.forEach((item) => {
                  const caseDirObj = {
                    value: item.row,
                    label: item.columnValues.i.case_dir_name,
                    disabled: true,
                    children: []
                  }
                  if (caseTree?.children) {
                    caseTree.children.push(caseDirObj)
                    caseTree.disabled = false
                  } else {
                    caseTree.push(caseDirObj)
                  }
                  const dirPath =
                    item.columnValues.i.case_dir_father_path +
                    '/' +
                    item.columnValues.i.case_dir_name
                  getCaseTree(caseDirObj, dirPath)
                })
              },
              () => {
                index--
              }
            )
            index++
            window.main.$case_socket.sendData(
              'Api.Case.List',
              [
                {
                  head: {
                    session_id: '',
                    from: 0,
                    size: 1000
                  },
                  msg: {
                    case_dir_father_path: casePath
                  }
                }
              ],
              (res) => {
                index--
                res.forEach((item) => {
                  const caseDirObj = {
                    value: item.row,
                    label: item.columnValues.i.case_name
                  }
                  if (caseTree?.children) {
                    caseTree.children.push(caseDirObj)
                  } else {
                    caseTree.push(caseDirObj)
                  }
                })
              },
              () => {
                index--
              }
            )
          }
          state.caseTreeTimer = setInterval(() => {
            if (index <= 0) {
              clearInterval(state.caseTreeTimer)
              state.caseTreeLoad = false
              localStorage.setItem('selectCaseData', JSON.stringify(state.selectCaseData))
            }
          }, 1000)
      }
    }, 500);
  },

  clearCaseTreeData({ commit }) {
    commit("CLEAR_CASE_TREE_DATA");
  },

  clearDataSearchList({ commit }) {
    commit("CLEAR_DATA_SEARCH_LIST");
  },

  setSearchData({ commit }, data) {
    commit("RESET_SEARCH_DATA_STATE", data);
  },

  async startInitialSearch({ commit, state, dispatch }) {
    if (state.searchPrefixLoad) return;
    commit("SET_STATE", { key: "searchPrefixLoad", value: true });

    const { value, database, caseId, pathArr, relationStr } = state.searchData;
    let temPath = [];

    pathArr.map((path) => {
      temPath.push({
        row_key:
          tools.md5(
            dispatch("calculateDataBse", database) +
              ";" +
              path +
              (relationStr ? ";" + relationStr : "")
          ) +
          ";" +
          dispatch("calculateDataBse", database) +
          (relationStr ? ";" + relationStr : "") +
          ";" +
          value,
        type: "public",
        path: path,
        relation: relationStr,
        prefix: value,
      });
    });

    const preciseResults = await new Promise((resolve) => {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixComp.DetailMultiObj",
        [{ msg: { row_key_obj: temPath } }],
        (res) => {
          const results = [];
          Object.keys(res).forEach((key) => {
            if (res[key].relation) {
              const path = pathArr.find((p) =>
                key.includes(
                  tools.md5(dispatch("calculateDataBse", database) + ";" + p)
                )
              );
              results.push({
                row: key,
                columnValues: {
                  title: value,
                  path: path || "",
                  r: res[key].relation,
                },
              });
            }
          });
          resolve(results);
        },
        () => resolve([])
      );
    });

    commit("ADD_TO_COMBINED_SEARCH_LIST", preciseResults);

    if (preciseResults.length >= 20) {
      commit("SET_STATE", { key: "searchPrefixLoad", value: false });
      return;
    }

    commit("SET_STATE", { key: "searchPrefixLoad", value: false });
    dispatch("fetchMorePrefixResults");
  },

  async fetchMorePrefixResults({ commit, state, dispatch}) {
    if (state.searchPrefixLoad || state.isSearchComplete) {
      return;
    }
    commit("SET_STATE", { key: "searchPrefixLoad", value: true });
    const { value, database, caseId, pathArr, prefixRow, relationStr } =
      state.searchData;

    if (state.prefixSearchPathIndex >= pathArr.length) {
      commit("SET_STATE", { key: "isSearchComplete", value: true });
      commit("SET_STATE", { key: "searchPrefixLoad", value: false });
      return;
    }
    const currentPath = pathArr[state.prefixSearchPathIndex];

    const prefixResult = await new Promise((resolve) => {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixComp.Query",
        [
          {
            head: { row_key: prefixRow, size: 20, family: ["r"] },
            msg: {
              type: database,
              case_id: caseId,
              path: currentPath,
              relation: relationStr,
              prefix: value,
            },
          },
        ],
        (res) => {
          const results = [];
          const resKeys = Object.keys(res);
          if (resKeys.length > 0) {
            const rowStr = `${tools.md5(
              dispatch("calculateDataBse", database) +
                ";" +
                currentPath +
                (relationStr ? ";" + relationStr : "")
            )};${dispatch("calculateDataBse", database)};`;
            resKeys.forEach((key) => {
              if (res[key].relation) {
                results.push({
                  row: key,
                  columnValues: {
                    title: key.split(rowStr).join(""),
                    path: currentPath,
                    r: res[key].relation,
                  },
                });
              }
            });
          }
          resolve({ keys: resKeys, items: results });
        },
        () => resolve({ keys: [], items: [] })
      );
    });

    commit("ADD_TO_COMBINED_SEARCH_LIST", prefixResult.items);
    commit("SET_STATE", { key: "searchPrefixLoad", value: false });

    if (prefixResult.keys.length < 20) {
      commit("SET_STATE", {
        key: "prefixSearchPathIndex",
        value: state.prefixSearchPathIndex + 1,
      });
      commit("SET_STATE", {
        key: "searchData",
        value: { ...state.searchData, prefixRow: [] },
      });
      if (state.combinedSearchList.length < 20) {
        dispatch("fetchMorePrefixResults");
      }
    } else {
      const newPrefixRow = [prefixResult.keys[prefixResult.keys.length - 1]];
      commit("SET_STATE", {
        key: "searchData",
        value: { ...state.searchData, prefixRow: newPrefixRow },
      });
    }
  },

  delDataFn({ state }, v) {
    let { database } = state.searchData;
    let rowArr = v.map((item) => item.row);
    window.main.$main_socket.sendData(
      "Api.Search.SearchPrefixComp.DelData",
      [
        {
          head: { row_key: rowArr },
          msg: {
            type: database,
            path: state.setNowsearchDataPath,
            relation: state.setNowsearchDataRelation,
          },
        },
      ],
      (res) => {
        if (res.status === "ok") {
          Message.success({ background: true, content: "删除成功！" });
        }
      }
    );
  },

  editDataFn({ commit, state, dispatch }, { editForm, nowpage }) {
    let { database } = state.searchData;
    window.main.$main_socket.sendData(
      "Api.Search.SearchPrefixComp.DelData",
      [
        {
          head: { row_key: [editForm.row] },
          msg: {
            type: database,
            path: state.setNowsearchDataPath,
            relation: state.setNowsearchDataRelation,
          },
        },
      ],
      (res) => {}
    );
    delete editForm.row;
    delete editForm.path;
    window.main.$main_socket.sendData(
      "Api.Search.SearchPrefixComp.AddData",
      [
        {
          head: {},
          msg: {
            type: database,
            path: state.setNowsearchDataPath,
            relation: state.setNowsearchDataRelation,
            prefix: editForm["_"],
            data: { data: editForm, _: editForm["_"] },
          },
        },
      ],
      (res) => {
        if (res.status === "ok") {
          Message.success({ background: true, content: "修改成功！" });
          commit("SET_STATE", { key: "editDialog", value: false });
          commit("SET_STATE", {
            key: "searchData",
            value: { ...state.searchData, dataRow: [] },
          });
          commit("SET_STATE", { key: "dataSearchList", value: [] });
          dispatch("fetchSearchData", { v: 1, twoSearchTags: [] });
        }
      }
    );
  },

  clearData({ commit }) {
    commit("CLEAR_DATA");
  },

  fetchSearchData({ commit, state }, { v, twoSearchTags }) {
    commit("SET_STATE", { key: "pathShow", value: state.setNowsearchDataPath });
    commit("SET_STATE", { key: "searchLastLoad", value: true });
    let { database, caseId } = state.searchData;
    let sendDataObj = {};
    if (twoSearchTags && twoSearchTags.length > 0) {
      let sub_string_filter_value = twoSearchTags.map((tag) => ({
        family: "d",
        qualifier: "data",
        sub_string: JSON.stringify(tag).slice(1, -1),
      }));
      sendDataObj = {
        head: {
          row_key: state.searchData.dataRow,
          size: 100,
          family: ["d", "h"],
          sub_string_filter: sub_string_filter_value,
        },
        msg: {
          type: database,
          case_id: caseId,
          path: state.setNowsearchDataPath,
          relation: state.setNowsearchDataRelation,
          prefix: "",
        },
      };
    } else {
      sendDataObj = {
        head: {
          row_key: state.searchData.dataRow,
          size: 100,
          family: ["d", "h"],
        },
        msg: {
          type: database,
          case_id: caseId,
          path: state.setNowsearchDataPath,
          relation: state.setNowsearchDataRelation,
          prefix: "",
        },
      };
    }

    window.main.$main_socket.sendData(
      "Api.Search.SearchPrefixComp.Query",
      [sendDataObj],
      (res) => {
        if (Object.keys(res).length !== 0) {
          const resultArray = Object.entries(res).map(([key, value]) => ({
            [key]: value,
          }));
          const newData = [];
          resultArray.forEach((item) => {
            let key = Object.keys(item)[0];
            let tmpItem = {
              columnValues: { row: key, path: state.setNowsearchDataPath },
            };
            if (item[key].hasOwnProperty("data")) {
              tmpItem.columnValues.d = item[key].data;
              tmpItem.columnValues.d.row = key;
              newData.push(tools.dataPreprocessing(tmpItem));
            }
            if (item[key].hasOwnProperty("hbase")) {
              tmpItem.columnValues.d = item[key].hbase.columnValues.d;
              tmpItem.columnValues.d.row = key;
              newData.push(tools.dataPreprocessing(tmpItem));
            }
          });
          commit("UPDATE_DATA_SEARCH_LIST", newData);

          if (v === 1) {
            commit(
              "SET_DATA_SEARCH_LIST_SHOW",
              state.dataSearchList.slice(0, 20)
            );
          }

          const newRowKey = [
            Object.keys(resultArray[resultArray.length - 1])[0],
          ];
          commit("SET_STATE", {
            key: "searchData",
            value: { ...state.searchData, dataRow: newRowKey },
          });

          if (resultArray.length < state.searchDataSizeAll) {
            Message.success({
              background: true,
              content: "已搜索路径下全部数据！",
            });
            commit("SET_STATE", { key: "searchLastLoad", value: false });
            commit("SET_STATE", { key: "dataSearchOver", value: true });
          }
          if (resultArray.length == state.searchDataSizeAll) {
            commit("SET_STATE", { key: "searchLastLoad", value: false });
          }
        } else {
          Message.success({ background: true, content: "无数据！" });
          commit("SET_STATE", { key: "searchLastLoad", value: false });
        }
      }
    );
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
