import VueCookies from 'vue-cookies'

// 定义 mutation types
const SET_SELECT_PATH_DATA = 'SET_SELECT_PATH_DATA'
const SET_ALL_PATH_TREE_DATA = 'SET_ALL_PATH_TREE_DATA'
const SET_ALL_PATH_ARR = 'SET_ALL_PATH_ARR'
const SET_ALL_PATH_DESC = 'SET_ALL_PATH_DESC'
const SET_SELECT_CASE_DATA = 'SET_SELECT_CASE_DATA'
const SET_CASE_TREE_LOAD = 'SET_CASE_TREE_LOAD'
const SET_PATH_TREE_LOAD = 'SET_PATH_TREE_LOAD'
const SET_SEARCH_PREFIX_LOAD = 'SET_SEARCH_PREFIX_LOAD'
const SET_SEARCH_LAST_LOAD = 'SET_SEARCH_LAST_LOAD'
const SET_PREFIX_SEARCH_OVER = 'SET_PREFIX_SEARCH_OVER'
const SET_DATA_SEARCH_OVER = 'SET_DATA_SEARCH_OVER'
const SET_STOP_SEARCH = 'SET_STOP_SEARCH'

// 初始状态
const state = {
  selectPathData: [],
  allPathTreeData: [],
  allPathArr: [],
  caseTreeTimer: null,
  pathTreeTimer: null,
  selectCaseData: [],
  // 搜索所需数据
  searchData: {
    value: '',
    database: '',
    caseId: '',
    pathArr: [],
    prefixRow: [],
    dataRow: [],
    relationStr: ''
  },
  // 前缀搜索类型
  prefixSearchPathIndex: 0,
  prefixSearchList: [],
  // 精确搜索类型
  preciseSearchList: [],
  // 前缀搜索数据
  dataSearchPathIndex: 0,
  dataSearchList: [],
  dataSizeAll: 20,
  dataHaveLastObj: {},
  caseTreeLoad: true,
  pathTreeLoad: true,
  searchPrefixLoad: false,
  searchLastLoad: false,
  searchTimer: null,
  allPathDesc: {},
  prefixSeachOver: false,
  dataSearchOver: false,
  pathCountNum: {},
  pathNumObj: {
    countRes: {
      num: 0,
      timestamp: null
    },
    countSta: {
      num: 0,
      timestamp: null
    }
  },
  isStopSearch: false
}

// getters
const getters = {}

// mutations
const mutations = {
  [SET_SELECT_PATH_DATA](state, data) {
    state.selectPathData = data
  },
  [SET_ALL_PATH_TREE_DATA](state, data) {
    state.allPathTreeData = data
  },
  [SET_ALL_PATH_ARR](state, data) {
    state.allPathArr = data
  },
  [SET_ALL_PATH_DESC](state, data) {
    state.allPathDesc = data
  },
  [SET_SELECT_CASE_DATA](state, data) {
    state.selectCaseData = data
  },
  [SET_CASE_TREE_LOAD](state, value) {
    state.caseTreeLoad = value
  },
  [SET_PATH_TREE_LOAD](state, value) {
    state.pathTreeLoad = value
  },
  [SET_SEARCH_PREFIX_LOAD](state, value) {
    state.searchPrefixLoad = value
  },
  [SET_SEARCH_LAST_LOAD](state, value) {
    state.searchLastLoad = value
  },
  [SET_PREFIX_SEARCH_OVER](state, value) {
    state.prefixSeachOver = value
  },
  [SET_DATA_SEARCH_OVER](state, value) {
    state.dataSearchOver = value
  },
  [SET_STOP_SEARCH](state, value) {
    state.isStopSearch = value;
  },
  // 设置前缀搜索数据
  setPrefixSearchData(state, data) {
    state.prefixSearchList = []
    state.preciseSearchList = []
    state.dataSearchList = []
    state.dataHaveLastObj = {}
    state.prefixSearchPathIndex = 0
    state.dataSearchPathIndex = 0
    state.dataSizeAll = 20
    state.searchData.value = data.value
    state.searchData.database = data.database
    state.searchData.caseId = data.caseId
    state.searchData.prefixRow = []
    state.searchData.dataRow = []
    if (data.relationArr.length === 1) {
      state.searchData.relationStr = ''
    } else {
      let relNewArr = data.relationArr.slice(1)
      state.searchData.relationStr = relNewArr.join(';')
    }
    if (data.pathArr.length) {
      state.searchData.pathArr = data.pathArr
    } else {
      state.searchData.pathArr = state.allPathArr
    }
    state.isStopSearch = false;
  },
  // 清除路径树数据
  clearPathTreeData(state) {
    state.selectPathData = []
    state.allPathTreeData = []
    state.allPathArr = []
    state.allPathDesc = {}
  },
  // 清除案件树数据
  clearCaseTreeData(state) {
    state.selectCaseData = []
  },
  // 更新前缀搜索列表
  updatePrefixSearchList(state, item) {
    if (state.isStopSearch) return;
    state.prefixSearchList.push(item)
  },
  // 更新精确搜索列表
  updatePreciseSearchList(state, items) {
    if (state.isStopSearch) return;
    state.preciseSearchList = state.preciseSearchList.concat(items)
  },
  // 更新数据搜索列表
  updateDataSearchList(state, item) {
    if (state.isStopSearch) return;
    state.dataSearchList.push(item)
  },
  // 更新数据行
  updateDataRow(state, row) {
    state.searchData.dataRow = [row]
  },
  // 更新前缀行
  updatePrefixRow(state, row) {
    state.searchData.prefixRow = [row]
  },
  // 更新前缀搜索路径索引
  incrementPrefixSearchPathIndex(state) {
    state.prefixSearchPathIndex++
  },
  // 更新数据搜索路径索引
  incrementDataSearchPathIndex(state) {
    state.dataSearchPathIndex++
  },
  // 更新数据大小
  incrementDataSizeAll(state) {
    state.dataSizeAll += 20
  },
  // 更新数据是否有下一层
  updateDataHaveLastObj(state, { key, value }) {
    state.dataHaveLastObj[key] = value
  },
  // 更新路径计数
  updatePathCountNum(state, { key, num }) {
    state.pathCountNum[key] = num
  },
  // 更新路径数量对象
  updatePathNumObj(state, { type, num, timestamp }) {
    state.pathNumObj[type].num = num
    state.pathNumObj[type].timestamp = timestamp
  },
  // 更新所有路径树数据
  updateAllPathTreeData(state, data) {
    state.allPathTreeData = data
  }
}

// actions
const actions = {
  calculateDataBse(type) {
    console.log("calculateDataBse:", type);
    return type.charAt(0)
  },
  // 获取路径树
  getPathTreeData({ state, commit}, callback) {
    let index = 0
    // 用于跟踪已经添加的路径，避免重复
    const addedPaths = new Set()
    
    const getPathTree = (treeData, path) => {
      index++
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.ListDir',
        [
          {
            head: {
              session_id: '',
              row_key: [],
              size: 200
            },
            msg: {
              path: path
            }
          }
        ],
        (res) => {
          if (state.isStopSearch) return;
          res.forEach((item) => {
            let treeObject = {
              label: item.columnValues?.p?.alias
                ? item.columnValues.p.alias
                : item.columnValues.i.name,
              expand: true,
              value: item.columnValues.i.path,
              id: item.columnValues.i.path,
              selected: false,
              checked: false,
              children: []
            }
            
            // 检查路径是否已经存在，避免重复键
            if (!state.allPathDesc.hasOwnProperty(item.columnValues.i.path)) {
              state.allPathDesc[item.columnValues.i.path] = item.columnValues?.p?.alias
                ? item.columnValues.p.alias
                : item.columnValues.i.path
            }
            
            if (!state.allPathArr.includes(item.columnValues.i.path)) {
              state.allPathArr.push(item.columnValues.i.path)
            }
            
            // 使用 Set 来检查路径是否已经添加过
            if (!addedPaths.has(item.columnValues.i.path)) {
              addedPaths.add(item.columnValues.i.path)
              if (treeData?.children) {
                treeData.children.push(treeObject)
              } else {
                treeData.push(treeObject)
              }
              getPathTree(treeObject, item.columnValues.i.path)
            }
          })
          index--
        },
        () => {
          index--
        }
      )
    }
    
    getPathTree(state.selectPathData, '/')
    
    state.pathTreeTimer = setInterval(() => {
      if (index <= 0) {
        clearInterval(state.pathTreeTimer)
        commit(SET_ALL_PATH_TREE_DATA, state.selectPathData)
        commit(SET_PATH_TREE_LOAD, false)
        // 确保去重
        state.allPathArr = [...new Set(state.allPathArr)]
        window.localStorage.setItem(window.location.host+'selectPathData', JSON.stringify(state.selectPathData))
        window.localStorage.setItem(window.location.host+'allPathArr', JSON.stringify(state.allPathArr))
        window.localStorage.setItem(window.location.host+'allPathDesc', JSON.stringify(state.allPathDesc))
        if (typeof callback === 'function') callback();
      }
    }, 1000)
  },
  // 获取案件树
  getCaseTreeData({ state, commit }) {
    let index = 0
    index++
    window.main.$pki_socket.sendData(
      'Api.SubAuthority.List',
      [
        {
          head: {
            from: 0,
            size: 1000
          },
          msg: {
            authority: VueCookies.get('authority'),
            sub_authority_father: '/63617365'
          }
        }
      ],
      (res) => {
        index--
        getCaseTree(state.selectCaseData, res[0].sub_authority)
      }
    )
    
    const getCaseTree = (caseTree, casePath) => {
      index++
      window.main.$case_socket.sendData(
        'Api.CaseDir.List',
        [
          {
            head: {
              session_id: '',
              from: 0,
              size: 1000
            },
            msg: {
              case_dir_father_path: casePath
            }
          }
        ],
        (res) => {
          index--
          res.forEach((item) => {
            const caseDirObj = {
              value: item.row,
              label: item.columnValues.i.case_dir_name,
              disabled: true,
              children: []
            }
            if (caseTree?.children) {
              caseTree.children.push(caseDirObj)
              caseTree.disabled = false
            } else {
              caseTree.push(caseDirObj)
            }
            const dirPath =
              item.columnValues.i.case_dir_father_path + '/' + item.columnValues.i.case_dir_name
            getCaseTree(caseDirObj, dirPath)
          })
        },
        () => {
          index--
        }
      )
      
      index++
      window.main.$case_socket.sendData(
        'Api.Case.List',
        [
          {
            head: {
              session_id: '',
              from: 0,
              size: 1000
            },
            msg: {
              case_dir_father_path: casePath
            }
          }
        ],
        (res) => {
          index--
          res.forEach((item) => {
            const caseDirObj = {
              value: item.row,
              label: item.columnValues.i.case_name
            }
            if (caseTree?.children) {
              caseTree.children.push(caseDirObj)
            } else {
              caseTree.push(caseDirObj)
            }
          })
        },
        () => {
          index--
        }
      )
    }
    
    state.caseTreeTimer = setInterval(() => {
      if (index <= 0) {
        clearInterval(state.caseTreeTimer)
        commit(SET_CASE_TREE_LOAD, false)
        localStorage.setItem(window.location.host+'selectCaseData', JSON.stringify(state.selectCaseData))
      }
    }, 1000)
  },
  // 前缀搜索数据类型
  getPrefixSearchRelation({ state, commit, dispatch }) {
    const { value, database, caseId, pathArr, prefixRow, relationStr } = state.searchData
    console.log("getPrefixSearchRelation-value:",value);
    console.log("getPrefixSearchRelation-pathArr:",pathArr);
    console.log("getPrefixSearchRelation-prefixRow:",prefixRow, relationStr);
    window.main.$main_socket.sendData(
      'Api.Search.SearchPrefix.Query',
      [
        {
          head: {
            row_key: prefixRow,
            size: 20,
            family: ['r']
          },
          msg: {
            type: database,
            case_id: caseId,
            path: pathArr[state.prefixSearchPathIndex],
            relation: relationStr,
            prefix: value
          }
        }
      ],
      (res) => {
        if (state.isStopSearch) return;
        console.log("Api.Search.SearchPrefix.Query:",res)
        if (res.length !== 0) {
          console.log(relationStr, '000000')
          commit('updatePrefixRow', res[res.length - 1].row)
          
          const pathStr = `p;${pathArr[state.prefixSearchPathIndex]}${relationStr ? ';' + relationStr : ''}`
          const rowStr = `${window.main.$tools.sha512(pathStr)};${pathStr};`
          
          console.log(pathStr, '00000')
          console.log(rowStr, '00000')
          console.log(res, '0000')
          
          res.forEach((item) => {
            const title = item.row.split(rowStr).join('')
            item.columnValues.title = title
            item.columnValues.path = pathArr[state.prefixSearchPathIndex]
            if (item.columnValues?.r) {
              commit('updatePrefixSearchList', item)
            }
          })
        }
        
        if (res.length < 20) {
          if (state.prefixSearchPathIndex === pathArr.length - 1) {
            window.main.$message.success('已搜索路径下全部类型！')
            commit(SET_SEARCH_PREFIX_LOAD, false)
            commit(SET_PREFIX_SEARCH_OVER, true)
          } else {
            commit('incrementPrefixSearchPathIndex')
            state.searchData.prefixRow = []
            dispatch('getPrefixSearchRelation')
          }
        } else {
          commit(SET_SEARCH_PREFIX_LOAD, false)
        }
      }
    )
  },
  // 精确搜索数据类型
  getPreciseSearchRelation({ state, commit, dispatch }) {
    const { value, database, caseId, pathArr, relationStr } = state.searchData
    console.log("getPreciseSearchRelation:", value, database, caseId, pathArr, relationStr);
    pathArr.forEach((path) => {
      const pathRow =
        window.main.$tools.sha512(
          'p;' + path + (relationStr ? ';' + relationStr : '')
        ) +
        ';p;' +
        path +
        (relationStr ? ';' + relationStr : '') +
        ';' +
        value
      
        window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.DetailMulti',
        [
          {
            head: {
              row_key: [pathRow],
              size: 20
            },
            msg: {
              type: database,
              case_id: caseId,
              path: path,
              relation: relationStr,
              prefix: value
            }
          }
        ],
        (res) => {
          if (state.isStopSearch) return;
          console.log("Api.Search.SearchPrefix.DetailMulti:",res)
          if (res[0].row) {
            res[0].columnValues.title = value
            res[0].columnValues.path = path
            if (res[0].columnValues.r) {
              commit('updatePreciseSearchList', res)
            } else if (res[0].columnValues.d) {
              res.forEach(item => {
                commit('updateDataSearchList', item)
              })
              dispatch('sendDataLast', res)
            }
          }
        }
      )
    })
  },
  // 搜索数据内容
  getSearchData({ state, commit, dispatch }) {
    const { value, database, caseId, pathArr, dataRow, relationStr } = state.searchData
    console.log("getSearchData:", state.searchData);
    
    window.main.$main_socket.sendData(
      'Api.Search.SearchPrefix.Query',
      [
        {
          head: {
            row_key: dataRow,
            size: 20,
            family: ['d']
          },
          msg: {
            type: database,
            case_id: caseId,
            path: pathArr[state.dataSearchPathIndex],
            relation: relationStr,
            prefix: value
          }
        }
      ],
      (res) => {
        if (state.isStopSearch) return;
        if (res.length !== 0) {
          console.log("getSearchData:",res);
          res.forEach((item) => {
            item.columnValues.path = pathArr[state.dataSearchPathIndex]
            if (item.columnValues?.d) {
              commit('updateDataSearchList', window.main.$tools.dataPreprocessing(item))
            }
          })
          commit('updateDataRow', res[res.length - 1].row)
          dispatch('sendDataLast', res)
        }
        
        if (state.dataSearchList.length < state.dataSizeAll) {
          if (state.dataSearchPathIndex === pathArr.length - 1) {
            window.main.$message.success('已搜索路径下全部数据！')
            commit(SET_SEARCH_LAST_LOAD, false)
            commit(SET_DATA_SEARCH_OVER, true)
          } else {
            commit('incrementDataSearchPathIndex')
            state.searchData.dataRow = []
            dispatch('getSearchData')
          }
        } else {
          commit('incrementDataSizeAll')
          commit(SET_SEARCH_LAST_LOAD, false)
        }
      }
    )
  },
  // 搜索数据下是否还有一层
  sendDataLast({ state, commit, dispatch}, res) {
    const { database, caseId, relationStr } = state.searchData
    let dataLastObj = {}
    
    res.forEach((item) => {
      const dataObj = item.columnValues.d
      for (const key in dataObj) {
        if (Object.hasOwnProperty.call(dataObj, key)) {
          const value = dataObj[key]
          if (window.main.$tools.isPlainObject(value)) {
            if (!Object.hasOwnProperty.call(state.dataHaveLastObj, key)) {
              dataLastObj[key] = item.columnValues.path
            }
            if (!Object.hasOwnProperty.call(state.dataHaveLastObj, value)) {
              dataLastObj[value] = item.columnValues.path
            }
          }
        }
      }
    })
    
    for (const key in dataLastObj) {
      if (Object.hasOwnProperty.call(dataLastObj, key)) {
        const value = dataLastObj[key]
        window.main.$main_socket.sendData(
          'Api.Search.SearchPrefix.Query',
          [
            {
              head: {
                row_key: [],
                size: 20,
                family: ['r']
              },
              msg: {
                type: database,
                case_id: caseId,
                path: value,
                relation: relationStr ? relationStr + ';' + key : key,
                prefix: ''
              }
            }
          ],
          (res) => {
            if (res?.length) {
              commit('updateDataHaveLastObj', { key, value })
            }
          }
        )
      }
    }
  },
  // 向爬虫发送统计任务
  sendStatisticsPath() {
    window.main.$main_socket.sendData(
      'Api.DataAnalysisTask.SendAsyncTask',
      [
        {
          head: {},
          msg: {
            key: 'query_data',
            topic: 'ParseTools.Database.HbaseDataCount',
            value: {}
          }
        }
      ],
      (res) => {
        if (res?.status === 'ok') {
          window.main.$message.success('添加统计任务成功！')
        } else {
          window.main.$message.error('添加统计任务失败！')
        }
      }
    )
  },
  // 获取所有路径下的数据量
  getAllPathDataNum({ state, commit }) {
    window.main.$constant_socket.sendData(
      'Api.Node.NodeData',
      [
        {
          msg: {
            '/etc/kappa/hbase_query_data/path_count_num_result': ''
          }
        }
      ],
      (res) => {
        const resData = res['/etc/kappa/hbase_query_data/path_count_num_result']
        const countNumRes = resData.count_num_obj
        const timestamp = Number(resData.timestamp)
        let andNum = 0
        
        for (const key in countNumRes) {
          const item = countNumRes[key]
          let num = (item.d?.count ?? 0) + (item.r?.count ?? 0)
          commit('updatePathCountNum', { key, num })
          andNum += num
        }
        
        commit('updatePathNumObj', { type: 'countRes', num: andNum, timestamp })
      }
    )
    
    window.main.$constant_socket.sendData(
      'Api.Node.NodeData',
      [
        {
          msg: {
            '/etc/kappa/hbase_query_data/path_count_num': ''
          }
        }
      ],
      (res) => {
        const resData = res['/etc/kappa/hbase_query_data/path_count_num']
        const countNum = resData.count_num_obj
        const timestamp = Number(resData.timestamp)
        let andNum = 0
        
        for (const key in countNum) {
          const item = countNum[key]
          let num = (item.d?.count ?? 0) + (item.r?.count ?? 0)
          andNum += num
        }
        
        commit('updatePathNumObj', { type: 'countSta', num: andNum, timestamp })
      }
    )
  },
  // 设置所有路径下的数据量
  setPathDataNum({ state, commit }) {
    let allPathTreeData = state.allPathTreeData
    
    function traverseTree(data) {
      let title = ''
      const parenthesisIndex = data.title.indexOf('(')
      if (parenthesisIndex !== -1) {
        title = data.title.substring(0, parenthesisIndex)
      } else {
        title = data.title
      }
      
      let path = data.value
      if (state.pathCountNum[path]) {
        data.title = `${title} (${window.main.$tools.formatNumber(state.pathCountNum[path])}条)`
      }
      
      if (data.children && data.children.length > 0) {
        for (const child of data.children) {
          traverseTree(child)
        }
      }
    }
    
    for (const child of allPathTreeData) {
      traverseTree(child)
    }
    
    commit('updateAllPathTreeData', allPathTreeData)
  }
}

// 添加导出语句
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
