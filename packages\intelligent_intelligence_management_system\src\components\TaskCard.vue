<template>
  <div class="task-card">
    <div class="card-header">
      <div class="task-id">{{ taskName }}</div>
      <div class="card-actions">
        <button class="action-btn edit-btn" @click="handleEdit">
          <i class="el-icon-edit-outline"></i>
        </button>
        <button class="action-btn delete-btn" @click="handleDelete">
          <i class="el-icon-delete"></i>
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="info-item">
        <span class="label">频次:</span>
        <span class="value">{{ frequency }}</span>
      </div>
      <!-- <div class="info-item">
        <span class="label">日期范围:</span>
        <span class="value">{{ startDate }}<template v-if="endDate"> -- {{ endDate }}</template></span>
      </div> -->
      <div class="info-item">
        <span class="label">时间:</span>
        <span class="value">{{ time }}</span>
      </div>
      <div class="info-item">
        <span class="label">Cron表达式:</span>
        <span class="value">{{ cronExpression }}</span>
      </div>
      <div class="info-item">
        <span class="label">任务模板:</span>
        <span class="value">{{ templateName }}</span>
      </div>
      <div class="info-item">
        <span class="label">描述信息:</span>
        <span class="value">{{ describe }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskCard',
  props: {
    taskId: {
      type: String,
      required: true
    },
    frequency: {
      type: String,
      default: '每天'
    },
    // startDate: {
    //   type: String,
    //   default: ''
    // },
    // endDate: {
    //   type: String,
    //   default: ''
    // },
    time: {
      type: String,
      default: ''
    },
    cronExpression: {
      type: String,
      default: ''
    },
    templateName: {
      type: String,
      default: ''
    },
    describe: {
      type: String,
      default: ''
    },
    environment: {
      type: Array,
      default: () => []
    },
    taskName: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleEdit() {
      this.$emit('edit', this.taskId);
    },
    handleDelete() {
      this.$emit('delete', this.taskId);
    }
  }
}
</script>

<style lang="scss" scoped>
.task-card {
  width: 230px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  transition: 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f7f7f7;
    
    .task-id {
      font-size: 15px;
      font-weight: bold;
      color: #333;
    }
    
    .card-actions {
      display: flex;
      gap: 8px;
      
      .action-btn {
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        
        i {
          font-size: 14px;
          color: #fff;
        }
        
        &.edit-btn {
          background-color: #409EFF;
          
          &:hover {
            background-color: #66b1ff;
          }
        }
        
        &.delete-btn {
          background-color: #F56C6C;
          
          &:hover {
            background-color: #f78989;
          }
        }
      }
    }
  }
  
  .card-body {
    padding: 12px;
    
    .info-item {
      margin-bottom: 8px;
      font-size: 13px;
      line-height: 1.5;
      display: flex;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #606266;
        width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: #303133;
        word-break: break-all;
        flex: 1;
      }
    }
  }
}
</style> 