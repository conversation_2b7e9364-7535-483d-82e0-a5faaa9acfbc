module.exports = {
  // common
  collision_task: "碰撞任务",
  hdfs: "hdfs",
  parsing_total: "正在分析",
  total: "总数",
  parse_end_total: "已完成数量",
  error_total: "异常数量",
  parse_ready_total: "未执行分析的数量",
  task_queues_parsing_total: "正在执行的任务数量",
  watching_task_total: "监控任务数量",
  spider_ready_total: "待爬取任务数量",
  spider_running_total: "爬取中任务数量",
  spider_pause_total: "爬取中暂停任务数量",
  initializing_total: "初始化中任务数量",
  health: "health",
  index: "index",
  hbase: "hbase",
  pri: "pri",
  twitter_article: "推特文章",
  twitter_person: "推特目标人",
  spider_end_total: "爬虫",
  elasticsearch: "弹性搜索",
  pri: "pri",
  hdfs: "文件搜索",
  elasticsearch: "数据搜索",
  total_indices: "索引",
  indices_number: "同步表",
  open_number: "打开表",
  green_number: "正常表",
  error_data_range_number: "失败",
  total_data: "同步数据",
  healthy_data: "正常数据",
  delete_data: "删除数据",
  health: "表状态",
  status: "是否开启",
  index: "索引名",
  data_range_index_prefix: "索引组",
  uuid: "索引ID",
  pri: "主分片数",
  rep: "备份分片数",
  docs_count: "数据总条数",
  docs_deleted: "已删除",
  store_size: "大小",
  pri_store_size: "字节",
  update_timestamp: "更新时间",
  yellow_number: "加载中的表",
  red_number: "异常的表",
  updating: "同步中",
  max_number: "最大数",
  twitter_person: "推特目标人",
  twitter_article: "推特文章",
  spider_end_total: "完成数",
  total_data_size: "总体积",
  parse_document_file_task: "文档分析任务",
  collision_task: "碰撞任务",
  csv: "csv",
  excel: "excel",
  "mysql.sql": "mysql",
  parse_end: "完成",
};
