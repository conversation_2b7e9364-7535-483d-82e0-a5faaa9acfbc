<template>
  <div v-if="item && !isBlankMessage" class="message">
    <el-popover
      style="margin: 10px auto"
      placement="left"
      width="200"
      trigger="hover"
      :open-delay="1000"
    >
      <el-descriptions
        size="mini"
        :column="1"
        border
        v-if="msgIcon && msgIcon.columnValues && msgIcon.columnValues.d"
      >
        <el-descriptions-item label="用户名">
          <span
            v-for="(item, key, index) in msgIcon.columnValues.d"
            :key="index"
          >
            <span v-if="key.startsWith('username') && item.username"
              >({{ item.username }})</span
            >
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="昵称">
          <span
            v-for="(item, key, index) in msgIcon.columnValues.d"
            :key="index"
          >
            <span v-if="key.startsWith('nickname') && item.nickname"
              >({{ item.nickname }})</span
            >
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="用户ID">
          <span
            v-for="(item, key, index) in msgIcon.columnValues.d"
            :key="index"
          >
            <span v-if="key.startsWith('user_id') && item.user_id"
              >({{ item.user_id }})</span
            >
          </span>
        </el-descriptions-item>
      </el-descriptions>
      <div class="msg_icon" slot="reference">
        <img
          v-if="iconSha512"
          :src="
            '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
            iconSha512 +
            '?session_id=' +
            $store.state.userInfo.session_id
          "
        />
        <img v-else src="@/assets/images/user.png" />
      </div>
    </el-popover>
    <div class="mag_body">
      <div style="display: flex">
        <template v-if="user_id == $store.state.telegramSearch.telegramSearchInformation.loadUserId">
          <p v-for="(item, key, index) in userIcon" :key="index">
            <span v-if="key.startsWith('nickname') && item.nickname"></span>
            ({{ item.nickname }})
          </p>
        </template>
        <template
          v-else-if="
            $store.state.telegramSearch.telegramSearchInformation.loadUserId + ';' + user_id ===
            $store.state.telegramSearch.telegramSearchInformation.loadGroupId
          "
        >
          <p v-if="friendIcon.group_name">
            {{ friendIcon.group_name }}
          </p>
        </template>
        <template v-else>
          <template
            v-if="msgIcon && msgIcon.columnValues && msgIcon.columnValues.d"
          >
            <p
              v-for="(item, key, index) in msgIcon.columnValues.d"
              :key="index"
            >
              <span v-if="key.startsWith('nickname') && item.nickname"></span>
              {{ item.nickname }}
            </p>
          </template>
        </template>
        <p v-if="user_id.length">({{ user_id[0].user_id }})</p>
      </div>
      <div
        class="msg_content"
        :style="
          msgKey === $store.state.telegramSearch.telegramSearchDataDetail.telegramHbaseGroup.nowCheckMsgRow
            ? 'background-color: #F8EAEA;'
            : 'background-color: #d2f0f6;'
        "
      >
        <!-- 显示时间 -->
        <div class="prec">
          <div style="color: #999" v-if="timestamp">
            {{ $tools.timestampToTime(timestamp) }}
          </div>
          <!-- <div class="iconList">
            <el-tooltip content="非常重要" placement="top" effect="light">
              <i
                v-if="ishave('非常重要')"
                style="color: red"
                class="el-icon-s-flag"
                @click="handleDelMsgTag('非常重要')"
              ></i>
              <i
                v-else
                class="el-icon-s-flag"
                @click="handleInputConfirm('非常重要')"
              ></i>
            </el-tooltip>
            <el-tooltip content="比较重要" placement="top" effect="light">
              <i
                v-if="ishave('比较重要')"
                style="color: blue"
                class="el-icon-s-flag"
                @click="handleDelMsgTag('比较重要')"
              ></i>
              <i
                v-else
                class="el-icon-s-flag"
                @click="handleInputConfirm('比较重要')"
              ></i>
            </el-tooltip>
            <el-tooltip content="重要" placement="top" effect="light">
              <i
                v-if="ishave('重要')"
                style="color: green"
                class="el-icon-s-flag"
                @click="handleDelMsgTag('重要')"
              ></i>
              <i
                v-else
                class="el-icon-s-flag"
                @click="handleInputConfirm('重要')"
              ></i>
            </el-tooltip>
          </div> -->
        </div>
        <!-- 显示内容 -->
        <div
          v-if="
            item.d.content_article && item.d.content_article.content_article
          "
        >
          <p>{{ item.d.content_article.content_article }}</p>
        </div>
        <!-- 显示图片 -->
        <div v-for="(data, key, index) in item.d" :key="index">
          <div v-if="key.startsWith('content_img') && data.content_img">
            <div v-if="data.content_img.file_name" class="title">
              <strong>图片标题:</strong>
              <p>{{ data.content_img.file_name }}</p>
            </div>
            <el-image
              title="点击查看大图"
              style="width: 100%"
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                data.content_img.sha512_hash +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
              :preview-src-list="[
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                  data.content_img.sha512_hash +
                  '?session_id=' +
                  $store.state.userInfo.session_id,
              ]"
            >
            </el-image>
          </div>
        </div>
        <!-- 显示pdf -->
        <div
          v-for="(data, key, index) in item.d"
          :key="index + 20"
          style="width: 100%"
        >
          <div v-if="key.startsWith('content_pdf') && data.content_pdf">
            <div class="title">
              <strong>PDF标题:</strong>
              <p>{{ data.content_pdf.file_name }}</p>
            </div>
            <a
              style="text-decoration: underline"
              :href="
                '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                data.content_pdf.sha512_hash +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
              target="_blank"
            >
              <!-- <pdf
            :src="'/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/'+
            item.d.content_pdf.content_pdf.sha512_hash+'?session_id='+
            $store.state.userInfo.session_id"
            /> -->
              点击预览pdf
            </a>
          </div>
        </div>
        <!-- 显示视频 -->
        <div v-for="(data, key, index) in item.d" :key="index + 40">
          <div v-if="key.startsWith('content_video') && data.content_video">
            <div v-if="data.content_video.file_name" class="title">
              <strong>视频标题:</strong>
              <p>{{ data.content_video.file_name }}</p>
            </div>
            <video
              id="video"
              controls="controls"
              muted="muted"
              preload="auto"
              loop="loop"
              style="width: 100%"
            >
              <source
                :src="
                  '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/' +
                  data.content_video.sha512_hash +
                  '?session_id=' +
                  $store.state.userInfo.session_id
                "
              />
            </video>
            播放速率：
            <el-select
              v-model="videoSelect"
              size="mini"
              style="width: 80px"
              @change="changeVideo"
            >
              <el-option label="0.5" value="0.5"></el-option>
              <el-option label="1.0" value="1"></el-option>
              <el-option label="1.5" value="1.5"></el-option>
              <el-option label="2.0" value="2"></el-option>
              <el-option label="3.0" value="3"></el-option>
              <el-option label="4.0" value="4"></el-option>
            </el-select>
          </div>
        </div>
        <!-- 显示音频 -->
        <div v-for="(data, key, index) in item.d" :key="index + 60">
          <div v-if="key.startsWith('content_voice') && data.content_voice">
            <div v-if="data.content_voice.file_name" class="title">
              <strong>音频标题:</strong>
              <p>{{ data.content_voice.file_name }}</p>
            </div>
            <audio
              id="audio"
              playbackRate="3"
              controls="controls"
              muted="muted"
              preload="auto"
              loop="loop"
              style="width: 100%"
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_voice/' +
                data.content_voice.sha512_hash +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
            ></audio>
          </div>
        </div>
        <!-- 显示文件 -->
        <div
          v-for="(data, key, index) in item.d"
          :key="index + 80"
          style="width: 100%"
        >
          <div v-if="key.startsWith('content_file') && data.content_file">
            <div v-if="data.content_file.file_name" class="title">
              <strong>文件标题:</strong>
              <p>{{ data.content_file.file_name }}</p>
            </div>
            <a
              style="color: blue"
              title="点击下载"
              @click="
                handleClickDownload(
                  '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_file/' +
                    data.content_file.sha512_hash +
                    '?session_id=' +
                    $store.state.userInfo.session_id,
                  data.content_file.file_name
                )
              "
            >
              点击下载文件</a
            >
          </div>
        </div>
        <div class="msg_btns">
          <div>
            <!-- <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="addTagInput"
              ref="saveTagInput"
              size="mini"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="mini"
              type="primary"
              @click.stop="showInput"
              >+ 添加标签</el-button
            > -->
            <!-- <el-button
              style="margin-left: 10px"
              type="primary"
              size="mini"
              @click="addTemporary(item)"
              >添加分组</el-button
            > -->
          </div>
          <!-- <div>
            <a id="downLoadMsg" @click="exportHtml(item)">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-download"
              ></el-button>
            </a>
            <el-dropdown
              v-if="equipment === 'telegram'"
              style="margin-left: 10px"
              trigger="click"
              @command="DelGroupMsg"
            >
              <el-button size="mini" type="danger" icon="el-icon-delete">
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="a">删除本地库消息</el-dropdown-item>
                <el-dropdown-item command="b">删除服务端消息</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              style="margin-left: 10px"
              v-else
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="delTypeMsg()"
            ></el-button>
          </div> -->
        </div>
        <div style="display: flex; flex-wrap: wrap">
          <div
            v-for="tag in msgTagList[item.d._._]"
            :key="tag.row"
            style="padding-top: 5px"
          >
            <el-tag
              v-if="
                tag.columnValues.d._._ !== '非常重要' &&
                tag.columnValues.d._._ !== '比较重要' &&
                tag.columnValues.d._._ !== '重要'
              "
              closable
              type="warning"
              :disable-transitions="false"
              @close="handleDelMsgTag(tag.columnValues.d._._)"
              effect="dark"
            >
              <el-tooltip placement="bottom">
                <el-button
                  slot="content"
                  type="danger"
                  size="mini"
                  @click.stop="handleDelMsgTag(tag.columnValues.d._._)"
                  >删除</el-button
                >
                <span>{{ tag.columnValues.d._._ }}</span>
              </el-tooltip>
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import pdf from "vue-pdf";
import axios from "axios";
export default {
  name: "message-single",
  props: {
    item: {
      type: Object,
      default: {},
    },
    msgIcon: {
      type: Object,
    },
    msgKey: {
      type: String,
      default: "",
    },
  },
  components: {
    pdf,
  },
  data() {
    return {
      addTagInput: "",
      videoSelect: "1",
      inputVisible: false,
      inputValue: "",
      group_id: [],
      user_id: [],
      timestamp: "",
      iconSha512: "",
      content_voice: [],
      content_video: [],
      content_img: [],
      content_file: [],
      content_pdf: [],
      msg_id: "",
      isBlankMessage: false,
      userInfo: {
        content_article: "",
        type: "",
        group_id: "",
        user_id: "",
        timestamp: "",
        content_voice: "",
        content_video: "",
        content_img: "",
        content_file: "",
        content_pdf: "",
      },
    };
  },
  created() {
    this.getMessageInfo();
    // this.$store.dispatch("information/sendMsgTag", this.item.d._._);
  },
  computed: {
    userIcon() {
      return this.$store.state.telegramSearch.telegramSearchInformation.userIcon;
    },
    friendIcon() {
      return this.$store.state.telegramSearch.telegramSearchInformation.friendIcon;
    },
    msgTagList() {
      return this.$store.state.telegramSearch.telegramSearchInformation.msgTagList;
    },
    equipment() {
      return this.$store.state.telegramSearch.telegramSearchInformation.equipment;
    },
  },
  watch: {
    msgIcon: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getMsgIcon();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 获取头像
    getMsgIcon() {
      if (this.user_id == this.$store.state.telegramSearch.telegramSearchInformation.loadUserId) {
        for (const key in this.userIcon) {
          if (key.startsWith("icon") && this.userIcon[key].icon.sha512_hash) {
            this.iconSha512 = this.userIcon[key].icon.sha512_hash;
          }
        }
      } else if (
        this.$store.state.telegramSearch.telegramSearchInformation.loadUserId + ";" + this.user_id ===
        this.$store.state.telegramSearch.telegramSearchInformation.loadGroupId
      ) {
        this.iconSha512 = friendIcon.iconSha512;
      } else {
        for (const key in this.msgIcon.columnValues.d) {
          if (
            key.startsWith("icon") &&
            this.msgIcon.columnValues.d[key].icon.sha512_hash
          ) {
            this.iconSha512 = this.msgIcon.columnValues.d[key].icon.sha512_hash;
          }
        }
      }
    },
    // 获取消息的数据
    getMessageInfo() {
      const info = this.item.d;
      for (const key in info) {
        if (key.startsWith("group_id") && info[key].group_id) {
          this.group_id.push(info[key]);
        }
        if (key.startsWith("user_id") && info[key].user_id) {
          this.user_id.push(info[key]);
        }
        if (key.startsWith("timestamp") && info[key].timestamp) {
          this.timestamp = info[key].timestamp;
        }
        if (key.startsWith("content_voice") && info[key].content_voice) {
          this.content_voice.push(info[key].content_voice);
        }
        if (key.startsWith("content_video") && info[key].content_video) {
          this.content_video.push(info[key].content_video);
        }
        if (key.startsWith("content_img") && info[key].content_img) {
          this.content_img.push(info[key].content_img);
        }
        if (key.startsWith("content_file") && info[key].content_file) {
          this.content_file.push(info[key].content_file);
        }
        if (key.startsWith("content_pdf") && info[key].content_pdf) {
          this.content_pdf.push(info[key].content_pdf);
        }
        if (key.startsWith("msg_id") && info[key].msg_id) {
          this.msg_id = info[key].msg_id;
        }
      }
      if (
        !this.item.d?.content_article?.content_article &&
        !this.content_voice.length &&
        !this.content_video.length &&
        !this.content_img.length &&
        !this.content_file.length &&
        !this.content_pdf.length
      ) {
        this.isBlankMessage = true;
      } else {
        this.isBlankMessage = false;
      }
    },
    handleClickDownload(url, fileName) {
      let x = new XMLHttpRequest();
      x.open("GET", url, true);
      x.responseType = "blob";
      x.onload = function (e) {
        let url = window.URL.createObjectURL(x.response);
        let a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        a.click();
      };
      x.send();
    },
    //删除消息
    DelGroupMsg(command) {
      this.$confirm("此操作将永久删除该消息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (command === "a") {
            this.$store.dispatch("telegramSearch/telegramSearchInformation/sendDelGroupMsg", this.msgKey);
          } else if (command === "b") {
            this.$store.dispatch("telegramSearch/telegramSearchInformation/sendDelTelegrameMsg", {
              group_id: this.disposeStr(this.group_id[0].group_id + ""),
              msg_id: this.msg_id,
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    delTypeMsg() {
      this.$confirm("此操作将永久删除该消息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("telegramSearch/telegramSearchInformation/sendDelGroupMsg", this.msgKey);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //处理字符串
    disposeStr(item) {
      if (item.indexOf(";") === -1) {
        return item;
      } else {
        return item?.substring(item.indexOf(";") + 1, item.length);
      }
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 添加标签
    handleInputConfirm(addFixTag) {
      if (!addFixTag.type) {
        // 添加重要性标签
        this.$store.dispatch("telegramSearch/telegramSearchInformation/addMsgTag", {
          msgRowKey: this.msgKey,
          msgTag: addFixTag,
          groupId: this.group_id[0].group_id,
          msgId: this.item.d._._,
        });
      } else {
        if (this.addTagInput) {
          // 添加自定义标签
          this.$store.dispatch("telegramSearch/telegramSearchInformation/addMsgTag", {
            msgRowKey: this.msgKey,
            msgTag: this.addTagInput,
            groupId: this.group_id[0].group_id,
            msgId: this.item.d._._,
          });
        }
        this.addTagInput = "";
        this.inputVisible = false;
      }
    },
    //删除标签
    handleDelMsgTag(tag) {
      this.$store.dispatch("telegramSearch/telegramSearchInformation/delMsgTag", {
        msgTag: tag,
        groupId: this.group_id[0].group_id,
        msgId: this.item.d._._,
      });
    },
    //格式化时间
    formatDateTime(value) {
      let date = new Date(value);
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + MM + "-" + d + " " + h + "：" + m + "：" + s;
    },
    // need
    exportOneMsg(item) {
      // 导出消息
      if (item.d?.content_article?.content_article) {
        let arrUser = [];
        let str = item.d?.group_id?.group_id;
        arrUser.push([
          str?.substring(str.indexOf(";") + 1, str.length),
          item.d?.user_id?.user_id,
          this.$tools.timestampToTime(item.d?.timestamp?.timestamp),
          item.d?.content_article?.content_article?.replace(/\r|\n|,/gi, ""),
          item.d?.type?.type,
        ]);
        let csvContent = "群组ID,发言人ID,发言时间,消息内容,类型" + "\r\n";
        arrUser.forEach((item) => {
          let row = item.join();
          csvContent += row + "\r\n";
        });
        let blob = new Blob(["\ufeff" + csvContent], {
          type: "text/plain;charset=utf-8",
        });
        let link = document.createElement("a");
        let d = new Date();
        d = this.formatDateTime(d);
        link.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.csv`;
        link.href = window.URL.createObjectURL(blob);
        link.click();
      }
    },
    //导出html
    exportHtml(item) {
      for (const key in item.d) {
        if (key.startsWith("content_article") && item.d[key].content_article) {
          this.userInfo.content_article = item.d[key].content_article;
        }
        if (key.startsWith("type") && item.d[key].type) {
          this.userInfo.type = item.d[key].type;
        }
        if (key.startsWith("user_id") && item.d[key].user_id) {
          this.userInfo.user_id = item.d[key].user_id;
        }
        if (key.startsWith("group_id") && item.d[key].group_id) {
          this.userInfo.group_id = item.d[key].group_id;
        }
        if (key.startsWith("timestamp") && item.d[key].timestamp) {
          this.userInfo.timestamp = item.d[key].timestamp;
        }
        if (key.startsWith("content_voice") && item.d[key].content_voice) {
          this.userInfo.content_voice = item.d[key].content_voice;
        }
        if (key.startsWith("content_img") && item.d[key].content_img) {
          this.userInfo.content_img = item.d[key].content_img;
        }
        if (key.startsWith("content_file") && item.d[key].content_file) {
          this.userInfo.content_file = item.d[key].content_file;
        }
        if (key.startsWith("content_pdf") && item.d[key].content_pdf) {
          this.userInfo.content_pdf = item.d[key].content_pdf;
        }
        if (key.startsWith("content_video") && item.d[key].content_video) {
          this.userInfo.content_video = item.d[key].content_video;
        }
      }
      //导出音频
      if (this.userInfo.content_voice) {
        // 使用获取到的blob对象创建的url
        const filePath =
          "/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_voice/" +
          this.userInfo.content_voice.sha512_hash +
          "?session_id=" +
          this.$store.state.userInfo.session_id; // mp3的地址
        fetch(filePath)
          .then((res) => res.blob())
          .then((blob) => {
            const a = document.createElement("a");
            document.body.appendChild(a);
            a.style.display = "none";
            // 使用获取到的blob对象创建的url
            const url = window.URL.createObjectURL(blob);
            a.href = url;
            // 指定下载的文件名
            a.download = data.file_name + ".mp3";
            a.click();
            document.body.removeChild(a);
            // 移除blob对象的url
            window.URL.revokeObjectURL(url);
          });
        //导出音频html
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmlvioce(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${this.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
      //导出视频
      else if (this.userInfo.content_video) {
        // 使用获取到的blob对象创建的url
        const filePaths =
          "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/" +
          this.userInfo.content_video.sha512_hash +
          "?session_id=" +
          this.$store.state.userInfo.session_id;
        fetch(filePaths)
          .then((res) => res.blob())
          .then((blob) => {
            const a = document.createElement("a");
            document.body.appendChild(a);
            a.style.display = "none";
            // 使用获取到的blob对象创建的url
            const url = window.URL.createObjectURL(blob);
            a.href = url;
            // 指定下载的文件名
            a.download = data.file_name + ".video";
            a.click();
            document.body.removeChild(a);
            // 移除blob对象的url
            window.URL.revokeObjectURL(url);
          });

        //导出视频html
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmlvideo(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
      // 导出图片
      else if (this.content_img) {
        axios({
          //设置图片路径
          url:
            "/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/" +
            this.userInfo.content_img.sha512_hash +
            "?session_id=" +
            this.$store.state.userInfo.session_id,
          //设置请求方法为get请求
          method: "get",
          //设置相应类型为blob
          responseType: "blob",
        }).then(
          //得到的是一个blob对象
          (res) => {
            let url = window.URL.createObjectURL(res.data);
            const a = document.createElement("a");
            a.href = url;
            a.download = data.file_name;
            a.click();
          }
        );
        //导出图片html
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmlimage(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
      //导出文件html
      else if (this.userInfo.content_file) {
        let x = new XMLHttpRequest();
        x.open(
          "GET",
          "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_file/" +
            this.userInfo.content_file.sha512_hash +
            "?session_id=" +
            this.$store.state.userInfo.session_id,
          true
        );
        x.responseType = "blob";
        x.onload = function (e) {
          let url = window.URL.createObjectURL(x.response);
          let a = document.createElement("a");
          a.href = url;
          a.download = data.file_name;
          a.click();
        };
        x.send();
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmlfile(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
      //导出pdfhtml
      else if (this.userInfo.content_pdf) {
        const link = document.createElement("a");
        link.download = data.file_name;
        link.href =
          "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/" +
          this.userInfo.content_pdf.sha512_hash +
          "?session_id=" +
          this.$store.state.userInfo.session_id;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmlpdf(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(
          new Blob([this.gethtmltext(this.userInfo)], {
            type: "",
          })
        );
        a.href = url;
        let d = new Date();
        d = this.formatDateTime(d);
        a.download = `${d}发言人ID(${item.d?.user_id?.user_id})消息.html`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    },
    gethtmltext(item) {
      let str = this.group_id + "";

      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;">${item.content_article?.replace(
                      /\r|\n|,/gi,
                      ""
                    )}</p>




                  </div>

                </div>

                <script>
                function aaaa(){
                  }
                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    //pdf的html
    gethtmlpdf(item) {
      let str = this.group_id + "";
      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let fileHtml = ` <a style="color: blue" target="_blank" href="${this.userInfo.content_pdf.file_name}" >
                                    打开文件</a>`;
      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <strong style="padding-left:20px;margin-top:5px;">文件标题:</strong>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;"> ${
                      item.content_pdf.file_name
                    }</p>


                     <div style="margin-top:5px;padding-left:20px;padding-right:20px "
                               >
                              ${fileHtml}
                             </div>


                  </div>

                </div>

                <script>

                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    //文件的html
    gethtmlfile(item) {
      let str = this.group_id + "";
      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let fileHtml = ` <a style="color: blue" target="_blank" href="${this.userInfo.content_file.file_name}" >
                                    打开文件</a>`;

      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <strong style="padding-left:20px;margin-top:5px;">文件标题:</strong>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;"> ${
                      item.content_file.file_name
                    }</p>


                     <div style="margin-top:5px;padding-left:20px;padding-right:20px "
                               >
                               ${fileHtml}
                             </div>


                  </div>

                </div>

                <script>

                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    //图片的html
    gethtmlimage(item) {
      let str = this.group_id + "";
      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let fileHtml = ` <img style="width: 100%"  src="${this.userInfo.content_img.file_name}.jpg"/>`;
      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;">${item.content_article?.replace(
                      /\r|\n|,/gi,
                      ""
                    )}</p>

                     <div style="margin-top:5px;padding-left:20px;padding-right:20px "
                               >
                              ${fileHtml}
                      </div>


                  </div>

                </div>

                <script>
                function aaaa(){
                  }
                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    //视频的html
    gethtmlvideo(item) {
      let str = this.group_id + "";
      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let fileHtml = ` <video id="video" controls="controls" muted="muted" preload="auto" loop="loop" style="width: 100%">
                                    <source src="${this.userInfo.content_video.file_name}.video" />
                                  </video>`;
      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;">${item.content_article?.replace(
                      /\r|\n|,/gi,
                      ""
                    )}</p>

                     <div style="margin-top:5px;padding-left:20px; padding-right:20px"
                               >
                               ${fileHtml}
                             </div>


                  </div>

                </div>

                <script>
                function aaaa(){
                  }
                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    //音频的html
    gethtmlvioce(item) {
      let str = this.group_id + "";
      let id = str?.substring(str.indexOf(";") + 1, str.length);
      let fileHtml = ` <audio id="audio" playbackRate="3" controls="controls" muted="muted" preload="auto" loop="loop" style="width: 70%"
                                     src='${this.userInfo.content_voice.file_name}.mp3'   >
                                 </audio>`;
      let html = `<!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width,initial-scale=1.0">

                </head>
                <body>

                <div style="width:100%;height:100%;display: flex;justify-content: space-around;align-items: center;">
                  <div style="width:500px;height:auto;margin-top:100px;border:1px solid #ccc;background-color: rgb(210, 240, 246);border-radius: 10px;box-shadow: 2px 2px 2px 0 #333333;">
                    <div style="padding-left:20px;margin-top:5px; margin-bottom:20px">发言时间:<span style="color: rgb(153, 153, 153);font-size: 18px;">${this.$tools.timestampToTime(
                      item.timestamp
                    )}</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">类型:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.type
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">群组ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.group_id
                    }</span></div>
                    <div style="padding-left:20px;margin-top:5px;margin-bottom:20px">发言人ID:<span style="color: rgb(153, 153, 153);font-size: 18px;">${
                      item.user_id
                    }</span></div>

                    <div style="padding-left:20px;margin-top:5px;white-spance:nowrap;">消息内容:</div>
                    <p style="padding-left:20px;width: 100%;height: auto;word-wrap:break-word;word-break:break-all;overflow: hidden;">${item.content_article?.replace(
                      /\r|\n|,/gi,
                      ""
                    )}</p>

                     <div style="margin-top:5px;padding-left:20px;"
                               >
                                 ${fileHtml}
                             </div>


                  </div>

                </div>

                <script>
                function aaaa(){
                  }
                <\/script>
                <style>
                html{
                  background: #dfe9f7
                }
                  </style>
              </body>

                </html>`;
      return html;
    },
    changeVideo(value) {
      let video = document.getElementById("video");
      video.playbackRate = value;
    },
    // 添加临时分组
    addTemporary(item) {
      this.$store.dispatch("telegramSearch.telegramSearchInformation/sendAddTemporaryMsg", item.d);
    },
    // 判断是什么标签
    ishave(str) {
      return this.msgTagList[this.item.d._._]?.some(
        (item) => item.columnValues.d._._ == str
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.message {
  display: flex;
  font-size: 14px;
  padding: 20px 0;
  border: 1px solid #eee;
  border-radius: 10px;
  p {
    padding: 2px 0;
  }
  .msg_icon {
    width: 15%;

    img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: block;
    }
  }

  .mag_body {
    width: 85%;

    .msg_content {
      overflow: hidden;
      width: 80%;
      margin-top: 10px;
      padding: 15px;
      border-radius: 10px;
      box-shadow: 2px 2px 2px 0 #333333;

      .prec {
        padding-bottom: 10px;
        display: flex;
        justify-content: space-between;

        i {
          margin-right: 10px;
          color: #ccc;
          font-size: 16px;
          cursor: pointer;
        }
      }

      .title {
        padding-bottom: 15px;
      }

      .msg_btns {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }

      .el-tag {
        margin-right: 5px;
      }
    }
  }
}

.input-new-tag {
  width: 90px;
  vertical-align: bottom;
}
</style>
