import Vue from 'vue'
import Router from 'vue-router'
import store from '../store'
import NProgress from 'nprogress' 
import 'nprogress/nprogress.css'
NProgress.configure({ showSpinner: false })

Vue.use(Router)                

/* Layout */
import Routes from './router' 
const createRouter = () => new Router({
  mode:'hash',
  base: process.env.BASE_URL,
  routes: Routes
})

const router = createRouter()
 

router.beforeEach((to, from ,next) => {

  NProgress.start()
  // store.commit('userInfo/setToPath', to.path)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
  const getPageTitle =function(pageTitle) {
    if (pageTitle) {
      return `${pageTitle}`
    }
    return `${'智能情报管理系统'}`
  }
  document.title = getPageTitle(to.meta.title)
  let path = to.path
  store.commit('activefn', path)
  next()
  NProgress.done()
})

export default router
