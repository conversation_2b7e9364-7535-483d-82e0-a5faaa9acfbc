<template>
    <div class="tree_box">
      <div v-if="dataNode">
        <div v-if="dataNode.subMap">
          <div v-for="(item, index) in dataNode.subMap" :key="index" class="tree_list">
            <component v-bind:is="subComponent" :data-node="item" :gofn="gofn"></component>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script>
  export default {
    name: "TreeBox",
    props: { 
      dataNode:{},
      subComponent:{},
      gofn:{
        type: Function,
      },
    },
    components: {
      "mail-manage-tree": () => import("@/components/caseTree/intellManageTree.vue"),
    },
  };
  </script>