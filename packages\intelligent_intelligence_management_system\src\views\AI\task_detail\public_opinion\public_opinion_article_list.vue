<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #dbeafe;
          line-height: 42px;
          font-weight: bold;
          color: #2244b0;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-s-order"></i
          ><span style="margin-left: 10px">文章列表</span>
        </div>
        <div style="display: flex">
          <div style="color: #333; margin-right: 10px">
            共<span
              style="color: #2244b0"
              v-if="dataObj && dataObj.hasOwnProperty('all_news_num')"
              >{{ dataObj.all_news_num }}</span
            >篇文章
          </div>
          <div @click="getAllArticle" style="cursor: pointer">查看全部文章</div>
        </div>
      </div>
      <div
        style="padding: 10px; line-height: 42px"
        v-if="dataObj && dataObj.hasOwnProperty('_ids')"
      >
        <div
          v-for="(val, index) in dataObj['_ids']"
          :key="index"
          class="newContent with-dot"
        >
          <div
            class="content_title"
            v-html="
              val.title && val.title.trim() ? '• ' + val.title : '• 无标题'
            "
            @click="NewsDisplay(val)"
          ></div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "article_list",
  data() {
    return {
      listData: [],
    };
  },
  computed: {
    ...mapState({
      dataObj: (state) => state.aiTaskQueue.taskDetail.public_opinion,
      searchConditions: (state) => state.aiTaskQueue.searchConditions,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  methods: {
    NewsDisplay(val) {
      const routeData = this.$router.resolve({
        name: "NewsDisplay",
        query: {
          id: val.id ? val.id : val._id,
          index: val.index ? val.index : val._index,
        },
      });
      window.open(routeData.href, "_blank");
    },
    getAllArticle() {
      console.log("searchConditions", this.searchConditions);
      this.$store.commit("search/conditions/setSearchTaskTimeRange", {
        time_range_end: this.searchConditions.end_timestamp,
        time_range_begin: this.searchConditions.start_timestamp, //this.searchConditions.start_timestamp||
      });
      this.$store.commit(
        "search/conditions/setQueryString",
        this.searchConditions.add_es_query_conditions.bool.must[0]
          .simple_query_string.query
      );
      window.main.$store.commit(
        "newsSearchList/setAddEsQueryConditions",
        this.searchConditions.add_es_query_conditions
      );
      window.main.$router.push({ name: "searchList" });
    },
  },
};
</script>

<style scoped lang="scss">
.content_title {
  width: 90%;
  padding-left: 10px;
  font-size: 18px;
  color: #2440b3;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  min-height: 20px;
}

.content_title:hover {
  text-decoration: underline;
}
/* 或直接修改圆点颜色 */
.content_title::first-letter {
}
</style>
