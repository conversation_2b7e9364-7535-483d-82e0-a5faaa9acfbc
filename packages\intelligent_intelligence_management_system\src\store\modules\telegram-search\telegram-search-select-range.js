export default {
  namespaced: true,
  state: {
    dataList: [],
    res: false,
    nowPage: 1,
    size: 20,
    pageObj: {},
    nowPageItem: '',
    pathArr: [],
    pathArrObject: {},
    initNote: null,
    initResolve: null,
    computedPathArr: [],
    prefixArr: [],
  },
  mutations: {
    setClearPrefixArr(state, v) {
      state.prefixArr = []
    },
    setPrefixArrPush(state, v) {
      state.prefixArr.push(v)
    },
    setPrefixArrMinus(state, v) {
      for (var i = 0; i < state.pathArr.length; i++) {
        if (state.prefixArr[i] == v) {
          state.prefixArr.splice(i, 1);

          break;
        }
      }

    },
    setComputedPathArr(state, v) {
      state.computedPathArr = v
      let arr = [];
      state.pathArr.forEach((element, ind) => {
        state.computedPathArr.forEach((item, index) => {
          if (element.data_range_path === item) {
            arr.push(element)
          }
        })
      });
      state.pathArr = arr
    },
    setInitNote(state, v) {
      state.initNote = v.node
      state.initResolve = v.resolve
    },
    clearPathArr(state, v) {
      state.pathArr = v
    },
    clearPathObject(state, v) {
      state.pathArrObject = v
    },
    setPathArr(state, v) {
      state.pathArr.push(v)
      state.pathArrObject[v.data_range_path] = v
      var result = [state.pathArr[0]]
      for (var i = 1; i < state.pathArr.length; i++) {
        var item = state.pathArr[i]
        var repeat = false
        for (var j = 0; j < result.length; j++) {
          if (item['data_range_path'] === result[j]['data_range_path']) {
            repeat = true
            break
          }
        }
        if (!repeat) {
          result.push(item)
        }
      }
      state.pathArr = result

    },
    setPathArrMinus(state, v) {
      for (let str in state.pathArrObject) {
        if (v.data_range_path === str) {
          delete state.pathArrObject[str]
        }
      }
      let arr = []
      for (let str in state.pathArrObject) {
        arr.push(state.pathArrObject[str])
      }
      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (arr[j].data_range_father_path === arr[i].data_range_path) {
            arr.splice(j, 1);
          }
        }
      }
      state.pathArr = arr
    },
    setNowPageItem(state, v) {
      state.nowPageItem = v
    },
    setPage(state, v) {
      state.pageObj = Object.assign({}, state.pageObj, { [v]: { nowPage: 1, count: 0 } })
    },
    setNowPage(state, v) {
      if (state.pageObj[v].nowPage * state.size < state.pageObj[v].count) {
        state.pageObj[v].nowPage++
        window.main.$store.commit('telegramSearch/telegramSearchSelectRange/sendGetDataRangeList', v)
      } else {
        alert('已经是最后一页')
      }

    },
    setRes(state, v) {
      state.res = v
    },
    sendGetDataRangeCount(state, v) {
      window.main.$main_socket.sendData('Api.Search.DataRange.Count', [{
        head: {
          session_id: window.main.$store.state.userInfo.session_id,

        },
        "control": {
          "query_type": window.main.$store.state.telegramSearch.telegramSearchList.use_public
        },
        msg: {
          data_range_father_path: v,
        },
      }], 'telegramSearch/telegramSearchSelectRange/setDataCount');
    },
    setDataCount(state, data) {
      if (data) {
        state.pageObj[state.nowPageItem].count = data
      } else {
        state.pageObj[state.nowPageItem].count = 0
      }
    },
    sendGetDataRangeList(state, v) {
      window.main.$main_socket.sendData('Api.Search.DataRange.List', [{
        head: {
          session_id: window.main.$store.state.userInfo.session_id,
          from: (state.pageObj[v].nowPage - 1) * state.size,
          size: state.size,
        },
        "control": {
          "query_type": window.main.$store.state.telegramSearch.telegramSearchList.use_public
        },
        msg: {
          data_range_father_path: v,
        },
      }], 'telegramSearch/telegramSearchSelectRange/setDataList');
    },
    setDataList(state, data) {
      if (data && data[0].data_range_type) {
        state.dataList = data.map((item) => {
          if (item.data_range_type) {
            return item
          }
        })
        state.res = true
      } else {
        state.dataList = []
        state.res = true
      }
    },
  }
}
