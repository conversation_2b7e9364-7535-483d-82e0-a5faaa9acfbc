<template>
  <IndexChat ref="sessionIndex" :window-data="windowData"></IndexChat>
</template>

<script>
import SessionTypeConstant from "/src/common/constants/SessionType";
import ContentShowType from "@/common/constants/ContentShowType";
import IndexChat from "/src/components/session/IndexChat";
const windowData = {
  title: "Ai聊天室",
  description: "与Ai一起畅所欲言",
  sessionType: SessionTypeConstant.CHAT,
  contentShowType: ContentShowType.Markdown,
};
export default {
  data() {
    return { windowData };
  },
  components: {
    IndexChat,
  },
  created() {
    // this.init();
    //this.sendFn();
  },
  methods: {
    init() {
      // this.$store.commit("chat/setSessionList", []);
    },
    async sendFn() {
      /* try {
        const newData = await this.$store.dispatch("chat/getHistory");
      } catch (error) {
        console.error("加载数据失败:", error);
      } */
      /* let system_content = "你是谁";

      async function main() {
        window.main.$api
          .post("/chat", {
            messages: [{ role: "user", content: system_content }],
            model: "deepseek-r1:32b",
            stream: true,
            temperature: 1.3,
          })
          .then((res) => {
            console.log("chat返回的数据", res);
          });
      }

      main(); */
    },
  },
};
</script>

<style scoped lang="scss">
.main-session {
  height: 100%;
  width: 100%;
  display: flex;
}
.main-session-list {
  width: 15%;
  min-height: 100%;
  display: flex;
  transition: width 0.2s;
  word-break: keep-all;
}
.main-session-list.hiddenStatusSession {
  width: 0;
  transition-property: all;
}

.main-session-window {
  position: relative;
  min-width: 80%;
  width: auto;
  height: 100vh;
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
