import telegramSearchList from "./telegram-search-list";
import telegramSearchConditions from "./telegram-search-conditions";
import telegramSearchDataRange from "./telegram-search-data-range.js";
import telegramSearchSelectRange from "./telegram-search-select-range";
import telegramSearchDataDetail from "./telegram-search-data-detail";
import telegramSearchEScharts from "./telegram-search-EScharts";
import telegramSearchExportEScharts from "./telegram-search-exportEScharts";
import telegramSearchTask from "./telegram-search-task";
import telegramSearchStatistics from "./telegram-search-statistics";
import telegramSearchChartData from "./telegram-search-chart-data";
import telegramSearchChartRange from "./telegram-search-chart-range";
import telegramSearchInformation from "./telegram-search-information";
import telegramSearchCollection from "./telegram-search-collection";

export default {
  namespaced: true,
  state: {},
  mutations: {
    setKeyWord(state, params) {
      state.keyWord = params;
    },
  },
  actions: {},
  modules: {
    telegramSearchList,
    telegramSearchConditions,
    telegramSearchDataRange,
    telegramSearchSelectRange,
    telegramSearchDataDetail,
    telegramSearchEScharts,
    telegramSearchExportEScharts,
    telegramSearchTask,
    telegramSearchStatistics,
    telegramSearchChartData,
    telegramSearchChartRange,
    telegramSearchInformation,
    telegramSearchCollection,
  },
};
