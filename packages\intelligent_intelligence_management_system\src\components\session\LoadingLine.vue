<template>
  <div class="line" v-show="loadingLine"></div>
</template>

<script>
export default {
  name: "LoadingLine",
  props: {
    loadingLine: { type: Boolean, default: false },
  },
};
</script>

<style scoped>
/* 加载线 */
.line {
  position: relative;
  width: 94%;
  margin-left: 2%;
  min-height: 4px;
  background: linear-gradient(
    to right,
    rgba(217, 234, 243, 0.8) 0%,
    rgba(127, 225, 255, 0.9) 20%,
    rgba(119, 168, 251, 1) 40%,
    rgba(127, 225, 255, 0.9) 60%,
    rgba(217, 234, 243, 0.8) 80%
  );
  background-size: 200% 100%;
  border-radius: 2px;
  animation: loading-gradient 2s ease-in-out infinite;
}

.line::before,
.line::after {
  content: "";
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  background: inherit;
}

.line::before {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  left: 0;
  transform-origin: left;
  animation: shrink-left 2s ease-in-out infinite;
}

.line::after {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  right: 0;
  transform-origin: right;
  animation: shrink-right 2s ease-in-out infinite;
}

@keyframes loading-gradient {
  0% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes shrink-left {
  0%,
  50% {
    transform: scaleX(1);
  }

  50.1%,
  100% {
    transform: scaleX(0);
  }
}

@keyframes shrink-right {
  0%,
  50% {
    transform: scaleX(1);
  }

  50.1%,
  100% {
    transform: scaleX(0);
  }
}
</style>
