module.exports = {
  last_msg_timestamp: "最后发送时间",
  last_msg: "最后消息内容",
  case_key_person: "关键人",
  location: "归属地",
  // common
  username: "账号",
  type: "数据类型",
  "@username": "数据归属",
  "@timestamp": "入库时间",
  "@authority": "权限",
  group_member: "群成员号",
  group_name: "群号",

  // special
  file_path: "未整理文件名",
  timestamp: "时间",
  keyword_list: "关键词",
  sentence_list: "摘要",

  content_img_type: "图片正文类型",
  content_voice_type: "语音正文类型",
  content_video_type: "视频正文类型",
  content_file_type: "附件正文类型",
  content: "内容",
  create_time: "创建时间",
  group_nickname: "群昵称",
  tags: "自定义标签",
  icon: "头像文件",
  icon_url: "头像url",
  icon_type: "图片类型",
  friends_id: "好友ID",
  relation: "关系",
  task_id: "任务ID",
  summary: "简介",
  title: "标题",
  content_article: "正文",
  content_id: "正文ID",
  author_id: "作者ID",
  content_language: "正文语言",
  content_pdf: "PDF文件",
  content_img: "图片正文",
  content_img_url: "图片正文URL",
  content_img_type: "图片正文类型",
  content_voice: "音频正文",
  content_voice_url: "音频正文URL",
  content_voice_type: "音频正文类型",
  content_video: "视频正文",
  content_video_url: "视频正文URL",
  content_video_type: "视频正文类型",
  content_file: "附件正文",
  content_file_url: "附件正文URL",
  content_file_type: "附件正文类型",
  content_file_name: "附件名",
  group_authority: "群成员权限",
  content_article_father_author_id: "原作者ID",
  content_article_father_id: "原正文ID",
  password: "密码",
  user_id: "用户ID",
  firstname: "名字",
  lastname: "名字",
  middlename: "名字",
  fullname: "名字",
  nickname: "名字",
  email: "邮箱",
  wechat_name: "微信名称",
  wechat_nickname: "微信昵称",
  occupation: "工作",
  sesame_credit: "芝麻信用",
  web_site: "个人网站",
  organization: "组织",
  identity_number: "身份证号",
  birthday: "生日",
  father_birthday: "父亲生日",
  mather_birthday: "母亲生日",
  sex: "性别",
  address: "地址",
  stay_street: "居住街道",
  stay_city: "居住城市",
  stay_state: "居住状态",
  stay_country: "居住国家",
  contact_address: "现居地址",
  zip: "邮编",
  telephone: "电话",
  telephone_company: "公司电话",
  telephone_home: "家庭电话",
  credit_card: "信用卡",
  company: "公司",
  nationality: "国籍",
  birth_place: "出生地",
  address_company: "公司地址",
  zip_company: "公司邮编",
  target_id: "目标ID",
  friends_count: "好友数",
  article_count: "文章数",
  likes_count: "点赞数",
  followers_count: "粉丝数",
  following_count: "关注数",
  friends: "好友",
  lists_count: "列表数",
  midia_count: "媒体数",
  forward_count: "转发数",
  reply_count: "评论数",
  project: "项目",
  url: "URL",
  device_model: "设备型号",
  all_num: "总数",
  all_num_received: "已完成",
  query_string: "查询关键字",
  status: "状态",
  task_type: "任务类型",
  query_mode: "查询模式",
  time_range: "查询路径",
  update_time: "上传时间",
  error_index_num: "失败表总量",
  success_index_num: "成功表总量",
  content_source: "内容源",
  create_time_str: "创建时间",
  index: "所属库",
  moudule: "所属组",
  _id: "ID",
  spider_extend_task_running: "正在分析扩展任务",
  spider_base_task_running: "正在分析基础任务",
  nlp_parse_flag: "语义分析",
  success_data_num: "成功数据量",
  create_timestamp: "创建时间",
  pathSuffix: "文件名",
  accessTime: "访问",
  blockSize: "占用空间",
  length: "文件大小",
  modificationTime: "修改",
  replication: "复制",
  content_article_id: "正文ID",
  collection_time_range: "采集时间",
  collection_time_range: "采集时间",
  collection_time_range_begin: "开始采集",
  collection_time_range_end: "结束采集",
  time_range_begin: "开始",
  time_range_end: "结束",
  quote_count: "引用",
  name: "名字",
  zip_number: "邮编",
  content_article_father_author_id: "文章父权限ID",
  content_article_father_id: "文章父ID",
  parse_path: "数据路径",
};
