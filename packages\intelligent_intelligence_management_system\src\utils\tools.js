const CryptoJS = require("crypto-js");
module.exports = {
  utf16to8(str) {
    var out, i, len, c;
    out = "";
    len = str.length;
    for (i = 0; i < len; i++) {
      c = str.charCodeAt(i);
      if (c >= 0x0001 && c <= 0x007f) {
        out += str.charAt(i);
      } else if (c > 0x07ff) {
        out += String.fromCharCode(0xe0 | ((c >> 12) & 0x0f));
        out += String.fromCharCode(0x80 | ((c >> 6) & 0x3f));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
      } else {
        out += String.fromCharCode(0xc0 | ((c >> 6) & 0x1f));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
      }
    }
    return out;
  },
  generateESQuery(queryString, fields, matchType) {
    console.log("queryString", queryString, fields, matchType);

    // 参数验证
    if (
      !queryString ||
      !Array.isArray(fields) ||
      fields.length === 0 ||
      !matchType
    ) {
      throw new Error("Invalid parameters");
    }

    // 词法分析：将查询字符串转换为标记流
    function tokenize(input) {
      const tokens = [];
      let currentToken = "";
      let isNegative = false;

      for (let i = 0; i < input.length; i++) {
        const char = input[i];

        if (["-", "(", ")", "+", "|"].includes(char)) {
          if (currentToken.trim()) {
            tokens.push({
              type: "TERM",
              value: currentToken.trim(),
              isNegative: isNegative,
            });
            currentToken = "";
            isNegative = false;
          }
          if (char === "-") {
            isNegative = true;
          } else {
            tokens.push({ type: "OPERATOR", value: char });
          }
        } else {
          currentToken += char;
        }
      }

      if (currentToken.trim()) {
        tokens.push({
          type: "TERM",
          value: currentToken.trim(),
          isNegative: isNegative,
        });
      }

      return tokens;
    }

    // 创建字段查询
    function createFieldQuery(field, terms, isAnd = false) {
      if (isAnd) {
        return {
          bool: {
            must: terms.map((term) => ({
              [matchType]: {
                [field]: term,
              },
            })),
          },
        };
      } else {
        return {
          bool: {
            should: terms.map((term) => ({
              [matchType]: {
                [field]: term,
              },
            })),
          },
        };
      }
    }

    // 语法分析：解析标记流并构建查询
    function parseTokens(tokens) {
      // 确定是否是AND查询
      const isAndQuery = tokens.some(
        (token) => token.type === "OPERATOR" && token.value === "+"
      );

      // 提取所有词条
      const terms = tokens
        .filter((token) => token.type === "TERM" && !token.isNegative)
        .map((token) => token.value);

      // 提取所有需要排除的词条
      const excludeTerms = tokens
        .filter((token) => token.type === "TERM" && token.isNegative)
        .map((token) => token.value);

      // 构建查询
      const query = {
        bool: {
          should: fields.map((field) =>
            createFieldQuery(field, terms, isAndQuery)
          ),
        },
      };

      // 添加排除条件
      if (excludeTerms.length > 0) {
        query.bool.must_not = excludeTerms.flatMap((term) =>
          fields.map((field) => ({
            [matchType]: {
              [field]: term,
            },
          }))
        );
      }

      return query;
    }

    try {
      const tokens = tokenize(queryString);
      return parseTokens(tokens);
    } catch (error) {
      throw new Error(`Failed to generate query: ${error.message}`);
    }
  },
  debounce(func, delay, immediate) {
    let timer;
    return function () {
      if (timer) clearTimeout(timer);
      if (immediate) {
        // 复杂的防抖函数
        // 判断定时器是否为空，如果为空，则会直接执行回调函数
        let firstRun = !timer;
        // 不管定时器是否为空，都会重新开启一个新的定时器,不断输入，不断开启新的定时器，当不在输入的delay后，再次输入就会立即执行回调函数
        timer = setTimeout(() => {
          timer = null;
        }, delay);
        if (firstRun) {
          func.apply(this, arguments);
        }
        // 简单的防抖函数
      } else {
        timer = setTimeout(() => {
          func.apply(this, arguments);
        }, delay);
      }
    };
  },
  //字符串补全足够长度
  padString(str, currentLength) {
    const strValue = String(str || "");
    let currentWidth = 0;
    for (const char of strValue) {
      currentWidth += /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
    }
    if (currentWidth >= currentLength) return strValue;
    const spacesNeeded = currentLength - currentWidth;
    return strValue + " ".repeat(spacesNeeded);
  },
  //超长字符隐藏显示省略号
  longText(str, len, word) {
    let strs = str.replace(/\s*/g, "");
    let result = "";
    let realLength = 0;
    let rule = "";
    rule = word ? /[\u4e00-\u9fa5，；“”？：《》]/ : /[\u4e00-\u9fa5]/;
    for (const char of strs) {
      realLength += rule.test(char) ? 2 : 1;
      // const charCode = strs.charCodeAt(i);
      // if (charCode >= 0 && charCode <= 128) {
      //   realLength += 1;
      // } else {
      //   realLength += 2;
      // }
      if (realLength <= len) {
        result += char;
      } else {
        break;
      }
    }
    if (realLength > len) {
      result = result.substring(0, result.length - 1) + "...";
    }
    return result;
  },
  //数字金额转大写
  changeNumToHan(num) {
    var arr1 = new Array(
      "零",
      "一",
      "二",
      "三",
      "四",
      "五",
      "六",
      "七",
      "八",
      "九"
    );
    var arr2 = new Array(
      "",
      "十",
      "百",
      "千",
      "万",
      "十",
      "百",
      "千",
      "亿",
      "十",
      "百",
      "千",
      "万",
      "十",
      "百",
      "千",
      "亿"
    ); //可继续追加更高位转换值
    if (!num || isNaN(num)) {
      return "零";
    }
    var english = num.toString().split("");
    var result = "";
    for (var i = 0; i < english.length; i++) {
      var des_i = english.length - 1 - i; //倒序排列设值
      result = arr2[i] + result;
      var arr1_index = english[des_i];
      result = arr1[arr1_index] + result;
    }
    //将【零千、零百】换成【零】 【十零】换成【十】
    result = result.replace(/零(千|百|十)/g, "零").replace(/十零/g, "十");
    //合并中间多个零为一个零
    result = result.replace(/零+/g, "零");
    //将【零亿】换成【亿】【零万】换成【万】
    result = result.replace(/零亿/g, "亿").replace(/零万/g, "万");
    //将【亿万】换成【亿】
    result = result.replace(/亿万/g, "亿");
    //移除末尾的零
    result = result.replace(/零+$/, "");
    //将【零一十】换成【零十】
    //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
    //将【一十】换成【十】
    result = result.replace(/^一十/g, "十");
    return result;
  },
  timestampToTime(timestamp, format) {
    // 参数验证
    if (!timestamp) return "";
    if (!format) format = "YYYY-MM-DD HH:mm:ss";

    if (typeof timestamp !== "number") {
      timestamp = parseInt(timestamp);
    }

    // 处理毫秒级时间戳
    if (timestamp.toString().length === 13) {
      timestamp = Math.floor(timestamp / 1000);
    }

    const date = new Date(timestamp * 1000);
    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (match) => {
      switch (match) {
        case "YYYY":
          return date.getFullYear();
        case "MM":
          return (date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
        case "DD":
          return (date.getDate() < 10 ? "0" : "") + date.getDate();
        case "HH":
          return (date.getHours() < 10 ? "0" : "") + date.getHours();
        case "mm":
          return (date.getMinutes() < 10 ? "0" : "") + date.getMinutes();
        case "ss":
          return (date.getSeconds() < 10 ? "0" : "") + date.getSeconds();
        default:
          return match;
      }
    });
  },
  // 时间戳转时间到时
  timestampToTimeH(timestamp, format) {
    // 参数验证
    if (!timestamp) return "";
    if (!format) format = "MM-DD HH:mm";

    if (typeof timestamp !== "number") {
      timestamp = parseInt(timestamp);
    }

    // 处理毫秒级时间戳
    if (timestamp.toString().length === 13) {
      timestamp = Math.floor(timestamp / 1000);
    }

    const date = new Date(timestamp * 1000);
    return format.replace(/MM|DD|HH|mm/g, (match) => {
      switch (match) {
        case "MM":
          return (date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
        case "DD":
          return (date.getDate() < 10 ? "0" : "") + date.getDate();
        case "HH":
          return (date.getHours() < 10 ? "0" : "") + date.getHours();
        case "mm":
          return (date.getMinutes() < 10 ? "0" : "") + date.getMinutes();

        default:
          return match;
      }
    });
  },
  // 七天内的时间戳范围数组
  timeRangeArray() {
    let nowTime = Math.round(
      new Date(new Date().setHours(23, 59, 59, 99)).getTime() / 1000
    );
    let oneDay = 86400;
    let nowTime1 = nowTime - 86399;
    let nowTime2 = nowTime1 - oneDay;
    let nowTime3 = nowTime2 - oneDay;
    let nowTime4 = nowTime3 - oneDay;
    let nowTime5 = nowTime4 - oneDay;
    let nowTime6 = nowTime5 - oneDay;
    let nowTime7 = nowTime6 - oneDay;
    let timeArr = [
      {
        from: nowTime7,
        to: nowTime6,
      },
      {
        from: nowTime6,
        to: nowTime5,
      },
      {
        from: nowTime5,
        to: nowTime4,
      },
      {
        from: nowTime4,
        to: nowTime3,
      },
      {
        from: nowTime3,
        to: nowTime2,
      },
      {
        from: nowTime2,
        to: nowTime1,
      },
      {
        from: nowTime1,
        to: nowTime,
      },
    ];
    return timeArr;
  },
  sha512(data) {
    return CryptoJS.SHA512(data).toString();
  },
  formatNumber(num) {
    num *= 10;
    if (num < 10000) {
      return num.toString(); // 不加单位
    }
    const units = ["万", "千万", "亿"];
    const divisors = [10000, 10000000, 100000000];

    for (let i = divisors.length - 1; i >= 0; i--) {
      if (num >= divisors[i]) {
        const formattedNum = num / divisors[i];
        const decimalPart = formattedNum % 1;
        return formattedNum.toFixed(decimalPart > 0 ? 2 : 0) + units[i];
      }
    }
  },
  dataPreprocessing(data) {
    let obj = {
      path: data.columnValues.path,
    };
    const d = data.columnValues.d;
    for (const key in d) {
      if (Object.hasOwnProperty.call(d, key)) {
        const item = d[key];
        if (Object.prototype.toString.call(item) === "[object Object]") {
          for (const key2 in item) {
            if (Object.hasOwnProperty.call(item, key2)) {
              const value = item[key2];
              if (Object.prototype.toString.call(value) === "[object Object]") {
                obj[key2] = value;
              } else {
                if (obj[key2]) {
                  obj[key2].push(value);
                } else {
                  obj[key2] = [value];
                }
              }
            }
          }
        } else {
          obj[key] = item;
        }
      }
    }
    return obj;
  },
  isPlainObject(value) {
    return (
      Object.prototype.toString.call(value) === "[object Number]" ||
      Object.prototype.toString.call(value) === "[object String]"
    );
  },
  reduceNumber() {
    let soleValue = Math.round(new Date().getTime() / 1000).toString();
    let random = new Array(
      "a",
      "b",
      "c",
      "d",
      "e",
      "f",
      "g",
      "h",
      "i",
      "j",
      "k",
      "l",
      "m",
      "n"
    );
    for (let i = 0; i < 6; i++) {
      let index = Math.floor(Math.random() * 13);
      soleValue += random[index];
    }
    return soleValue;
  },
};
