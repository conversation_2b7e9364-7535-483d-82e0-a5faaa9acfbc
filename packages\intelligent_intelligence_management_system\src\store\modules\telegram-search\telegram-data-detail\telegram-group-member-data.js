export default {
  namespaced: true,
  state: {
    from: 0,
    size: 20,
  },
  mutations: {
    clearGroupMemberData(state) {
      state.from = 0;
      this.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "group_member_data",
        value: [],
      });
    },
    async sendGetDataDetailGroupMemberData(state) {
      let tmpObj =
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["elasticsearch_data"];
      if (tmpObj["_source"]) {
        tmpObj["_source"] = tmpObj["_source"];
      }
      await window.main.$main_socket.sendData(
        "Api.Search.DataDetail.GroupMemberData",
        [
          {
            head: {
              session_id: this.state.userInfo.session_id,
              from: state.from,
              size: state.size,
            },
            msg: tmpObj,
          },
        ],
        "telegramSearch.telegramSearchDataDetail/telegramGroupMemberData/setDataDetailGroupMemberData"
      );
    },
    setDataDetailGroupMemberData(state, data) {
      if (data == null) {
        return;
      }
      if (data.hits == null) {
        return;
      }
      if (!data.hits.hasOwnProperty("hits")) {
        window.main.$message({
          message: "没有数据了",
          type: "warning",
        });
        return;
      }
      if (
        !window.main.$store.state.telegramSearch.telegramSearchDataDetail.hasOwnProperty(
          "group_member_data"
        )
      ) {
        window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_member_data"] =
          data.hits.hits;
      } else {
        window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_member_data"] =
          window.main.$store.state.telegramSearch.telegramSearchDataDetail[
            "group_member_data"
          ].concat(data.hits.hits);
      }
      state.from =
        window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_member_data"].length;
    },
  },
};
