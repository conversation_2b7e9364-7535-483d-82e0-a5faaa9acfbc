<template>
    <div class="socialWorkLibrary">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" v-show="dataList.length">
            <span v-if="!checkedCities.length">全选</span>
            <el-button v-else size="mini" @click="toDelCheckCol">
                删除
            </el-button>
        </el-checkbox>
        <div v-masonry item-selector=".item" horizontal-order="true" class="social_data_item">
            <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange"
                style="display: flex;flex-wrap: wrap;">
                <div v-masonry-tile v-for="(item, index) in dataList" :key="index" class="social_item">
                    <el-checkbox :label="item" :key="index" style="width: 10%;"><br></el-checkbox>
                    <template v-if="item">
                        <Content :itemData="item"></Content>
                    </template>
                </div>
            </el-checkbox-group>
        </div>
        <!-- <p class="social_search_over_tip" v-if="dataList.length">数据已经到底了!</p> -->
    </div>
</template>
<script>
import Content from '../social-enginnering-database/content-view.vue'
import { mapState } from "vuex";
export default {
    name: 'socialWorkLibrary',
    data() {
        return {
            checkAll: false,
            isIndeterminate: false,
        };
    },
    components: {
        Content,
    },
    computed: {
        ...mapState({
            dataList: (state) => state.collect.dataList,
            nowCollect: (state) => state.collect.nowCollect,
        }),
        checkedCities: {
            get() {
                return this.$store.state.collect.checkedCities;
            },
            set(val) {
                this.$store.commit("collect/setCheckedCities", val);
            },
        },
    },
    mounted() {
        this.$store.commit("collect/clearDataList")
        setTimeout(() => {
            if (this.nowCollect) {
                this.$store.commit("collect/getOpinionCollect");
            }
        }, 500);
    },
    methods: {

        toDelCheckCol() {
            console.log("checkedCities", this.checkedCities);
            if (this.checkedCities.length) {
                this.$confirm("是否取消选中的收藏列表?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        let row = []
                        this.checkedCities.forEach(element => {
                            row.push(element.row)
                        });
                        this.$store.commit("collect/sendDelFileData", { row, collCache: this.collCache });
                        this.handleCheckedCitiesChange([])
                    })
                    .catch((err) => {
                        console.log(err);
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                this.$message.warning('未选择收藏内容')
            }
        },
        handleCheckedCitiesChange(value) {
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.dataList.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.dataList.length;
        },
        // 全导出新闻全选
        handleCheckAllChange(val) {
            this.checkedCities = val ? this.dataList : [];
            this.isIndeterminate = false;
        },
    },

};
</script>

<style scoped lang="scss">
.socialWorkLibrary {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    // display: grid;
    // grid-template-columns: repeat(3, 32%);
    // grid-auto-rows: 140px;
    // gap: 10px;
    padding-bottom: 15px;
}

.newContent {
    padding: 3px 10px 5px 5px;
    border: 1px solid #ccc;
}

.social_data_item {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;

    .social_item {
        overflow: auto;
        width: 33%;
        font-size: 14px;
        height: 200px;
        margin: 0 4px 4px 0;
        border: 3px solid #e9e7e7;
        background-color: #fff;
        border-radius: 5px;
        padding: 10px;
        box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: row;
    }
}
</style>