<template>
  <div class="newsDisplay">
    <div class="translateList">
      <el-tree
        :data="translatedTree"
        :props="defaultProps"
        @node-click="handleNodeClick"
        node-key="id"
        highlight-current
        :current-node-key="1"
        default-expand-all
      ></el-tree>
    </div>
    <div class="title">
      {{ $store.state.NewsDisplay.NewsDet._source.title }}
    </div>
    <div
      class="infoNews"
      style="
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        min-height: 160px;
      "
    >
      <div
        class="word"
        style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); width: 69.5%"
      >
        <div
          v-if="$store.state.NewsDisplay.NewsDet._source.nlp_parse_flag"
          style="display: flex; justify-content: space-around"
        >
          <div
            v-show="
              $store.state.NewsDisplay.NewsDet._source.analysis_doc_keyword
            "
            id="myChart"
            :style="{ height: '160px', width: '400px' }"
            style="padding-top: 5px"
          ></div>
          <li
            class="morInfor_r_li"
            v-if="$store.state.NewsDisplay.NewsDet._source.sentence_list"
          >
            <div
              v-if="$store.state.NewsDisplay.NewsDet._source.keyword_list"
              class="morInfor_r_li_h"
              style="display: flex"
            >
              <div class="morInfor_r_li_h" style="width: 70px">关键词：</div>
              <span
                v-for="(item, index) in $store.state.NewsDisplay.NewsDet._source
                  .keyword_list"
                :key="index"
                style="color: red"
              >
                {{ item }}
                <span
                  style="color: black"
                  v-show="
                    index + 1 !=
                    $store.state.NewsDisplay.NewsDet._source.keyword_list.length
                  "
                  v-html="comma"
                ></span>
              </span>
            </div>
            <div style="display: flex">
              <div
                class="morInfor_r_li_h"
                style="width: 70px; text-align: justify"
              >
                摘要：
              </div>
              <div class="abstract" style="color: red; width: 80%">
                <div
                  class="morInfor_r_li_c"
                  v-for="(item, index) in $store.state.NewsDisplay.NewsDet
                    ._source.sentence_list"
                  :key="index"
                >
                  {{ index + 1 + ":" + item }}
                </div>
              </div>
            </div>
          </li>
        </div>
      </div>
      <div class="operation">
        <div class="newsInfo">
          <p>文章来源：{{ $store.state.NewsDisplay.NewsDet._source.type }}</p>
          <p
            style="
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            "
          >
            原文链接：<a
              style="text-decoration: underline"
              target="_blank"
              :href="$store.state.NewsDisplay.NewsDet._source.url"
              >{{ $store.state.NewsDisplay.NewsDet._source.url }}</a
            >
          </p>
          <p>入库时间：{{ NewsTime }}</p>
        </div>
        <div
          class="operationIn"
          style="
            width: 25%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            padding: 20px 15px;
            box-sizing: border-box;
            align-items: center;
            font-size: 14px;
            text-align: center;
          "
        >
          <!-- <el-button
              v-if="
                this.$store.state.NewsDisplay.NewsDet._source.tags &&
                this.$store.state.NewsDisplay.NewsDet._source.tags.indexOf(
                  'collect'
                ) != -1
              "
              >已收藏</el-button
            > -->
          <!-- <el-button size="mini" @click="collectedData()" style="margin-top: 15px">收藏</el-button> -->
          <!-- <div>
            重要新闻
            <el-rate @change="fastCollect" v-model="collectImportant" :max="3" :texts="impText" show-text>
            </el-rate>
          </div> -->
        </div>
      </div>
    </div>
    <div
      class="allContent"
      style="
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.2);
        margin-bottom: 50px;
      "
      v-if="transltedId == 1 || transltedId == 2 || tranType"
    >
      <div
        style="
          display: flex;
          flex-wrap: wrap;
          justify-content: space-around;
          padding: 10px 0;
          min-height: 12vh;
        "
        v-if="$store.state.NewsDisplay.NewsDet._source.content_img"
      >
        <div
          class="pic"
          v-for="(item, index) in $store.state.NewsDisplay.NewsDet._source
            .content_img"
          :key="index"
        >
          <div class="demo-image__preview">
            <el-image
              style="width: 100px; height: 100px; background: #ccc"
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                item.sha512_hash
              "
              :preview-src-list="imgList"
              :initial-index="index"
            >
            </el-image>
          </div>
        </div>
      </div>
      <div class="allImgText" style="display: flex">
        <div
          class="contentText"
          style="
            display: flex;
            flex: 1;
            padding: 0 10px 0 10px;
            box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1);
          "
        >
          <div
            class="hasText"
            style="height: 550px; white-space: pre-wrap; height: 100%"
          >
            <div
              title="视频"
              name="4"
              v-if="$store.state.NewsDisplay.NewsDet._source"
            >
              <div
                v-for="(imgItem, imgindex) in $store.state.NewsDisplay.NewsDet
                  ._source.content_video"
                :key="imgindex"
              >
                <span v-if="!imgItem">链接为空</span>
                <video
                  controls="controls"
                  preload="auto"
                  muted
                  loop
                  :ref="'videoB' + imgindex"
                  :id="'videoB' + imgindex"
                >
                  <source
                    :src="
                      '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/' +
                      imgItem.sha512_hash
                    "
                    :type="imgItem.content_video_type"
                    :alt="imgItem ? imgItem : '链接为空'"
                  />
                </video>
                <p>
                  选择播放速率：<select
                    ref="selRate"
                    @change="videoFn('videoB' + imgindex)"
                  >
                    <option value="0.5">0.5</option>
                    <option value="1" selected>1.0</option>
                    <option value="1.25">1.25</option>
                    <option value="1.5">1.5</option>
                    <option value="2">2.0</option>
                    <option value="2">3.0</option>
                    <option value="2">4.0</option>
                  </select>
                </p>
                <p>{{ imgItem.file_name }}</p>
              </div>
            </div>
            <div class="transltedTitle">原文</div>
            <div style="display: flex">
              <div
                v-if="
                  $store.state.NewsDisplay.NewsDet._source && isPdf && !tranType
                "
                class="hasPdf"
              >
                <div
                  style="height: 100%; width: 100%"
                  v-for="(imgItem, imgindex) in $store.state.NewsDisplay.NewsDet
                    ._source.content_pdf"
                  :key="imgindex"
                >
                  <pdf
                    v-for="i in numPages[imgindex]"
                    ref="pdf"
                    :key="i"
                    :src="
                      '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                      imgItem.sha512_hash
                    "
                    :page="i"
                  ></pdf>
                </div>
              </div>
              <div
                v-html="oriContenttext"
                class="content"
                style="white-space: pre-wrap; flex: 1"
              ></div>
            </div>
          </div>
        </div>
        <div
          class="transltedText"
          style="
            width: 50%;
            padding: 0 10px 0 10px;
            box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1);
          "
          v-if="tranType"
        >
          <div class="transltedTitle">译文</div>
          <div
            v-html="tranContenttext"
            class="content"
            style="white-space: pre-wrap"
          ></div>
        </div>
      </div>
    </div>
    <!-- 添加收藏数据 -->
    <!-- <el-dialog title="添加收藏数据" :visible.sync="favoriteDataVisible" :close-on-click-modal="false"
        @close="clearexfavoriteData" width="40%" append-to-body>
        <el-form>
          <el-form-item label="数据库：">
            <el-radio-group v-model="databaseRadio">
              <el-radio v-for="item in databaseList" :label="item.name" :key="item.id" border @change="selectFile">{{
                item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="databaseRadio === 'case'" label="案件名：">
            <div style="display: flex; width: 72%">
              <el-input type="text" :value="file_case_name" disabled></el-input>
              <el-button type="primary" plain @click="showSelectCaseId()">选取案件</el-button>
            </div>
          </el-form-item>
          <el-form-item v-else label="文件名：">
            <div style="display: flex; width: 72%">
              <el-input type="text" :value="selectFileName" disabled></el-input>
              <el-button type="primary" plain @click="showSelectFileId()">选取文件</el-button>
            </div>
          </el-form-item>
        </el-form> -->
    <!-- 选取案件 -->
    <!-- <el-dialog :visible.sync="caseDirIdDialog" title="选取案件" top="10px" width="40%" append-to-body>
          <div style="height: 500px; overflow: auto">
            <tree-box style="color: #333" :key="componentKey" :data-node="rootNode2"
              sub-component="selectID-tree"></tree-box>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="selectCaseID()">取 消</el-button>
          </div>
        </el-dialog>
        <el-dialog :visible.sync="fileDirIdDialog" title="选取文件" top="10px" width="40%" append-to-body @close="clearTree">
          <div style="height: 500px; overflow: auto">
            <div style="padding-top: 15px">
              <el-tree :data="dataTree" node-key="id" default-expand-all :expand-on-click-node="false"
                @node-click="selectClickNode">
                <span class="custom-tree-node" slot-scope="{ node }">
                  <span><i :class="node.data.icon"></i> {{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="fileDirIdDialog = false">取 消</el-button>
          </div>
        </el-dialog> -->
    <!-- <span slot="footer">
          <el-button @click="clearexfavoriteData">取 消</el-button>
          <el-button type="primary" @click="sendFavoriteData">收 藏</el-button>
        </span>
      </el-dialog> -->
  </div>
</template>

<script>
import pdf from "vue-pdf";
import { mapState } from "vuex";
export default {
  name: "NewsDisplay",
  data() {
    return {
      collectImportant: null,
      collCache: null,
      impText: ["一般", "重要", "非常重要"],
      impList: ["common", "important", "veryImport"],
      impListSecond: ["commonSecond", "importantSecond", "veryImportSecond"],
      comma: ",&nbsp;",
      transltedId: 1,
      orginalInfo: "",
      defaultProps: {
        children: "children",
        label: "label",
      },

      isPdf: true,
      hasPdf: false,
      show: "",
      NewsAllInformation: {},
      NewsMedia: "",
      imgList: [],
      NewsTime: "",
      pdfsha: "",
      numPages: [],
      errNum: 0,

      //收藏
      favoriteDataVisible: false,
      selectFileName: "",
      databaseList: [
        { label: "公共库", name: "public", id: 0 },
        { label: "私有库", name: "username", id: 1 },
        { label: "权限库", name: "authority", id: 2 },
        { label: "案件库", name: "case", id: 3 },
      ],
      databaseRadio: "public",
      fileDirIdDialog: false,
      dataTree: [
        {
          id: 0,
          label: "/",
          icon: "el-icon-folder",
          showInput: false,
          children: [],
        },
      ],
      selectFileId: "",
    };
  },
  mounted() {
    this.slowLoading();
    this.getCase();
  },
  methods: {
    async fastCollect(type) {
      if (this.collCache && type == this.collCache) {
        return;
      }
      await this.getReadList(type);
      this.collCache = type;
      this.$store.dispatch("collect/sendFastAddFileData", {
        activeName: this.databaseRadio,
        file_id: this.selectFileId,
        case_id: this.file_case_id,
        item: this.$store.state.NewsDisplay.NewsDet,
        type,
      });
    },
    handleNodeClick(data) {
      this.$store.commit("NewsDisplay/settranType", data.type);
      if (!data.type) {
        this.$store.commit("NewsDisplay/searchListQuery", {
          id: data.oriInfo.tranId,
          index: data.oriInfo.index,
        });
      } else {
        this.$store.commit("NewsDisplay/searchListQuery", {
          id: data.oriInfo.tranId,
          index: data.oriInfo.index,
        });
        this.$store.commit("NewsDisplay/setTransearchListQuery", {
          id: data.tranInfo.tranId,
          index: data.tranInfo.index,
        });
      }
    },
    // 获取案件树
    getCase() {
      if (window.main.$pki_socket) {
        let id = this.$route.query.id;
        let index = this.$route.query.index;
        this.morefn(window.main.$store.state.NewsDisplay.NewsDet);

        this.$store.commit("NewsDisplay/searchListQuery", {
          id,
          index,
          changetree: true,
        });
        // this.$store.commit("NewsDisplay/getTranslatedIndex", { id, index });
        // this.$store.commit("personnelTree/resetCaseDirTree");
        // this.$store.commit("personnelTree/setCaseDirFatherListObj", []);
        // this.$store.commit("personnelTree/setCaseDirFatherList", []);
        // this.$store.commit("personnelTree/setCaseDirFather", "");
        // this.$store.commit("personnelTree/sendCaseDirFatherArr");
      } else {
        setTimeout(() => {
          this.getCase();
        }, 1000);
      }
    },
    slowLoading() {
      if (window.main.$store.state.NewsDisplay.NewsDet._source.timestamp) {
        window.main.$store.state.NewsDisplay.NewsDet["_source"]
          ? (this.hasPdf = true)
          : "";
        if (window.main.$pki_socket) {
          this.pdfFn();
        }

        if (
          window.main.$store.state.NewsDisplay.NewsDet["_source"]
            .analysis_doc_keyword &&
          window.main.$store.state.NewsDisplay.NewsDet["_source"]
            .analysis_doc_number
        ) {
          this.getNewsKeys(
            window.main.$store.state.NewsDisplay.NewsDet["_source"]
              .analysis_doc_keyword,
            window.main.$store.state.NewsDisplay.NewsDet["_source"]
              .analysis_doc_number
          );
        }
        this.addRead();
        // this.getReadList()
        if (
          window.main.$store.state.NewsDisplay.NewsDet?.hasOwnProperty(
            "_source"
          )
        ) {
          let jieguo = window.main.$store.state.NewsDisplay.NewsDet._source;
          this.NewsAllInformation = jieguo;
          // 新闻内容
          this.oriContent = jieguo.content_article
            ? jieguo.content_article
            : "";
          this.NewsMedia = jieguo.type;
          // 新闻时间
          this.NewsTime = this.changeTime(jieguo.timestamp * 1000);
          this.show = jieguo.hasOwnProperty("content_img");
          if (jieguo.content_img && jieguo.content_img.length) {
            jieguo.content_img.forEach((e) => {
              let imgUrl =
                window.location.href.split("#")[0] +
                "filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/" +
                e.sha512_hash;
              this.imgList.push(imgUrl);
            });
          }
        }
      } else {
        setTimeout(() => {
          this.slowLoading();
        }, 1000);
      }
    },
    //快速收藏查询
    async getReadList(type) {
      try {
        // 将所有查询转换为 Promise 数组
        const promises = this.impList.map((element, index) => {
          return new Promise((resolve, reject) => {
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefixTable.Query",
              [
                {
                  head: {
                    size: 200,
                  },
                  msg: {
                    table: "favorites_data",
                    type: "username",
                    relation: element,
                    prefix: this.$route.query.id,
                  },
                },
              ],
              (res) => {
                if (res.length && res[0]) {
                  if (type) {
                    // 如果是删除操作
                    window.main.$main_socket.sendData(
                      "Api.Search.SearchPrefixTable.DelData",
                      [
                        {
                          head: {
                            row_key: [res[0].row],
                          },
                          msg: {
                            table: "favorites_data",
                            type: "username",
                            relation: element,
                          },
                        },
                      ]
                    );
                    window.main.$main_socket.sendData(
                      "Api.Search.SearchPrefixTable.Query",
                      [
                        {
                          head: {
                            size: 200,
                          },
                          msg: {
                            table: "favorites_data",
                            type: "username",
                            relation: this.impListSecond[index],
                            prefix: res.prefixSecond,
                          },
                        },
                      ],
                      (ress) => {
                        if (ress.length && ress[0]) {
                          window.main.$main_socket.sendData(
                            "Api.Search.SearchPrefixTable.DelData",
                            [
                              {
                                head: {
                                  row_key: [ress[0].row],
                                },
                                msg: {
                                  table: "favorites_data",
                                  type: "username",
                                  relation: this.impListSecond[index],
                                },
                              },
                            ]
                          );
                        }
                      }
                    );
                  } else {
                    // 如果是查询操作
                    if (res[0].columnValues.d.type) {
                      this.collectImportant = res[0].columnValues.d.type * 1;
                      this.collCache = res[0].columnValues.d.type;
                    }
                  }
                }
                resolve();
              }
            );
          });
        });

        // 等待所有 Promise 完成
        await Promise.all(promises);

        // 这里可以执行下一步操作
        return true;
      } catch (error) {
        return false;
      }
    },

    // getReadList(type){
    //   this.impList.forEach(element => {
    //     window.main.$main_socket.sendData(
    //     "Api.Search.SearchPrefixTable.Query",
    //     [
    //       {
    //         head: {
    //           size: 200,
    //         },
    //         msg: {
    //           table: "favorites_data",
    //           type: 'username',
    //           relation: element,
    //           prefix:this.$route.query.id
    //         },
    //       },
    //     ],
    //     (res) => {
    //       if(res.length&&res[0]){
    //         if(type){
    //           window.main.$main_socket.sendData(
    //           "Api.Search.SearchPrefixTable.DelData",
    //           [
    //             {
    //               head: {
    //                 row_key: [res[0].row],
    //               },
    //               msg: {
    //                 table: "favorites_data",
    //                 type: 'username',
    //                 relation:element
    //               },
    //             },
    //           ],
    //         );
    //         }else{
    //           if(res[0].columnValues.d.type){
    //             this.collectImportant = res[0].columnValues.d.type
    //           }
    //         }
    //       }
    //     }
    //   );
    //   });

    // },
    getNewsKeys(keyword, number) {
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      let option = {
        grid: {
          left: "10%",
          top: "19%",
          right: "5%",
          bottom: "16%",
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true },
            saveAsImage: { show: true, backgroundColor: "rgba(0, 0, 0, 0.75)" },
          },
        },
        xAxis: {
          type: "category",
          data: keyword,
          axisLabel: {
            interval: 0, //强制显示文字
            rotate: 20,
            fontSize: "14",
          },
          axisLine: {
            lineStyle: {
              color: "#000",
            },
          },
        },
        yAxis: {
          name: "词频/次",
          type: "value",
          axisLine: {
            lineStyle: {
              color: "#000",
            },
          },
        },
        series: [
          {
            label: {
              show: true,
              formatter: function (data) {
                return data.value;
              },
            },
            data: number,
            type: "bar",
            barWidth: 20,
          },
        ],
      };
      myChart.setOption(option);
    },
    //收藏
    collectedData() {
      this.favoriteDataVisible = true;
    },
    // 取消收藏操作
    clearexfavoriteData() {
      this.selectFileName = "";
      this.favoriteDataVisible = false;
    },
    //选择案件
    showSelectCaseId() {
      this.caseDirIdDialog = true;

      this.$store.commit("selectTree/resetCaseDirTree");
      this.$store.commit("selectTree/setCaseDirFatherListObj", []);
      this.$store.commit("selectTree/setCaseDirFatherList", []);
      this.$store.commit("selectTree/sendCaseDirFatherArr");
    },
    // 选择文件夹
    showSelectFileId() {
      this.sendDirList(this.databaseRadio);
      this.fileDirIdDialog = true;
    },
    // 切换数据库
    selectFile() {
      this.selectFileName = "";
    },
    // 取消选择案件
    selectCaseID() {
      this.$message.warning("取消选择案件");
      this.caseDirIdDialog = false;
    },
    // 选择文件
    selectClickNode(data) {
      if (data.children.length) {
        return;
      }
      if (data.icon === "el-icon-folder") {
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",
          [
            {
              head: {
                size: 200,
              },
              msg: {
                table: "favorites_data",
                prefix: "",
                type: this.databaseRadio,
                relation: data.id + ";dir",
              },
            },
          ],
          (res) => {
            res?.forEach((item) => {
              data.children.push({
                id: item.columnValues.d._._,
                label: item.columnValues.d.name.name,
                row: item.row,
                showInput: false,
                icon:
                  item.columnValues.d.type.type === "dir"
                    ? "el-icon-folder"
                    : "el-icon-tickets",
                children: [],
              });
            });
          }
        );
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",
          [
            {
              head: {
                size: 200,
              },
              msg: {
                table: "favorites_data",
                prefix: "",
                type: this.databaseRadio,
                relation: data.id + ";file",
              },
            },
          ],
          (res) => {
            res?.forEach((item) => {
              data.children.push({
                id: item.columnValues.d._._,
                label: item.columnValues.d.name.name,
                row: item.row,
                showInput: false,
                icon:
                  item.columnValues.d.type.type === "dir"
                    ? "el-icon-folder"
                    : "el-icon-tickets",
                children: [],
              });
            });
          }
        );
      } else {
        this.$confirm("确定选择此文件?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.selectFileName = data.label;
            this.selectFileId = data.id;
            this.fileDirIdDialog = false;
            this.$message.success("设置文件为：" + this.selectFileName);
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消选择",
            });
          });
      }
    },
    // 清空文件树
    clearTree() {
      this.dataTree = [
        {
          id: 0,
          label: "/",
          icon: "el-icon-folder",
          showInput: false,
          children: [],
        },
      ];
    },
    // 获取目录表
    sendDirList(v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: 200,
            },
            msg: {
              table: "favorites_data",
              prefix: "",
              type: v,
            },
          },
        ],
        (res) => {
          res?.forEach((item) => {
            if (!item.columnValues.d._) {
              return;
            }
            this.dataTree[0].children.push({
              id: item.columnValues.d._._,
              label: item.columnValues.d.name.name,
              row: item.row,
              showInput: false,
              icon:
                item.columnValues.d.type.type === "dir"
                  ? "el-icon-folder"
                  : "el-icon-tickets",
              children: [],
            });
          });
        }
      );
    },
    // 发送收藏请求
    sendFavoriteData() {
      this.$store.dispatch("collect/sendAddFileData", {
        activeName: this.databaseRadio,
        file_id: this.selectFileId,
        case_id: this.file_case_id,
        item: this.$store.state.NewsDisplay.NewsDet,
        type: this.databaseRadio,
      });
      this.clearexfavoriteData();
    },
    /**文章添加已读 */
    addRead() {
      let res = {
        newId: this.$route.query.id + this.$route.query.index,
        soleValue: (1e13 - Math.round(new Date().getTime() / 1000)).toString(),
      };
      this.$store.commit("NewsDisplay/toAddRead", res);
    },
    morefn(v) {
      console.log("morefn", v, this.$route.query.id, this.$route.query.index);
      this.$store.commit("NewsDisplay/setTmpDataDetail", {
        key: "d",
        value: v,
      });
      this.$store.commit("NewsDisplay/sendGetDataDetailBaseData", {
        id: this.$route.query.id,
        index: this.$route.query.index,
      });
    },
    pdfFn() {
      this.numPages = [];
      if (
        window.main.$store.state.NewsDisplay.NewsDet._source.hasOwnProperty(
          "content_pdf"
        )
      ) {
        window.main.$store.state.NewsDisplay.NewsDet._source.content_pdf.forEach(
          (element, index) => {
            let loadingTask = pdf.createLoadingTask(
              "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/" +
                element.sha512_hash
            );
            loadingTask.promise
              .then((pdf) => {
                this.numPages.push(pdf.numPages);
                this.isPdf = true;
              })
              .catch((err) => {
                console.error("pdf 加载失败", err);
                this.errNum++;
                this.isPdf = false;
                // if (this.errNum < 3) {
                //   this.pdfFn();
                // }
              });
          }
        );
      } else {
        this.isPdf = false;
      }
    },
    changeTime(time) {
      var date = new Date(time);
      var Y = date.getFullYear() + "-";
      var M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      var D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";

      var h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      var m =
        (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
        ":";
      var s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      var strDate = Y + M + D + h + m + s;
      return strDate;
    },
    add0(m) {
      return m < 10 ? "0" + m : m;
    },
    format(timeStamp) {
      //timeStamp是整数，否则要parseInt转换
      var time = new Date(timeStamp);
      var y = time.getFullYear();
      var m = time.getMonth() + 1;
      var d = time.getDate();
      return y + "-" + this.add0(m) + "-" + this.add0(d);
    },
  },

  computed: {
    ...mapState({
      NewsTitle: (state) => state.NewsDisplay.NewsTitle,
      translatedTree: (state) => state.NewsDisplay.translatedTree,
      tranType: (state) => state.NewsDisplay.tranType,
    }),
    oriContenttext() {
      let content = "";
      if (this.oriContent) {
        let arr = this.oriContent.split("");
        content = arr
          .map((item) => {
            return item === "\n" ? "<br>" : item;
          })
          .join("")
          .replace(/(<br>){2,}/g, "<br><br>");
      }

      return content;
    },
    tranContenttext() {
      let arr = this.tranContent.split("");

      let content = "";
      if (this.tranContent) {
        content = arr
          .map((item) => {
            return item === "\n" ? "<br>" : item;
          })
          .join("")
          .replace(/(<br>){2,}/g, "<br><br>")
          .split(/[\t\r\f\n\s{5}]*/g)
          .join("");
      }

      return content;
    },
    // caseDirIdDialog: {
    //   get() {
    //     return this.$store.state.selectTree.caseDirIdDialog;
    //   },
    //   set(newVal) {
    //     this.$store.state.selectTree.caseDirIdDialog = newVal;
    //   },
    // },
    tranContent: {
      get() {
        return this.$store.state.NewsDisplay.tranContent;
      },
      set(newVal) {
        this.$store.commit("NewsDisplay/setTrancontent", newVal);
      },
    },
    oriContent: {
      get() {
        return this.$store.state.NewsDisplay.oriContent;
      },
      set(newVal) {
        this.$store.commit("NewsDisplay/setOricontent", newVal);
      },
    },
    imgoriContent: {
      get() {
        return this.$store.state.NewsDisplay.imgoriContent;
      },
      set(newVal) {
        this.$store.commit("NewsDisplay/setimgOricontent", newVal);
      },
    },
    componentKey() {
      return this.$store.state.selectTree.componentKey;
    },
    rootNode2() {
      return this.$store.state.selectTree.rootNode;
    },
    caseDirIdDialog: {
      get() {
        return this.$store.state.selectTree.caseDirIdDialog;
      },
      set(newVal) {
        this.$store.state.selectTree.caseDirIdDialog = newVal;
      },
    },
    file_case_name() {
      return this.$store.state.selectTree.file_case_name;
    },
    file_case_id() {
      return this.$store.state.selectTree.file_case_id;
    },
  },
  components: {
    pdf,
    "tree-box": () => import("@/components/caseTree/tree_box.vue"),
    // "elImageViewer":import("element-ui/packages/image/src/image-viewer.vue")
  },
};
</script>

<style scoped>
.newsDisplay {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 10%;
  display: flex;
  flex-direction: column;
  color: #000;
}

.content {
  font-size: 18px;
  letter-spacing: 1px;
  line-height: 32px;
  text-indent: 2em;
  text-align: left;
  /* white-space:pre; */
  word-wrap: break-word;
  word-break: normal;
  padding: 0 1vh 0 1vh;
  margin-bottom: 25px;
}

.pic {
  text-align: center;
}

.title {
  text-align: left;
  color: tomato;
  font-size: 26px;
  font-weight: bold;
  margin: 0 0 10px 0;
  /* line-height: 32px; */
  min-height: 36px;
}

canvas {
  position: static;
}

.lefttext {
  width: 30%;
}

.hasPdf {
  width: 80%;
  height: 100%;
}

.rightpdf {
  width: 20%;
}

.hasText {
  width: 100%;
}

.translateList {
  position: absolute;
  left: 9vh;
  top: 8vh;
  padding: 15px 25px 15px 0;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.transltedTitle {
  padding: 15px 30px;
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: bold;
}

.morInfor_r_li {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 50%;
  font-size: 1.8vh;
}

.morInfor_r_li_c {
  margin-bottom: 8px;
  width: 100%;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  box-orient: vertical;
}

.operation {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 29.5%;
  font-size: 16px;
  display: flex;
  padding: 10px 10px;
}

.newsInfo {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 75%;
}
</style>
