<template>
  <div class="intell_box">
    <div class="markDownBox">
        <div class="session_box">
          <div class="session_list" ref="scrollContainer" v-show="intelligenceList.length">
            <el-card shadow="hover" class="session_card" v-for="item in intelligenceList" :key="item._id" @click.native="handleSelectMDcontent(item)">
              <div class="item_header">
                <p class="item_title">{{ item._source.title }}</p>
                <div class="item_btn_list">
                  <el-button type="primary" size="mini" icon="el-icon-edit" circle  @click.stop="redactBulletin(item)"></el-button>
                  <el-button type="success" size="mini" icon="el-icon-download" circle  @click.stop="downloadBulletin(item)"></el-button>
                  <el-button type="danger" size="mini" icon="el-icon-delete" circle @click.stop="deleteBulletin(item)"></el-button>
                </div>
              </div>
              <div class="item_content">
                <span v-html="item._source.content_article"></span>
              </div>
              <div class="item_time">
                <span>{{ $tools.timestampToTime(item._source['@timestamp']) }}</span>
              </div>
            </el-card>
          </div>
          <div style="width: 100%; height: 100%" v-show="!intelligenceList.length">
            <el-empty :image-size="300" description="暂无情报"></el-empty>
          </div>
          <div class="session_page_box">
            <el-pagination
              layout="prev, pager, next"
              :current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
              >
            </el-pagination>
          </div>
        </div>
        <div class="session_preview">
          <div class="preview_box" v-if="MDcontent">
            <mavon-editor
              v-model="MDcontent"
              :externalLink="externalLink"
              style="height: 100%"
              :editable="false"
              :subfield="false"
              :toolbarsFlag="false"
              :defaultOpen="'preview'"
            />
          </div>
          <div class="preview_no_data" v-if="!MDcontent">
            <span>暂无内容</span>
          </div>
        </div>
    </div>
    <!-- 编辑 -->
     <el-dialog :close-on-click-modal="false" :visible.sync="buildIntell" append-to-body width="80%" top="20px" title="编辑情报">
      <div>
        <div class="MDbutton">
          <div class="file">
            <span>上传文件</span>
            <input type="file" accept=".md" @change="jaince($event)" />
          </div>
          <div style="margin-right: 15px">
            <el-button type="primary" @click="MDout()">导出情报</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveMD()">保存情报</el-button>
          </div>
        </div>
        <div class="MDinput">
          <mavon-editor
            ref="md"
            v-model="MDcontent"
            :externalLink="externalLink"
            style="height: 100%"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';

  export default {
    data() {
      return {
        externalLink: {
          markdown_css: function () {
            return "/mavon-editor/markdown/github-markdown.min.css";
          },
          hljs_js: function () {
            return "/mavon-editor/highlightjs/highlight.min.js";
          },
          hljs_css: function (css) {
            return "/mavon-editor/highlightjs/styles/" + css + ".min.css";
          },
          hljs_lang: function (lang) {
            return "/mavon-editor/highlightjs/languages/" + lang + ".min.js";
          },
          katex_css: function () {
            return "/mavon-editor/katex/katex.min.css";
          },
          katex_js: function () {
            return "/mavon-editor/katex/katex.min.js";
          },
        },
        MDcontent: "",
        bulletinTitle: "",
        bulletinID: "",
        buildIntell: false,
        case_id: "",
      }
    },
    
    computed:{
      ...mapState({
        intelligenceList: (state) => state.detailIntelligence.intelligenceList,
        currentPage: (state) => state.detailIntelligence.currentPage,
        pageSize: (state) => state.detailIntelligence.pageSize,
        total: (state) => state.detailIntelligence.total,
      })
    },

    created() {
      let info = this.$route.query.data;
      let intelligence = JSON.parse(info);
      // if (window.main.$route.name == "oriDetails") {
      //   intelligence = JSON.parse(localStorage.getItem("intelligence_organi"))
      // } else {
      //   intelligence = JSON.parse(localStorage.getItem("intelligence_person"))
      // }
      if (intelligence) {
        window.main.$store.commit("detailIntelligence/getRelatedInformation", intelligence._id)
      }
    },

    methods: {
      // 翻页
      handleCurrentChange(page) {
        window.main.$store.commit("detailIntelligence/setCurrentPage", page)
        window.main.$store.commit("detailIntelligence/getRelatedInformationList")
        this.$refs.scrollContainer.scrollTop = 0;
      },

      // 编辑情报
      redactBulletin(item){
        this.buildIntell = true;
        this.MDcontent = item._source.content;
        this.bulletinTitle = item._source.title;
        this.bulletinID = item._id;
        this.case_id = item._source.case_id
      },

      // 保存情报
      saveMD() {
        if (this.bulletinTitle == "") {
          window.main.$message.warning("请填写标题!");
          return;
        }
        this.$store.commit("detailIntelligence/sendSaveIntelligence", {content: this.MDcontent, title: this.bulletinTitle, id: this.bulletinID, case_id: this.case_id});
        this.buildIntell = false;
        this.bulletinID = "";
      },

      // 下载情报
      downloadBulletin(item) {
        const blob = new Blob([item._source.content], {
          type: "text/markdown",
        });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = `${item._source.title}.md`;
        link.click();
        URL.revokeObjectURL(link.href);
      },

      // 删除情报
      deleteBulletin(item) {
        console.log("item:", item);
        
        this.$confirm("是否删除情报?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$store.commit("detailIntelligence/sendDelFileData", item._id);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
      },

      // 上传文件事件
      jaince(e) {
        const file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = (e) => {
          this.MDcontent = e.target.result;
        };
        reader.readAsText(file);
      },

      // 导出情报
      MDout() {
        const blob = new Blob([this.MDcontent], { type: "text/markdown" });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = `docunment_${Date.now()}.md`;
        link.click();
        URL.revokeObjectURL(link.href);
      },

      // 选择情报，进行展示
      handleSelectMDcontent(item) {
        this.MDcontent = item._source.content_article
      },
    }
  }
</script>

<style lang="scss" scoped>
.intell_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .markDownBox {
    border: 1px solid #eee;
    height: 94%;
    width: 100%;
    display: flex;
  
    .session_box {
      width: 50%;
      height: 100%;

      .session_list {
        width: 100%;
        height: 90%;
        overflow-y: auto;

        .session_card {
          height: auto;
          min-height: 6.25rem;
          width: 100%;
          display: flex;
          margin-bottom: .625rem;
          border: none;
          padding: .1875rem;
          &:hover {
            cursor: pointer;
            background-color: #40a0ff4d;
          }
  
          :deep .el-card__body {
            width: 100%;
            padding: 5px;
          }
  
          .item_header {
            height: 2.5rem;
            width: 100%;
            display: flex;
            align-content: center;
            justify-content: space-between;
            align-items: center;
            
            .item_title {
              font-size: 16px;
              font-weight: bold;
              width: 60%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
  
            .item_btn_list {
              display: flex;
              gap: 5px;
            }
          }
  
          .item_content {
            width: 100%;
            max-height: 6.25rem;
            min-height: 3.125rem;
            padding: 3px;
            font-size: .875rem;
            span {
              gap: .0625rem;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 4;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
  
          .item_time {
            height: 2.5rem;
            padding: .3125rem;
            text-align: left;
            align-content: center;
            font-size: .875rem;
            color: #777777;
          }
        }
      }

      .session_page_box {
        width: 100%;
        height: 10%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

    }

    .session_preview {
      height: 100%;
      width: 50%;
      
      .preview_box {
        height: 100%;
        width: 100%;
        
        ::v-deep .v-note-show, .single-show {
          overflow: hidden;
        }
      }

      .preview_no_data {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.MDbutton {
  display: flex;
  height: 6vh;
  justify-content: end;
  align-items: center;
  padding-right: 20px;
}
</style>