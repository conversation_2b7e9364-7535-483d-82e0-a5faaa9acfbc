export default {
  namespaced: true,
  state: {
    from: 0,
    size: 20,
    upFrom: 0,
    direction: false,
    group_content_data: [],
  },
  mutations: {
    setDirection(state, v) {
      state.direction = v;
    },
    clearGroupContentData(state) {
      state.from = 0;
      this.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "group_content_data",
        value: [],
      });
    },
    sendGetDataDetailGroupContentData(state, direction) {
      let tmpObj = this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["base_data"];
      if (tmpObj["_source"]) {
        tmpObj["_source"] = tmpObj.d["_source"];
      }
      let from;
      if (direction) {
        from = state.upFrom;
      } else {
        from = state.from;
      }
      window.main.$main_socket.sendData(
        "Api.search.dataDetail.GroupContentData",
        [
          {
            head: {
              session_id: this.state.userInfo.session_id,
              from: from,
              size: state.size,
              direction: direction,
            },
            msg: tmpObj,
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramGroupContentData/setDataDetailGroupContentData"
      );
    },
    setDataDetailGroupContentData(state, data) {
      if (data == null) {
        return;
      }
      if (data.hits == null) {
        return;
      }
      if (!data.hits.hasOwnProperty("hits")) {
        window.main.$message({
          message: "没有数据了",
          type: "warning",
        });
        return;
      }
      if (state.direction) {
        if (
          !window.main.$store.state.telegramSearch.telegramSearchDataDetail.hasOwnProperty(
            "group_content_data"
          )
        ) {
          window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_content_data"] =
            data.hits.hits;
        } else {
          window.main.$store.state.telegramSearch.telegramSearchDataDetail[
            "group_content_data"
          ].unshift(...data.hits.hits);
        }
        state.upFrom += data.hits.hits.length;
      } else {
        if (
          !window.main.$store.state.telegramSearch.telegramSearchDataDetail.hasOwnProperty(
            "group_content_data"
          )
        ) {
          window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_content_data"] =
            data.hits.hits;
        } else {
          window.main.$store.state.telegramSearch.telegramSearchDataDetail["group_content_data"] =
            window.main.$store.state.telegramSearch.telegramSearchDataDetail[
              "group_content_data"
            ].concat(data.hits.hits);
        }

        state.from += data.hits.hits.length;
      }
    },
  },
};
