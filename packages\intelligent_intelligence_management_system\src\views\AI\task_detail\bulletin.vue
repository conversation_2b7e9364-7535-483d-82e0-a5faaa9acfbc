<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: bisque;
          line-height: 42px;
          font-weight: bold;
          color: brown;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-document"></i
          ><span style="margin-left: 10px">舆情简报</span>
        </div>
        <div style="cursor: pointer">
          <i
            class="iconfont icon-baocun"
            title="保存"
            style="margin-right: 15px"
            @click="saveBulletin(markdownData)"
          ></i>
          <i
            class="el-icon-download"
            @click="downloadBulletin(markdownData)"
          ></i>
        </div>
      </div>
      <mavon-editor
        v-model="markdownData"
        :externalLink="externalLink"
        style="height: 100%"
        :editable="false"
        :subfield="false"
        :toolbarsFlag="false"
        :defaultOpen="'preview'"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: "bulletin",
  props: {},
  components: {},
  computed: {
    externalLink() {
      return {
        markdown_css: "/static/css/markdown/github-markdown.min.css",
        hljs_js: "/static/js/highlight.min.js",
        hljs_css: "/static/css/highlight/github.min.css",
        hljs_lang: "/static/lang.hljs.min.js",
        katex_css: "/static/css/katex.min.css",
        katex_js: "/static/js/katex.min.js",
      };
    },
    markdownData: {
      get() {
        return this.$store.state.aiTaskQueue.taskDetail.bulletin.result;
      },
      set(val) {
        console.log(val);
      },
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    saveBulletin(v) {
      this.$emit("child-event", v);
    },
    downloadBulletin(detail) {
      console.log("下载");
      const blob = new Blob([detail], { type: "text/markdown" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `舆情简报.md`;
      link.click();
      URL.revokeObjectURL(link.href);
    },
  },
};
</script>

<style scoped lang="scss"></style>
