<template>
  <div class="main">
    <slot name="chatInputs">
      <div class="input-box">
        <!--输入框-->
        <textarea
          :disabled="!isTools"
          class="inputs rounded-md"
          id="textareaMsg"
          placeholder="请输入您的内容~（Ctrl+Enter换行）"
          v-autoheight
          rows="3"
          dir
          autocorrect="off"
          aria-autocomplete="both"
          spellcheck="false"
          autocapitalize="on"
          autocomplete="off"
          v-model="inputMsg"
          @keydown.enter.prevent="handleKeyDown"
        ></textarea>
      </div>
      <!--上传图片-->
      <el-tooltip content="上传" placement="top">
        <div class="input-button-box pointer" @click="tipFn()">
          <img :src="require('/src/assets/images/附件.png')" alt="" />
        </div>
      </el-tooltip>
      <!--发送-->
      <el-tooltip
        content="发送"
        placement="top"
        v-if="!responsing && !loadingLine"
      >
        <div class="input-button-box pointer" @click="sendInputMessage">
          <img :src="require('/src/assets/images/send.png')" alt="" />
        </div>
      </el-tooltip>
      <!--停止-->
      <el-tooltip content="停止" placement="top" v-else>
        <div class="input-button-box pointer" @click="stopRes">
          <img :src="require('/src/assets/images/停止.png')" alt="" />
        </div>
      </el-tooltip>
    </slot>
  </div>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "inputMsg",
  data() {
    return {
      inputMsg: "",
    };
  },
  watch: {
    inputMsg(val) {
      this.$emit("setInputMsg", val);
    },
  },
  props: {
    isTools: { type: Boolean, default: false },
    responsing: { type: Boolean, default: false },
    loadingLine: { type: Boolean, default: false },
  },
  methods: {
    tipFn() {
      this.$message("功能暂未开发");
    },
    handleKeyDown(event) {
      if (event.keyCode === 13) {
        // Enter 键
        if (event.ctrlKey) {
          // 如果按住了 Ctrl 键
          // 在光标位置插入换行符
          const start = event.target.selectionStart;
          const end = event.target.selectionEnd;
          const text = this.inputMsg;
          this.inputMsg = text.substring(0, start) + "\n" + text.substring(end);
          // 将光标移动到换行符后
          this.$nextTick(() => {
            event.target.selectionStart = event.target.selectionEnd = start + 1;
          });
        } else if (!event.shiftKey) {
          // 如果不是按住 shift 键的回车
          this.sendInputMessage();
        }
      }
    },
    sendInputMessage() {
      this.$emit("sendInputMessage", this.inputMsg);
      this.inputMsg = "";
    },
    stopRes() {
      this.$confirm("此操作将终止ai回复, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$emit("stopRes");
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
  directives: {
    //用于自适应文本框的高度
    autoheight: {
      inserted: function (el) {
        var Msg = document.getElementById("textareaMsg").value;
        if (Msg == "") {
          el.style.height = "26px";
        } else {
          el.style.height = el.scrollHeight + "px";
        }
      },
      update: function (el) {
        var Msg = document.getElementById("textareaMsg").value;
        if (Msg == "") {
          el.style.height = "26px";
        } else {
          el.style.height = el.scrollHeight + "px";
        }
      },
    },
  },
};
</script>

<style scoped lang="scss">
.main {
  width: 850px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: right;
  flex-direction: row;
  align-items: center;
  padding-bottom: 12px;
  padding-top: 15px;
  box-shadow: 5px 0px 1px 1px var(--item-border-default-color);
  border: 1px solid #e2e2e2;
  border-radius: 10px;
  background-color: #f5f5f5;
}

.input-box {
  width: 100%;
  height: 100%;
  position: relative;
  margin: 0 15px;

  .inputs {
    width: 100%;
    height: 100%;
    padding: 10px;
    background-color: var(--background-textarea);
    box-sizing: border-box;
    transition: 0.2s;
    font-size: 15px;
    color: var(--font-color-default);
    font-weight: 100;
    margin-top: 1px;
    border: 0px solid #787878;
    &:focus {
      outline: none;
    }
  }
}

.input-button-box {
  width: 48px;
  height: 100%;
  margin: 0 5px;
  padding: 6px;
  text-align: center;
  align-items: center;
  justify-content: center;
  display: flex;
  border: 1px solid var(--item-border-normal-color);
  border-radius: 15%;
  transition: 0.2s;

  &:hover {
    box-shadow: 0px 0px 2px 1px rgba(255, 255, 255, 0.3) inset;
  }

  &:active {
    transform: scale(0.92);
    transition: all 0.1s ease-in-out;
  }

  img {
    flex: 1;
    width: 100%;
    height: 100%;
    transition: transform 0.2s;
    transform-origin: center center;
  }
}

.input-button-box.active {
  box-shadow: 0px 0px 6px 2px var(--item-border-active-color) inset;
}

textarea {
  min-height: 2.8rem;
  max-height: 20rem;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
}
</style>
