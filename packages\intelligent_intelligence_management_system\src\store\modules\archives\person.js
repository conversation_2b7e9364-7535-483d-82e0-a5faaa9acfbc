export default {
  namespaced: true,
  state: {
    from: 0,
    size: 12,
    total: 0,
    personDetailLoading: false,
    showSecondaryPerson: false, // 展示次要目标人
    warnLastRowkey: [],
    personList: [], // 目标人数据
    relationFlag: false,
    personData: {}, // 要添加的目标人信息
    personRelationInfo: [], // 关系人信息,
    infoList: ["name", "phone", "identity"],
    personBar: [],
    selectPerson: null,
    twitter: null,
    telegram: null,
    facebook: null,
    linkedin: null,
    identity: null,
    phone: null,
    email: null,
    personName: null,
    condition: {
      "query_mode": "match",
      "time_range": "无",
      "time_range_begin": 0,
      "time_range_end": 0,
      "collection_time_range": "无",
      "collection_time_range_begin": 0,
      "collection_time_range_end": 0
    },
    twitterData: [],
    facebookData: [],
    linkedinData: [],
    telegramData: [],
    processedPersonData: null,
    processedOrganizationData: null,
    searchKeyword: '',

  },
  mutations: {
    // 人员详情刷新后数据重置 
    resetPersonDetailData(state) {
      state.personDetailLoading = false;
      state.selectPerson = null;
      state.twitterData = [];
      state.facebookData = [];
      state.linkedinData = [];
      state.telegramData = [];
      state.processedPersonData = null;
      state.processedOrganizationData = null;
      console.log("resetPersonDetailData");
    },

    // 设置分页
    setPage(state, data) {
      state.from = (data - 1) * state.size;
      state.personList = [];
    },

    // 刷新后重置部分数据
    resetPersonData(state) {
      state.from = 0;
      state.total = 0;
      state.personList = [];
    },

    // 重置数据
    resetAllData(state) {
      state.showSecondaryPerson = false;
      state.from = 0;
      state.total = 0;
      state.searchKeyword = '';
      state.personList = [];
    },

    // 设置展示次要目标人
    setShowSecondary(state, data) {
      state.from = 0;
      state.total = 0;
      state.personList = [];
      state.showSecondaryPerson = data;
    },

    // 设置搜索关键词
    setSearchKeyword(state, data) {
      state.searchKeyword = data;
    },

    // 获取目标人功能项
    sendPersonBar(state) {
      state.personBar = [];
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 20,
            },
            control: {
              query_string: "",
              query_type: "username",
              condition: {
                query_mode: "match",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [],
                },
              },
            },
            msg: {
              data_range_index_name:
                "intelligen_intelligence_management_project_management",
            },
          },
        ],
        "person/setPersonBar"
      );
    },

    setPersonBar(state, data) {
      console.log("获取目标人功能项", data);
      state.personBar = [
        { key: "关系拓扑", value: "关系拓扑" },
        { key: "情报", value: "intelligence" },
        {key: "关系人", value:"relevantPerson"},
        {key: "关系组织", value:"relevantOriganization"}
      ]; // 先重置，避免重复push

      if (data?.hits?.hits?.length) {
        // 1. 过滤掉 value 为 "social_work_library" 的项
        const filtered = data.hits.hits.filter(item => {
          const content = item._source.project_content;
          // 兼容字符串和对象
          const value = typeof content === 'string' ? JSON.parse(content).value : content.value;
          return value !== "social_work_library";
        });

        // 2. 按 index 升序排序
        filtered.sort((a, b) => {
          const aContent = typeof a._source.project_content === 'string' ? JSON.parse(a._source.project_content) : a._source.project_content;
          const bContent = typeof b._source.project_content === 'string' ? JSON.parse(b._source.project_content) : b._source.project_content;
          return aContent.index - bContent.index;
        });

        // 3. push 到 personBar
        filtered.forEach(item => {
          const content = typeof item._source.project_content === 'string' ? JSON.parse(item._source.project_content) : item._source.project_content;
          state.personBar.push({
            key: content.label,
            value: content.value,
          });
        });
      }
    },

    // 获取目标人数据
    getPerson(state) {
      let add_es_query_conditions = {}
      if (state.showSecondaryPerson) {
        add_es_query_conditions = {
          bool: {
            should: [
              {
                match: {
                  "type": "secondary_key_person",
                },
              },
              {
                match: {
                  "type": "key_person",
                },
              },
            ],
          },
        };
      } else {
        add_es_query_conditions = {
          bool: {
            should: [
              {
                match: {
                  "type": "key_person",
                },
              },
            ],
          },
        };
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: state.from,
              size: state.size,
            },
            control: {
              query_type: "public",
              query_string: state.searchKeyword,
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        "person/setPersonList"
      );
    },

    setPersonList(state, data) {
      console.log("获取目标人数据", data);
      if (data?.hits?.hits?.length) {
        state.personList = data.hits.hits.map((item) => {
          const newItem = { ...item };
          // 标准字段
          const standardFields = [
             "type","avatar", "name", "remark", "sex", "age", "phoneNumbers", "identity", "email", "dateBirth",
            "relation", "twitterIds", "facebookIds", "linkedInIds", "telegramIds", "basic"
          ];
          // 1. 处理params为对象的情况
          if (newItem._source && typeof newItem._source.params === 'object' && !Array.isArray(newItem._source.params)) {
            const p = newItem._source.params || {};
            // 组装basic对象
            const basicObj = {};
            standardFields.forEach(field => {
              if (field === 'basic') return; // basic后面单独处理
              if (Array.isArray(p[field])) {
                basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field][0] || '';
              } else if (typeof p[field] === 'string' || typeof p[field] === 'number') {
                basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field];
              }
            });
            // 合并basic字段
            if (p.basic && typeof p.basic === 'object') {
              Object.assign(basicObj, p.basic);
            }
            // 组装customFields
            const customFields = {};
            Object.keys(p).forEach(key => {
              if (!standardFields.includes(key)) {
                // 只处理非标准字段
                customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
              }
            });
            // 组装media对象
            const buildMedia = (p) => {
              // 兼容后端可能的字段名
              const platforms = [
                { key: 'telegram', idKey: 'telegramIds' },
                { key: 'twitter', idKey: 'twitterIds' },
                { key: 'facebook', idKey: 'facebookIds' },
                { key: 'linkedin', idKey: 'linkedInIds' },
              ];
              const media = {};
              platforms.forEach(({ key, idKey }) => {
                const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
                // 组装为对象数组，结构与PersonDetails.vue一致
                media[key] = arr.map(id => ({
                  idNum: id,
                  name: id,
                  // 可扩展更多字段
                }));
              });
              return media;
            };
            // 处理关系数据
            let relationData = { person: [], organization: [] };
            if (p.relation && Array.isArray(p.relation)) {
              p.relation.forEach(relationStr => {
                try {
                  const parsed = JSON.parse(relationStr);
                  if (parsed.person && Array.isArray(parsed.person)) {
                    relationData.person = [...relationData.person, ...parsed.person];
                  }
                  if (parsed.organization && Array.isArray(parsed.organization)) {
                    relationData.organization = [...relationData.organization, ...parsed.organization];
                  }
                } catch (e) {
                  console.warn("解析关系数据失败:", relationStr, e);
                }
              });
            }
            
            // 组装params
            newItem._source.params = {
              ...p,
              basic: basicObj,
              customFields,
              media: buildMedia(p),
              relation: relationData
            };
          }
          // 2. 处理params为数组的情况（兼容旧数据）
          else if (newItem._source && Array.isArray(newItem._source.params)) {
            // 保持原逻辑不变
            const paramsObj = {};
            newItem._source.params.forEach((param) => {
              paramsObj[param.k] = param.v;
            });
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
              remark: Array.isArray(paramsObj.remark) ? paramsObj.remark[0] : (paramsObj.remark || ''),
              sex: Array.isArray(paramsObj.sex) ? paramsObj.sex[0] : (paramsObj.sex || ''),
              age: paramsObj.age ? parseInt(Array.isArray(paramsObj.age) ? paramsObj.age[0] : paramsObj.age) : 0,
              phone: Array.isArray(paramsObj.phoneNumbers) ? paramsObj.phoneNumbers[0] : (paramsObj.phoneNumbers || ''),
              identity: Array.isArray(paramsObj.identity) ? paramsObj.identity[0] : (paramsObj.identity || ''),
              email: Array.isArray(paramsObj.email) ? paramsObj.email[0] : (paramsObj.email || ''),
              avatar: Array.isArray(paramsObj.avatar) ? paramsObj.avatar[0] : (paramsObj.avatar || ''),
              dateBirth: Array.isArray(paramsObj.dateBirth) ? paramsObj.dateBirth[0] : (paramsObj.dateBirth || '')
            };
            // 组装customFields
            const customFields = {};
            Object.keys(paramsObj).forEach(key => {
              if (!standardFields.includes(key)) {
                customFields[key] = Array.isArray(paramsObj[key]) ? paramsObj[key][0] : paramsObj[key];
              }
            });
            // 组装media对象
            const buildMedia = (p) => {
              // 兼容后端可能的字段名
              const platforms = [
                { key: 'telegram', idKey: 'telegramIds' },
                { key: 'twitter', idKey: 'twitterIds' },
                { key: 'facebook', idKey: 'facebookIds' },
                { key: 'linkedin', idKey: 'linkedInIds' },
              ];
              const media = {};
              platforms.forEach(({ key, idKey }) => {
                const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
                // 组装为对象数组，结构与PersonDetails.vue一致
                media[key] = arr.map(id => ({
                  idNum: id,
                  name: id,
                  // 可扩展更多字段
                }));
              });
              return media;
            };
            newItem._source.params = {
              ...paramsObj,
              basic: basicObj,
              customFields,
              media: buildMedia(paramsObj)
            };
          }
          return newItem;
        });
        console.log("personList:", state.personList);
      } else {
        state.personList = [];
      }
      window.main.$store.commit("intellManageTree/setIntellList", {
        data: state.personList,
        index: 1,
      });
      state.total = data?.hits?.total?.value;
    },

    // 添加目标人
    addPerson(state, data) {
      console.log("添加目标人", data);
      let params = [];

      // 检查数据格式：新格式 vs 旧格式
      if (data._source && Array.isArray(data._source.params)) {
        // 新格式：直接使用 params 数组
        params = data._source.params;
      } else {
        // 旧格式：需要转换
        let personRelation = [];
        let organizationRelation = [];

        if (data) {
          Object.entries(data).forEach(([key, value]) => {
            if (key === 'customFields') {
              if (typeof value === 'object' && value !== null) {
                Object.entries(value).forEach(([customKey, customValue]) => {
                  params.push({
                    k: customKey,
                    v: [String(customValue)]
                  });
                });
              }
            } else if (key === 'relation' && value.length > 0) {
              value.forEach(item => {
                if (item.intellValue[0] === '目标人' || item.intellValue[0] === '次要目标人') {
                  personRelation.push(item.personData._id);
                }
                if (item.intellValue[0] === '目标组织') {
                  organizationRelation.push(item.organizationData._id);
                }
              });
              params.push({
                k: 'relation',
                v: [JSON.stringify({ 'person': personRelation }), JSON.stringify({ 'organization': organizationRelation })]
              });
            }
            else {
              let formattedValue;
              if (Array.isArray(value)) {
                formattedValue = value.map(String);
              }
              else {
                formattedValue = [String(value)];
              }
              params.push({
                k: key,
                v: formattedValue
              });
            }
          });
        }
      }
      console.log("params", params);

      // 获取正确的 id、type、name
      let personId, personType, personName;
      if (data._source) {
        // 新格式
        personId = data._id;
        personType = data._source.type;
        personName = data._source.name;
      } else {
        // 旧格式
        personId = data.id;
        personType = data.type || "key_person";
        personName = [data.name];
      }

      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "public",
              index: "key_person",
              id: personId,
            },
            msg: {
              type: personType,
              name: personName,
              params: params,
            },
          },
        ],
        (res) => {
          console.log("添加目标人", res);
          if (res?.status == "ok") {
            window.main.$message.success("添加目标人成功，请刷新页面");
            state.relationFlag = true;
            window.main.$store.commit("person/resetAllData");
            window.main.$store.commit("person/getPerson");
            // 处理关系数据（如果存在）
            if (data.basic && data.basic.relation && data.basic.relation.length > 0) {
              window.main.$store.commit("person/addPersonRelationFn", data);
            }
          }
        }
      );
    },

    // 封装函数 循环处理添加目标人关系的数据
    addPersonRelationFn(state, data) {
      data.basic.relation.forEach(item => {
        if (item.intellValue[0] === '目标人') {
          window.main.$store.commit("person/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.personRelation,
            targetID: item.personData._id, 
            relation: 'key_person',
            desc: item.personRelation, 
          });
        } else if (item.type === '次要目标人') {
          window.main.$store.commit("person/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.personRelation,
            targetID: item.personData._id, 
            relation: 'key_person', 
            desc: item.personRelation
          });
        } else if (item.type === '目标组织') {
          window.main.$store.commit("person/addForwardRelation", {
            id: data.id, 
            table: 'key_person',
            name: item.organizationRelation,
            targetID: item.organizationData._id, 
            relation: 'key_organization', 
            desc: item.organizationRelation
          });
        }
      });
    },

    // 正向的关系
    addForwardRelation(state, data) {
      console.log("添加人与目标人关系", data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {},
            msg: {
              type: 'public',
              table: data.table,
              prefix: data.targetID,
              relation: data.id + ";" + data.relation,
              data: {
                data: {
                  relation: data.relation,
                  id: data.id,
                  targetID: data.targetID,
                  desc: data.desc
                }
              }
            },
          },
        ],
        (res) => {
          console.log("添加正向关系1", res);
        }
      );
    },

    // 编辑目标人信息
    editPerson(state, person) {
      // 更新主目标人列表
      const index = state.personList.findIndex(p => p._id === person._id);
      if (index !== -1) {
        state.personList.splice(index, 1, person);
      }

      let params = [];

      // 检查数据格式：新格式 vs 旧格式
      if (Array.isArray(person._source.params)) {
        // 新格式：直接使用 params 数组
        params = person._source.params;
      } else {
        // 旧格式：需要转换
        // 同步次要目标人列表
        const secondaryPersons = person._source.params.secondaryPersons || [];
        if (secondaryPersons.length > 0) {
          secondaryPersons.forEach(sp => {
            const secondaryIndex = state.secondaryPersonList.findIndex(s => s.id === sp.id);
            if (secondaryIndex !== -1) {
              // 如果找到了，就更新
              state.secondaryPersonList.splice(secondaryIndex, 1, sp);
            } else {
              // 如果没找到，就添加
              state.secondaryPersonList.push(sp);
            }
          });
        }

        let personRelation = [];
        let organizationRelation = [];

        if (person._source.params) {
          Object.entries(person._source.params).forEach(([key, value]) => {
            if (key === 'customFields') {
              if (typeof value === 'object' && value !== null) {
                Object.entries(value).forEach(([customKey, customValue]) => {
                  params.push({
                    k: customKey,
                    v: [String(customValue)]
                  });
                });
              }
            } else if (key === 'relation' && value.length > 0) {
              value.forEach(item => {
                if (item.intellValue[0] === '目标人' || item.intellValue[0] === '次要目标人') {
                  personRelation.push(item.personData._id);
                }
                if (item.intellValue[0] === '目标组织') {
                  organizationRelation.push(item.organizationData._id);
                }
              });
              params.push({
                k: 'relation',
                v: [JSON.stringify({ 'person': personRelation }), JSON.stringify({ 'organization': organizationRelation })]
              });
            }
            else {
              let formattedValue;
              if (Array.isArray(value)) {
                formattedValue = value.map(String);
              }
              else {
                formattedValue = [String(value)];
              }
              params.push({
                k: key,
                v: formattedValue
              });
            }
          });
        }
      }

      console.log("params", params);

      // 获取正确的 name
      let personName;
      if (Array.isArray(person._source.name)) {
        personName = person._source.name;
      } else if (person._source.name) {
        personName = [person._source.name];
      } else if (person.name) {
        personName = [person.name];
      } else {
        personName = [""];
      }

      // 8. 发送编辑请求
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "public",
              index: "key_person",
              id: person._id,
            },
            msg: {
              type: "key_person",
              name: personName,
              params: params,
            },
          },
        ],
        "person/editCallBack"
      );
    },
    editCallBack(state, data) {
      console.log("编辑的结果", data);
      if (data.status == "ok") {
        window.main.$message.success("修改成功,请关闭当前页面。回到目标人并刷新页面");
      }
    },

    // 删除目标人
    deletePer(state, data) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Del",
        [
          {
            head: {},
            msg: {
              query_type: "public",
              index: "key_person",
              id: data,
            },
          },
        ],
        "person/delCall"
      );
    },

    delCall(state, data) {
      console.log("删除", data);
      if (data?.status == "ok") {
        window.main.$message.success("删除成功,即将关闭窗口,请刷新页面");
        setTimeout(() => {
          window.close();
        }, 1200);
      }
    },

    // 选择的人员信息
    selectPerson(state, data) {
      console.log("选择的人员信息", data);
      // 兼容新数据格式
      let p = data._source.params;
      const standardFields = [
        "type","avatar", "name", "remark", "sex", "age", "phoneNumbers", "identity", "email", "dateBirth",
        "relation", "twitterIds", "facebookIds", "linkedInIds", "telegramIds", "basic", "socialize", "media", "customFields",
      ];
      // 组装basic对象
      const basicObj = {};
      const socialMediaFields = ['twitterIds', 'facebookIds', 'linkedInIds', 'telegramIds', 'phoneNumbers'];
      standardFields.forEach(field => {
        if (field === 'basic') return;
        if (socialMediaFields.includes(field)) {
          // 保证为数组格式
          if (Array.isArray(p[field])) {
            basicObj[field] = p[field];
          } else if (typeof p[field] !== 'undefined') {
            basicObj[field] = [p[field]];
          } else {
            basicObj[field] = [];
          }
        } else if (Array.isArray(p[field])) {
          basicObj[field] = p[field][0] || '';
        } else if (typeof p[field] === 'string' || typeof p[field] === 'number') {
          basicObj[field] = p[field];
        }
      });
      if (p.basic && typeof p.basic === 'object') {
        Object.assign(basicObj, p.basic);
      }
      // 组装customFields
      const customFields = {};
      Object.keys(p).forEach(key => {
        if (!standardFields.includes(key)) {
          customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
        }
      });
      // 组装params
      data._source.params = {
        ...p,
        basic: basicObj,
        customFields
      };
      state.selectPerson = data;
      console.log("state.selectPerson", state.selectPerson);
      
      // 遍历社交媒体数据，提取 name 和 email
      const socialPlatforms = ["telegram", "twitter", "facebook", "linkedin"];
      socialPlatforms.forEach((platform) => {
        state[platform] = []; // 初始化对应的 state 数组
        if (
          data._source.params.media &&
          data._source.params.media[platform] &&
          Array.isArray(data._source.params.media[platform])
        ) {
          data._source.params.media[platform].forEach((item) => {
            if (item.name) {
              state[platform].push(item.name);
            }
          });
        }
      });
      state.personName = data._source.params.basic.name;
      state.identity = data._source.params.basic.identity;
      state.phone = data._source.params.basic.phone;
      state.email = data._source.params.basic.email;
      window.main.$store.commit("person/getRelation", data._id);
    },

    /***** 获取人员关系数据 *****/
    // 获取关系数据
    getRelation(state, id) {
      state.personDetailLoading = true;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_person",
              type: "public",
              relation: id +';key_person',
            },
          },
        ],
        (res) => {
          console.log("获取关系数据", res);
          res.forEach(item => {
            window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [
                {
                  head: {
                    from: 0,
                    size: 200,
                  },
                  control: {
                    query_type: "public",
                    query_string: "",
                    condition: state.condition,
                    add_es_query_conditions: {
                      bool: {
                        must: [
                          {
                            term: {
                              "_id": item.columnValues.d.id,
                            },
                          },
                        ],
                      },
                    },
                  },
                  msg: {
                    data_range_index_name: "key_person",
                  },
                }
              ],
              (data) => {
                console.log("获取详细数据1", data);
                if (data.hits.hits.length > 0) {
                  window.main.$store.commit("person/buildSocialRelation", data.hits.hits);
                }
              }
            );
          })
        }
      );
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_person",
              type: "public",
              relation: id +';key_organization',
            },
          },
        ],
        (res) => {  
          console.log("获取组织关系数据", res);
          res.forEach(item => {
            window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [
                {
                  head: {
                    from: 0,
                    size: 200,
                  },
                  control: {
                    query_type: "public",
                    query_string: "",
                    condition: state.condition,
                    add_es_query_conditions: {
                      bool: {
                        must: [
                          {
                            term: {
                              "_id": item.columnValues.d.id,
                            },
                          },
                        ],
                      },
                    },
                  },
                  msg: {
                    data_range_index_name: "key_organization",
                  },
                }
              ],
              (data) => {
                console.log("获取详细数据2", data);
                if (data.hits.hits.length > 0) {
                  window.main.$store.commit("person/buildOrganizationRelation", data.hits.hits);
                }
              }
            )
          })
        }
      );
      

      console.log("state.selectPerson", state.selectPerson);
      
      setTimeout(() => {
        state.personDetailLoading = false;
      }, 3000);

      /***** 获取社交媒体详细信息 *****/
      // window.main.$store.commit("person/getSocialMediaInfo", state.selectPerson._source.params.media);
    },

    // 构造人员关系
    buildSocialRelation(state, data) {
      state.processedPersonData = null
      // 确保 socialize.relation 存在
      if (!state.selectPerson._source.params.socialize) {
        state.selectPerson._source.params.socialize = {};
      }
      if (!state.selectPerson._source.params.socialize.relation) {
        state.selectPerson._source.params.socialize.relation = [];
      }
      state.selectPerson._source.params.socialize.relation = [];
      console.log("state.selectPerson", state.selectPerson._source.params.socialize);
      
      // 遍历构造data中的数据与state.selectPerson中的数据格式相同
      data.forEach(item => {
        window.main.$store.commit("person/buildData", {type :'person', value:item});
        console.log("state.processedPersonData2222", state.processedPersonData);
        if (state.processedPersonData) {
          // 1. 处理基础关系数据
          const relationData = {
            relationName: "人员关系",
            relationInfo: {
              name: state.processedPersonData._source.params.basic.name,
              sex: state.processedPersonData._source.params.basic.sex,
              age: state.processedPersonData._source.params.basic.age,
              dateBirth: state.processedPersonData._source.params.basic.dateBirth,
              identity: state.processedPersonData._source.params.basic.identity,
              phone: state.processedPersonData._source.params.basic.phone,
              remark: state.processedPersonData._source.params.basic.remark
            }
          };
          state.selectPerson._source.params.socialize.relation.push(relationData);
        }
      });
      localStorage.setItem('intelligence_person', JSON.stringify(state.selectPerson));
    },
    // 构造组织关系
    buildOrganizationRelation(state, data) {
      console.log("buildOrganizationRelation", data);
      state.processedOrganizationData = null
      // 确保 socialize.relation 存在
      if (!state.selectPerson._source.params.socialize) {
        state.selectPerson._source.params.socialize = {};
      }
      if (!state.selectPerson._source.params.socialize.relation) {
        state.selectPerson._source.params.socialize.relation = [];
      }
      data.forEach(item => {
        window.main.$store.commit("person/buildData", {type :'organization', value:item});
        console.log("state.processedOrganizationData2222", state.processedOrganizationData);
        if (state.processedOrganizationData) {
          // 1. 处理基础关系数据
          const relationData = {
            relationName: "组织关系",
            relationInfo: {
              name: state.processedOrganizationData._source.params.basic.name,
              remark: state.processedOrganizationData._source.params.basic.remark,
              createTime: state.processedOrganizationData._source.params.basic.createTime,
              belong: state.processedOrganizationData._source.params.basic.belong,
              desi: state.processedOrganizationData._source.params.basic.desi,
              avatar: state.processedOrganizationData._source.params.basic.avatar,
            }
          };
          state.selectPerson._source.params.socialize.relation.push(relationData);
        }
      })
      localStorage.setItem('intelligence_person', JSON.stringify(state.selectPerson));
    },

    // 数据构造函数
    buildData(state,data) {
      console.log("buildData", data);
      // 根据setPersonList函数功能对传进来的数据进行构造，并返回一个对象
      const newItem = { ...data.value };
      // 标准字段
      const standardFields = [
        "type","avatar", "name", "remark", "sex", "age", "phoneNumbers", "identity", "email", "dateBirth",
        "relation", "twitterIds", "facebookIds", "linkedInIds", "telegramIds", "basic", "socialize", "media", "customFields",
      ];
      
      // 组装media对象
      const buildMedia = (p) => {
        const platforms = [
          { key: 'telegram', idKey: 'telegramIds' },
          { key: 'twitter', idKey: 'twitterIds' },
          { key: 'facebook', idKey: 'facebookIds' },
          { key: 'linkedin', idKey: 'linkedInIds' },
        ];
        const media = {};
        platforms.forEach(({ key, idKey }) => {
          const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
          media[key] = arr.map(id => ({
            idNum: id,
            name: id,
          }));
        });
        return media;
      };
      
      // 1. 处理params为对象的情况
      if (newItem._source && typeof newItem._source.params === 'object' && !Array.isArray(newItem._source.params)) {
        const p = newItem._source.params || {};
        // 组装basic对象
        const basicObj = {};
        standardFields.forEach(field => {
          if (field === 'basic') return;
          if (Array.isArray(p[field])) {
            basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field][0] || '';
          } else if (typeof p[field] === 'string' || typeof p[field] === 'number') {
            basicObj[field === 'phoneNumbers' ? 'phone' : field] = p[field];
          }
        });
        // 合并basic字段
        if (p.basic && typeof p.basic === 'object') {
          Object.assign(basicObj, p.basic);
        }
        // 组装customFields
        const customFields = {};
        Object.keys(p).forEach(key => {
          if (!standardFields.includes(key)) {
            customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
          }
        });
        // 处理关系数据
        let relationData = { person: [], organization: [] };
        if (p.relation && Array.isArray(p.relation)) {
          p.relation.forEach(relationStr => {
            try {
              const parsed = JSON.parse(relationStr);
              if (parsed.person && Array.isArray(parsed.person)) {
                relationData.person = [...relationData.person, ...parsed.person];
              }
              if (parsed.organization && Array.isArray(parsed.organization)) {
                relationData.organization = [...relationData.organization, ...parsed.organization];
              }
            } catch (e) {
              console.warn("解析关系数据失败:", relationStr, e);
            }
          });
        }
        
        // 组装params
        newItem._source.params = {
          ...p,
          basic: basicObj,
          customFields,
          media: buildMedia(p),
          relation: relationData
        };
      }
      // 2. 处理params为数组的情况（兼容旧数据）
      else if (newItem._source && Array.isArray(newItem._source.params)) {
        const paramsObj = {};
        newItem._source.params.forEach((param) => {
          paramsObj[param.k] = param.v;
        });
        const basicObj = {
          id: newItem._id,
          name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
          remark: Array.isArray(paramsObj.remark) ? paramsObj.remark[0] : (paramsObj.remark || ''),
          sex: Array.isArray(paramsObj.sex) ? paramsObj.sex[0] : (paramsObj.sex || ''),
          age: paramsObj.age ? parseInt(Array.isArray(paramsObj.age) ? paramsObj.age[0] : paramsObj.age) : 0,
          phone: Array.isArray(paramsObj.phoneNumbers) ? paramsObj.phoneNumbers[0] : (paramsObj.phoneNumbers || ''),
          identity: Array.isArray(paramsObj.identity) ? paramsObj.identity[0] : (paramsObj.identity || ''),
          email: Array.isArray(paramsObj.email) ? paramsObj.email[0] : (paramsObj.email || ''),
          avatar: Array.isArray(paramsObj.avatar) ? paramsObj.avatar[0] : (paramsObj.avatar || ''),
          dateBirth: Array.isArray(paramsObj.dateBirth) ? paramsObj.dateBirth[0] : (paramsObj.dateBirth || '')
        };
        // 组装customFields
        const customFields = {};
        Object.keys(paramsObj).forEach(key => {
          if (!standardFields.includes(key)) {
            customFields[key] = Array.isArray(paramsObj[key]) ? paramsObj[key][0] : paramsObj[key];
          }
        });
        // 处理关系数据（兼容旧数据格式）
        let relationData = { person: [], organization: [] };
        if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
          paramsObj.relation.forEach(relationStr => {
            try {
              const parsed = JSON.parse(relationStr);
              if (parsed.person && Array.isArray(parsed.person)) {
                relationData.person = [...relationData.person, ...parsed.person];
              }
              if (parsed.organization && Array.isArray(parsed.organization)) {
                relationData.organization = [...relationData.organization, ...parsed.organization];
              }
            } catch (e) {
              console.warn("解析关系数据失败:", relationStr, e);
            }
          });
        }
        
        newItem._source.params = {
          ...paramsObj,
          basic: basicObj,
          customFields,
          media: buildMedia(paramsObj),
          relation: relationData
        };
      }
      if (data.type === 'person') {
        state.processedPersonData = newItem;
        console.log("state.processedPersonData", state.processedPersonData);
      } else if (data.type === 'organization') {
        state.processedOrganizationData = newItem;
        console.log("state.processedOrganizationData", state.processedOrganizationData);
      }
    },

    /***** 获取社交媒体详细信息 */
    getSocialMediaInfo(state, data) {
      // 根据数据类型调用不同的方法
      // if (data && typeof data === 'object') {
      //   // 处理 Telegram 数据
      //   if (data.telegram && Array.isArray(data.telegram) && data.telegram.length > 0) {
      //     window.main.$store.commit("person/getTelegramUserInfo", data.telegram);
      //   }
        
      //   // 处理 Twitter 数据
      //   if (data.twitter && Array.isArray(data.twitter) && data.twitter.length > 0) {
      //     window.main.$store.commit("person/getTwitterUserInfo", data.twitter);
      //   }
        
      //   // 处理 Facebook 数据
      //   if (data.facebook && Array.isArray(data.facebook) && data.facebook.length > 0) {
      //     window.main.$store.commit("person/getFacebookUserInfo", data.facebook);
      //   }
        
      //   // 处理 LinkedIn 数据
      //   if (data.linkedin && Array.isArray(data.linkedin) && data.linkedin.length > 0) {
      //     window.main.$store.commit("person/getLinkedinUserInfo", data.linkedin);
      //   }
      // }
      // 将数据添加到state.selectPerson._source.params.media对象的对应key中,如果数据为 undefined 则把原数据删除
      if (state.telegramData) {
        state.selectPerson._source.params.media.telegram = state.telegramData;
      } else {
        state.selectPerson._source.params.media.telegram = [];
      }
      if (state.twitterData) {
        state.selectPerson._source.params.media.twitter = state.twitterData;
      } else {
        state.selectPerson._source.params.media.twitter = [];
      }
      if (state.facebookData) {
        state.selectPerson._source.params.media.facebook = state.facebookData;
      } else {
        state.selectPerson._source.params.media.facebook = [];
      }
      if (state.linkedinData) {
        state.selectPerson._source.params.media.linkedin = state.linkedinData;
      } else {
        state.selectPerson._source.params.media.linkedin = [];
      }
      console.log("state.selectPerson", state.selectPerson);
      localStorage.setItem('intelligence_person', JSON.stringify(state.selectPerson));
    },
    // twitter用户获取
    getTwitterUserInfo(state, data) {
      state.twitterData = [];
      data.forEach(item => {
        window.main.$main_socket.sendData(
          "Api.Search.SearchList.Query",
          [
            {
              head: {
                from: 0,
                size: 200,
              },
              control: {
                query_type: "public",
                query_string: item.idNum,
                condition: {
                  "query_mode": "match_phrase",
                  "time_range": "无",
                  "time_range_begin": 0,
                  "time_range_end": 0
                }
              },
              msg: {
                data_range_index_name: "social_platform_information_prefix_twitter__",
              }
            }
          ],
          (data) => {
            console.log("获取twitter用户信息", data);
            if (data?.hits?.hits) {
              data.hits.hits.forEach(item => {
                state.twitterData.push(item);
              })
            }
          }
        ) 
      })
    },
    // facebook用户获取
    getFacebookUserInfo(state, data) {
      state.facebookData = [];
      data.forEach(item => {
        window.main.$main_socket.sendData(
          "Api.Search.SearchList.Query",
          [
            {
              head: {
                from: 0,
                size: 200,
              },
              control: {
                query_type: "public",
                query_string: item.idNum,
                condition: {
                  "query_mode": "match_phrase",
                  "time_range": "无",
                  "time_range_begin": 0,
                  "time_range_end": 0
                }
              },
              msg: {
                data_range_index_name: "social_platform_information_prefix_facebook__",
              }
            }
          ],
          (data) => {
            console.log("获取facebook用户信息", data);
            if (data?.hits?.hits) {
              data.hits.hits.forEach(item => {
                state.facebookData.push(item);
              })
            }
          }
        )
      })
    },
    // linkedin用户获取
    getLinkedinUserInfo(state, data) {
      state.linkedinData = [];
      data.forEach(item => {
        window.main.$main_socket.sendData(
          "Api.Search.SearchList.Query",
          [
            {
              head: {
                from: 0,
                size: 200,
              },
              control: {
                query_type: "public",
                query_string: item.idNum,
                condition: {
                  "query_mode": "match_phrase",
                  "time_range": "无",
                  "time_range_begin": 0,
                  "time_range_end": 0
                }
              },
              msg: {
                data_range_index_name: "social_platform_information_prefix_linkedin",
              }
            }
          ],
          (data) => {
            console.log("获取linkedin用户信息", data);
            if (data?.hits?.hits) {
              data.hits.hits.forEach(item => {
                state.linkedinData.push(item);
              })
            }
          }
        )
      })
    },
    // telegram用户获取
    getTelegramUserInfo(state, data) {
      state.telegramData = [];
      data.forEach(item => {
        window.main.$main_socket.sendData(
          "Api.Search.SearchList.Query",
          [
            {
              head: {
                from: 0,
                size: 200,
              },
              control: {
                query_type: "public",
                query_string: item.idNum,
                condition: {
                  "query_mode": "match_phrase",
                  "time_range": "无",
                  "time_range_begin": 0,
                  "time_range_end": 0
                },
              },
              msg: {
                data_range_index_name: "group_member_data_telegram",
              }
            }
          ],
          (data) => {
            console.log("获取telegram用户信息", data);
            if (data?.hits?.hits) {
              data.hits.hits.forEach(item => {
                state.telegramData.push(item);
              })
            }
          }
        )
      })
    },
  },
  actions: {},
  modules: {},
};
