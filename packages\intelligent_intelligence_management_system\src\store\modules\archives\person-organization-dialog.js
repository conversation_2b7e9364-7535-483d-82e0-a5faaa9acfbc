export default {
  namespaced: true,
  state: {
    personList: [],
    organizationList: [],
    secondaryPersonList: [],
  },
  mutations: {
    resetData(state) {
      state.personList = [];
      state.organizationList = [];
      state.secondaryPersonList = [];
    },
    
    getPersonDialog(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 200,
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        "type": "key_person",
                      },
                    },
                  ],
                },
              },
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        (data) => {
          if (data?.hits?.hits?.length) {
            state.personList = data.hits.hits.map((item) => {
              const newItem = { ...item };
              
              // 定义一个辅助函数来获取数组中的第一个元素或默认值
              const getFirstOrDefault = (arr, defaultVal = '') => {
                return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
              };
              
              if (newItem._source && Array.isArray(newItem._source.params)) {
                // 转为对象结构
                const paramsObj = {};
                newItem._source.params.forEach((param) => {
                  paramsObj[param.k] = param.v;
                });
                
                // 创建basic对象以兼容现有视图
                const basicObj = {
                  id: newItem._id,
                  name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
                  remark: getFirstOrDefault(paramsObj.remark),
                  sex: getFirstOrDefault(paramsObj.sex),
                  age: paramsObj.age ? parseInt(getFirstOrDefault(paramsObj.age, '0')) : 0,
                  phone: getFirstOrDefault(paramsObj.phoneNumbers),
                  identity: getFirstOrDefault(paramsObj.identity),
                  email: getFirstOrDefault(paramsObj.email),
                  avatar: getFirstOrDefault(paramsObj.avatar),
                  dateBirth: getFirstOrDefault(paramsObj.dateBirth)
                };
                
                // 将原始数据和basic对象合并
                newItem._source.params = {
                  ...paramsObj,
                  basic: basicObj
                };
              } else if (newItem._source && !Array.isArray(newItem._source.params)) {
                // 处理已经是对象结构的params
                const p = newItem._source.params || {};
                const basicObj = {
                  id: newItem._id,
                  name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
                  remark: getFirstOrDefault(p.remark),
                  sex: getFirstOrDefault(p.sex),
                  age: p.age || 0,
                  phone: p.phoneNumbers || '',
                  identity: p.identity || '',
                  email: getFirstOrDefault(p.email),
                  avatar: getFirstOrDefault(p.avatar),
                  dateBirth: getFirstOrDefault(p.dateBirth)
                };
                
                newItem._source.params = {
                  ...p,
                  basic: basicObj
                };
              }
              return newItem;
            });
            console.log("personList:", state.personList);
          } else {
            state.personList = [];
          }
        }
      );
    },

    getSecondaryPersonDialog(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 200,
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        "type": "secondary_key_person",
                      },
                    },
                  ],
                },
              },
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        (data) => {
          if (data?.hits?.hits?.length) {
            state.secondaryPersonList = data.hits.hits.map((item) => {
              const newItem = { ...item };
              
              // 定义一个辅助函数来获取数组中的第一个元素或默认值
              const getFirstOrDefault = (arr, defaultVal = '') => {
                return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
              };
              
              if (newItem._source && Array.isArray(newItem._source.params)) {
                // 转为对象结构
                const paramsObj = {};
                newItem._source.params.forEach((param) => {
                  paramsObj[param.k] = param.v;
                });
                
                // 创建basic对象以兼容现有视图
                const basicObj = {
                  id: newItem._id,
                  name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
                  remark: getFirstOrDefault(paramsObj.remark),
                  sex: getFirstOrDefault(paramsObj.sex),
                  age: paramsObj.age ? parseInt(getFirstOrDefault(paramsObj.age, '0')) : 0,
                  phone: getFirstOrDefault(paramsObj.phoneNumbers),
                  identity: getFirstOrDefault(paramsObj.identity),
                  email: getFirstOrDefault(paramsObj.email),
                  avatar: getFirstOrDefault(paramsObj.avatar),
                  dateBirth: getFirstOrDefault(paramsObj.dateBirth)
                };
                
                // 将原始数据和basic对象合并
                newItem._source.params = {
                  ...paramsObj,
                  basic: basicObj
                };
              } else if (newItem._source && !Array.isArray(newItem._source.params)) {
                // 处理已经是对象结构的params
                const p = newItem._source.params || {};
                const basicObj = {
                  id: newItem._id,
                  name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
                  remark: getFirstOrDefault(p.remark),
                  sex: getFirstOrDefault(p.sex),
                  age: p.age || 0,
                  phone: p.phoneNumbers || '',
                  identity: p.identity || '',
                  email: getFirstOrDefault(p.email),
                  avatar: getFirstOrDefault(p.avatar),
                  dateBirth: getFirstOrDefault(p.dateBirth)
                };
                
                newItem._source.params = {
                  ...p,
                  basic: basicObj
                };
              }
              return newItem;
            });
            console.log("secondaryPersonList:", state.secondaryPersonList);
          } else {
            state.secondaryPersonList = [];
          }
        }
      );
    },

    getOrganizationDialog(state) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 200,
            },
            control: {
              query_type: "public",
              query_string: "",
            },
            msg: {
              data_range_index_name: "key_organization",
            },
          },
        ],
        (data) => {
          if (data?.hits?.hits?.length) {
            state.organizationList = data.hits.hits.map(item => {
              const newItem = { ...item };
              if (newItem._source && newItem._source.params) {
                let paramsObj = {};
                // 如果params是数组，则转换为对象
                if (Array.isArray(newItem._source.params)) {
                  newItem._source.params.forEach(param => {
                    paramsObj[param.k] = Array.isArray(param.v) && param.v.length === 1 ? param.v[0] : param.v;
                  });
                } else {
                  paramsObj = newItem._source.params; // 如果已经是对象，直接使用
                }
                console.log("paramsObj:", paramsObj);
                // 创建basic对象以兼容前端视图
                const basicObj = {
                  id: newItem._id,
                  name: Array.isArray(newItem._source.name) ? newItem._source.name[0] : (newItem._source.name || ''),
                  remark: paramsObj.remark || "",
                  createTime: paramsObj.createTime || "",
                  belong: paramsObj.belong || "",
                  desi: paramsObj.desi || "",
                  avatar: paramsObj.avatar || "",
                  relation: paramsObj.relation ? paramsObj.relation : [],
                  telegramIds: paramsObj.telegramIds ? paramsObj.telegramIds : [],
                  twitterIds: paramsObj.twitterIds ? paramsObj.twitterIds : [],
                  facebookIds: paramsObj.facebookIds ? paramsObj.facebookIds : [],
                  linkedInIds: paramsObj.linkedInIds ? paramsObj.linkedInIds : [],
                  customFields: paramsObj.customFields ? paramsObj.customFields : {},
                };
                
                newItem._source.params = { ...paramsObj, basic: basicObj };
              }
              return newItem;
            });
            console.log("处理后的 organizationList:", state.organizationList);
          } else {
            state.organizationList = [];
          }
        }
      );
    }
  }
};