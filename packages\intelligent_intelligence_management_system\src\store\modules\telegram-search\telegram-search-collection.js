export default {
  namespaced: true,
  state: {
      // 省份列表排序
      cityNameConfig: [
          {
            firstLetter: "A",
            provinceIndexs: [0]
          },
          {
            firstLetter: "B",
            provinceIndexs: [2]
          },
          {
            firstLetter: "C",
            provinceIndexs: [3]
          },
          {
            firstLetter: "F",
            provinceIndexs: [4]
          },
          {
            firstLetter: "G",
            provinceIndexs: [5, 6, 7, 8]
          },
          {
            firstLetter: "H",
            provinceIndexs: [9, 10, 11, 12, 13, 14]
          },
          {
            firstLetter: "J",
            provinceIndexs: [15, 16, 17]
          },
          {
            firstLetter: "L",
            provinceIndexs: [18]
          },
          {
            firstLetter: "N",
            provinceIndexs: [19, 20]
          },
          {
            firstLetter: "Q",
            provinceIndexs: [21]
          },
          {
            firstLetter: "S",
            provinceIndexs: [22, 23, 24, 25, 26]
          },
          {
            firstLetter: "T",
            provinceIndexs: [28]
          },
          {
            firstLetter: "X",
            provinceIndexs: [29, 31]
          },
          {
            firstLetter: "Y",
            provinceIndexs: [32]
          },
          {
            firstLetter: "Z",
            provinceIndexs: [33]
          }
      ],
      //城市列表热门城市
      hotCityConfig: [
          {
          provinceIndex: 16,
          cityIndex: 3
          },
          {
          provinceIndex: 16,
          cityIndex: 5
          },
          {
          provinceIndex: 2,
          cityIndex: 0
          },
          {
          provinceIndex: 25,
          cityIndex: 0
          },
          {
          provinceIndex: 6,
          cityIndex: 3
          },
          {
          provinceIndex: 6,
          cityIndex: 14
          },
          {
          provinceIndex: 2,
          cityIndex: 0
          },
          {
          provinceIndex: 11,
          cityIndex: 15
          },
          {
          provinceIndex: 30,
          cityIndex: 0
          },
          {
          provinceIndex: 1,
          cityIndex: 0
          },
          {
          provinceIndex: 27,
          cityIndex: 0
          }
      ],
      //港澳台地区
      otherCityConfig: [
          {
          provinceIndex: 30,
          cityIndex: 0
          },
          {
          provinceIndex: 1,
          cityIndex: 0
          },
          {
          provinceIndex: 27,
          cityIndex: 0
          }
      ],
      chinaCityData:[],// 中国省份/城市表
      cityList:[], // 城市列表
      dataList:[],// 日期列表
      NowDate:'',// 查询的日期
      NowDateNorm:'',//查看的采集日期
      nearbyUserDotList:[],// 附近用户
      nearbyGroupDotList:[],// 附近群组
      lastDataListRowKey:[],
      dataListIndex:null,
      nearbyUserDotRow:[],
      nearbyGroupDotRow:[],
      sendDotNearbyPoint:{},
      zoomLevel:'2',
      nearbyUserAllRow:[],
      nearbyGroupAllRow:[]
  },
  mutations: {
      //处理城市列表
      setChinaCityData(state,res){
          let cityArray = [];
          let citylist = []
          for (let item in res["/etc/web/instant_msg_analysis_platform/city_list"]) {
              cityArray.push({
                  value: item,
                  label: res["/etc/web/instant_msg_analysis_platform/city_list"][item]
              });
              citylist.push(res["/etc/web/instant_msg_analysis_platform/city_list"][item]);
          }
          state.cityList = citylist
          let provinceArray = []
          cityArray?.forEach(item => {
              let shortString = item.label.province
              if (item.label.province.indexOf("自治区") >= 0) {
                shortString = item.label.province.substring(0, 2);
              }
              let t_str_1 = item.label.city.replace("市", "");
              let t_shortCity = t_str_1.replace("自治州", "");
              let t_index = -1;
              for (let m = 0; m < provinceArray.length; m++) {
                if (provinceArray[m].province === item.label.province) {
                    t_index = m;
                    break;
                }
              }
              if (t_index < 0) {
                provinceArray.push({
                  province: item.label.province,
                  shortProvince: shortString,
                  citys: [{
                      city: item.label.city,
                      shortCity: t_shortCity,
                      country: item.label.country,
                      centralLongitude:item.label.centralLongitude,
                      centralLatitude: item.label.centralLatitude
                    }]
                });
              } else {
                let t_citys = provinceArray[t_index].citys;
                let t_city_index = -1;
                for (let n = 0; n < t_citys.length; n++) {
                  if (t_citys[n].city === item.label.city) {
                    t_city_index = n;
                    break;
                  }
                }
                if (t_city_index < 0) {
                  provinceArray[t_index].citys.push({
                    city: item.label.city,
                    shortCity: t_shortCity,
                    country: item.label.country,
                    centralLongitude: item.label.centralLongitude,
                    centralLatitude: item.label.centralLatitude
                  });
                }
              }
          });
          provinceArray = provinceArray.sort(function (a, b) {
            return a.province.localeCompare(b.province, "zhn-Hans-CN", {sensitivity: "accent"});
          });
          provinceArray.forEach(item => {
              item.citys = item.citys.sort(function(a,b) {
                  return a.city.localeCompare(b.city, "zhn-Hans-CN", {sensitivity: "accent"});
              });
          });
          state.chinaCityData = provinceArray
      },
      // 存储日期列表
      setDataList(state,res){
        if(res?.length >= 20){
          state.lastDataListRowKey = [res[res.length - 1].row]
          window.main.$store.dispatch('telegramSearch/telegramSearchCollection/sendDataList')
        }else{
          state.dataList.push(...res)
          state.dataListIndex = state.dataList.length
          state.NowDate = state.dataList[state.dataList.length - 1]?.columnValues.d.date.date
          let item = state.dataList[state.dataList.length - 1]?.columnValues.d.date.date
          let str1 = item?.slice (0, 4) + "-" + item?.slice (4)
          let str2 = str1?.slice (0, 7) + "-" + str1?.slice (7)
          state.NowDateNorm = str2

          state.nearbyUserDotList = []
          state.nearbyGroupDotList = []
          state.nearbyUserAllRow = []
          state.nearbyGroupAllRow = []
          window.main.$store.dispatch('telegramSearch/telegramSearchCollection/sendDotNearAllGroups')
          window.main.$store.dispatch('telegramSearch/telegramSearchCollection/sendDotNearAllUsers')
        }
      },
      clearDataList(state,v){
        state.dataList = []
      },
      // 存储查看的日期
      setNowDateNorm(state,v){
        state.NowDateNorm = v
      },
      //存储查询日期
      setNowDate(state,v){
        state.NowDate = v
      },
      //是否发送爬虫任务
      setCollectionSpiderTask(state,res){
        if(res?.status=== 'ok'){
          window.main.$message.success('添加爬虫任务成功!')
        }else{
          window.main.$message.error('添加爬虫任务失败!')
        }
      },
      //清空查询到的点
      clearDotList(state){
        state.nearbyUserDotList = []
        state.nearbyGroupDotList = []
        state.nearbyGroupDotRow = []
        state.nearbyUserDotRow = []
      },
      //存储查询到的用户
      setDotNearbyUsers(state,res){
        if(res.length < 20){
          state.nearbyUserDotList.push(...res)
        }else{
          state.nearbyUserDotList.push(...res)
          state.nearbyUserDotRow = [res[res.length-1].row]
          window.main.dispatch('telegramSearch/telegramSearchCollection/sendDotNearbyUsers')
        }
      },
      // 存储查询到的群组
      setDotNearbyGroups(state,res){
        if(res.length < 20){
          state.nearbyGroupDotList.push(...res)
        }else{
          state.nearbyGroupDotList.push(...res)
          state.nearbyGroupDotRow = [res[res.length-1].row]
          window.main.dispatch('telegramSearch/telegramSearchCollection/sendDotNearbyGroups')
        }
      },
      //存储查询到的用户
      setDotNearbyAllUsers(state,res){
        if(res.length < 20){
          state.nearbyUserDotList.push(...res)
        }else{
          state.nearbyUserDotList.push(...res)
          state.nearbyUserAllRow = [res[res.length-1].row]
          window.main.dispatch('telegramSearch/telegramSearchCollection/sendDotNearAllUsers')
        }
      },
      // 存储查询到的群组
      setDotNearbyAllGroups(state,res){
        if(res.length < 20){
          state.nearbyGroupDotList.push(...res)
        }else{
          state.nearbyGroupDotList.push(...res)
          state.nearbyGroupAllRow = [res[res.length-1].row]
          window.main.dispatch('telegramSearch/telegramSearchCollection/sendDotNearAllGroups')
        }
      },
  },
  actions: {
      // 获取城市列表
      sendChinaCityData({dispatch,commit,state},v){
          window.main.$constant_socket.sendData("Api.Node.NodeData",[{
              head: {
                  session_id: window.main.$store.state.userInfo.session_id
              },
              msg: {
                  "/etc/web/instant_msg_analysis_platform/city_list": ""
              }
          }],'telegramSearch/telegramSearchCollection/setChinaCityData')
      },
      // 获取日期列表
      sendDataList({dispatch,commit,state},v){
        window.main.$data_socket.sendData("Api.Search.SearchPrefix.Query",[
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: state.lastDataListRowKey,
              size: 20
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/collection_data",
              prefix: "",
            }
          }
        ],'telegramSearch/telegramSearchCollection/setDataList')
      },
      // 发送爬虫任务
      sendCollectionSpiderTask({state,dispatch,commit}, v){
         if(window.main.$store.state.home.isSeparation === true){
            window.main.$message.error('内网无法发送爬虫任务，请前往外网查询！')
            return
         }
          window.main.$data_socket.sendData('Api.DataAnalysisTask.SendAsyncTask', [{
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              key:"FakeGpsLocateNearby",
              topic: "HbaseTask.SpiderTask.Telegram.FakeGpsLocateNearby",
              value: {
                      longitude:v.longitude,
                      latitude:v.latitude
                    }
                  }
            }],'telegramSearch/telegramSearchCollection/setCollectionSpiderTask')
      },
      // 查询采集点附近的用户
      sendDotNearbyUsers({state,dispatch,commit},v){
        state.sendDotNearbyPoint = v
        window.main.$data_socket.sendData("Api.Search.SearchPrefix.Query",[
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: state.nearbyUserDotRow ? state.nearbyUserDotRow : [],
              size: 20
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/collection_data",
              prefix: state.sendDotNearbyPoint.prefixLat + "," + state.sendDotNearbyPoint.prefixLng,
              relation: state.NowDate + ";layer;"+state.sendDotNearbyPoint.zoomLevel+";user"
            }
          }
        ],'telegramSearch/telegramSearchCollection/setDotNearbyUsers')
      },
      // 查询采集点附近的群
      sendDotNearbyGroups({state,dispatch,commit},v){
        state.sendDotNearbyPoint = v
        window.main.$data_socket.sendData("Api.Search.SearchPrefix.Query",[
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key:state.nearbyGroupDotRow ? state.nearbyGroupDotRow : [],
              size: 20
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/collection_data",
              prefix: state.sendDotNearbyPoint.prefixLat + "," + state.sendDotNearbyPoint.prefixLng,
              relation: state.NowDate + ";layer;"+state.sendDotNearbyPoint.zoomLevel+";group"
            }
          }
        ],'telegramSearch/telegramSearchCollection/setDotNearbyGroups')
      },
      // 查询当日所有的群
      sendDotNearAllGroups({state,dispatch,commit,v}){
        window.main.$data_socket.sendData("Api.Search.SearchPrefix.Query",[
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: state.nearbyGroupAllRow ? state.nearbyGroupAllRow : [],
              size: 20
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/collection_data",
              prefix: '',
              relation: state.NowDate + ";layer;"+state.zoomLevel+";group"
            }
          }
        ],'telegramSearch/telegramSearchCollection/setDotNearbyAllGroups')
      },
      // 查询当日所有的用户
      sendDotNearAllUsers({state,dispatch,commit},v){
        window.main.$data_socket.sendData("Api.Search.SearchPrefix.Query",[
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: state.nearbyUserAllRow ? state.nearbyUserAllRow : [],
              size: 20
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/collection_data",
              prefix: '',
              relation: state.NowDate + ";layer;"+state.zoomLevel+";user"
            }
          }
        ],'telegramSearch/telegramSearchCollection/setDotNearbyAllUsers')
      }
  }
}
