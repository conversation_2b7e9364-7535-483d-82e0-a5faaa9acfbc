export default {
  namespaced: true,
  state: {
    searchTabs: null,
    showComponent: false,
    tabsActiveName: "",
  },
  mutations: {
    sendSearchData(state) {
      console.log("ssss", window.main.$store.state.search.conditions);
    },
    setTabs(state, data) {
      console.log("setTabs", data);
      state.searchTabs = [];
      /* state.searchTabs =
        data[
          "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
        ].tabs;
      state.tabsActiveName =
        data[
          "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
        ].tabs[0].value;
      window.main.$store.commit("search/searchList/setShowComponent", true); */
      if (
        !data?.hits.hasOwnProperty("hits") ||
        data?.hits?.hits?.length === 0
      ) {
        window.main.$message.warning("没有数据！");
        return;
      }
      data?.hits?.hits?.forEach((item) => {
        item._source.project_content = JSON.parse(item._source.project_content);
        state.searchTabs.push({
          label: item._source.project_content.label,
          value: item._source.project_content.value,
          id: item._id,
        });
      });
      console.log("state.searchTabs", state.searchTabs);
      // 创建一个映射，将value作为键，索引作为值
      const ruleArray = [
        {
          label: "舆情",
          value: "public_opinion",
        },
        {
          label: "Telegram",
          value: "telegram",
        },
        {
          label: "Twitter",
          value: "twitter",
        },
        {
          label: "Facebook",
          value: "facebook",
        },
        {
          label: "Linkedin",
          value: "linkedin",
        },
        {
          label: "社工库",
          value: "social_work_library",
        },
      ];
      const orderMap = new Map();
      ruleArray.forEach((item, index) => {
        orderMap.set(item.value, index);
      });

      // 根据规则数组的顺序排序原数组
      const sortedArray = state.searchTabs.sort((a, b) => {
        const indexA = orderMap.get(a.value) ?? Infinity; // 如果原数组中存在规则数组中没有的项，则排在最后
        const indexB = orderMap.get(b.value) ?? Infinity;
        return indexA - indexB;
      });
      state.tabsActiveName = state.searchTabs[0].value;
      window.main.$store.commit("search/searchList/setShowComponent", true);
    },
    setTabsActiveName(state, v) {
      state.tabsActiveName = v;
    },
    setShowComponent(state, v) {
      state.showComponent = v;
    },
  },
  actions: {
    //获取etc路径
    getTabs(state) {
      /* window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs":
                "",
            },
          },
        ],
        "search/searchList/setTabs"
      ); */
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 20,
            },
            control: {
              query_string: "",
              query_type: "username",
              condition: {
                query_mode: "match",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
              add_es_query_conditions: {
                bool: {
                  must: [],
                },
              },
            },
            msg: {
              data_range_index_name:
                "intelligen_intelligence_management_project_management",
            },
          },
        ],
        "search/searchList/setTabs"
      );
    },
  },
};
