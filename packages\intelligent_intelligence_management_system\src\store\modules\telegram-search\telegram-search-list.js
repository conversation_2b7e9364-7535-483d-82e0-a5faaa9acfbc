export default {
  namespaced: true,
  state: {
    statisticalList: {
      reception: {},
      send: {},
      fileType: {},
      time: {},
    },
    addEsQuerygroundMemberConditions: null,
    addEsQueryConditions: null,
    activeNamePrefix: "group_data_prefix_telegram",
    dataListGetter: {},
    dataListTotal: 0,
    dataListAllTotal: 0,
    dataList: [],
    queryString: "", // 搜索内容
    use_public: "public",
    direction: true,
    from: 0,
    size: 20,
    search: false,
    // 遮罩层定时
    beginBun: 0,
    // 遮罩层定时器
    timer: null,
    // 遮罩层
    loading: null,
    // 取消遮罩层的timer
    clearTimer: null,
    // 取消遮罩层定时
    clearBeginBun: 0,
    req: false,
    // 是否获取下一个dataRange的数据
    nextFlag: false,
    memberIcon: {},
    groupIcon: {},
    statisticalList: {
      reception: {},
      send: {},
      fileType: {},
      time: {},
    },
    showTranslate: null,

    index_prefix: "group_data_prefix_telegram",
    father_path: "/group_data/telegram/group_data_prefix",
    listTruefrom: 0,
    listTruesize: 20,
    listTrueEnd: false,
    queryEnd: false,
    tgLoading: false,
    querysize: 20
  },
  mutations: {
    // 是否显示翻译文章
    getShowTranslate(state) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              "/etc/web/instant_msg_analysis_platform/translate_doc": "",
            },
          },
        ],
        "telegramSearch/telegramSearchList/setShowTranslate"
      );
    },
    setShowTranslate(state, v) {
      state.showTranslate = v;
    },
    setClearStatisticalList(state, v) {
      state.statisticalList = {
        reception: {},
        send: {},
        fileType: {},
        time: {},
      };
    },

    // 设置查询的prefix、fatherPath
    setSearchPrefixFatherPath(state,param) {
      state.activeNamePrefix = param.index_prefix;
      state.index_prefix = param.index_prefix;
      state.father_path = param.father_path;
    },

    setActiveNamePrefix(state, v) {
      state.activeNamePrefix = v;
    },
    setLoadingLayer(state) {
      if (state.loading != null || state.timer != null) {
        return;
      }
      // 如果重新进入setLoadingLayer了，clearLoadingLayer无论是什么状态都取消掉。
      state.clearBeginBun = 0;
      clearInterval(state.clearTimer);
      state.clearTimer = null;
      state.clearBeginBun = 0;

      let a = document.createElement("div");
      a.id = "closeMyLoading";
      a.style.cssText =
        "cursor:pointer;position:absolute;right:20px;top:20px;background:#409eff;color:#fff;text-align:center;padding:5px 10px;border-radius:3px;z-index:2001 !important;width:100px;height:30px"; 
      a.innerHTML = "停止搜索";
      a.onclick = function () {
        if (state.timer) {
          clearInterval(state.timer);
        }
        window.main.$store.state.telegramSearch.telegramSearchList.dataListGetter.return();
        window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.return();
        window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter = null;

        // 关闭遮罩层
        window.main.$store.commit("telegramSearch/telegramSearchList/clearLoadingLayer");
        // 查询结束了强行关闭遮罩层
        window.main.$store.commit(
          "telegramSearch/telegramSearchList/clearLoadingLayerForceSet"
        );
      };
      document.querySelector("#closeESsearch").append(a);
      let closeESsearch = document.querySelector("#closeESsearch");
      state.loading = window.main.$loading({
        target: closeESsearch,
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        customClass: "my-loading"
      });

      state.beginBun = 0;
      state.timer = setInterval(() => {
        state.beginBun++;
        if (state.beginBun > 50) {
          window.main.$message({
            message: "加载超时",
            type: "warning",
          });
          state.loading.close();
          if (document.getElementById("closeMyLoading") != null) {
            document.getElementById("closeMyLoading").remove();
          }
          clearInterval(state.timer);
          state.loading = null;
          state.timer = null;
        }
      }, 300);
    },
    clearLoadingLayer(state) {
      if (state.clearTimer) {
        state.clearBeginBun = 0;
        return;
      }
      state.clearTimer = setInterval(() => {
        state.clearBeginBun++;
        if (state.clearBeginBun > 10) {
          if (state.loading) {
            state.loading.close();
            state.loading = null;
            if (document.getElementById("closeMyLoading") != null) {
              document.getElementById("closeMyLoading").remove();
            }
          }
          if (state.timer) {
            clearInterval(state.timer);
            state.timer = null;
          }
          clearInterval(state.clearTimer);
          state.clearTimer = null;
        }
      }, 300);
    },
    clearLoadingLayerForceSet(state) {
      state.clearBeginBun = 100;
    },
    clearLoadingBun(state) {
      state.beginBun = 0;
    },
    setAddEsQuerygroundMemberConditions(state, v) {
      state.addEsQuerygroundMemberConditions = {
        bool: {
          must: [
            {
              term: {
                group_member: v.groundMember,
              },
            },
          ],
        },
      };
    },
    setAddEsQueryConditions(state, v) {
      let obj = {
        bool: {
          must: [],
        },
      };
      let targetUserId = {
        bool: {
          should: [],
        },
      };
      let targetGroundId = {
        bool: {
          should: [],
        },
      };
      if (v.hasOwnProperty("targetUserId")) {
        v.targetUserId.forEach((item, index) => {
          if (item.value) {
            targetUserId.bool.should.push({
              term: {
                group_member: item.value,
              },
            });
          }
        });
        if (targetUserId.bool.should.length) {
          obj.bool.must.push(targetUserId);
        }
      }

      if (v.hasOwnProperty("groundIdData")) {
        v.groundIdData.forEach((item, index) => {
          if (item.value) {
            targetGroundId.bool.should.push({
              term: {
                group_id: item.value,
              },
            });
          }
        });
        if (targetGroundId.bool.should.length) {
          obj.bool.must.push(targetGroundId);
        }
      }
      state.addEsQueryConditions = obj;
    },
    clearEsQueryConditions(state, v) {
      state.addEsQueryConditions = null;
    },
    setDataListGetter(state, data) {
      console.log("setDataListGetter");
      function* getter(data) {
        if (data == null) {
          return;
        }
        while (true) {
          let condition = window.main.$store.state.telegramSearch.telegramSearchConditions;
          let control = {};

          if (
            state.activeNamePrefix === "group_content_data_prefix" &&
            condition.time_range !== "无"
          ) {
            control = {
              condition: condition,
              query_string: state.queryString,
              query_type: state.use_public,
              add_es_query_conditions: state.addEsQueryConditions,
              sort: [{ timestamp: { order: "desc" } }],
            };
          } else {
            control = {
              condition: condition,
              query_string: state.queryString,
              query_type: state.use_public,
              add_es_query_conditions: state.addEsQueryConditions,
            };
          }
          if (state.activeNamePrefix === "group_member_data_prefix") {
            control = {
              condition: condition,
              query_string: state.queryString,
              query_type: state.use_public,
              add_es_query_conditions: state.addEsQuerygroundMemberConditions,
            };
          }
          let resData = {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              from: state.from,
              size: state.size,
            },
            msg: data,
            control: control,
          };
          window.main.$store.commit("telegramSearch/telegramSearchList/setLoadingLayer");
          state.beginBun = 0;
          yield window.main.$main_socket.sendData(
            "Api.Search.SearchList.Query",
            [resData],
            "telegramSearch/telegramSearchList/setDataList"
          );
          state.from += state.size;
        }
        return;
      }

      state.dataListGetter = getter(data);
      let nextRes = state.dataListGetter.next();
      if (nextRes.value === undefined && nextRes.done) {
        window.main.$store.commit("telegramSearch/telegramSearchList/clearLoadingLayer");
      }
    },
    setReq(state, v) {
      state.req = false;
    },
    setSearch(state, v) {
      state.search = v;
    },
    // 清除搜索条件及内容
    clearSearchList(state) {
      state.dataListTotal = 0;
      state.dataListAllTotal = 0;
      state.dataList = [];
      state.queryString = "";
      state.direction = true;
      state.from = 0;
      state.memberIcon = {};
      state.tgLoading = false;
      state.listTrueEnd = false;
      state.listTruefrom = 0;
      state.queryEnd = false;
    },
    setTGLoading(state, v) {
      state.tgLoading = v
    },
    // 设置查询内容
    setQueryString(state, v) {
      state.queryString = v;
    },
    // 设置搜索结果
    async setDataList(state, v) {
      console.log("setDataList:",v);
      
      window.main.$store.commit("telegramSearch/telegramSearchList/clearLoadingLayer");
      try {
        if (
          v == null ||
          v == "undefined" ||
          !v.hasOwnProperty("hits") ||
          v.hits == null ||
          !v.hits.hasOwnProperty("hits") ||
          v.hits.hits == null
        ) {
          state.req = false;
          state.from = 0;
          window.main.$store.state.telegramSearch.telegramSearchList.dataListGetter.return();
          window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.next(
            "nextDataRangePage"
          );
          return;
        } else {
          state.req = true;
        }
        // 叠加查询总数
        if (v.hits.hits[0]["_index"].indexOf("group_member_data_prefix") > -1) {
          state.activeNamePrefix = "group_member_data_prefix";
        }
        if (
          v.hits.hits[0]["_index"].indexOf("group_content_data_prefix") > -1
        ) {
          state.activeNamePrefix = "group_content_data_prefix";
        }

        state.dataListTotal += v.hits.hits.length;
        state.dataListAllTotal = v.hits.total.value;
        // 判断并保存已经取下来的数据
        state.dataList = state.dataList.concat(v.hits.hits);

        if (
          v?.hits?.hits &&
          state.activeNamePrefix === "group_content_data_prefix"
        ) {
          let userArr = [];
          let groupArr = [];
          v.hits.hits.forEach((item) => {
            if (!state.memberIcon.hasOwnProperty(item._source.group_member)) {
              userArr.push(item._source.group_member);
            }
            if (!state.groupIcon.hasOwnProperty(item._source.group_id)) {
              groupArr.push(item._source.group_id);
            }
          });
          window.main.$store.dispatch("telegramSearch/telegramSearchList/sendMemberIcon", {
            userArr: userArr,
            groupArr: groupArr,
          });
        }
        // 判断如果还可以取数据，继续取
        if (state.dataList.length < state.size) {
          state.from = 0;
          window.main.$store.state.telegramSearch.telegramSearchList.dataListGetter.return();
          window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.next(
            "nextDataRangePage"
          );
          return;
        }
        function shijianc(time) {
          let date = new Date(time * 1000);
          let Y = date.getFullYear() + "-";
          let M =
            date.getMonth() + 1 < 10
              ? "0" + (date.getMonth() + 1)
              : date.getMonth() + 1;
          return Y + M;
        }

        let arrStatistical = {
          reception: {},
          send: {},
          fileType: {},
          time: {},
        };
        state.dataList.forEach((item) => {
          // 时间
          let timer = shijianc(item._source["@timestamp"]);
          if (arrStatistical.time.hasOwnProperty(timer)) {
            arrStatistical.time[timer] += 1;
          } else {
            arrStatistical.time[timer] = 1;
          }
          // 接收群
          let group_id = item._source.group_id;
          if (arrStatistical.reception.hasOwnProperty(group_id)) {
            arrStatistical.reception[group_id] += 1;
          } else {
            arrStatistical.reception[group_id] = 1;
          }
          //发送者
          let group_member = item._source.group_member;
          if (arrStatistical.send.hasOwnProperty(group_member)) {
            arrStatistical.send[group_member] += 1;
          } else {
            arrStatistical.send[group_member] = 1;
          }
          // 文本类型
          if (item._source.hasOwnProperty("content_article")) {
            if (arrStatistical.fileType.hasOwnProperty("文本类型")) {
              arrStatistical.fileType["文本类型"] += 1;
            } else {
              arrStatistical.fileType["文本类型"] = 1;
            }
          }
          // 图片类型
          if (item._source.hasOwnProperty("content_img")) {
            if (arrStatistical.fileType.hasOwnProperty("图片类型")) {
              arrStatistical.fileType["图片类型"] += 1;
            } else {
              arrStatistical.fileType["图片类型"] = 1;
            }
          }
          // PDF类型
          if (item._source.hasOwnProperty("content_pdf")) {
            if (arrStatistical.fileType.hasOwnProperty("PDF类型")) {
              arrStatistical.fileType["PDF类型"] += 1;
            } else {
              arrStatistical.fileType["PDF类型"] = 1;
            }
          }
          //视频类型
          if (item._source.hasOwnProperty("content_video")) {
            if (arrStatistical.fileType.hasOwnProperty("视频类型")) {
              arrStatistical.fileType["视频类型"] += 1;
            } else {
              arrStatistical.fileType["视频类型"] = 1;
            }
          }
          // 音频类型
          if (item._source.hasOwnProperty("content_voice")) {
            if (arrStatistical.fileType.hasOwnProperty("音频类型")) {
              arrStatistical.fileType["音频类型"] += 1;
            } else {
              arrStatistical.fileType["音频类型"] = 1;
            }
          }
          // 文件类型
          if (item._source.hasOwnProperty("content_file")) {
            if (arrStatistical.fileType.hasOwnProperty("文件类型")) {
              arrStatistical.fileType["文件类型"] += 1;
            } else {
              arrStatistical.fileType["文件类型"] = 1;
            }
          }
        });
        state.statisticalList = arrStatistical;
        // 如果是取满size数据了的情况，而且有新数据，强行关闭遮罩层。
        window.main.$store.commit(
          "telegramSearch/telegramSearchList/clearLoadingLayerForceSet"
        );
      } catch (err) {
        //throw err;
      }
    },

    setMemberIcon(state, res) {
      res?.forEach((item) => {
        if (item.columnValues && item.columnValues.d) {
          let MemberInfo = {
            telephone: "",
            nickname: "",
            location: "",
            username: "",
            user_id: "",
            icon: "",
          };
          for (const key in item.columnValues.d) {
            const dataItem = item.columnValues.d[key];
            if (key.startsWith("icon")) {
              MemberInfo.icon = dataItem?.icon?.sha512_hash;
            }
            if (key.startsWith("telephone")) {
              MemberInfo.telephone = dataItem?.telephone;
            }
            if (key.startsWith("nickname")) {
              MemberInfo.nickname = dataItem?.nickname;
            }
            if (key.startsWith("location")) {
              MemberInfo.location = dataItem?.location;
            }
            if (key.startsWith("username")) {
              MemberInfo.username = dataItem?.username;
            }
            if (key.startsWith("user_id")) {
              MemberInfo.user_id = dataItem?.user_id;
            }
          }
          window.main.$set(
            state.memberIcon,
            item.columnValues.d?._?._,
            MemberInfo
          );
          for (let str in state.statisticalList.send) {
            let ind = str.lastIndexOf(")");
            if (ind != -1) {
              let a = str.slice(ind + 1, str.length);
              if (a === item.columnValues.d._._) {
                state.statisticalList.send[str] += 1;
              }
              delete state.statisticalList.send[a];
            } else {
              if (str === item.columnValues.d._._) {
                state.statisticalList.send[
                  "(" +
                    (MemberInfo.username ? MemberInfo.username : "暂无名字") +
                    ")" +
                    item.columnValues.d._._
                ] = state.statisticalList.send[str];
                delete state.statisticalList.send[str];
              }
            }
          }
        }
      });
    },
    setGroup(state, res) {
      res?.forEach((item) => {
        if (item.columnValues && item.columnValues.d) {
          let groupInfo = {
            telephone: item.columnValues.d?.telephone?.telephone,
            last_msg_timestamp:
              item.columnValues.d?.last_msg_timestamp?.last_msg_timestamp,
            last_msg: item.columnValues.d?.last_msg?.last_msg,
            group_name: item.columnValues.d?.group_name?.group_name,
            group_id: item.columnValues.d?.group_id?.group_id,
            icon: item.columnValues.d?.icon?.icon?.sha512_hash,
          };
          for (const key in item.columnValues.d) {
            const dataItem = item.columnValues.d[key];
            if (key.startsWith("icon")) {
              groupInfo.icon = dataItem?.icon?.sha512_hash;
            }
            if (key.startsWith("telephone")) {
              groupInfo.telephone = dataItem?.telephone;
            }
            if (key.startsWith("last_msg_timestamp")) {
              groupInfo.last_msg_timestamp = dataItem?.last_msg_timestamp;
            }
            if (key.startsWith("last_msg")) {
              groupInfo.last_msg = dataItem?.last_msg;
            }
            if (key.startsWith("group_name")) {
              groupInfo.group_name = dataItem?.group_name;
            }
            if (key.startsWith("group_id")) {
              groupInfo.group_id = dataItem?.group_id;
            }
          }
          window.main.$set(
            state.groupIcon,
            item.columnValues.d?._?._,
            groupInfo
          );
          for (let str in state.statisticalList.reception) {
            let ind = str.lastIndexOf("]");
            if (ind != -1) {
              let a = str.slice(ind + 1, str.length);
              if (a === item.columnValues.d._._) {
                state.statisticalList.reception[str] += 1;
              }
              delete state.statisticalList.reception[a];
            } else {
              if (str === item.columnValues.d._._) {
                state.statisticalList.reception[
                  "[" +
                    (groupInfo.group_name ? groupInfo.group_name : "暂无群名") +
                    "]" +
                    item.columnValues.d._._
                ] = state.statisticalList.reception[str];
                delete state.statisticalList.reception[str];
              }
            }
          }
        }
      });
    },

    //获取listTrue
    getListTrue(state) {
      console.log("getListTrue:", state.father_path, state.index_prefix);
      state.tgLoading = true;
      let dataRangeDetail = {
        head: {
          from: state.listTruefrom,
          size: state.listTruesize,
        },
        control: {
          query_type: "public",
          prefix_filter: state.index_prefix
        },
        msg: {
          data_range_father_path: state.father_path,
          data_range_index_prefix: state.index_prefix,
        },
      };
      window.main.$main_socket.sendData(
        "Api.Search.DataRange.ListTrue", 
        [dataRangeDetail],
        "telegramSearch/telegramSearchList/setListTrue"
      )
    },
    //获取query下的hits
    getHits(state, v) {
      console.log("getHits:", v); 
      let condition = window.main.$store.state.search.conditions.conditionsData;
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [{
          head: {
            from: state.queryfrom,
            size: state.querysize,
          },
          msg: v,
          control: {
            query_type: "public",
            condition: {
              time_range: condition.timeRange,
              time_range_begin: condition.time_range_begin,
              time_range_end: condition.time_range_end,
              query_mode: condition.ppqueryMode,
            },
            add_es_query_conditions: state.addEsQueryConditions,
            query_string: condition.queryString,
          },
        }],
        "telegramSearch/telegramSearchList/setHits"
      )
    },

    // 设置listTrue
    setListTrue(state, data) {
      console.log("setListTrue:", data);
      if (data) {
        state.queryEnd = false;
        if (data.length > 0) {
          let data_range_index_name_arr = [];
          for (let i = 0; i < data.length; i++) {
            data_range_index_name_arr.push(data[i].data_range_index_name);
          }
          let data_range_index_name = data_range_index_name_arr.join(",");
          //搜索query
          window.main.$store.commit("telegramSearch/telegramSearchList/getHits", {
            data_range_index_name: data_range_index_name,
          });
          if (data.length != 20) {
            state.listTrueEnd = true;
            console.log("listTrueEndstate.", state.listTrueEnd, state.queryEnd);
          }
        }
      } else {
        state.listTrueEnd = true;
        state.queryEnd = true;
        console.log("elselistTrueEndstate.", state.listTrueEnd, state.queryEnd);
      }
    },

    // 设置数据
    setHits(state, data) {
      console.log("设置数据:", data);
      if (data.hits.hasOwnProperty("hits") && data.hits.hits.length > 0) {
        state.dataList = state.dataList.concat(data.hits.hits);
        state.queryfrom += state.querysize;
        state.tgLoading = false;
        if (state.activeNamePrefix === "group_content_data_prefix") {
          let userArr = []
          let groupArr = []
          data.hits.hits.forEach((item) => {
            if (!state.memberIcon.hasOwnProperty(item._source.group_member)) {
              userArr.push(item._source.group_member);
            }
            if (!state.groupIcon.hasOwnProperty(item._source.group_id)) {
              groupArr.push(item._source.group_id);
            }
          })
          window.main.$store.dispatch("telegramSearch/telegramSearchList/sendMemberIcon", {
            userArr: userArr,
            groupArr: groupArr,
          });
        }
        if (data.hits.hits.length < 20) {
          state.listTruefrom += state.listTruesize;
          state.queryEnd = true;
          state.queryfrom = 0;
          window.main.$store.commit("telegramSearch/telegramSearchList/getListTrue")
        }
      } else {
        state.listTruefrom += state.listTruesize;
        state.queryEnd = true;
        state.queryfrom = 0;
        window.main.$store.commit("telegramSearch/telegramSearchList/getListTrue")
      }
    }
  },
  actions: {
    // 获取消息列表发言人的头像信息
    async sendMemberIcon({ state, dispatch, commit }, v) {
      const sha512 = require("sha512");
      let memberRowArr = [];
      const memberHash =
        sha512("p;/instant_msg/telegram/user_id").toString("hex") +
        ";p;/instant_msg/telegram/user_id;";
      v.userArr.forEach((item) => {
        memberRowArr.push(memberHash + item);
      });
      fn(
        memberRowArr,
        "/instant_msg/telegram/user_id",
        "telegramSearch/telegramSearchList/setMemberIcon"
      );

      //group_id
      let groupRowArr = [];
      const groupHash =
        sha512("p;/instant_msg/telegram/group_id").toString("hex") +
        ";p;/instant_msg/telegram/group_id;";
      v.groupArr.forEach((item) => {
        groupRowArr.push(groupHash + item);
      });
      fn(
        groupRowArr,
        "/instant_msg/telegram/group_id",
        "telegramSearch/telegramSearchList/setGroup"
      );

      function fn(newArr, newPath, newCallback) {
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefix.DetailMulti",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                row_key: newArr,
              },
              msg: {
                type: "public",
                path: newPath,
                relation: "",
                prefix: "",
              },
            },
          ],
          newCallback
        );
      }
    },
  },
};
