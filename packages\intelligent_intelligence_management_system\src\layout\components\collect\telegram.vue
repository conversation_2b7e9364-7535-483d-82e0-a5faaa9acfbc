<template>
    <div class="telegram">
        <!-- <div v-for="(val, index) in dataList" :key="index" class="newContent">
        </div> -->

        <el-table :data="dataList" style="width: 100%" :height="'100%'" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column><!-- 多选收藏 -->
            <el-table-column>
                <template slot="header">
                    <el-button size="mini" @click="delFileData" v-show="checkedCities.length">删除</el-button>
                </template>
                <template slot-scope="scope">
                    <div class="left_box">
                        <div v-if="scope.$index % 20 === 0 && scope.$index !== 0" class="index-marker">
                            第 {{ scope.$index / 20 }} 页
                        </div>
                        <div class="left_item">
                            <div class="item_info">
                                <div class="user_info">
                                    <img v-if="
                                        memberIcon.hasOwnProperty(
                                            scope.row._source.group_member
                                        ) && memberIcon[scope.row._source.group_member].icon
                                    " :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                                        memberIcon[scope.row._source.group_member].icon +
                                        '?session_id=' +
                                        userInfo.session_id
                                        " />
                                    <img v-else :src="require('@/assets/images/user.png')" />
                                    <div class="perc" v-if="
                                        memberIcon.hasOwnProperty(
                                            scope.row._source.group_member
                                        )
                                    ">
                                        <p v-if="
                                            memberIcon[scope.row._source.group_member]
                                                .nickname
                                        ">
                                            <b>昵称：</b>{{
                                                memberIcon[scope.row._source.group_member]
                                                    .nickname
                                            }}
                                        </p>
                                        <p v-if="
                                            memberIcon[scope.row._source.group_member]
                                                .username
                                        ">
                                            <b>用户名：</b>{{
                                                memberIcon[scope.row._source.group_member]
                                                    .username
                                            }}
                                        </p>
                                        <p v-if="
                                            memberIcon[scope.row._source.group_member]
                                                .telephone
                                        ">
                                            <b>手机号：</b>{{
                                                memberIcon[scope.row._source.group_member]
                                                    .telephone
                                            }}
                                        </p>
                                        <p v-if="
                                            memberIcon[scope.row._source.group_member]
                                                .location
                                        ">
                                            <b>归属地：</b>{{
                                                memberIcon[scope.row._source.group_member]
                                                    .location
                                            }}
                                        </p>
                                    </div>
                                </div>
                                <div class="msg_info">
                                    <p v-if="
                                        scope.row._source && scope.row._source.group_member
                                    ">
                                        <b>发言成员（ID）：</b>{{ scope.row._source.group_member }}
                                    </p>
                                    <p v-if="scope.row._source && scope.row._source.group_id">
                                        <b>发言群组（ID）：</b>{{ scope.row._source.group_id }}
                                    </p>
                                    <p v-if="
                                        scope.row._source && scope.row._source.timestamp
                                    ">
                                        <b>发言时间：</b>{{
                                            $tools.timestampToTime(scope.row._source.timestamp)
                                        }}
                                    </p>
                                    <p v-if="
                                        scope.row._source && scope.row._source['@timestamp']
                                    ">
                                        <b>入库时间：</b>{{
                                            $tools.timestampToTime(
                                                scope.row._source["@timestamp"]
                                            )
                                        }}
                                    </p>
                                    <p v-if="scope.row._source">
                                        <b>语义分析：</b>{{
                                            scope.row._source.nlp_parse_flag
                                                ? "已分析"
                                                : "未分析"
                                        }}
                                    </p>
                                    <p v-if="scope.row._score">
                                        <b>搜索评分：</b>{{ scope.row._score }}
                                    </p>
                                </div>
                            </div>
                            <div class="item_body">
                                <div class="body_btn">
                                    <b>消息内容：</b>
                                    <div v-if="!(scope.row == null)">
                                        <!-- <el-button
                                            type="primary"
                                            icon="el-icon-menu"
                                            size="mini"
                                            v-if="val._source.hasOwnProperty('content_article')"
                                            @click.native="addTemporary(val)"
                                            circle
                                            title="添加分组"
                                        ></el-button> -->
                                        <!-- <el-button type="primary" icon="el-icon-more" size="mini"
                                            @click.native="morefn(scope.row)" circle title="详情"></el-button> -->
                                    </div>
                                </div>
                                <div class="body_text">
                                    <p v-if="
                                        scope.row['_source'].hasOwnProperty(
                                            'content_article'
                                        ) && !scope.row.hasOwnProperty('highlight')
                                    ">
                                        {{ scope.row["_source"].content_article }}
                                    </p>
                                    <p v-html="scope.row['highlight'].content.join(
                                        '&l t ;' + 'b r /' + '&gt;'
                                    )
                                        " v-if="scope.row.hasOwnProperty('highlight')"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
import ElTableInfiniteScroll from "el-table-infinite-scroll";
export default {
    directives: {
        "el-table-infinite-scroll": ElTableInfiniteScroll,
    },
    name: 'telegram',
    data() {
        return {
            checkedCities: []
        };
    },
    computed: {
        ...mapState({
            dataList: (state) => state.collect.dataList,
            nowCollect: (state) => state.collect.nowCollect,
            memberIcon: (state) => state.telegramSearch.telegramSearchList.memberIcon,
        }),
        ...mapMutations({
            setStoreMyname: "telegramSearch/telegramSearchChartData/setStoreMyname",
            clearDataDetail: "telegramSearch/telegramSearchDataDetail/clearDataDetail",
            setTmpDataDetail: "telegramSearch/telegramSearchDataDetail/setTmpDataDetail",
            sendGetDataDetailBaseData: "telegramSearch/telegramSearchDataDetail/telegramBaseData/sendGetDataDetailBaseData",
            getTranslated: "telegramSearch/telegramSearchDataDetail/getTranslated",
            initDataMore: "telegramSearch/telegramSearchChartData/initDataMore",
            initData: "telegramSearch/telegramSearchChartRange/initData",
        })
    },
    created() {
        // this.setPdf(false);
        // this.onGroupContentScroll = this.throttle(this.loadGroupContentData, 1000);
        this.loadMoreEsSeasrch = this.throttle(this.loadMoreEs, 300);
    },
    mounted() {
        this.$store.commit("collect/clearDataList")
        setTimeout(() => {
            console.log("nowCollect", this.nowCollect);
            if (this.nowCollect) {
                this.$store.commit("collect/getOpinionCollect");
            }
        }, 500);
    },
    methods: {
        delFileData() {
            console.log("checkedCities", this.checkedCities);
            if (this.checkedCities.length) {
                this.$confirm("是否取消选中的收藏列表?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        let row = []
                        this.checkedCities.forEach(element => {
                            row.push(element.row)
                        });
                        this.$store.commit("collect/sendDelFileData", { row, collCache: this.collCache });
                    })
                    .catch((err) => {
                        console.log(err);
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                this.$message.warning('未选择收藏内容')
            }
        },
        morefn(v) {
            this.checkGroupDetails = v;
            this.bian = "";
            this.dataType = v._source.type;
            this.tabactiveName = "baseData";
            this.setStoreMyname("baseData");
            this.clearDataDetail();
            this.setTmpDataDetail({ key: "d", value: v });
            this.sendGetDataDetailBaseData();
            this.getTranslated();
            this.initDataMore("likes_count");
            this.initData("likes_count");
            this.initDataMore("article_count");
            this.initData("article_count");
            this.initDataMore("groupMemberData");
            this.initData("groupMemberData");
            this.initDataMore("groupData");
            this.initData("groupData");
            this.initDataMore("groupContentData");
            this.initData("groupContentData");
            let tmpThis = this;
            setTimeout(() => {
                tmpThis.bian = "数据库";
            }, 1000);
        },
        // 节流函数
        throttle(func, delay) {
            let time = null;
            return function () {
                let args = Array.from(arguments);
                if (time === null) {
                    time = setTimeout(() => {
                        func(...args);
                        clearTimeout(time);
                        time = null;
                    }, delay);
                }
            };
        },
        // 选择需要导出、收藏的数据
        handleSelectionChange(val) {
            console.log("handleSelectionChange:", val);
            this.checkedCities = val;
        },
        // 加载es数据
        async loadMoreEs() {
            console.log("ssssssssssssss");
            if (this.nowCollect) {
                this.$store.commit("collect/getOpinionCollect");
            }
        },
    },

};
</script>

<style scoped lang="scss">
.telegram {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 15px;
}

.newContent {
    padding: 3px 10px 5px 5px;
    border: 1px solid #ccc;
}
</style>
<style lang="scss" scoped>
.left_box {
    .index-marker {
        text-align: center;
        line-height: 48px;
        background-color: #eee;
    }

    .left_item {
        width: 100%;
        border: 0.0625rem solid #ccc;
        border-radius: 0.1875rem;
        margin-bottom: 0.625rem;
        display: flex;

        .item_info {
            width: 25%;
            background: #b6c5f8;
            border-right: 0.0625rem solid #ccc;

            p {
                padding-bottom: 0.3125rem;
            }

            .user_info {
                display: flex;

                .perc {
                    padding: 0.625rem;
                    font-size: 0.75rem;
                }
            }

            img {
                margin: 0.625rem;
                width: 3.75rem;
                height: 3.75rem;
                border-radius: 50%;
                border: 0.125rem solid #409eff;
            }
        }

        .msg_info {
            padding: 0.625rem;
            font-size: 0.875rem;
        }

        .item_body {
            width: 75%;
            background: #a0add8;

            .body_btn {
                padding: 0.625rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 0.0625rem solid #ccc;
            }

            .body_text {
                white-space: pre-line;
                word-break: break-all;
                padding: 0.625rem;
            }
        }
    }
}
</style>