
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

.red_style {
  color: rgba(255, 70, 69, 0.8);
  font-size: 10px;
}

html, body, #app, .el-container {
  height: 100%;
  font-family: "Microsoft YaHei";
}

.model_lay {
  height: 100%;
  background: #fff;
}
.model_lay .data_content{
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}
.model_lay .data_content .content_left{
  width: 60%;
  height: 69vh;
  overflow: auto;
}
.model_lay .data_content .left_item{
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 10px;
  display: flex;
}
.model_lay .data_content .left_member{
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.model_lay .data_content .left_member .member_item{
  margin-right: 15px;
  margin-bottom: 15px;
  width: 48%;
  height: 200px;
  padding: 10px;
  background: #b6c5f8;
  border-radius: 3px;
}
.model_lay .data_content .left_member .member_item .member_btn{
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #867f7f;
  padding-bottom: 5px;
}
.model_lay .data_content .left_member .member_item .member_btn .info{
  width: 80%;
  display: flex;
  font-size: 14px;
}
.model_lay .data_content .left_member .member_item .member_btn .info p{
  padding-bottom: 5px;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.model_lay .data_content .left_member .member_item .member_btn img{
  margin-right: 20px;
  width: 60px;
  height: 60px;
  border: 2px solid #409eff;
  border-radius: 50%;
}
.model_lay .data_content .left_member .member_item .member_info{
  padding: 5px 10px 10px;
  font-size: 14px;
  display: flex;
}
.model_lay .data_content .left_member .member_item .member_info div{
  width: 50%;
}
.model_lay .data_content .left_member .member_item .member_info p{
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-bottom: 5px;
}
.model_lay .data_content .left_item .item_info{
  width: 25%;
  background: #b6c5f8;
  border-right: 1px solid #ccc;
}
.model_lay .data_content .left_item .item_info p{
  padding-bottom: 5px;
}
.model_lay .data_content .left_item .user_info{
  display: flex;
}
.model_lay .data_content .left_item .user_info .perc{
  padding: 10px;
  font-size: 12px;
}
.model_lay .data_content .left_item .msg_info{
  padding: 10px;
  font-size: 14px;
}
.model_lay .data_content .left_item .item_body{
  width: 75%;
  background: #a0add8;
}
.model_lay .data_content .left_item .item_body .body_btn{
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
}
.model_lay .data_content .left_item .item_body .body_text{
  white-space: pre-line;
  word-break: break-all;
  padding: 10px;
}
.model_lay .data_content .left_item .item_info img{
  margin: 10px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #409eff;
}



.omitted {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.searchoption {
  padding: 0px 10px;
  display: flex;
  margin-top: 10px;
  justify-content: space-between;
  align-items: flex-start;
}

.searchoption .search_input {
  position: relative;
  flex: 1;
}

.searchoption .search_input .search_input_a {
  display: flex;
  justify-content: space-between;
}

.searchoption .search_input .search_input_a .search_inpue_text {
  flex: 1;
  border: 1px solid #304156;
  border-radius: 0px;
  border-right: 0px;
}

.searchoption .search_input .search_input_a .search_btn {
  letter-spacing: 2px;
  font-weight: bold;
  padding-left: 30px;
  padding-right: 30px;
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
}

.searchoption .autoTime {
  width: 100%;
  display: flex;
  align-items: center;
}

.searchoption .timeRangeRadio {
  margin-top: 15px;
  margin-bottom: 15px;
}

.searchoption .timeRangeRadio .el-radio__original:checked + span {
  border: #409eff 5px solid;
  background: #fff;
}

.searchoption .advancedSearch {
  color: #666;
  font-size: 12px;
  margin-left: 5px;
  line-height: 40px;
  cursor: pointer;
}
.searchoption .clearadvancedSearch {
  color: #5557f1;
  float: right;
  font-size: 12px;
  margin-left: 5px;
  line-height: 25px;
  cursor: pointer;
}
.advancedLay {
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-top: 0px;
  min-height: 100px;
  position: absolute;
  left: 0px;
  top: 30px;
  width: 100%;
  background: #fff;
  z-index: 1;
}

.model_lay {
  position: relative;
}

.list_title .cartLink {
  float: right;
  margin-right: 15px;
  position: relative;
  background: #F56C6C;
  color: #fff;
  border-radius: 12px;
  padding: 0 10px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

.list_title span {
  font-size: 14px;
  color: rgb(255, 0, 0);
}
.list_title b {
  font-size: 14px;
  margin-left: 20px;
}
.list_lay {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
}
.list_lay .unit {
  display: flex;
  justify-content: space-between;
}

.list_lay .unit .unit_l {
  border: 1px solid #ddd;
  border-right: 0px;
  width: 114px;
  /* display: flex; */
  justify-content: center;
  padding: 10px 0 0;
  text-align: center;
}

.list_lay .unit .unit_l img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.list_lay .unit .unit_m {
  flex: 1;
  border: 1px solid #ddd;
  border-right: 0px;
}

.list_lay .unit .unit_m .unit_m_table {
  display: flex;
  flex-wrap: wrap;
}

.list_lay .unit .unit_m .unit_m_table .list_table {
  width: 170px;
  line-height: 30px;
  padding: 5px;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
}

.list_lay .unit .unit_m .unit_m_marka {
  word-break: break-all;
  background: #dbe3fd;
  border-top: 1px solid #ccc;
  padding: 10px;
}

.list_lay .unit .unit_r {
  border: 1px solid #ddd;
  width: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.morInfor {
  display: flex;
}

.morInfor .morInfor_l {
  width: 100px;
  padding: 10px;
  display: flex;
  justify-content: center;
}

.morInfor .morInfor_l img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.morInfor .morInfor_r .morInfor_r_li {
  display: flex;
  margin-top: 10px;
}

.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_h {
  color: #969696;
}

.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_c {
  margin-left: 15px;
}

.group_lay {
  max-height: 700px;
  margin-top: 20px;
}

.group_lay li {
  display: flex;
  justify-content: space-between;
  background: #eee;
  border-bottom: 2px solid #fff;
  padding-right: 10px;
  padding-bottom: 10px;
}

.group_lay li .group_l_img {
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.group_lay li .group_l_img p {
  margin-top: 10px;
  width: 140px;
  display: block;
  text-align: center;
  color: #e6a23c;
  font-weight: bold;
}

.group_lay li .group_l_img img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.group_lay li .group_r {
  flex: 1;
  padding-top: 10px;
}

.group_lay li .group_r .group_r_timestamp {
  color: #bbb;
  text-align: right;
}



.group_member_data_unit {
  background: #eee;
  border-bottom: 2px solid #fff;
  padding: 10px;
}

.histoy {
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-top: 0px;
  position: absolute;
  left: 0px;
  top: 28px;
  width: 91%;
  background: #fff;
  z-index: 1;
}
 .histoy .item{
  padding: 5px 0;
}
 .histoy .item .prec{
  padding: 5px 10px;
  color: rgb(129, 124, 124);
  cursor: pointer;
}
 .histoy .item .prec:hover{
  background-color: #eee;
}
 .histoy .item .clearHistory{
  width: 50%;
  padding: 5px;
  cursor: pointer;
  text-align: center;
}
 .histoy .item .clearHistory:hover{
  background-color: #eee;
}
.histoy .item .closeHistory{
  width: 50%;
  padding: 5px;
  cursor: pointer;
  text-align: center;
}
 .histoy .item .closeHistory:hover{
  background-color: #eee;
}
