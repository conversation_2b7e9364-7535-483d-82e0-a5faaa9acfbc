.think-block {
  padding: 12px;
  margin: 10px 0;
  background: #f3f5f7;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  color: #2c3e50;
}
.v-note-wrapper {
  min-width: auto !important;
  min-height: auto !important;
  border: 0 !important;
  border-radius: 10px !important;
  background: rgba(244, 244, 244) !important;
}
.markdown-body {
  line-height: 1.4 !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: pre-wrap !important;
}
.v-show-content {
  width: 800px !important;
  border-radius: 4px !important;
  background-color: rgb(244, 244, 244) !important;
}
.v-show-content > div:first-child {
  color: #8b8b8b !important;
  background: #fff !important;
  /* margin-bottom: 30px !important; */
  padding: 10px;
  pre {
    background-color: #a8a8a2 !important;
  }
}
.collapsed .v-show-content > div:not(.collapse-header) {
  display: none !important;
}
.v-note-wrapper .v-note-panel .v-note-show .v-show-content,
.v-note-wrapper .v-note-panel .v-note-show .v-show-content-html {
  padding: 8px !important;
}
.markdown-body {
  box-shadow: var(--markdown-box-shadow) !important;
  color: var(--font-color-markdown) !important;
}

.markdown-body p {
  margin-bottom: 0px !important;
}
.markdown-body hr {
  margin: 0px;
}
.markdown-body li {
  list-style: inherit !important;
  margin-bottom: -10px;
  margin-top: -10px;
}
.markdown-body ol {
  margin-bottom: -10px;
  margin-top: -10px;
}
.markdown-body ul {
  margin-bottom: -10px;
  margin-top: -10px;
}
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-bottom: 0;
  margin-top: 0px;
}
pre {
  margin: 10px 0 !important;
  padding: 10px !important;
  opacity: 1 !important;
  border: var(--markdown-pre-border) !important;
  background-color: var(--markdown-pre-background-color) !important;
  border-radius: 15px !important;
}

.line-num-box {
  display: inline-block !important;
  color: var(--markdown-line-num-box-color) !important;
  border-right: var(--markdown-line-num-box-border-right) !important;
  line-height: 20px !important;
  font-size: 16px !important;
  text-align: right !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.code-box {
  display: inline-block !important;
  vertical-align: top !important;
  width: calc(100% - 50px) !important;
  border-left-style: none !important;
}

code {
  line-height: 20px !important;
  font-size: 16px !important;
  vertical-align: top;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 10px !important;
  background-color: #000;
}

code::-webkit-scrollbar {
  height: 10px !important;
  border-radius: 5px !important;
  background-color: var(
    --markdown-code-webkit-scrollbar-background-color
  ) !important;
}

code::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px !important;
  background-color: var(
    --markdown-code-webkit-scrollbar-thumb-background-color
  ) !important;
}

code::-webkit-scrollbar-button {
  /*滚动条的轨道的两端按钮，允许通过点击微调小方块的位置*/
  border-radius: 5px !important;
  background-color: var(
    --markdown-code-webkit-scrollbar-button-background-color
  ) !important;
}

.mac-icon {
  height: 30px !important;
  margin-bottom: 5px !important;
}

.mac-icon > span {
  display: inline-block !important;
  letter-spacing: 5px !important;
  word-spacing: 5px !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 8px !important;
}

.mac-icon-lang {
  width: 50px !important;
  padding-left: 10px !important;
  font-size: 15px !important;
  vertical-align: top !important;
}

.copy-button {
  padding: 2px 8px !important;
  color: var(--markdown-copy-button-color) !important;
  background-color: var(--markdown-copy-button-background-color) !important;
  margin-bottom: 5px !important;
  margin-right: 5px !important;
  margin-top: 5px !important;
  border-radius: 5px !important;
  outline: none !important;
  border: none !important;
}

.copy-button {
  float: right !important;
}

.copy-button:hover {
  cursor: pointer !important;
  background-color: var(--markdown-copy-button-hover-background) !important;
}

.markdown-body .lang- {
  display: block !important;
  overflow: auto !important;
  font-size: 15px !important;
  background: var(--markdown-lang-background) !important;
  color: var(--markdown-lang-color) !important;
}
.markdown-body pre {
  background: #000 !important;
}
.markdown-body pre .code-box {
  padding-left: 10px !important;
}

.markdown-body pre code.lang-properties {
  color: white !important;
}
.markdown-body pre code {
  color: white !important;
}

.markdown-body .hljs {
  display: block !important;
  overflow: auto !important;
  font-size: 15px !important;
  background: var(--markdown-pre-background-color) !important;
  color: var(--markdown-pre-font-color) !important;
}

.hljs,
.hljs-tag,
.hljs-subst {
  color: #f8f8f2 !important;
}

.hljs-strong,
.hljs-emphasis {
  color: #a8a8a2 !important;
}

.hljs-bullet,
.hljs-quote,
.hljs-number,
.hljs-regexp,
.hljs-literal,
.hljs-link {
  color: #ae81ff !important;
}

.hljs-code,
.hljs-title,
.hljs-section,
.hljs-selector-class {
  color: #a6e22e !important;
}

.hljs-strong {
  font-weight: bold !important;
}

.hljs-emphasis {
  font-style: italic !important;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-name,
.hljs-attr {
  color: #f92672 !important;
}

.hljs-symbol,
.hljs-attribute {
  color: #66d9ef !important;
}

.hljs-params,
.hljs-class .hljs-title {
  color: #f8f8f2 !important;
}

.hljs-string,
.hljs-type,
.hljs-built_in,
.hljs-builtin-name,
.hljs-selector-id,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-addition,
.hljs-variable,
.hljs-template-variable {
  color: #e6db74 !important;
}

.hljs-comment,
.hljs-deletion,
.hljs-meta {
  color: #75715e !important;
}
