<template>
    <div class="facebook">
        facebook
        <div v-for="(val, index) in dataList" :key="index" class="newContent">
        </div>
    </div>
</template>
<script>
import { mapState } from "vuex";
export default {
    name: 'facebook',
    data() {
        return {
        };
    },
    computed: {
        ...mapState({
            dataList: (state) => state.collect.dataList,
            nowCollect: (state) => state.collect.nowCollect,
        }),
    },
    mounted() {
        this.$store.commit("collect/clearDataList")
        setTimeout(() => {
            if (this.nowCollect) {
                this.$store.commit("collect/getOpinionCollect");
            }
        }, 500);
    },
    methods: {
    },

};
</script>

<style scoped lang="scss">
.facebook {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(3, 32%);
    grid-auto-rows: 140px;
    gap: 10px;
    padding-bottom: 15px;
}

.newContent {
    padding: 3px 10px 5px 5px;
    border: 1px solid #ccc;
}
</style>