export default {
  namespaced: true,
  state: {
    dataRangeGetter: null,
    dataRangeDetail: {},
    dataRangeQueryIndexMaxList: [],
    dataRangeQueryIndexMaxCount: 0,

    searchedTableTotal: 0,
    searchedDataTotal: 0,
    searchedDataTotalSize: 0,
    size: 20,
    pauseFlag: false,
    chartOn: false,
    searchedPathArr: [],
    equipmentOptions: [],
  },
  mutations: {
    setEquipmentList(state, res) {
      state.equipmentOptions =
        res[
          "/etc/web/instant_msg_analysis_platform/equipment_list"
        ]?.equipment_list;
    },
    setMaxInit(state) {
      state.dataRangeQueryIndexMaxList = [];
      state.dataRangeQueryIndexMaxCount = 0;
    },
    setSearchedPathArr(state, v) {
      state.searchedPathArr.push(v);
    },
    setChartOn(state, v) {
      state.chartOn = v;
    },
    setDataRangeGetter(state, dataRangeList) {
      console.log("setDataRangeGetter:", dataRangeList);
      function* getter(dataRangeList) {
        if (dataRangeList == null || dataRangeList.length <= 0) {
          return;
        }
        for (let index = 0; index < dataRangeList.length; index++) {
          let dataRangeOne = dataRangeList[index];
          let from = 0;
          while (true) {
            console.log("dataRange", dataRangeOne);
            
            if (!dataRangeOne.data_range_type) {
              console.log("ssssssssssssssss");
              let prefix =
                window.main.$store.state.telegramSearch.telegramSearchList
                  .activeNamePrefix +
                "_" +
                dataRangeOne._source.type +
                "_" +
                window.main.$md5(dataRangeOne._source.group_id) +
                "_*";
              state.dataRangeQueryIndexMaxList.push(prefix);
              if (
                state.dataRangeQueryIndexMaxList.length < state.size &&
                state.dataRangeQueryIndexMaxCount < 300 * 1024 * 1024
              ) {
                if (index >= dataRangeList.length - 1) {
                  let tmpDataRangeDetail = {};
                  // 如果没有获取到有效的index表，不要继续查询表里的数据了
                  if (state.dataRangeQueryIndexMaxList.length <= 0) {
                    break;
                  }
                  tmpDataRangeDetail.data_range_index_name =
                    state.dataRangeQueryIndexMaxList.join(",");
                  window.main.$store.commit(
                    "telegramSearch/telegramSearchList/setDataListGetter",
                    tmpDataRangeDetail
                  );
                  yield { res: dataRangeOne };
                  state.dataRangeQueryIndexMaxList = [];
                  state.dataRangeQueryIndexMaxCount = 0;
                  break;
                } else {
                  break;
                }
              } else {
                let tmpDataRangeDetail = {};
                tmpDataRangeDetail.data_range_index_name =
                  state.dataRangeQueryIndexMaxList.join(",");
                window.main.$store.commit(
                  "telegramSearch/telegramSearchList/setDataListGetter",
                  tmpDataRangeDetail
                );
                yield { res: dataRangeOne };
                state.dataRangeQueryIndexMaxList = [];
                break;
              }
            }
            let dataRangeDetail;
            let add_es_query_conditions = {
              bool: {
                should: [],
              },
            };
            state.equipmentOptions.forEach((item) => {
              add_es_query_conditions.bool.should.push({
                term: { type: item.value },
              });
            });
            dataRangeDetail = {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                from: from,
                size: state.size,
              },
              control: {
                query_type:
                  window.main.$store.state.telegramSearch.telegramSearchList
                    .use_public,
                condition: {
                  query_mode: "match",
                },
                add_es_query_conditions: add_es_query_conditions,
                sort: [
                  {
                    "@timestamp": {
                      order: "desc",
                    },
                  },
                ],
              },
              msg: {
                data_range_index_name: dataRangeOne.data_range_index_name,
              },
            };
            window.main.$store.commit(
              "telegramSearch/telegramSearchList/clearLoadingBun"
            );
            window.main.$store.commit(
              "telegramSearch/telegramSearchList/setLoadingLayer"
            );
            let nextValue = yield window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [dataRangeDetail],
              "telegramSearch/telegramSearchDataRange/setDataRangeTree"
            );
            if (nextValue == "nextDataRangePage") {
              from = from + state.size;
              continue;
            }
            yield* getter(nextValue);
            if (nextValue == null || nextValue.length < state.size) {
              from = 0;
              break;
            } else {
              from = from + state.size;
            }
          }
        }
        if (dataRangeList.length < state.size) {
          // 取消遮罩层
          window.main.$store.commit(
            "telegramSearch/telegramSearchList/clearLoadingLayerForceSet"
          );
        }
        return;
      }
      // 如果没有被置空，忽略异常出现的查询操作。
      if (state.dataRangeGetter != null) {
        return;
      }
      state.dataRangeGetter = getter(dataRangeList);
      let nextRes = state.dataRangeGetter.next(dataRangeList);
      if (nextRes.value === undefined && nextRes.done) {
        window.main.$store.commit(
          "telegramSearch/telegramSearchList/clearLoadingLayer"
        );
      }
    },
    detailLockfn(state, v) {
      state.detailLock = v;
    },
    clearDataRangeTree(state) {
      state.searchedTableTotal = 0;
      state.searchedDataTotal = 0;
      state.dataRangeQueryIndexMaxCount = 0;
      state.searchedDataTotalSize = 0;
      state.from = 0;
      state.dataRangeGetter = null;
    },
    async setDataRangeTree(state, dataRangeList) {
      console.log("setDataRangeTree:", dataRangeList);
      // if (
      //   window.main.$store.state.telegramSearch.telegramSearchDataRange
      //     .dataRangeGetter
      // ) {
      //   window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.return();
      //   window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter =
      //     null;
      // }
      // if (window.main.$store.state.telegramSearch.telegramSearchList.dataListGetter) {
      //   window.main.$store.state.telegramSearch.telegramSearchList.dataListGetter.return();
      // }
      // window.main.$store.commit(
      //   "telegramSearch/telegramSearchList/clearLoadingLayer"
      // );
      // // 查询结束了强行关闭遮罩层
      // window.main.$store.commit(
      //   "telegramSearch/telegramSearchList/clearLoadingLayerForceSet"
      // );
      if (dataRangeList == null || dataRangeList?.hits?.hits?.length == 0) {
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.next([]);
      } else {
        let list = dataRangeList.hits.hits;
        let listTrue = {};
        list?.forEach((item) => {
          state.searchedTableTotal += 1; //一次搜索一张表
          if (listTrue[item._source.type]) {
            listTrue[item._source.type].push("(" + window.main.$md5(item._source.group_id) + ")");
          } else {
            listTrue[item._source.type] = ["(" + window.main.$md5(item._source.group_id) + ")"];
          }
        });
        for (const key in listTrue) {
          let md5Str = listTrue[key].join("|");
          window.main.$main_socket.sendData(
            "Api.Search.DataRange.ListTrue",
            [
              {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: 0,
                  size: 20,
                },
                control: {
                  query_type: 'public',
                  prefix_filter: `^${window.main.$store.state.telegramSearch.telegramSearchList.activeNamePrefix}_${key}_${md5Str}_.?.?.?_`,
                },
                msg: {
                  data_range_father_path:
                    "/group_data/" +
                    key +
                    "/" +
                    window.main.$store.state.telegramSearch.telegramSearchList
                      .activeNamePrefix,
                  data_range_index_prefix:
                    window.main.$store.state.telegramSearch.telegramSearchList
                      .activeNamePrefix +
                    "_" +
                    key,
                },
              },
            ],
            (res) => {
              res?.forEach((item) => {
                state.searchedDataTotal += Number(item.total)
                  ? Number(item.total)
                  : 0;
                state.searchedDataTotalSize += Number(item.total_size)
                  ? Number(item.total_size)
                  : 0;
                state.dataRangeQueryIndexMaxList.push(
                  item.data_range_index_name
                );
                state.dataRangeQueryIndexMaxCount += Number(item.total_size)
                  ? Number(item.total_size)
                  : 0;
                state.dataRangeDetail.data_range_name = item.data_range_name;
              });
            }
          );
        }
        let tmpObj =
          window.main.$store.state.telegramSearch.telegramSearchDataRange.dataRangeGetter.next(list);
      }
    },
  },
};
