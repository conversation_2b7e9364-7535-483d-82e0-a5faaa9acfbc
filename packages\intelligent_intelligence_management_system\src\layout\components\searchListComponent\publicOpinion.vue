<template>
  <div
    style="
      width: 100%;
      height: 78vh;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;
      position: relative;
    "
    v-loading="mainLoading"
    element-loading-text="加载中..."
    element-loading-background="rgba(0,0,0,0.7)"
  >
    <!-- <div v-show="exportResults"> -->
    <div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 45%;
        "
      >
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          ><span v-if="!checkAll">全选</span
          ><span v-if="checkAll">取消全选</span></el-checkbox
        >
        <div v-if="checkedCities.length > 0">
          <el-button
            type="success"
            size="mini"
            @click="exportList"
            v-show="Conditions"
            >导出</el-button
          >
          <el-button size="mini" @click="toCollect">收藏</el-button>
          <!-- <el-button type="info" size="mini" @click="cancelExport">取消</el-button> -->
        </div>
      </div>
    </div>
    <div style="display: flex; height: 100%">
      <div class="newsList" :class="{ longList: exportResults }">
        <div
          style="width: 46%; height: 100%"
          v-loading="timeLoading"
          element-loading-text="加载中..."
          element-loading-background="rgba(0,0,0,0.7)"
        >
          <el-checkbox-group
            v-model="checkedCities"
            @change="handleCheckedCitiesChange"
          >
            <div v-for="(val, index) in dataList" :key="val._id + val._index">
              <div class="newContent">
                <div class="newsListTitle">
                  <el-checkbox
                    :label="val"
                    :key="index"
                    style="display: flex; align-items: end; margin-bottom: 5px"
                    >{{ " " }}
                  </el-checkbox>
                  <div
                    v-if="
                      val.highlight &&
                      val.highlight.title &&
                      val.highlight.title.length
                    "
                    class="content_title pointerLine"
                    :class="readClass(index)"
                    @click="NewsDisplay(val, index)"
                    v-html="val.highlight.title[0]"
                  ></div>
                  <div
                    v-else
                    class="content_title pointerLine"
                    :class="readClass(index)"
                    @click="NewsDisplay(val, index)"
                  >
                    {{
                      val._source.title && val._source.title.trim()
                        ? val._source.title
                        : "无标题"
                    }}
                  </div>
                </div>

                <!-- <div v-if="val.highlight && val.highlight.title && val.highlight.title.length && !exportResults"
                  class="content_title pointerLine" :class="readClass(index)" @click="NewsDisplay(val, index)"
                  v-html="val.highlight.title[0]">
                </div>
                <div v-else-if="!exportResults" class="content_title pointerLine" :class="readClass(index)"
                  @click="NewsDisplay(val, index)">{{
                    val._source.title && val._source.title.trim() ?
                      val._source.title : "无标题" }}
                </div> -->
                <div class="content_body">
                  <el-image
                    style="margin-right: 10px"
                    v-if="val._source.content_img"
                    :src="
                      'filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                      val._source.content_img[0].sha512_hash
                    "
                    :preview-src-list="previewList"
                    @click="handlePreview(val._source.content_img)"
                  />
                  <div
                    class="content_details"
                    v-if="val.highlight && val.highlight.content_article"
                  >
                    <span
                      @click="NewsDisplay(val, index)"
                      style="
                        color: #9a95a3;
                        padding-right: 10px;
                        cursor: pointer;
                      "
                      >{{
                        $tools.timestampToTime(val._source.timestamp * 1000)
                      }}</span
                    >
                    <span
                      v-for="(item, index) in val.highlight.content_article"
                      :key="index"
                      v-html="item"
                    ></span>
                  </div>
                  <div class="content_details" v-else>
                    <span
                      @click="NewsDisplay(val, index)"
                      style="
                        color: #9a95a3;
                        padding-right: 10px;
                        cursor: pointer;
                      "
                      >{{
                        $tools.timestampToTime(val._source.timestamp * 1000)
                      }}</span
                    >
                    <span
                      v-for="(item, index) in val._source.content_article"
                      :key="index"
                      v-html="item"
                    ></span>
                  </div>
                </div>
                <div class="content_link">
                  <div>
                    <span style="padding-right: 15px" v-if="val._source.type"
                      >来自：<span
                        class="pointerLine"
                        @click="aggsListTypeChange(val._source.type, true)"
                        >{{ val._source.type }}</span
                      > </span
                    ><el-tooltip
                      class="item"
                      effect="dark"
                      :content="val._source.author_id"
                      placement="right"
                    >
                      <span v-if="val._source.author_id"
                        >作者：<span
                          class="pointerLine"
                          @click="
                            aggsListAuthorChange(val._source.author_id, true)
                          "
                          >{{
                            $tools.longText(val._source.author_id, 20)
                          }}</span
                        >
                      </span></el-tooltip
                    >
                  </div>

                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="decodeURIComponent(val._source.url)"
                    placement="right"
                  >
                    <a
                      :href="val._source.url"
                      target="_blank"
                      style="text-decoration: underline"
                      >原文链接
                    </a>
                  </el-tooltip>
                  <!-- <span style="float: right; cursor: pointer" v-if="
                    val['_source'].tags &&
                    val['_source'].tags.indexOf('collect') != -1
                  ">已收藏</span> -->
                </div>
              </div>
            </div>
          </el-checkbox-group>
          <div
            class="block"
            v-show="(dataList.length || showPage) && !timeLoading"
            style="margin-bottom: 5px"
          >
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :key="pageKey"
              :page-size="20"
              layout="prev, pager, next, jumper"
              :total="$store.state.newsSearchList.total"
            >
            </el-pagination>
          </div>
          <div
            v-if="!dataList.length && isSearch && !timeLoading"
            style="padding: 20px"
          >
            暂无符合条件的新闻
          </div>
        </div>
      </div>
    </div>

    <div class="searchRight">
      <div
        class="aggsSearch"
        style="
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        "
        v-show="(dataList.length || isSearch) && agList.type"
      >
        <!-- <div v-show="Conditions && Conditions.queryString">
          <el-button  size="mini" type="primary" style="margin:0 10px 10px"
            @click="exportResults = true">导出新闻</el-button>
        </div> -->
        <div
          class="searchSecondary shadow"
          v-if="isSearch && agList.type"
          style="overflow-y: auto"
        >
          <el-form
            ref="form"
            label-width="50px"
            @submit.native.prevent
            style="
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              height: 100%;
            "
          >
            <el-form-item label="媒体">
              <el-checkbox-group
                v-model="aggsListType.type"
                @change="aggsListTypeChange"
                style="line-height: 25px; margin-bottom: 10px"
              >
                <el-checkbox
                  v-for="(type, index) in agList.type.buckets"
                  :label="type.key"
                  :key="index"
                  >{{ type.key }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="时间">
              <el-checkbox-group
                v-model="aggsListType.time"
                @change="aggsListTimeChange"
                style="line-height: 25px; margin-bottom: 10px"
              >
                <el-checkbox
                  v-for="(timestamp, index) in agList.timestamp.buckets"
                  :label="timestamp.from"
                  :key="index"
                  >{{
                    $tools.timestampToTime(timestamp.from, "MM-DD")
                  }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="作者">
              <el-checkbox-group
                v-model="aggsListType.author"
                @change="aggsListAuthorChange"
                style="line-height: 25px; margin-bottom: 10px"
              >
                <el-checkbox
                  v-for="(author, index) in agList.author_id.buckets"
                  :label="author.key"
                  :key="index"
                  >{{ $tools.longText(author.key, 20) }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
        <div v-show="isSearch && agList.type" class="timeVisualDrawing shadow">
          <div id="timeVisualDrawing"></div>
        </div>
        <div v-show="isSearch && agList.type" class="typeVisualDrawing shadow">
          <div id="typeVisualDrawing"></div>
        </div>
        <div
          v-show="isSearch && agList.type"
          class="autherVisualDrawing shadow"
        >
          <div id="autherVisualDrawing"></div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="collectDialog"
      :close-on-click-modal="false"
      title="选取目录"
      top="10px"
      width="40%"
      append-to-body
    >
      <div style="width: 95%">
        <collectTree
          :listType="'username'"
          :tableName="'favorites_data'"
          :getCollect="getCollect"
        ></collectTree>
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fileDirIdDialog = false">确 定</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { mapState } from "vuex";
import { Document, Packer, Paragraph, TextRun } from "docx";
import { saveAs } from "file-saver";
import { collectTree } from "@/layout/components";
export default {
  name: "newsSearchList",
  data() {
    return {
      collectNum: 0,
      collectDialog: false,
      exportResults: false, //是否可以导出(有搜索条件时可以)
      isIndeterminate: false,
      checkAll: false,
      checkedCities: [],
      showPage: false, //是否展示分页,
      pageKey: 0, //缓存浏览页码
      Conditions: null,
      previewList: [], //图片预览列表
      aggsListType: {
        type: [],
        time: [],
        author: [],
      },
      ppList: [
        { type: "match_phrase", text: "精确匹配" },
        { type: "match", text: "分词匹配" },
        { type: "wildcard", text: "分词模糊匹配" },
        { type: "expression", text: "表达式匹配" },
        { type: "regexp", text: "正则匹配" },
      ],
      rangeList: [
        { type: "content_article", text: "内容查询" },
        { type: "title", text: "标题查询" },
        { type: "type", text: "媒体查询" },
        { type: "author_id", text: "作者查询" },
      ],
    };
  },
  components: {
    collectTree,
  },
  computed: {
    ...mapState({
      queryString: (state) =>
        state.search.conditions.conditionsData.queryString,
      queryMode: (state) => state.search.conditions.conditionsData.queryMode,
      ppqueryMode: (state) =>
        state.search.conditions.conditionsData.ppqueryMode,
      timeRange: (state) => state.search.conditions.conditionsData.timeRange,
      customTime: (state) => state.search.conditions.conditionsData.customTime,
      mainLoading: (state) => state.newsSearchList.mainLoading,
      timeLoading: (state) => state.newsSearchList.timeLoading,
      dataList: (state) => state.newsSearchList.dataList,
      isSearch: (state) => state.newsSearchList.isSearch,
      readList: (state) => state.newsSearchList.readList,
      agList: (state) => state.newsSearchList.agList,
      aggsList: (state) => state.newsSearchList.aggsList,
      chartUpdaters: (state) => state.newsSearchList.chartUpdaters,

      tabsActiveName: (state) => state.search.searchList.tabsActiveName,
    }),
    currentPage: {
      get() {
        return this.$store.state.newsSearchList.currentPage;
      },
      set(val) {
        this.$store.commit("newsSearchList/setcurrentPage", val);
      },
    },
    hasAggs: {
      get() {
        return this.$store.state.newsSearchList.hasAggs;
      },
      set(val) {
        this.$store.commit("newsSearchList/setHasAggs", val);
      },
    },
  },
  watch: {
    aggsList: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          Object.entries(this.chartUpdaters).forEach(([key, drawMethod]) => {
            if (newVal[key]) {
              setTimeout(() => {
                this[drawMethod](newVal[key]);
              }, 1000);
            }
          });
        });
        // if (newVal.timestamp) {
        //   this.$nextTick(() => {
        //     this.drawTimeChart(newVal.timestamp);
        //   });
        // }
        // if (newVal.type) {
        //   this.$nextTick(() => {
        //     this.drawTypeChart(newVal.type);
        //   });
        // }
        // if (newVal.author_id) {
        //   this.$nextTick(() => {
        //     this.drawAuthChart(newVal.author_id);
        //   });
        // }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //使用当前时间戳与随机数结合当作预警词的唯一id
    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = new Array(
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n"
      );
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },
    getCollect(data) {
      this.$confirm("确定选择此目录收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log("收藏成功", this.checkedCities);

          this.checkedCities.forEach((element) => {
            let prefix =
              1e13 -
              Math.round(new Date().getTime() / 1000) +
              data.id +
              this.reduceNumber();
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefixTable.AddData",
              [
                {
                  msg: {
                    type: "username",
                    authority:
                      window.main.$store.state.userInfo.userinfo.authority,
                    username:
                      window.main.$store.state.userInfo.userinfo.username,
                    table: "favorites_data",
                    prefix,
                    relation: data.id + ";" + this.tabsActiveName,
                    data: {
                      data: {
                        file_data: element,
                      },
                    },
                  },
                },
              ],
              (res) => {
                console.log("收藏", res);
                if (res.status === "ok") {
                  this.collectNum++;
                  if (this.collectNum === this.checkedCities.length) {
                    this.$message.success("收藏成功!");
                    this.collectNum = 0;
                    this.checkAll = false;
                    this.checkedCities = [];
                    this.collectDialog = false;
                  }
                }
              }
            );
          });
        })
        .catch((err) => {
          console.log("收藏", err);
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
      // this.$confirm("确定选择此目录收藏?", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   })
      //     .then(() => {
      //       connsole.log("收藏成功",data)
      //     })
      //     .catch(() => {
      //       this.$message({
      //         type: "info",
      //         message: "已取消选择",
      //       });
      //     });
    },
    toCollect() {
      if (this.checkedCities.length) {
        this.collectDialog = true;
      } else {
        this.$message({
          type: "info",
          message: "请选择收藏文章",
        });
      }
    },
    //设置预览图片列表
    handlePreview(imgList) {
      this.previewList = imgList.map(
        (item) =>
          window.location.href.split("#")[0] +
          "filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/" +
          item.sha512_hash
      );
    },
    NewsDisplay(val, index) {
      console.log("val", val);

      const routeData = this.$router.resolve({
        name: "NewsDisplay",
        query: {
          id: val._id,
          index: val._index,
        },
      });
      window.open(routeData.href, "_blank");
      this.$store.commit("newsSearchList/addRead", index); //点击添加已读
    },
    getEsJson() {
      console.log("getEsJson", this.queryMode);
      return this.$tools.generateESQuery(
        this.queryString,
        this.queryMode == "content_article"
          ? ["content_article", "title"]
          : [this.queryMode],
        this.ppqueryMode
      );
    },
    gofn(searchJson, agType, once) {
      console.log("this.queryString", searchJson, this.queryString);
      this.$store.commit("newsSearchList/setqueryString", this.queryString);
      this.$store.commit("newsSearchList/settimeRange", this.timeRange);
      this.cacheConditions();
      this.$store.commit("newsSearchList/setGetRead", true); //需要获取已读列表
      this.$store.commit("newsSearchList/setMainLoading", true); //需要大遮罩加载
      setTimeout(() => {
        this.$store.commit("newsSearchList/setMainLoading", false); //关闭大遮罩加载
      }, 10000);
      if (agType) {
        //如果重新搜索的话，清除之前的查询条件
        this.$store.commit("newsSearchList/setagType");
        this.aggsListType = {
          type: [],
          time: [],
          author: [],
        };
        this.aggsFilter = {};
      }
      console.log("this.queryString610", this.queryString);
      if (this.queryString != "" || this.hasAggs) {
        console.log("if");
        if (window.main.$route.name !== "personDetails" && window.main.$route.name !== "oriDetails") {
          console.log("searchJson:", searchJson);
          if (searchJson) {
            this.$store.commit(
              "newsSearchList/setAddEsQueryConditions",
              searchJson
            );
          }
        } else {
          console.log("search:", this.queryString)
          let search = {
            bool: {
              should: [
                {
                  match: {
                    "content": this.queryString,
                  },
                },
                {
                  match: {
                    "title": this.queryString,
                  },
                }
              ],
            },
          };
          this.$store.commit(
              "newsSearchList/setAddEsQueryConditions",
              search
            );
        }
      } else {
        console.log("else");
        let searchJson = {
          bool: {
            must: [
              {
                simple_query_string: {
                  query: this.queryString,
                  fields: [this.queryMode],
                  default_operator: "and",
                },
              },
            ],
          },
        };
        this.$store.commit(
          "newsSearchList/setAddEsQueryConditions",
          searchJson
        );
      }

      // this.$store.commit("newsSearchList/setqueryMode", this.queryMode);
      this.$store.commit("newsSearchList/clearDataRangeTree"); //清除之前的查询数据范围
      this.$store.commit("newsSearchList/clearSearchList", once); //清除之前的查询结果数据
      window.main.$store.commit("newsSearchList/setDataRangeGetter", [
        { data_range_path: "/public_sentiment", data_range_type: true },
      ]);
      window.main.$message.success("正在搜索请稍等");
    },
    // 绘制时间折线图
    drawTimeChart(timeArr) {
      let dataX = [];
      let dataY = [];
      timeArr.buckets.forEach((item) => {
        dataX.push(this.$tools.timestampToTime(item.from, "MM-DD"));
        dataY.push({
          value: item.doc_count,
          item: item,
        });
      });
      const timeChart = echarts.init(
        document.getElementById("timeVisualDrawing")
      );
      // window.addEventListener("resize", () => {
      //   timeChart.resize();
      // });
      timeChart.setOption({
        grid: {
          left: "10%",
          top: "30%",
          right: "5%",
          bottom: "15%",
        },
        title: {
          show: true,
          text: "新闻数量变化",
        },

        xAxis: {
          type: "category",
          data: dataX,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "文章数量",
            data: dataY,
            type: "line",
            label: {
              show: true,
              color: "#000",
              fontStyle: "bold",
              fontSize: 14,
            },
            itemStyle: {
              normal: {
                color: "#5087ff", //改变折线点的颜色
                lineStyle: {
                  color: "#5087ff", //改变折线颜色
                },
              },
            },
          },
        ],
      });
    },
    // 绘制类型统计图
    drawTypeChart(typeArr) {
      let dataX = [];
      let dataY = [];
      typeArr.buckets.forEach((item) => {
        dataX.push(item.key);
        dataY.push(item.doc_count);
      });
      const typeChart = echarts.init(
        document.getElementById("typeVisualDrawing")
      );
      // window.addEventListener("resize", () => {
      //   typeChart.resize();
      // });
      typeChart.setOption({
        grid: {
          left: "10%",
          top: "30%",
          right: "5%",
          bottom: "15%",
        },
        title: {
          show: true,
          text: "媒体文章统计",
        },
        xAxis: {
          type: "category",
          data: dataX,
          axisLabel: {
            interval: 0,
            margin: 8,
            color: "#000",
            formatter: function (params) {
              return window.main.$tools.longText(params, 12);
            },
          },
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: dataY,
            type: "bar",
            barMaxWidth: "50",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              color: "#000",
              fontStyle: "bold",
              fontSize: 14,
              position: "top",
            },
            itemStyle: {
              normal: {
                color: "#5087ff",
              },
            },
          },
        ],
      });
    },
    // 绘制作者统计图
    drawAuthChart(typeArr) {
      let dataX = [];
      let dataY = [];
      typeArr.buckets.forEach((item) => {
        dataX.push(item.key);
        dataY.push(item.doc_count);
      });
      const authChart = echarts.init(
        document.getElementById("autherVisualDrawing")
      );
      authChart.setOption({
        grid: {
          left: "10%",
          top: "30%",
          right: "5%",
          bottom: "20%",
        },
        title: {
          show: true,
          text: "媒体作者文章量统计",
        },
        xAxis: {
          type: "category",
          data: dataX,
          axisLabel: {
            interval: 0,
            margin: 8,
            color: "#000",
            formatter: function (params) {
              return window.main.$tools.longText(params, 9);
            },
          },
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: dataY,
            type: "bar",
            barMaxWidth: "50",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              color: "#000",
              fontStyle: "bold",
              fontSize: 14,
              position: "top",
            },
            itemStyle: {
              normal: {
                color: "#5087ff",
              },
            },
          },
        ],
      });
    },
    readClass(index) {
      return this.readList[index] ? "read" : "";
    },
    // 全导出新闻全选
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.dataList : [];
      this.isIndeterminate = false;
    },
    exportList() {
      if (this.checkedCities.length) {
        let html = "";
        let Conditions = this.Conditions;
        html +=
          "头检索条件<br>    [查询内容]" +
          Conditions.queryString +
          "<br>    [时间范围]" +
          (Conditions.timeRange == "自定义时间"
            ? window.main.$tools.timestampToTime(
                Conditions.customTime[0],
                "YYYY-MM-DD"
              ) +
              "至" +
              window.main.$tools.timestampToTime(
                Conditions.customTime[1],
                "YYYY-MM-DD"
              )
            : Conditions.timeRange) +
          "<br>    [查询模式]" +
          Conditions.matchPhraseText +
          "<br>    [匹配模式]" +
          Conditions.ppqueryMode +
          "<br><br>";
        this.checkedCities.forEach((e, index_) => {
          html +=
            "头" +
            window.main.$tools.changeNumToHan(index_ + 1) +
            "." +
            (e._source.title && e._source.title.trim()
              ? e._source.title.trim()
              : "无标题") +
            "<br>";

          if (e._source) {
            //原文导出
            /* if (e.highlight && e.highlight.content_article.length) {
              html += "    ";
              e.highlight.content_article.forEach((element) => {
                html += element.replace(/<[^>]+>/g, "").trim();
              });
              html += "<br>";
            } else if (e._source.content_article) {
              html += e._source.content_article + "<br>";
            } */
            html +=
              "    [来源]" +
              e._source.type +
              "<br>" +
              "    [时间]" +
              this.$tools.timestampToTime(e._source.timestamp * 1000) +
              "<br>" +
              "    [原文链接]" +
              decodeURIComponent(e._source.url) +
              "<br>";

            if (e._source.nlp_parse_flag && e._source.sentence_list) {
              let sentence_list = e._source.sentence_list;
              sentence_list.forEach((element, index) => {
                html += "    " + (index + 1) + "." + element + "<br>";
              });
            }
          }
          html += " <br>";
        });
        this.html = html;
        this.docx();
      } else {
        this.$message.error("请选择导出文章");
      }
    },
    docx(title) {
      //生成段落 根据options进行配置
      const generateParagraph = (options) => {
        let {
          text = "",
          size = 24,
          margin = {
            left: 30,
            right: 30,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              font: {
                name: "宋体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      const generateParagraphHead = (options) => {
        let {
          text = "",
          bold = true,
          size = 26,
          margin = {
            left: 50,
            right: 50,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              bold,
              font: {
                name: "黑体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      let test = this.html.replace(/<[^>]*>/g, "<br>").split("<br>");
      let paragraphList = test.map((e) => {
        if (e.slice(0, 1) == "头") {
          let opt = {
            text: e.slice(1),
          };

          return generateParagraphHead(opt);
        } else {
          let opt = {
            text: e,
          };
          return generateParagraph(opt);
        }
      });
      //按照数据填充生成文档 内容放置于sections
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: paragraphList,
          },
        ],
      });

      //保存导出为word
      Packer.toBlob(doc).then((blob) => {
        saveAs(
          blob,
          title
            ? title
            : new Date().toLocaleDateString("zh-CN") + "舆情简报.docx"
        );
      });
      this.checkAll = false;
    },
    //取消导出
    cancelExport() {
      this.exportResults = false;
      this.checkAll = false;
      this.checkedCities = [];
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.dataList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.dataList.length;
    },
    //分页页码改变
    handleCurrentChange(e) {
      this.$store.commit("newsSearchList/setGetRead", true); //需要获取已读列表
      this.$store.commit("newsSearchList/setTimeLoading", true);
      setTimeout(() => {
        this.$store.commit("newsSearchList/setTimeLoading", false); //关闭检索列表遮罩加载
      }, 10000);
      this.showPage = true;
      this.currentPage = e;
      this.key = e;
      this.cacheConditions();
      // 翻页时候判断是否有aggs过滤条件
      if (Object.values(this.aggsFilter).some((el) => el.length > 0)) {
        this.hasAggs = true;
      } else {
        this.hasAggs = false;
      }
      this.$store.commit("newsSearchList/clearDateList");
      this.loadMoreEs();
    },
    // 翻页
    async loadMoreEs() {
      let nextRes =
        window.main.$store.state.newsSearchList.dataListGetter.next();
      if (nextRes.value === undefined && nextRes.done) {
        //   window.main.$store.commit("search/searchList/clearLoadingLayer");
      }
    },
    cacheConditions() {
      this.Conditions = {
        queryString: this.queryString,
        matchPhraseText:
          this.rangeList.filter((item) => item.type === this.queryMode)[0]
            ?.text || "",
        ppqueryMode:
          this.ppList.filter((item) => item.type === this.ppqueryMode)[0]
            ?.text || "",
        timeRange: this.timeRange,
        customTime: this.customTime,
      };
      this.checkedCities = [];
      this.isIndeterminate = false;
      this.checkAll = false;
      this.exportResults = false;
    },
    //aggs过滤媒体
    aggsListTypeChange(val, listFind) {
      let should = [];
      if (listFind) {
        if (!this.agList.type.buckets.some((ele) => ele.key === val)) {
          this.agList.type.buckets.push({ key: val });
        }

        if (!this.aggsListType.type.some((ele) => ele === val)) {
          this.aggsListType.type.push(val);
          val = this.aggsListType.type;
        } else {
          this.$message({
            type: "warning",
            message: "已添加此检索过滤条件",
          });

          return;
        }
      }
      if (val.length) {
        this.hasAggs = true;
      }
      this.aggsFilter["type"] = val;

      val.forEach((e) => {
        should.push({ term: { type: e } });
      });

      if (this.aggsFilter.hasOwnProperty("author")) {
        this.hasAggs = true;

        this.aggsFilter.author.forEach((e) => {
          should.push({ term: { author_id: e } });
        });
      }
      if (this.aggsFilter.hasOwnProperty("time")) {
        this.hasAggs = true;

        this.aggsFilter.time.forEach((e) => {
          should.push({
            range: {
              timestamp: {
                from: e,
                to: e + 86400,
              },
            },
          });
        });
      }

      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      should.push(tmpCondition);
      console.log("should,", should);
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: should,
        },
      });
      this.gofn(false);
    },
    //aggs过滤时间
    aggsListTimeChange(val) {
      if (val.length) {
        this.hasAggs = true;
      }
      this.aggsFilter["time"] = val;
      let should = [];
      val.forEach((e) => {
        should.push({
          range: {
            timestamp: {
              from: e,
              to: e + 86400,
            },
          },
        });
      });

      /* if (this.queryString != "") {
        must.push(this.getEsJson());
      } */
      if (this.aggsFilter.hasOwnProperty("author")) {
        this.hasAggs = true;

        this.aggsFilter.author.forEach((e) => {
          should.push({ term: { author_id: e } });
        });
      }
      if (this.aggsFilter.hasOwnProperty("type")) {
        this.hasAggs = true;

        this.aggsFilter.type.forEach((e) => {
          should.push({ term: { type: e } });
        });
      }
      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      should.push(tmpCondition);
      console.log("should,", should);
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: should,
        },
      });
      this.gofn(false);
    },
    //aggs过滤作者
    aggsListAuthorChange(val, listFind) {
      let should = [];
      if (listFind) {
        if (!this.agList.author_id.buckets.some((ele) => ele.key === val)) {
          this.agList.author_id.buckets.push({ key: val });
        }

        if (!this.aggsListType.author.some((ele) => ele === val)) {
          this.aggsListType.author.push(val);
          val = this.aggsListType.author;
        } else {
          this.$message({
            type: "warning",
            message: "已添加此检索过滤条件",
          });
          return;
        }
      }
      if (val.length) {
        this.hasAggs = true;
      }
      this.aggsFilter["author"] = val;

      val.forEach((e) => {
        should.push({ term: { author_id: e } });
      });
      /*  let must = [
        {
          bool: {
            should,
          },
        },
      ]; */
      /* if (this.queryString != "") {
        must.push(this.getEsJson());
      } */
      if (this.aggsFilter.hasOwnProperty("type")) {
        this.hasAggs = true;

        this.aggsFilter.type.forEach((e) => {
          should.push({ term: { type: e } });
        });
        /* must.push({
          bool: {
            should,
          },
        }); */
      }
      if (this.aggsFilter.hasOwnProperty("time")) {
        this.hasAggs = true;

        this.aggsFilter.time.forEach((e) => {
          should.push({
            range: {
              timestamp: {
                from: e,
                to: e + 86400,
              },
            },
          });
        });
        /* must.push({
          bool: {
            should,
          },
        }); */
      }
      /* let newSearchJson = {
        bool: {
          must: must,
        },
      }; */
      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      should.push(tmpCondition);
      console.log("should,", should);
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: should,
        },
      });
      this.gofn(false);
    },
  },
  mounted() {
    // setTimeout(() => {
    this.gofn(false, true);
    // }, 2000);
  },
};
</script>
<style scoped>
.newsListTitle {
  display: flex;
}

.newsList {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

.longList {
  height: 78vh;
}

.pointerLine {
  cursor: pointer;
}

.pointerLine:hover {
  text-decoration: underline;
}

.read {
  /* text-decoration: none!important; */
  color: #333 !important;
}
</style>
<style lang="scss">
.newContent {
  padding: 5px 5px;
  border-bottom: 1px solid #dac3c3;

  .content_title {
    width: 5vh;
    font-size: 18px;
    color: #2440b3;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .content_title:hover {
    text-decoration: underline;
  }

  .content_body {
    display: flex;

    img {
      width: 100px;
      height: 63px;
      background: rgba(0, 0, 0, 0.3);
      object-fit: fill;
    }

    .content_details {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      line-clamp: 3;
      -webkit-line-clamp: 3;
      overflow: hidden;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }
  }

  .content_link {
    padding-left: 10px;
    color: #888;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
  }
}

.typeVisualDrawing,
.timeVisualDrawing,
.autherVisualDrawing {
  width: 100%;
  height: 20%;
  padding: 5px 0;
}

#typeVisualDrawing,
#timeVisualDrawing,
#autherVisualDrawing {
  height: 100%;
}

#timeVisualDrawing {
  min-width: 500px;
}

.autherVisualDrawing {
  height: 22%;
}

.aggsSearch .searchSecondary {
  height: 28%;
}
</style>
<style lang="scss">
.searchList {
  width: 80%;
  height: 100%;
  margin: 0 auto;
}

.searchSecondary {
  width: 100%;
  padding: 15px;
}

.searchRight {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 50%;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
}

.shadow {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 6px 0;
}
</style>
<style>
.searchRight .el-checkbox__inner {
  border: none;
  width: 0;
}

.searchRight .el-checkbox__label {
  padding: 1px 4px;
  border: 1px solid #aaa;
  border-radius: 3px;
  font-size: 12px;
}

.searchRight .el-checkbox {
  margin-right: 12px;
  line-height: 14px;
}

.searchRight .el-form-item {
  margin-bottom: 0;
}
</style>
<style lang="scss" scoped>
::v-deep .el-select .el-input,
::v-deep .el-select .el-input__inner {
  width: 60px;
  height: 24px;
  font-size: 14px;
  padding: 0px 5px 0 8px;
}

::v-deep .el-select .el-input__icon {
  line-height: 25px;
}

::v-deep .el-select .el-input__suffix {
  right: -10px;
}

::v-deep .el-form-item:nth-child(2) .el-input,
::v-deep .el-form-item:nth-child(2) .el-input__inner {
  width: 80px;
}

::v-deep .el-form-item:nth-child(3) .el-input,
::v-deep .el-form-item:nth-child(3) .el-input__inner {
  width: 100px;
}

::v-deep .searchRight .el-form-item__label {
  line-height: 20px;
}

::v-deep .customTime .el-input__inner {
  width: 250px;
  height: 24px;
}

::v-deep .customTime .el-range__icon {
  line-height: 14px;
}

::v-deep .customTime .el-range-input {
  font-size: 14px;
}

::v-deep .customTime .el-range-separator {
  font-size: 14px;
  line-height: 14px;
}

::v-deep .customTime.el-range-input {
  width: 55%;
}

::v-deep .el-select .el-select-dropdown__wrap {
  max-height: 350px;
}
</style>
