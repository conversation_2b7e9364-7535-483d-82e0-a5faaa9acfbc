import parse from "emailjs-mime-parser";
export default {
  namespaced: true,
  state: {
    homeDataList: [], //主页总览数据
    accountDataList: [], //账号分布数据
  },
  mutations: {
    setHomeData(state, res) {
      state.homeDataList = res["/apocalypse_client/data_screen"];
      console.log(state.homeDataList, "123");
    },
    setAccountData(state, res) {
      state.accountDataList = res["/tianqi_analysis_report"];
      console.log(state.accountDataList, "789");
    },
    //建立消息系统链接
    sendMessageSystem(state) {
      if (
        !window.main.$store.state.userInfo.userinfo.authority ||
        !window.main.$store.state.userInfo.userinfo.username
      ) {
        //alert('用户名或权限为空')
        return;
      } else {
        //alert('发送请求')
      }
      window.main.$emlmsgs_socket.sendData("Api.Msg.Send", [
        {
          head: {
            session_id: window.main.$store.state.userInfo.session_id,
          },
          msg: {
            type: "username",
            to: [
              {
                authority: window.main.$store.state.userInfo.userinfo.authority,
                username: window.main.$store.state.userInfo.userinfo.username,
              },
            ],
            subject: "",
            text_body: {
              charset: "utf-8",
              transfer_encoding: "7bit",
              body: window.main.$store.state.userInfo.session_id,
            },
          },
        },
      ]);
    },
    setOverviewAnalisy(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("已加入分析任务列表!");
      } else {
        window.main.$message.error("添加分析任务失败!");
      }
    },
    // 查看回推消息
    showMessage(state, v) {
      const data = parse(v.result);
      let textString = JSON.parse(
        data.childNodes[0].raw.slice(data.childNodes[0].raw.indexOf("\n\n") + 2)
      );
      switch (textString.msg) {
        case "running":
          window.main.$notify({
            title: "提示",
            message: `数据总览分析中!`,
            type: "success",
            duration: 0,
          });
          break;
        case "ok":
          window.main.$notify({
            title: "提示",
            message: `数据总览已分析完成!`,
            type: "success",
            duration: 0,
          });
          break;
        case "new":
          window.main.$notify({
            title: "提示",
            message: `数据总览已是最新!`,
            type: "warning",
            duration: 0,
          });
          break;
        default:
          break;
      }
    },
  },
  actions: {
    // 获取主页总览数据
    senHomeOverview({ state, dispatch, commit }, v) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              "/apocalypse_client/data_screen": "",
            },
          },
        ],
        "home/setHomeData"
      );
    },
    // 获取账号分布数据
    senAccountData({ state, dispatch, commit }, v) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              "/tianqi_analysis_report": "",
            },
          },
        ],
        "home/setAccountData"
      );
    },
    // 分析总览数据
    sendOverviewAnalisy({ state, dispatch, commit }, v) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.SendAsyncTask",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              key: "data_screening",
              topic: "AsyncTask.AnalysisTask.InstantMsg.DataScreening",
              value: {
                type: "telegram",
              },
            },
          },
        ],
        "home/setOverviewAnalisy"
      );
    },
  },
};
