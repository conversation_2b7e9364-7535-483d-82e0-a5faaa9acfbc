export default {
  namespaced: true,
  state: {
    searchType: {},
    //搜索类型
    linkedinTypeRadio: "social_platform_timeline_prefix_linkedin",
    typeRadio: "social_platform_timeline_prefix_twitter",
    //query数据
    queryfrom: 0,
    querysize: 20,

    queryData: { hits: [] },
    loading: false,
    queryEnd: false,
    //详情数据
    detailObj: {},
    detailDialogVisible: false,
    Pdf: false,
    //listTrue数据
    listTruefrom: 0,
    listTruesize: 20,
    listTrueEnd: false,
    add_es_query_conditions: {},
    /* twitterSearchType:
      "/social_platform/twitter/timeline,/social_platform/twitter/information", */
    isPerson: false, // 目标人
    numberOfType: null
  },
  mutations: {
    setNumberOfType(state, v) {
      state.numberOfType = v;
    },

    setLinkedinTypeRadio(state, v) {
      state.linkedinTypeRadio = v;
    },
    setTypeRadio(state, v) {
      state.typeRadio = v;
    },
    setPdf(state, v) {
      state.Pdf = v;
    },
    //设置详情遮罩层
    setdialogVisible(state, v) {
      state.detailDialogVisible = v;
    },
    //设置搜索类型
    setSearchType(state, v) {
      state.searchType = v;
    },
    //初始化数据
    initialData(state, v) {
      state.Pdf = false;
      state.queryfrom = 0;
      state.loading = false;
      state.queryData = { hits: [] };
      state.listTruefrom = 0;
      state.listTrueEnd = false;
      state.queryEnd = false;
      state.isPerson = false;
      //
    },
    //设置底部loading
    setLoading(state, v) {
      state.loading = v;
    },
    //设置listTrue
    setListTrue(state, data) {
      if (data) {
        state.queryEnd = false;
        if (data.length > 0) {
          let data_range_index_name_arr = [];
          for (let i = 0; i < data.length; i++) {
            data_range_index_name_arr.push(data[i].data_range_index_name);
          }
          let data_range_index_name = data_range_index_name_arr.join(",");
          //搜索query
          window.main.$store.dispatch("search/twLinFacSearch/getHits", {
            data_range_index_name: data_range_index_name,
          });
          if (data.length != 20) {
            state.listTrueEnd = true;
            console.log("listTrueEndstate.", state.listTrueEnd, state.queryEnd);
          }
        }
      } else {
        // state.loading = false;
        state.listTrueEnd = true;
        state.queryEnd = true;
        console.log("elselistTrueEndstate.", state.listTrueEnd, state.queryEnd);
      }
    },
    //设置hits
    setHits(state, data) {
      if (data.hits.hasOwnProperty("hits") && data.hits.hits.length > 0) {
        state.queryData.hits = state.queryData.hits.concat(data.hits.hits);
        state.queryfrom += state.querysize;
        console.log("state.queryData.hits", state.queryData.hits);
        state.loading = false;

        if (state.numberOfType == "twitter") {
          window.main.$store.commit("channelNum/setTwitterCount", data.hits.total.value);
        } else if (state.numberOfType == "linkedin") {
          window.main.$store.commit("channelNum/setLinkedInCount", data.hits.total.value);
        } else if (state.numberOfType == "facebook") {
          window.main.$store.commit("channelNum/setFacebookCount", data.hits.total.value);
        }
        if (data.hits.hits.length < 20) {
          state.listTruefrom += state.listTruesize;

          state.queryEnd = true;
          console.log("queryEndstate.", state.queryEnd);
          state.queryfrom = 0;
          window.main.$store.dispatch(
            "search/twLinFacSearch/getListTrue",
            state.searchType
          );
        }
      } else {
        state.listTruefrom += state.listTruesize;
        state.queryEnd = true;
        state.queryfrom = 0;
        window.main.$store.dispatch(
          "search/twLinFacSearch/getListTrue",
          state.searchType
        );
      }
      console.log(
        "state.queryEnd:",
        state.queryEnd,
        "state.listTrueEnd:",
        state.listTrueEnd
      );
    },
    //add_es_query_conditions
    setAddEsQueryConditions(state, v) {
      console.log("setAddEsQueryConditions", v);
      state.add_es_query_conditions = v;
    },
    setIsPerson(state, v) {
      state.isPerson = v;
    },
  },
  actions: {
    //1.将已有的详情送过来2.并且去获取更多的详情
    getDataDetailBaseData({ state, commit }, v) {
      //v是这条数据的信息
      let detailObj = { baseData: v };
      let tmpObj = v;
      const sha512 = require("sha512");
      const hash =
        sha512(tmpObj["_index"] + "/_doc/" + tmpObj["_id"]).toString("hex") +
        ";" +
        tmpObj["_index"] +
        "/_doc/" +
        tmpObj["_id"];
      window.main.$main_socket.sendData(
        "Api.Search.DataDetail.BaseData",
        [
          {
            head: {
              row_key: [hash],
            },
          },
        ],
        (res) => {
          Object.assign(detailObj, { detailData: res });
          state.detailObj = detailObj;
          state.detailDialogVisible = true;
          if (detailObj.detailData.columnValues.hasOwnProperty("nlp")) {
            if (
              detailObj.detailData.columnValues.nlp.hanlp_server.analysis_doc !=
                null &&
              detailObj.detailData.columnValues.nlp.hanlp_server.analysis_doc
                .length == 2
            ) {
              window.main.$nextTick(() => {
                // 基于准备好的dom，初始化echarts实例
                let myChart = window.main.$echarts.init(
                  document.getElementById("myChart")
                );
                let option = {
                  xAxis: {
                    type: "category",
                    data: detailObj.detailData.columnValues.nlp.hanlp_server
                      .analysis_doc[0],
                    axisLabel: {
                      rotate: 60,
                    },
                  },
                  yAxis: {
                    type: "value",
                  },
                  series: [
                    {
                      data: detailObj.detailData.columnValues.nlp.hanlp_server
                        .analysis_doc[1],
                      type: "bar",
                      barWidth: 20,
                    },
                  ],
                };
                myChart.setOption(option);
              });
            }
          }
        }
      );
    },
    //获取query 下的hits
    getHits({ state, commit }, v) {
      let condition = window.main.$store.state.search.conditions.conditionsData;
      let control = {
        condition: {
          time_range: condition.timeRange,
          time_range_begin: condition.time_range_begin,
          time_range_end: condition.time_range_end,
          query_mode: condition.ppqueryMode,
        },
        query_string: condition.queryString,
        query_type: "public",
      };
      /* if (
        window.main.$store.state.deployment.twitter.userId.length > 0 ||
        window.main.$route.params.id ||
        state.isPerson
      ) {
        console.log(
          "state.add_es_query_conditions207",
          state.add_es_query_conditions
        );
        Object.assign(control, {
          add_es_query_conditions: state.add_es_query_conditions,
        });
      } */
      if (condition.queryString) {
        Object.assign(control, {
          add_es_query_conditions: state.add_es_query_conditions,
        });
      }

      if (window.main.$route.name == "oriDetails" || window.main.$route.name == "personDetails") {
        Object.assign(control, {
          add_es_query_conditions: state.add_es_query_conditions,
        });
      }

      let sendData = {
        head: {
          from: state.queryfrom,
          size: state.querysize,
        },
        msg: v,
        control: control,
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [sendData],
        "search/twLinFacSearch/setHits"
      );
    },
    //获取listTrue
    getListTrue({ state, commit }, v) {
      state.loading = true;
      let control = {
        query_type: "public",
      };
      if (
        window.main.$store.state.deployment.twitter.userId.length > 0 ||
        window.main.$route.params.id ||
        state.isPerson
      ) {
        console.log(
          "state.add_es_query_conditions",
          state.add_es_query_conditions
        );
        Object.assign(control, {
          add_es_query_conditions: state.add_es_query_conditions,
        });
      }
      let dataRangeDetail = {
        head: {
          from: state.listTruefrom,
          size: state.listTruesize,
        },
        control: control,
        msg: {
          data_range_father_path: v.data_range_father_path,
          data_range_index_prefix: v.data_range_index_prefix,
        },
      };

      window.main.$main_socket.sendData(
        "Api.Search.DataRange.ListTrue",
        [dataRangeDetail],
        "search/twLinFacSearch/setListTrue"
      );
    },
  },
  modules: {},
};
