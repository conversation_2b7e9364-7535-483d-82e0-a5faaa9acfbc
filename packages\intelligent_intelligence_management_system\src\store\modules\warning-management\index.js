export default {
  namespaced: true,
  state: {
    warningList: [], // 所有预警数据
    warningListAll: [], // 存储所有查询到的数据
    mediaList: [], // 媒体列表
    warningLastRowkey: null,
    warningLoading: false,
    currentOperation: null,
    allAccounts: [], // 所有可用账户
    allMedias: {}, // 所有可用媒体
    // 分页相关状态
    currentPage: 1, // 当前页码
    pageSize: 15, // 每页数据量
    total: 0, // 总数据量
    // 账户列表滚动加载相关状态
    accountListLastRowkey: null,
    accountListLoading: false,
    accountListHasMoreData: true,
    accountListIsLoadingMore: false,
  },
  mutations: {
    // 重置所有数据
    resetAllData(state) {
      state.warningList = []
      state.warningListAll = []
      state.warningLastRowkey = null
      state.warningLoading = false
      // 重置分页状态
      state.currentPage = 1
      state.total = 0
      // 重置账户列表状态
      state.allAccounts = []
      state.accountListLastRowkey = null
      state.accountListLoading = false
      state.accountListHasMoreData = true
      state.accountListIsLoadingMore = false
    },

    // 获取预警列表 - 一次性查询所有数据
    getWarningList(state) {
      state.warningLoading = true
      // 查询所有数据，不设置size限制
      window.main.$main_socket.sendData('Api.Search.SearchPrefixTable.Query', [{
        'head': {
          'row_key': [],
          'size': 1000 // 设置一个较大的值来获取所有数据
        },
        'msg': {
          'type': 'username',
          'table': 'warning_condition',
          'prefix': ''
        }
      }], 'warningManagement/setWarningList')
    },
    setWarningList(state, data) {
      state.warningLoading = false

      if (data?.length) {
        // 存储所有查询到的数据
        state.warningListAll = data
        state.total = data.length
        
        // 计算当前页应该显示的数据
        this.commit('warningManagement/updateCurrentPageData')
        
        console.log('setWarningList - 总数据量:', state.total, '当前页数据量:', state.warningList.length)
      } else {
        // 没有数据时
        state.warningListAll = []
        state.warningList = []
        state.total = 0
        if (state.warningList.length === 0) {
          window.main.$message.warning('没有数据！')
        }
      }
    },

    // 更新当前页数据
    updateCurrentPageData(state) {
      const startIndex = (state.currentPage - 1) * state.pageSize
      const endIndex = startIndex + state.pageSize
      state.warningList = state.warningListAll.slice(startIndex, endIndex)
    },

    // 设置当前页码
    setCurrentPage(state, page) {
      state.currentPage = page
      this.commit('warningManagement/updateCurrentPageData')
    },

    // 设置每页数据量
    setPageSize(state, size) {
      state.pageSize = size
      state.currentPage = 1 // 重置到第一页
      this.commit('warningManagement/updateCurrentPageData')
    },

    // 添加或更新预警
    addWarning(state, data) {
      const isUpdate = state.warningList.some(item => item.row === data.data._._)

      // 根据是否为更新操作设置不同的状态提示
      const operationType = isUpdate ? '更新' : '添加'

      console.log(`request data: }`, data)

      window.main.$main_socket.sendData('Api.Search.SearchPrefixTable.AddData', [{
        'msg': {
          'type': 'username',
          'table': 'warning_condition',
          'prefix': data.data._._,
          'data': data
        }
      }], 'warningManagement/setAddWarning')

      // 保存操作类型到state中，供setAddWarning使用
      state.currentOperation = operationType
    },
    setAddWarning(state, data) {
      console.log('setAddWarning', data)
      if (data?.status === 'ok') {
        window.main.$message.success(`${state.currentOperation}预警成功！`)
        window.main.$store.commit('warningManagement/resetAllData')
        window.main.$store.commit('warningManagement/getWarningList')
      } else {
        window.main.$message.error(`${state.currentOperation}预警失败！`)
      }

      // 清除临时状态
      state.currentOperation = null
    },

    // 删除预警
    delWarning(state, data) {
      window.main.$main_socket.sendData('Api.Search.SearchPrefixTable.DelData', [{
        'head': {
          'row_key': [data]
        },
        'msg': {
          'type': 'username',
          'table': 'warning_condition'
        }
      }], 'warningManagement/setDelWarning')
    },
    setDelWarning(state, data) {
      if (data?.status === 'ok') {
        window.main.$message.success('删除预警成功！')
        window.main.$store.commit('warningManagement/resetAllData')
        window.main.$store.commit('warningManagement/getWarningList')
      } else {
        window.main.$message.error('删除预警失败！')
      }
    },

    // 获取当前账户下的所有联系人
    getAllAccounts(state) {
      // 如果是加载更多数据，设置加载状态
      if (state.allAccounts.length > 0) {
        state.accountListIsLoadingMore = true
      } else {
        state.accountListLoading = true
      }

      window.main.$main_socket.sendData('Api.Search.SearchPrefix.Query', [{
        'head': {
          'row_key': state.accountListLastRowkey ? [state.accountListLastRowkey] : [],
          'size': state.pageSize
        },
        'msg': {
          'type': 'username',
          'path': '/instant_msg/system',
          'relation': 'super;username',
          'prefix': ''
        }
      }
      ], 'warningManagement/setAllAccounts')
    },

    // 设置预警账户联系人
    setAllAccounts(state, data) {
      console.log('setAllAccounts:', data)
      state.accountListLoading = false
      state.accountListIsLoadingMore = false

      if (data?.length) {
        state.allAccounts = state.allAccounts.concat(data)
        state.accountListLastRowkey = data[data.length - 1]?.row
        console.log("data: ", data)
        console.log("state.accountListLastRowkey: ", state.accountListLastRowkey)
        // 如果返回的数据少于pageSize条，说明没有更多数据了
        if (data.length < state.pageSize) {
          state.accountListHasMoreData = false
        }
      } else {
        // 没有数据时，设置没有更多数据标记
        state.accountListHasMoreData = false
        if (state.allAccounts.length === 0) {
          window.main.$message.warning('没有可用的账户数据！')
        }
      }

      console.log('setAllAccounts - accountListHasMoreData:', state.accountListHasMoreData, 'data length:', data?.length)
    },

    // 从 zookeeper 获取所有网站节点
    getAllWebsites(state) {
      window.main.$constant_socket.sendData('Api.Node.ChildNodeList', [{
        msg: {
          nodePath: '/etc/kappa/news/kappa_news_spider'
        }
      }], 'warningManagement/getAllWebsitesData')
    },

    // 获取当前所有节点下的数据
    getAllWebsitesData(state, data) {
      console.log('setAllWebsites: ', data)

      // 构造查询数据
      const basePath = '/etc/kappa/news/kappa_news_spider/'
      const result = {}
      data.forEach(id => result[`${basePath}${id}`] = '')
      if (data?.length) {
        window.main.$constant_socket.sendData('Api.Node.NodeData', [{
          msg: result
        }], 'warningManagement/setMediaList')
      } else {
        window.main.$message.warning('没有可用的网站数据！')
      }
    },

    setMediaList(state, data) {
      console.log('setMediaList: ', data)
      if (data?.length) {
        // 通过全局状态传递
        state.mediaList = data
      } else {
        window.main.$message.warning('没有可用的网站数据！')
      }
    },

    // 获取所有媒体数据
    getAllMedias(state) {
      window.main.$constant_socket.sendData('Api.Node.ChildNodeList', [{
        msg: {
          nodePath: '/etc/kappa/news/kappa_news_spider'
        }
      }], 'warningManagement/getAllMediasData')
    },

    // 获取媒体节点数据
    getAllMediasData(state, data) {
      console.log('getAllMediasData: ', data)
      console.log('getAllMediasData type: ', typeof data)
      console.log('getAllMediasData length: ', data?.length)


      // 构造查询数据
      const basePath = '/etc/kappa/news/kappa_news_spider/'
      const result = {}
      data.forEach(id => result[`${basePath}${id}`] = '')
      console.log('getAllMediasData result: ', result)
      if (data?.length) {
        window.main.$constant_socket.sendData('Api.Node.NodeData', [{
          msg: result
        }], 'warningManagement/setAllMedias')
      } else {
        window.main.$message.warning('没有可用的媒体数据！')
      }
    },

    // 设置所有媒体数据
    setAllMedias(state, data) {
      console.log('setAllMedias: ', data)
      console.log('setAllMedias type: ', typeof data)
      console.log('setAllMedias keys: ', data ? Object.keys(data) : 'null')
      if (data) {
        // 只保留type字段用于显示
        const processedData = {}
        Object.keys(data).forEach(key => {
          if (data[key] && data[key].type) {
            processedData[key] = {
              type: data[key].type
            }
          }
        })
        state.allMedias = processedData
        console.log('state.allMedias set to: ', state.allMedias)
      } else {
        window.main.$message.warning('没有可用的媒体数据！')
      }
    }

  },
  actions: {},
  modules: {}
}
