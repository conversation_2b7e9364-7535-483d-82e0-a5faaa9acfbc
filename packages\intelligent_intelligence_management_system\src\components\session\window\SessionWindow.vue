<template>
  <div class="session-window">
    <div
      class="chat-session-content rounded-md"
      style="margin-bottom: 130px"
      ref="sessionWindow"
      @scroll="onScroll"
    >
      <div class="chat-content" ref="chatContent">
        <li v-for="(item, index) in sessionRecordData" :key="index">
          <window-session-content
            :titleName="titleName"
            :role="item.role"
            :content-show-type="windowData.contentShowType"
            :item-data="item"
          ></window-session-content>
        </li>
        <div
          class="el-icon-loading"
          style="margin-left: 20%; font-size: 32px"
          v-if="responsing"
        ></div>
      </div>
      <div class="spacer" ref="spacer">
        <div
          class="createSession pointer"
          style="flex: 0 5%; border-radius: 10px; margin: 0 auto"
          v-if="sessionRecordData.length == 0 || loadingLine ? false : true"
        >
          <div @click="handleCreateSession" style="">
            <i class="el-icon-plus" style="font-size: 18px"></i>
            <span>新的对话</span>
          </div>
        </div>
      </div>
      <!--  <div style="line-height: 140px; color: #fff">k</div> -->
    </div>
    <div
      class="input-main"
      :style="{ bottom: sessionRecordData.length == 0 ? '' : '0px' }"
    >
      <transition name="fade">
        <div
          v-if="sessionRecordData.length == 0 && !isDataLoading"
          class="aiStatistics"
        >
          <div class="statisticsItem" style="margin-left: -400px">
            <div class="itemTit">
              总数据量：<b>{{ (allDataNum / 100000000).toFixed(2) }}亿</b>
            </div>
            <div class="itemCon">
              <div
                class="itemConIndex"
                v-for="(item, index) in IndicesStatus"
                :key="index"
              >
                <img :src="item.src" />
                <b>{{ item.num }}</b>
              </div>
            </div>
          </div>
          <!-- <div class="statisticsItem">
            <div class="itemTit">总数据量：<b>3.5亿</b></div>
            <div class="itemCon">
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
            </div>
          </div> -->
        </div>
      </transition>
      <transition name="fade">
        <div
          v-if="sessionRecordData.length == 0 && !isDataLoading"
          class="welcomeAi"
        >
          欢迎使用AI智能搜索！
        </div>
      </transition>
      <InputMsg
        :responsing="responsing"
        :loadingLine="loadingLine"
        :isTools="isTools"
        ref="componentInputMsg"
        @setInputMsg="setInputMsg"
        @sendInputMessage="sendInputMessage"
        @stopRes="stopRes"
      ></InputMsg>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { type } from "@/i18n/zh/list";
import InputMsg from "./inputMsg/inputMsg";
import WindowSessionContent from "@/components/session/window/chat/WindowSessionContent";
export default {
  name: "SessionWindow",
  components: {
    InputMsg,
    WindowSessionContent,
  },
  data() {
    return {
      isAutoScroll: true,
      inputMsg: "",
      sessionRecordData: [],
      isDataLoading: true,
    };
  },
  props: {
    windowData: {
      type: Object,
      default: () => {},
    },
    isTools: { type: Boolean, default: false },
    loadingLine: { type: Boolean, default: false },
    responsing: { type: Boolean, default: false },
    titleName: { type: String, default: "" },
  },
  watch: {
    sessionRecordData: {
      handler() {
        if (this.isAutoScroll) {
          this.scrollToBottom();
        }
      },
      deep: true,
    },
    responsing: {
      handler(newVal) {
        if (newVal === false) {
          // 当响应结束时，滚动到底部
          this.scrollToBottom();
        }
      },
    },
  },
  computed: {
    ...mapState({
      IndicesStatus: (state) => state.chat.IndicesStatus,
      allDataNum: (state) => state.chat.allDataNum,
    }),
  },
  created() {
    this.isDataLoading = true;
    setTimeout(() => {
      this.isDataLoading = false;
    }, 500);
  },
  methods: {
    scrollToBottom() {
      this.$nextTick(() => {
        const sessionWindow = this.$refs.sessionWindow;
        if (sessionWindow) {
          sessionWindow.scrollTo({
            top: sessionWindow.scrollHeight,
            behavior: "smooth",
          });
        }
      });
    },
    setLoadingLine(val) {
      this.$emit("setLoadingLine", val);
    },
    onScroll() {
      const scrollDom = this.$refs.sessionWindow;
      const scrollTop = scrollDom.scrollTop;
      const clientHeight = scrollDom.clientHeight;
      const scrollHeight = scrollDom.scrollHeight;

      if (Math.abs(scrollTop + clientHeight - scrollHeight) <= 1) {
        this.isAutoScroll = true;
      } else {
        this.isAutoScroll = false;
      }
    },
    handleCreateSession() {
      this.$emit("handleCreateSession");
    },
    stopRes() {
      this.$emit("stopRes");
    },
    sendInputMessage() {
      this.$emit("sendInputMessage", this.inputMsg);
    },
    setInputMsg(val) {
      this.inputMsg = val;
    },
    setSessionRecord(val) {
      this.isDataLoading = true;
      setTimeout(() => {
        this.sessionRecordData = val;
        this.isAutoScroll = true;
        this.scrollToBottom();
        setTimeout(() => {
          this.isDataLoading = false;
        }, 100);
        console.log("this.sessionRecordData", this.sessionRecordData);
      }, 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.session-window {
  position: relative;
  width: 100%;
  flex: 1;
  max-height: 100%;
  display: flex;
  overflow: hidden;
}

.chat-session-content {
  width: 100%;
  background-size: 100% 100%;
  box-sizing: border-box;
  overflow: auto;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  flex: 1;
}

.chat-content {
  width: 100%;
  padding: 8px 0 14px 0;
  box-sizing: border-box;
  flex-grow: 1;
  .chat-main-content {
    width: 100%;
    display: flex;
  }

  li {
    list-style: none;
    height: auto;
    width: 1020px;
    margin: 0 auto;
    display: flex;
  }
}

.input-main {
  background-color: #fff;
  position: absolute;
  width: 100%;
  min-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  /*  bottom: 0;
  left: 0; */
  box-sizing: border-box;
  z-index: 1510;
  .aiStatistics {
    display: flex;
    color: #000;

    .statisticsItem {
      border-radius: 4px;
      background: #f7f9fd;
      margin: 10px;
      padding: 10px;
      .itemTit {
        margin-left: 15px;
        font-size: 16px;
        font-weight: bold;
        b {
          font-size: 24px;
        }
      }
      .itemCon {
        width: 400px;
        margin-top: 10px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        .itemConIndex {
          width: 100px;
          margin-left: 15px;
          margin-right: 15px;
          display: flex;
          align-items: center;
          margin-top: 15px;
          img {
            width: 36px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          }
          b {
            margin-left: 10px;
          }
        }
      }
    }
  }
  .welcomeAi {
    color: #4d6bfe;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 25px;
    margin-top: 40px;
  }
}

.input-main .stop {
  width: 100%;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;

  .stop-item {
    width: 10%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--font-color-default);
    border: 1px var(--item-border-normal-color) solid;
    z-index: 1;
    transition: all 0.1s ease-out;
    background: var(--background-main);

    &:hover {
      box-shadow: 0 0 3px 1px var(--item-border-default-color);
      transition: all 0.2s ease-out;
    }
    &:active {
      transform: scale(0.92);
      transition: all 0.2s ease-in-out;
    }

    span {
      margin: 0 5px;
    }
  }
}
::v-deep .stop img {
  width: 15%;
}

.spacer {
}

::v-deep .markdown-body {
  font-size: 15px;
}
.createSession {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  background-color: rgb(219, 234, 254);
  color: #4d6bfe;
  cursor: pointer;
  margin-bottom: 10px;
}
.createSession:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  background-color: rgb(203, 224, 251);
  transform: scale(0.99);
}
.createSession > div {
  width: 100%;
  height: 32px;
  max-height: 32px;
  line-height: 42px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border: 1px var(--session-list-create-session-border) dashed;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}
.createSession span {
  font-size: 15px;
  padding: 0 2px;
}
.createSession > div:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  transform: scale(0.99);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
