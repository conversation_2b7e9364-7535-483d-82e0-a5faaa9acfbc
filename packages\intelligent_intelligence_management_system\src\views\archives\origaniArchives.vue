<template>
  <div class="intellManage">
    <!-- 头部信息 -->
    <div class="header">
      <div class="left">
        <div class="search-container">
          <el-button 
            style="margin-right: 10px;"
            type="primary"  
            @click="handleRefresh"
          >刷新</el-button>
          <el-input
            placeholder="输入关键字匹配组织"
            v-model="searchKeyword"
            clearable
            @keyup.enter.native="handleEnter"
            @clear="handleClear"
            class="search-input"
          >
          </el-input>
          <el-button 
            type="primary"  
            @click="handleEnter"
            class="search-btn"
          >搜索</el-button>
        </div>
      </div>
      <el-button
        type="primary"
        icon="el-icon-plus"
        class="add-btn"
        @click="openOrganPerson"
      >添加组织</el-button>
    </div>
    <!-- 搜索结果头部 -->
    <div v-if="showSearchHeader" class="search-header-bar">
      <el-button type="text" icon="el-icon-arrow-left" @click="handleBack">返回</el-button>
      <span style="margin-left: 10px;">搜索关键词：{{ lastSearchKeyword }}</span>
    </div>
    <!-- 主体部分，身份证样式卡片 -->
    <div class="container">
      <div
        class="id-card"
        v-for="(item, index) in organiList"
        :key="index"
        @click="clickCard(item)"
      >
        <!-- 组织卡片正面 -->
        <div class="id-card-front">
          <div class="id-card-content">
            <div class="id-card-photo">
              <el-avatar 
                :size="120" 
                :src="getAvatarSrc(item._source.params.basic.avatar)"
              >
              </el-avatar>
            </div>
            <div class="id-card-info">
              <div class="info-row">
                <span class="label">组织名称:</span>
                <span class="value" :title="item._source.params.basic.name" @click.stop>{{ item._source.params.basic.name || '未知'}}</span>
              </div>
              <div class="info-row remark">
                <span class="label">备注:</span>
                <span class="value" :title="item._source.params.basic.remark || '暂无备注'" @click.stop>{{ item._source.params.basic.remark || '暂无备注' }}</span>
              </div>
              <div class="info-row">
                <span class="label">所属:</span>
                <span class="value" :title="item._source.params.basic.belong" @click.stop>{{ item._source.params.basic.belong || '未知' }}</span>
              </div>
              <div class="info-row">
                <span class="label">创建时间:</span>
                <span class="value" :title="item._source.params.basic.createTime" @click.stop>{{ item._source.params.basic.createTime || '未知' }}</span>
              </div>
              <div class="info-row">
                <span class="label">信息完整度:</span>
                <span class="value">{{ getCompleteness(item._source.params.basic) }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 卡片悬停效果 -->
        <div class="id-card-hover"></div>
      </div>
      <!-- 空状态展示 -->
      <el-empty 
        v-if="organiList.length === 0" 
        description="暂无组织数据"
        class="empty-state"
      >
        <el-button type="primary" @click="openOrganPerson">添加组织</el-button>
      </el-empty>
    </div>
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-if="total > 0"
        :total="total"
        :page-size="size"
        :current-page="currentPage"
        @current-change="handleCurrentChange"
        layout="total, prev, pager, next, jumper"
        background
      />
    </div>
    <!-- 组织人员关系组件弹窗 -->
    <organization-personnel
      v-if="organizationData.flag"
      :data="organizationData"
      :organiList="organiList"
      @close="handleOrganizationClose" 
    />
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      origaniData: [],
      organizationData: {
        flag: false,
        title: "",
      },
      showSearchHeader: false, // 控制返回按钮和关键词显示
      lastSearchKeyword: "",  // 记录上次搜索关键词
    };
  },
  components: {
    "organization-personnel": () =>
      import("@/layout/components/archives/OrganizationPersonnel.vue"), // 组织人员关系
  },
  created() {
    this.$store.commit("organization/resetAllData");
    this.$store.commit("organization/getOrgani");
  },
  computed: {
    ...mapState({
      organiList: (state) => state.organization.organiList,
      total: (state) => state.organization.total,
      size: (state) => state.organization.size,
      currentPage: (state) => state.organization.from / state.organization.size + 1,
    }),
    searchKeyword: {
      get() {
        return this.$store.state.organization.searchKeyword;
      },
      set(value) {
        this.setSearchKeyword(value);
      }
    },
  },
  methods: {
    ...mapMutations({
      setSearchKeyword: "organization/setSearchKeyword",
      setPage: "organization/setPage",
      resetAllData: "organization/resetAllData",
      resetData: "organization/resetData",
      getOrgani: "organization/getOrgani",
    }),

    // 信息完整度处理函数
    getCompleteness(basicInfo) {
      const totalFields = 15; // 假设有5个基本信息字段
      const filledFields = Object.values(basicInfo).filter(value => value).length;
      return `${filledFields} / ${totalFields}`;
    },

    // 处理头像函数
    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require('@/assets/images/user.png');
    },

    // 刷新
    handleRefresh() {
      this.resetData();
      this.getOrgani();
    },

    // 分页切换
    handleCurrentChange(page) {
      this.setPage(page);
      this.getOrgani();
    },
    // 搜索框清除事件
    handleClear() {
      this.setSearchKeyword("");
    },
    // 搜索框回车事件
    handleEnter() {
      this.resetData();
      if (!this.searchKeyword || !this.searchKeyword.trim()) {
        this.showSearchHeader = false;
        this.lastSearchKeyword = "";
      } else {
        this.showSearchHeader = true;
        this.lastSearchKeyword = this.searchKeyword;
      }
      this.$store.commit("organization/getOrgani");
    },

    //返回按钮点击事件
    handleBack() {
      this.setSearchKeyword("");
      this.showSearchHeader = false;
      this.lastSearchKeyword = "";
      this.resetData();
      this.getOrgani();
    },

    openOrganPerson() {
      this.organizationData = {
        flag: true,
        title: "添加组织",
      };
    },
    clickCard(data) {
      let organi = JSON.stringify(data);
      const routeData = this.$router.resolve({
        name: 'oriDetails',
        query: {
          data: organi,
        }
      })
      window.open(routeData.href, "_blank");
    },
    handleOrganizationClose() {
      this.organizationData.flag = false
    },
  },
};
</script>
<style lang="scss" scoped>
.intellManage {
  padding: 24px;
  min-height: 100%;
  background: #f0f2f5;
  height: 100%;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    height: 5%;
    .left {
      flex: 1;
      max-width: 450px;
      display: flex;
      align-items: center;
      
      .search-container {
        display: flex;
        align-items: center;
        
        .search-input {
          flex: 1;
          :deep(.el-input__inner) {
            height: 40px;
            border-radius: 4px 0 0 4px;
            border-right: none;
            
            &:focus {
              border-color: #409eff;
            }
          }
        }
        
        .search-btn {
          height: 40px;
          border-radius: 0 4px 4px 0;
          padding: 0 15px;
          margin-left: -1px;
        }
      }
    }
    
    .add-btn {
      margin-left: 16px;
    }
  }

  .container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    padding: 8px;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
    height: 90%;
    .id-card {
      position: relative;
      width: 80%;
      height: 170px;
      perspective: 1000px;
      cursor: pointer;
      
      .id-card-front {
        position: relative;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
        transition: all 0.3s ease;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 6px;
          background: linear-gradient(90deg, #1890ff, #36cfc9);
        }
        
        .id-card-content {
          display: flex;
          gap: 20px;
          
          .id-card-photo {
            width: 100px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            :deep(.el-avatar) {
              width: 100% !important;
              height: 100% !important;
              border-radius: 12px !important;
              object-fit: cover;
              box-shadow: 0 2px 8px rgba(0,0,0,0.12);
            }
          }
          
          .id-card-info {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .info-row {
              display: flex;
              height: 25px;
              line-height: 25px;
              .label {
                width: auto;
                color: #666;
                font-size: 14px;
              }
              
              .value {
                margin-left: 5px;
                flex: 1;
                color: #333;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: default;
                max-width: 12.5rem;
                min-width: 0;
              }
              
              &.remark {
                .value {
                  color: #666;
                  font-size: 13px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }
            }
          }
        }
      }
      .id-card-hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 12px;
        i {
          font-size: 32px;
          margin-bottom: 8px;
        }
        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
      &:hover {
        .id-card-front {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        .id-card-hover {
          opacity: 1;
        }
      }
    }
    .empty-state {
      grid-column: 1 / -1;
      margin-top: 120px;
    }
  }
  .pagination {
    margin-top: .625rem;
    display: flex;
    justify-content: center;
    position: relative;
  }
}

.search-header-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 15px;
}

// 响应式布局
@media screen and (max-width: 768px) {
  .intellManage {
    padding: 16px;
    .header {
      flex-direction: column;
      align-items: stretch;
      .left {
        width: 100%;
        max-width: 100%;
        margin-bottom: 16px;
        .search-container {
          width: 100%;
        }
      }
      .add-btn {
        margin-left: 0;
      }
    }
    .container {
      grid-template-columns: 1fr;
    }
  }
}
// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .intellManage {
    background: #141414;
    .id-card-front {
      background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
      border-color: #303030;
      .id-card-header {
        .id-card-title {
          color: #1890ff;
        }
        .id-card-type {
          background: #111b26;
          color: #a3a6ad;
        }
      }
      .id-card-info {
        .info-row {
          .label {
            color: #a3a6ad;
          }
          .value {
            color: #e5eaf3;
          }
          &.remark .value {
            color: #a3a6ad;
          }
        }
      }
      .id-card-footer {
        background: rgba(255, 255, 255, 0.02);
        border-color: #303030;
      }
    }
    .pagination {
      :deep(.el-pagination) {
        color: #e5eaf3;
        .btn-prev, .btn-next {
          background-color: #1f1f1f;
          border-color: #303030;
          color: #e5eaf3;
          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
        }
        .el-pager li {
          background-color: #1f1f1f;
          border-color: #303030;
          color: #e5eaf3;
          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
        .el-pagination__jump {
          color: #a3a6ad;
          .el-input__inner {
            background-color: #1f1f1f;
            border-color: #303030;
            color: #e5eaf3;
          }
        }
      }
    }
  }
}
</style>
