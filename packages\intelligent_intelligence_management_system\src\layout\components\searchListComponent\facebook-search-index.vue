<template>
  <div class="hitsLay">
    <div style="display: flex; justify-content: space-between">
      <!-- 全选复选框 -->
      <label>
        <input
          style="cursor: pointer"
          type="checkbox"
          v-model="isAllSelected"
          @change="handleSelectAll"
        />
        <span v-if="isAllSelected">取消全选</span
        ><span v-if="!isAllSelected">全选</span>
      </label>
      <div v-show="selectedItems.length > 0">
        <el-button
          style="margin-left: 10px"
          type="primary"
          @click="exportToExcel"
          size="mini"
          >导出Excel</el-button
        >
        <el-button size="mini" @click="toCollect">收藏</el-button>
      </div>
    </div>
    <div ref="scroll" @scroll="handleScroll">
      <!-- 其他滚动内容 -->

      <div v-for="(val, index) in queryData" :key="index">
        <div v-if="index % 20 === 0 && index !== 0" class="index-marker">
          第 {{ index / 20 }} 页
        </div>
        <div class="twitterlistRow">
          <label>
            <input
              style="cursor: pointer"
              type="checkbox"
              :value="val"
              v-model="selectedItems"
              @change="handleSingleSelect(val)"
            />
          </label>
          <div
            class="twitterlistRowLeft"
            v-if="
              !(val == null) &&
              val.hasOwnProperty('_source') &&
              val._source.hasOwnProperty('icon')
            "
          >
            <img
              :onerror="defaultImg"
              :src="
                '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
                val._source.icon[0].sha512_hash +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
              :type="val._source.icon[0].icon_type"
            />
          </div>
          <div class="twitterlistRowLeft" v-else>
            <img :src="require('@/assets/images/winter.jpg')" />
          </div>
          <div class="twitterlistRowRight">
            <div class="twitterlistRowTop">
              <div>
                <b>{{ val._source.nickname[0] }}</b>
                <span style="margin-left: 10px">{{ val._source.user_id }}</span>
                <span
                  style="margin-left: 10px; color: #999"
                  v-if="val._source.hasOwnProperty('create_timestamp')"
                  >{{
                    $tools.timestampToTime(val._source.create_timestamp)
                  }}</span
                >
                <span
                  style="margin-left: 10px; color: #999"
                  v-if="val._source.hasOwnProperty('timestamp')"
                  >{{ $tools.timestampToTime(val._source.timestamp) }}</span
                >
              </div>
              <div style="margin-right: 10px">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  @click.native="morefn(val)"
                  >详情</el-button
                >
              </div>
            </div>
            <div class="twitterlistRowMid">
              <span v-if="val._source.hasOwnProperty('summary')">{{
                val._source.summary[0] === ""
                  ? "暂无简介"
                  : val._source.summary[0]
              }}</span>
              <span v-if="val._source.hasOwnProperty('content_article')">{{
                val._source.content_article === ""
                  ? "暂无内容"
                  : val._source.content_article
              }}</span>
            </div>
            <div class="twitterlistRowFoot">
              <div
                class="footItem"
                title="点赞"
                v-if="val._source.hasOwnProperty('likes_count')"
                @click="
                  likefn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe619;</i>点赞
                <span style="margin-left: 4px">{{
                  val._source.likes_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="粉丝"
                v-if="val._source.hasOwnProperty('followers_count')"
                @click="
                  hotFollowersListfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe60c;</i>
                <span style="margin-left: 4px">{{
                  val._source.followers_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="关注"
                v-if="val._source.hasOwnProperty('following_count')"
                @click="
                  hotFollowingListfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-star-off"></i>
                <span style="margin-left: 4px">{{
                  val._source.following_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="文章"
                v-if="val._source.hasOwnProperty('article_count')"
                @click="
                  getTuiWenDatalist({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="icon iconfont" style="">&#xe74b;</i>
                <span style="margin-left: 4px">{{
                  val._source.article_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="评论"
                v-if="val._source.hasOwnProperty('reply_count')"
                @click="
                  replyfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-chat-dot-round"></i>

                <span style="margin-left: 4px">{{
                  val._source.reply_count
                }}</span>
              </div>
              <div
                class="footItem"
                title="转发"
                v-if="val._source.hasOwnProperty('forward_count')"
                @click="
                  forwardfn({
                    user_id: val._source.user_id,
                    content_article_id: val._source.hasOwnProperty(
                      'content_article_id'
                    )
                      ? val._source.content_article_id
                      : '',
                    type: val._source.type,
                  })
                "
              >
                <i class="el-icon-share"></i>
                <span style="margin-left: 4px">{{
                  val._source.forward_count
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="loading && (!queryEnd || !listTrueEnd)" class="loading">
        加载中...
      </div>
      <div v-if="queryEnd && listTrueEnd" class="no-more">没有更多数据了</div>
    </div>
    <el-dialog
      title="详情"
      :visible.sync="detailDialogVisible"
      v-if="detailDialogVisible"
      :before-close="handleClose"
      width="80%"
      style="margin-top: -10vh !important"
      append-to-body
    >
      <div>
        <div>
          <el-collapse v-model="activeName" accordion style="margin-top: 20px">
            <el-collapse-item name="detailBase">
              <template slot="title"> 基础信息 </template>
              <div class="morInfor">
                <div
                  class="morInfor_l"
                  v-if="
                    !(detailObj.baseData == null) &&
                    detailObj.baseData.hasOwnProperty('_source') &&
                    detailObj.baseData._source.hasOwnProperty('icon')
                  "
                >
                  <img
                    :src="
                      '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
                      detailObj.baseData._source.icon[0].sha512_hash +
                      '?session_id=' +
                      $store.state.userInfo.session_id
                    "
                    :type="detailObj.baseData._source.icon[0].icon_type"
                  />
                </div>
                <div class="morInfor_r">
                  <ul>
                    <li
                      class="morInfor_r_li"
                      v-for="(lival, liname, liindex) in detailObj.baseData[
                        '_source'
                      ]"
                      :key="liindex"
                      v-show="
                        liname != 'analysis_doc' &&
                        liname != 'content' &&
                        liname != 'content_article' &&
                        liname != 'content_img' &&
                        liname != 'content_video' &&
                        liname != 'content_voice' &&
                        liname != 'content_file'
                      "
                    >
                      <div class="morInfor_r_li_h">
                        {{ $t("list." + liname) }}:
                      </div>
                      <div class="morInfor_r_li_c">
                        {{
                          liname == "timestamp" || liname == "@timestamp"
                            ? $tools.timestampToTime(lival)
                            : lival
                        }}
                      </div>
                    </li>
                    <li
                      class="morInfor_r_li"
                      v-if="
                        detailObj.detailData.columnValues &&
                        detailObj.detailData.columnValues.nlp &&
                        detailObj.detailData.columnValues.nlp.hanlp_server &&
                        detailObj.detailData.columnValues.nlp.hanlp_server
                          .keyword_list
                      "
                    >
                      <div class="morInfor_r_li_h">
                        {{ $t("list.keyword_list") }}:
                      </div>
                      <div class="morInfor_r_li_c">
                        {{
                          detailObj.detailData.columnValues.nlp.hanlp_server
                            .keyword_list
                        }}
                      </div>
                    </li>
                    <li
                      class="morInfor_r_li"
                      v-if="
                        detailObj.detailData.columnValues &&
                        detailObj.detailData.columnValues.nlp &&
                        detailObj.detailData.columnValues.nlp.hanlp_server &&
                        detailObj.detailData.columnValues.nlp.hanlp_server
                          .sentence_list
                      "
                    >
                      <div class="morInfor_r_li_h">
                        {{ $t("list.sentence_list") }}:
                      </div>
                      <div class="morInfor_r_li_c">
                        {{
                          detailObj.detailData.columnValues.nlp.hanlp_server
                            .sentence_list
                        }}
                      </div>
                    </li>
                  </ul>
                  <div
                    v-if="
                      detailObj.detailData.columnValues &&
                      detailObj.detailData.columnValues.nlp &&
                      detailObj.detailData.columnValues.nlp.hanlp_server &&
                      detailObj.detailData.columnValues.nlp.hanlp_server
                        .analysis_doc
                    "
                  >
                    <div id="myChart" :style="{ height: '400px' }"></div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="正文"
              name="content_article"
              v-if="
                detailObj.baseData['_source'].hasOwnProperty('content_article')
              "
            >
              <div style="white-space: pre-line; line-height: 20px">
                {{ detailObj.baseData["_source"].content_article }}
              </div>
            </el-collapse-item>

            <el-collapse-item
              title="翻译"
              name="translate"
              v-if="
                detailObj.detailData.columnValues.hasOwnProperty('nlp') &&
                detailObj.detailData.columnValues.nlp.hasOwnProperty(
                  'hanlp_server'
                ) &&
                detailObj.detailData.columnValues.nlp.hanlp_server.hasOwnProperty(
                  'translate'
                )
              "
            >
              <div style="white-space: pre-line; line-height: 20px">
                {{
                  detailObj.detailData.columnValues.nlp.hanlp_server.translate
                }}
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="图片"
              name="content_img"
              v-if="detailObj.baseData['_source'].hasOwnProperty('content_img')"
            >
              <div class="imgAllBox">
                <div
                  v-for="(imgItem, imgindex) in detailObj.baseData['_source'][
                    'content_img'
                  ]"
                  :key="imgindex"
                  style="width: 300px; margin-top: 20px"
                >
                  <el-image
                    style="width: 300px; height: 300px"
                    :fit="'cover'"
                    :src="
                      '/filesystem/api/rest/v1/small_file/get_sha512_file/content_img/' +
                      imgItem.sha512_hash +
                      '?session_id=' +
                      $store.state.userInfo.session_id
                    "
                    :preview-src-list="[
                      '/filesystem/api/rest/v1/small_file/get_sha512_file/content_img/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id,
                    ]"
                  >
                  </el-image>
                  <p style="fong-weight: bold">{{ imgItem.file_name }}</p>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="视频"
              name="content_video"
              v-if="
                detailObj.baseData['_source'].hasOwnProperty('content_video')
              "
            >
              <div
                v-for="(imgItem, imgindex) in detailObj.baseData['_source'][
                  'content_video'
                ]"
                :key="imgindex"
              >
                <span v-if="!imgItem">链接为空</span>
                <video
                  controls="controls"
                  preload="auto"
                  muted
                  loop
                  :ref="'videoB' + imgindex"
                  :id="'videoB' + imgindex"
                >
                  <source
                    :src="
                      '/filesystem/api/rest/v1/big_file/get_sha512_file/content_video/' +
                      imgItem.sha512_hash +
                      '?session_id=' +
                      $store.state.userInfo.session_id
                    "
                    :type="imgItem.content_video_type"
                    :alt="imgItem ? imgItem : '链接为空'"
                  />
                </video>
                <p>
                  选择播放速率：<select
                    ref="selRate"
                    @change="videoFn('videoB' + imgindex)"
                  >
                    <option value="0.5">0.5</option>
                    <option value="1" selected>1.0</option>
                    <option value="1.25">1.25</option>
                    <option value="1.5">1.5</option>
                    <option value="2">2.0</option>
                    <option value="2">3.0</option>
                    <option value="2">4.0</option>
                  </select>
                </p>
                <p>{{ imgItem.file_name }}</p>
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="音频"
              name="content_voice"
              v-if="
                detailObj.baseData['_source'].hasOwnProperty('content_voice')
              "
            >
              <div
                v-for="(imgItem, imgindex) in detailObj.baseData['_source'][
                  'content_voice'
                ]"
                :key="imgindex"
              >
                <video
                  controls="controls"
                  preload="auto"
                  muted
                  loop
                  autoplay
                  :type="detailObj.baseData['_source'].content_voice_type"
                  :ref="'videoB' + imgindex"
                  :id="'videoB' + imgindex"
                  :src="
                    '/filesystem/api/rest/v1/small_file/get_sha512_file/content_voice/' +
                    imgItem.sha512_hash +
                    '?session_id=' +
                    $store.state.userInfo.session_id
                  "
                  :alt="imgItem ? imgItem : '链接为空'"
                >
                  {{ imgItem.file_name }}
                </video>
                <p>
                  选择播放速率：<select
                    ref="selRate"
                    @change="videoFn('videoB' + imgindex)"
                  >
                    <option value="0.5">0.5</option>
                    <option value="1" selected>1.0</option>
                    <option value="1.25">1.25</option>
                    <option value="1.5">1.5</option>
                    <option value="2">2.0</option>
                    <option value="2">3.0</option>
                    <option value="2">4.0</option>
                  </select>
                </p>
                <p>{{ imgItem.file_name }}</p>
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="附件"
              name="content_file"
              v-if="
                detailObj.baseData['_source'].hasOwnProperty('content_file')
              "
            >
              <a
                style="color: blue; display: block; line-height: 50px"
                title="点击下载"
                v-for="(imgItem, imgindex) in detailObj.baseData['_source'][
                  'content_file'
                ]"
                :key="imgindex"
                :href="
                  '/filesystem/api/rest/v1/big_file/get_sha512_file/content_file/' +
                  imgItem.sha512_hash +
                  '?session_id=' +
                  $store.state.userInfo.session_id
                "
                :type="imgItem.content_file_type"
                :download="imgItem['file_name']"
                >{{ imgItem["file_name"] }}</a
              >
            </el-collapse-item>
            <el-collapse-item
              title="pdf预览"
              name="content_pdf"
              v-if="detailObj.baseData['_source'].hasOwnProperty('content_pdf')"
            >
              <div
                id="pdfBox"
                v-for="(imgItem, imgindex) in detailObj.baseData['_source'][
                  'content_pdf'
                ]"
                :key="imgindex"
                style="overflow: auto; height: 600px"
              >
                <p
                  style="
                    margin-top: 10px;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 10px;
                    font-size: 16px;
                  "
                >
                  {{ imgItem.file_name }}
                </p>
                <p
                  @click="pdfFn"
                  v-if="!$store.state.search.twLinFacSearch.Pdf"
                  style="text-align: center; cursor: pointer; color: #409eff"
                >
                  PDF走丢了ㄒoㄒㄒoㄒ，点击一下把他找回来吧~~~~！
                </p>

                <div v-if="pdferror" style="text-align: center">
                  <b>not found</b>
                </div>
                <div>
                  <a
                    :href="
                      '/filesystem/api/rest/v1/big_file/get_sha512_file/content_pdf/' +
                      imgItem.sha512_hash +
                      '?session_id=' +
                      $store.state.userInfo.session_id
                    "
                    target="_blank"
                  >
                    <pdf
                      v-for="i in numPages[imgindex]"
                      ref="pdf"
                      :key="i"
                      :src="
                        '/filesystem/api/rest/v1/big_file/get_sha512_file/content_pdf/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id
                      "
                      :page="i"
                    ></pdf>
                  </a>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item
              title="原始正文"
              name="content"
              v-if="detailObj.baseData['_source'].hasOwnProperty('content')"
            >
              <div style="white-space: pre-line; line-height: 20px">
                {{ detailObj.baseData["_source"].content }}
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="collectDialog"
      :close-on-click-modal="false"
      title="选取目录"
      top="10px"
      width="40%"
      append-to-body
    >
      <div style="width: 95%">
        <collectTree
          :listType="'username'"
          :tableName="'favorites_data'"
          :getCollect="getCollect"
        ></collectTree>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import pdf from "vue-pdf";
import { mapState, mapMutations } from "vuex";
import { collectTree } from "@/layout/components";
export default {
  data() {
    return {
      collectNum: 0,
      collectDialog: false,
      selectedItems: [],
      activeName: "detailBase",
      pdferror: false,
      numPages: [],
      defaultImg:
        'this.src="' + require("../../../assets/images/winter.jpg") + '"',
    };
  },
  watch: {},
  components: {
    collectTree,
  },
  computed: {
    ...mapState({
      facebook: (state) => state.person.facebook,
      queryData: (state) => state.search.twLinFacSearch.queryData.hits,
      loading: (state) => state.search.twLinFacSearch.loading,
      listTrueEnd: (state) => state.search.twLinFacSearch.listTrueEnd,
      queryEnd: (state) => state.search.twLinFacSearch.queryEnd,
      searchType: (state) => state.search.twLinFacSearch.searchType,
      detailObj: (state) => state.search.twLinFacSearch.detailObj,
      detailDialogVisible: (state) =>
        state.search.twLinFacSearch.detailDialogVisible,
      tabsActiveName: (state) => state.search.searchList.tabsActiveName,
    }),
    // 计算全选状态
    isAllSelected: {
      get() {
        if (this.queryData.length > 0) {
          return this.queryData.every((listItem) =>
            this.selectedItems.some(
              (selectedItem) => selectedItem["_id"] === listItem["_id"]
            )
          );
        }
      },
      set(value) {
        // 这里保持空函数，实际逻辑在@change处理
      },
    },
  },
  created() {
    //初始化数据
    this.initialData();
    if (window.main.$route.name == "personDetails" || window.main.$route.name == "oriDetails") {
        let add_es_query_conditions = {
          bool: {
            should: this.facebook.map(account => ({
              term: { user_id: account }
            }))
          }
        };
        this.setAddEsQueryConditions(add_es_query_conditions);
        this.setConditionsData();
      }
    //加载数据
    this.loadData();
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.scroll.style.height = "680px";
      this.$refs.scroll.style.overflowY = "auto";
    });
  },
  methods: {
    ...mapMutations({ 
      initialData: "search/twLinFacSearch/initialData",
      setAddEsQueryConditions: "search/twLinFacSearch/setAddEsQueryConditions",
      setConditionsData: "search/conditions/setConditionsData",
    }), // 将mutations映射为方法
    //使用当前时间戳与随机数结合当作预警词的唯一id
    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = new Array(
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n"
      );
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },
    //收藏
    toCollect() {
      if (this.selectedItems.length) {
        this.collectDialog = true;
      } else {
        this.$message({
          type: "info",
          message: "请选择收藏文章",
        });
      }
    },
    getCollect(data) {
      this.$confirm("确定选择此目录收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.selectedItems.forEach((element) => {
            let prefix =
              1e13 -
              Math.round(new Date().getTime() / 1000) +
              data.id +
              this.reduceNumber();
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefixTable.AddData",
              [
                {
                  msg: {
                    type: "username",
                    authority:
                      window.main.$store.state.userInfo.userinfo.authority,
                    username:
                      window.main.$store.state.userInfo.userinfo.username,
                    table: "favorites_data",
                    prefix,
                    relation: data.id + ";" + "facebook",
                    data: {
                      data: {
                        file_data: element,
                      },
                    },
                  },
                },
              ],
              (res) => {
                console.log("收藏", res);
                if (res.status === "ok") {
                  this.collectNum++;
                  if (this.collectNum === this.selectedItems.length) {
                    this.$message.success("收藏成功!");
                    this.collectNum = 0;

                    this.selectedItems = [];
                  }
                }
              }
            );
          });
        })
        .catch((err) => {
          console.log("收藏", err);
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 处理全选/取消全选
    handleSelectAll() {
      if (this.isAllSelected) {
        this.selectedItems = [];
      } else {
        // 创建新数组确保响应式更新
        this.selectedItems = this.queryData.map((item) => ({ ...item }));
      }
    },

    // 处理单个选择
    handleSingleSelect(v) {
      // 手动触发计算属性更新
      this.$forceUpdate();
    },
    exportToExcel() {
      let excelList = this.selectedItems;
      console.log("导出excelList", excelList);
      let excelDataArr = [];
      let excelHeaderArr = ["权重"];
      let excelKeyArr = [];
      for (let i = 0; i < excelList.length; i++) {
        for (let str in excelList[i]._source) {
          if (
            str != "content" &&
            str != "analysis_doc" &&
            /* str != "content_article" && */
            str != "content_img" &&
            str != "content_video" &&
            str != "content_voice" &&
            str != "content_file" &&
            str != "icon" &&
            str != "icon_type"
          ) {
            excelKeyArr.push(str);
            excelHeaderArr.push(this.$t("list." + str));
            excelHeaderArr = Array.from(new Set(excelHeaderArr));
            excelKeyArr = Array.from(new Set(excelKeyArr));
          }
        }
      }
      excelDataArr.push(excelHeaderArr);
      for (let i = 0; i < excelList.length; i++) {
        let data = excelList[i];
        let arr = [data._score];
        excelKeyArr;
        for (let j = 0; j < excelKeyArr.length; j++) {
          if (data._source.hasOwnProperty(excelKeyArr[j])) {
            if (
              excelKeyArr[j] === "@timestamp" ||
              excelKeyArr[j] === "timestamp" ||
              excelKeyArr[j] === "birthday" ||
              excelKeyArr[j] === "father_birthday" ||
              excelKeyArr[j] === "mather_birthday"
            ) {
              arr.push(
                this.$tools.timestampToTime(data._source[excelKeyArr[j]])
              );
            } else {
              arr.push(data._source[excelKeyArr[j]]);
            }
          } else {
            arr.push("暂无");
          }
        }
        excelDataArr.push(arr);
      }
      console.log("处理后的数据", excelDataArr);
      // 假设你有一个表格数据的数组
      /* const data = [
        ["姓名", "年龄", "职业"],
        ["Alice", 28, "Engineer"],
        ["Bob", 22, "Designer"]
      ]; */

      // 将数据转换为工作表
      const worksheet = XLSX.utils.aoa_to_sheet(excelDataArr);
      // 设置列宽
      let ws_cols = [];
      for (let i = 0; i < excelHeaderArr.length; i++) {
        ws_cols.push({ wpx: 100 });
      }
      console.log("ws_cols", ws_cols);
      worksheet["!cols"] = ws_cols;
      // 创建工作簿并添加工作表
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });

      // 使用blob和FileReader创建一个Blob URL
      const dataBlob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
      });
      const blobUrl = window.URL.createObjectURL(dataBlob);

      // 使用saveAs下载文件
      saveAs(
        dataBlob,
        new Date().toLocaleDateString("zh-CN") + "Facebook用户.xlsx"
      );

      // 清理
      window.URL.revokeObjectURL(blobUrl);
    },
    morefn(v) {
      this.$store.dispatch("search/twLinFacSearch/getDataDetailBaseData", v);
    },
    videoFn(v, event) {
      document.getElementById(v).playbackRate = event.target.value;
    },
    pdfFn() {
      this.$store.commit("search/twLinFacSearch/setPdf", true);
      document.getElementById("pdfBox").style.cssText =
        "height:600px;overflow: auto;";
      this.pdferror = false;
      this.numPages = [];
      if (detailObj.baseData._source.hasOwnProperty("content_pdf")) {
        detailObj.baseData._source.content_pdf.forEach((element, index) => {
          let loadingTask = pdf.createLoadingTask(
            "/filesystem/api/rest/v1/big_file/get_sha512_file/content_pdf/" +
              element.sha512_hash +
              "?session_id=" +
              this.$store.state.userInfo.session_id
          );
          loadingTask.promise
            .then((pdf) => {
              this.numPages.push(pdf.numPages);
            })
            .catch((err) => {
              document.getElementById("pdfBox").style.cssText =
                "height:40px;overflow: auto;";
              this.pdferror = true;
            });
        });
      }
    },
    //设置详情遮罩层
    handleClose() {
      this.$store.commit("search/twLinFacSearch/setdialogVisible", false);
    },
    //选择类型
    typeRadioChange(v) {
      //初始化数据
      this.initialData();

      //加载数据
      this.loadData();
    },
    // 加载数据
    async loadData() {
      if (this.loading || this.noMore) return;
      this.$store.commit("search/twLinFacSearch/setLoading", true);
      let obj = {
        data_range_father_path: "/social_platform/facebook/information",
        data_range_index_prefix: "social_platform_information_prefix_facebook",
      };
      this.$store.commit("search/twLinFacSearch/setSearchType", obj);
      
      try {
        const newData = await this.$store.dispatch(
          "search/twLinFacSearch/getListTrue",
          obj
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        this.loadData();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.hitsLay {
  height: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  position: relative;
  .type {
    height: 42px;
    line-height: 42px;
  }
  /* .item {
    padding: 10px;
    border-bottom: 1px solid #eee;
  } */
  .index-marker {
    text-align: center;
    line-height: 48px;
    background-color: #eee;
  }
  .twitterlistRow {
    display: flex;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
  }
  .twitterlistRowLeft {
    width: 100px;
    text-align: center;
  }
  .twitterlistRowLeft img {
    margin-top: 10px;
    background: #eee;
    border: 2px solid #e6a23c;
    width: 50px;
    height: 50px;
    border-radius: 50%;
  }
  .twitterlistRowRight {
    flex: 1 1;
  }
  .twitterlistRowTop {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
  }
  .twitterlistRowMid {
    margin-top: 10px;
  }
  .twitterlistRowFoot {
    display: flex;
    margin-top: 20px;
  }
  .loading,
  .no-more {
    text-align: center;
    padding: 10px;
    color: #999;
  }
}
.morInfor {
  display: flex;
}
.morInfor .morInfor_l {
  width: 100px;
  padding: 10px;
  display: flex;
  justify-content: center;
}
.morInfor .morInfor_l img {
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.morInfor .morInfor_r {
  width: 80%;
}
.morInfor .morInfor_r .morInfor_r_li {
  display: flex;
  margin-top: 10px;
}
.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_h {
  color: #969696;
}
.morInfor .morInfor_r .morInfor_r_li .morInfor_r_li_c {
  margin-left: 15px;
}
.morInfor .morInfor_r .morInfor_r_box {
  padding-bottom: 10px;
  color: #999;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_t {
  display: block;
  margin-top: 10px;
  color: #80a8e5;
  font-weight: bold;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_m {
  padding-top: 10px;
  padding-bottom: 10px;
  color: #666;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.morInfor .morInfor_r .morInfor_r_box .morInfor_r_box_b .tit {
  font-weight: bold;
}
</style>
