<template>
  <div class="linkedin">
    <el-checkbox
      :indeterminate="isIndeterminate"
      v-model="checkAll"
      @change="handleCheckAllChange"
      v-show="dataList.length"
    >
      <span v-if="!checkedCities.length">全选</span>
      <el-button v-else size="mini" @click="toDelCheckCol"> 删除 </el-button>
    </el-checkbox>
    <div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div v-for="(val, index) in dataList" :key="index">
          <div class="twitterlistRow">
            <label>
              <el-checkbox
                :label="val"
                :key="index"
                style="display: flex; align-items: end; margin-bottom: 5px"
                >{{ " " }}
              </el-checkbox>
            </label>
            <div
              class="twitterlistRowLeft"
              v-if="
                !(val == null) &&
                val.hasOwnProperty('_source') &&
                val._source.hasOwnProperty('icon')
              "
            >
              <img
                :onerror="defaultImg"
                :src="
                  '/filesystem/api/rest/v1/small_file/get_sha512_file/icon/' +
                  val._source.icon[0].sha512_hash +
                  '?session_id=' +
                  $store.state.userInfo.session_id
                "
                :type="val._source.icon[0].icon_type"
              />
            </div>
            <div class="twitterlistRowLeft" v-else>
              <img :src="require('@/assets/images/winter.jpg')" />
            </div>
            <div class="twitterlistRowRight">
              <div class="twitterlistRowTop">
                <div>
                  <b>{{ val._source.nickname[0] }}</b>
                  <span style="margin-left: 10px">{{
                    val._source.user_id
                  }}</span>
                  <span
                    style="margin-left: 10px; color: #999"
                    v-if="val._source.hasOwnProperty('create_timestamp')"
                    >{{
                      $tools.timestampToTime(val._source.create_timestamp)
                    }}</span
                  >
                  <span
                    style="margin-left: 10px; color: #999"
                    v-if="val._source.hasOwnProperty('timestamp')"
                    >{{ $tools.timestampToTime(val._source.timestamp) }}</span
                  >
                </div>
                <div style="margin-right: 10px">
                  <!-- <el-button type="primary" plain size="mini"
                                        @click.native="morefn(val)">详情</el-button> -->
                </div>
              </div>
              <div class="twitterlistRowMid">
                <span v-if="val._source.hasOwnProperty('summary')">{{
                  val._source.summary[0] === ""
                    ? "暂无简介"
                    : val._source.summary[0]
                }}</span>
                <span v-if="val._source.hasOwnProperty('content_article')">{{
                  val._source.content_article === ""
                    ? "暂无内容"
                    : val._source.content_article
                }}</span>
              </div>
              <div class="twitterlistRowFoot">
                <div
                  class="footItem"
                  title="点赞"
                  v-if="val._source.hasOwnProperty('likes_count')"
                  @click="
                    likefn({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                    })
                  "
                >
                  <i class="iconfont icon-dianzan_kuai" style=""></i>
                  <span>{{ val._source.likes_count }}</span>
                </div>
                <div
                  class="footItem"
                  title="粉丝"
                  v-if="val._source.hasOwnProperty('followers_count')"
                  @click="
                    hotFollowersListfn({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                      type: val._source.type,
                    })
                  "
                >
                  <i class="icon-fensi iconfont" style=""></i>
                  <span>{{ val._source.followers_count }}</span>
                </div>
                <div
                  class="footItem"
                  title="关注"
                  v-if="val._source.hasOwnProperty('following_count')"
                  @click="
                    hotFollowingListfn({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                      type: val._source.type,
                    })
                  "
                >
                  <i class="el-icon-view"></i>
                  <span>{{ val._source.following_count }}</span>
                </div>
                <div
                  class="footItem"
                  title="文章"
                  v-if="val._source.hasOwnProperty('article_count')"
                  @click="
                    getTuiWenDatalist({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                      type: val._source.type,
                    })
                  "
                >
                  <i class="el-icon-tickets" style=""></i>
                  <span>{{ val._source.article_count }}</span>
                </div>
                <div
                  class="footItem"
                  title="评论"
                  v-if="val._source.hasOwnProperty('reply_count')"
                  @click="
                    replyfn({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                      type: val._source.type,
                    })
                  "
                >
                  <i class="el-icon-chat-dot-round"></i>

                  <span>{{ val._source.reply_count }}</span>
                </div>
                <div
                  class="footItem"
                  title="转发"
                  v-if="val._source.hasOwnProperty('forward_count')"
                  @click="
                    forwardfn({
                      user_id: val._source.user_id,
                      content_article_id: val._source.hasOwnProperty(
                        'content_article_id'
                      )
                        ? val._source.content_article_id
                        : '',
                      type: val._source.type,
                    })
                  "
                >
                  <i class="el-icon-share"></i>
                  <span>{{ val._source.forward_count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-checkbox-group>
      <div class="no-more">没有更多数据了</div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "linkedin",
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      defaultImg:
        'this.src="' + require("../../../assets/images/winter.jpg") + '"',
    };
  },
  computed: {
    ...mapState({
      dataList: (state) => state.collect.dataList,
      nowCollect: (state) => state.collect.nowCollect,
    }),
    checkedCities: {
      get() {
        return this.$store.state.collect.checkedCities;
      },
      set(val) {
        this.$store.commit("collect/setCheckedCities", val);
      },
    },
  },
  mounted() {
    this.$store.commit("collect/clearDataList");
    setTimeout(() => {
      if (this.nowCollect) {
        this.$store.commit("collect/getOpinionCollect");
      }
    }, 500);
  },
  methods: {
    toDelCheckCol() {
      console.log("checkedCities", this.checkedCities);
      if (this.checkedCities.length) {
        this.$confirm("是否取消选中的收藏列表?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let row = [];
            this.checkedCities.forEach((element) => {
              row.push(element.row);
            });
            this.$store.commit("collect/sendDelFileData", {
              row,
              collCache: this.collCache,
            });
            this.handleCheckedCitiesChange([]);
          })
          .catch((err) => {
            console.log(err);
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      } else {
        this.$message.warning("未选择收藏内容");
      }
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.dataList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.dataList.length;
    },
    // 全导出新闻全选
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.dataList : [];
      this.isIndeterminate = false;
    },
  },
};
</script>

<style scoped lang="scss">
.linkedin {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 15px;
}

.newContent {
  padding: 3px 10px 5px 5px;
  border: 1px solid #ccc;
}

.twitterlistRow {
  display: flex;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  font-size: 14px;
}

.twitterlistRowLeft {
  width: 100px;
  text-align: center;
}

.twitterlistRowLeft img {
  margin-top: 10px;
  background: #eee;
  border: 2px solid #e6a23c;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.twitterlistRowRight {
  flex: 1 1;
}

.twitterlistRowTop {
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
}

.twitterlistRowMid {
  margin-top: 10px;
}

.twitterlistRowFoot {
  display: flex;
  margin-top: 20px;
}

.loading,
.no-more {
  text-align: center;
  padding: 10px;
  color: #999;
}
.footItem {
  margin-right: 10px;
}
</style>
