import Vue from "vue";
import App from "./App";
import store from "./store";
import router from "./router";
import apiRequest from "/src/utils/request/BaseRequest";
import cache from "@/utils/cache";
import { copyToClipboard } from "@/utils/util";
import mavonEditor from "mavon-editor";
import "mavon-editor/dist/css/index.css";
import "@/assets/css/markdown-theme.css";
import robotHelper from "@/utils/robotHelper";
import "./assets/fonts/font_ico/iconfont.css";

import "normalize.css/normalize.css";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import "@/styles/index.scss";
import i18n from "./i18n";
import ViewUIPlus from "view-design";
import "view-design/dist/styles/iview.css";
import { VueMasonryPlugin } from "vue-masonry";
import JsonViewer from "vue-json-viewer";
Vue.use(JsonViewer);

// import '@/icons'
import Vuecookies from "vue-cookies";
import axios from "axios";
import Router from "vue-router";
import tools from "./utils/tools";
import MD5 from "js-md5";
import echarts from "echarts";
import "../node_modules/echarts/map/js/world";
Vue.prototype.$echarts = echarts;
Vue.prototype.$axios = axios;
require("echarts-wordcloud");

//消息系统
import message_system from "message_system";
Vue.use(message_system);

//预警系统
import warning_system from "warning_system";
Vue.use(warning_system);

// 挂载serviceWorker
import register from "certificate-login";
// 传入参数为一个数组，两个回调函数
// 第一个参数是设置fetch请求拦截重新发送的,是一个数组，每个值为fetch请求的前缀值
// 第二个回调函数在收到加密狗推送消息DC_PUSH_WEB后执行的操作
// 第三个回调函数在收到加密狗推送消息Error后自定义的遮罩层，不传入显示默认遮罩层
register(
  [
    "/filesystem/api/rest/v2/node-0/small_file",
    "/filesystem/api/rest/v2/node-0/big_file",
    "/filesystem/api/rest/v2/node-0/main_file",
    "/eml_msg/api/rest/v1",
    "/offline_map/rest/v1",
  ],
  () => {
    if (window.main.$route.path === "/") {
      window.main.$router.push({ name: "aiSeach" }); //主页路由，根据个人项目填写
    }
  }
);

Vue.prototype.$api = apiRequest;
Vue.prototype.$cache = cache;
Vue.prototype.$copy = function (value, mes) {
  if (copyToClipboard(value)) {
    this.$message.success(mes);
  }
};
Vue.use(mavonEditor, { html: true });

// 建立websockt连接
import dcWebsocket from "dc-websocket-jsonrpc";
const installWebsocket = () => {
  dcWebsocket.WebSocketPlugin(
    {
      $main_socket: "/data_analysis_platform/api/ws-jsonrpc/v1",
      $case_socket: "/case/api/ws-jsonrpc/v2",
      $pki_socket: "/pki/api/ws-jsonrpc/v1",
      $fileManage_socket: "/filesystem/api/ws-jsonrpc/v2",
      $emlmsg_socket: "/eml_msg/api/ws-jsonrpc/v1",
      $constant_socket: "/websocket/constant_data/api/ws-jsonrpc/v1",
      $emlmsgs_socket: "/eml_msg/api/ws-jsonrpc/v1?msg",
      $cronjob_socket: "/cronjob/api/ws-jsonrpc/v2",
      $ai_socket: "/ai/api/ws-jsonrpc/v1",
    },
    store,
    (wsName, socketClient) => {
      Vue.prototype[wsName] = socketClient;
    }
  );
};
installWebsocket();

// 组件
Vue.use(ElementUI);
Vue.use(ViewUIPlus);
Vue.use(Vuecookies);
Vue.use(VueMasonryPlugin);
Vue.use(robotHelper);

Vue.prototype.$tools = tools;
Vue.prototype.$md5 = MD5;
Vue.prototype.eventBus = new Vue();

window.main = new Vue({
  el: "#app",
  router,
  store,
  i18n,
  render: (h) => h(App),
  beforeCreate() {
    Vue.prototype.$bus = this;
  },
});

const routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((error) => error);
};
