<template>
  <div class="tree_box">
    <div v-if="dataNode">
      <div v-if="dataNode.subMap">
        <div
          v-for="(item, index) in dataNode.subMap"
          :key="index"
          class="tree_list"
        >
          <component
            v-bind:is="subComponent"
            :data-node="item"
            :addClue="addClue"
          ></component>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "TreeBox",
  props: ["dataNode", "subComponent", "addClue"],
  data() {
    return {};
  },

  components: {
    "children-tree": () => import("./children_tree.vue"),
    "children-tree-sing": () => import("./children_tree_sing.vue"),
  },
  created() {},
  methods: {
    userListFn2(v) {
      this.$emit("userListFn3", v);
    },
  },
};
</script>
<style scoped></style>
