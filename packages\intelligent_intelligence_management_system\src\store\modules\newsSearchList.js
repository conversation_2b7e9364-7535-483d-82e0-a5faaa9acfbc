import Vue from "vue";

export default {
  namespaced: true,
  state: {
    conditions: {
      time_range: "24h",
      time_range_begin: 0,
      time_range_end: 0,
      customTime: "",
    },
    dataListGetter: {},
    dataRangeGetter: {},
    dataRangeDetail: {},
    time_range: "3天",
    use_public: "public",
    queryString: "",
    hasAggs: false,
    mainLoading: false,
    timeLoading: false,
    dataList: [],
    currentPage: 0,
    total: 0,
    isSearch: true,
    readList: [],
    agList: {},
    aggsList: {},
    getRead: false,
    agtype: false,
    add_es_query_conditions: "",
    queryMode: "content_article",
    dataRangeQueryIndexMaxCount: 0,
    from: 0,
    size: 20,
    dataRangeGetter: {},
    total: 0,
    currentPage: 0,
    dataListTotal: 0,
    dataRangeQueryIndexMaxList: [],
    dataRangeQueryIndexMaxCount: 0,
    // 遮罩层定时
    beginBun: 0,
    customTime: "",
    chartUpdaters: {
      timestamp: "drawTimeChart",
      type: "drawTypeChart",
      author_id: "drawAuthChart",
    },
  },
  mutations: {
    addRead(state, index) {
      Vue.set(state.readList, index, true);
    },
    setqueryString(state, data) {
      state.queryString = data;
    },
    setqueryMode(state, data) {
      console.log("setqueryMode", data);

      state.queryMode = data;
    },
    settimeRange(state, data) {
      state.time_range = data;
      console.log("state.time_range", state.time_range, data);
    },
    setnoloadCache(state, data) {
      state.noloadCache = data;
    },
    setTimeLoading(state, data) {
      state.timeLoading = data;
    },
    setFind(state, data) {
      state.isSearch = data ? data : false;
    },
    clearLoadingBun(state) {
      state.beginBun = 0;
    },
    setHasAggs(state, data) {
      state.hasAggs = data;
    },
    setGetRead(state, data) {
      state.getRead = data;
    },
    setMainLoading(state, data) {
      state.mainLoading = data;
    },
    setagType(state) {
      state.agtype = true;
    },
    setAddEsQueryConditions(state, data) {
      console.log("setAddEsQueryConditions", data);
      state.add_es_query_conditions = data;
    },
    clearDataRangeTree(state) {
      state.dataRangeQueryIndexMaxCount = 0;
      state.from = 0;
      state.dataRangeGetter = {};
    },
    clearDateList(state) {
      state.dataList = [];
    },
    setcurrentPage(state, data) {
      state.currentPage = data;
      localStorage.setItem("queryPage", data);
    },
    clearSearchList(state, once) {
      // state.tmpDataListRes = {};
      state.total = 0;
      state.currentPage = 0;
      if (!once) {
        localStorage.setItem("queryPage", 0);
        state.dataListTotal = 0;
      } else {
        state.currentPage = localStorage.getItem("queryPage") * 1;
      }

      state.dataList = [];
      state.from = 0;
      //state.use_public = 'username';
    },
    getReadList(state, data) {
      const sha512 = require("sha512");
      if (
        window.main.$store.state.userInfo.userinfo.authority &&
        window.main.$store.state.userInfo.userinfo.username
      ) {
        let userInfo =
          "u;" +
          window.main.$store.state.userInfo.userinfo.authority +
          ";" +
          window.main.$store.state.userInfo.userinfo.username;
        let rowTit = sha512(userInfo).toString("hex") + ";" + userInfo + ";";
        let newsList = [];
        state.dataList.forEach((e) => {
          newsList.push(rowTit + e._id + e._index);
        });
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.DetailMulti",
          [
            {
              head: {
                size: 20,
                row_key: newsList,
              },
              msg: {
                type: "username",
                table: "already_read",
                prefix: "",
              },
            },
          ],
          "newsSearchList/setReadList"
        );
      }
    },
    setReadList(state, data) {
      let newreadList = [];
      data.forEach((e, index) => {
        if (e.row) {
          newreadList[index] = true;
        } else {
          newreadList[index] = false;
        }
      });
      Vue.set(state, "readList", newreadList);
    },
    setDataRangeGetter(state, dataRangeList) {
      let noaggs = dataRangeList[0].noAggs ? dataRangeList[0].noAggs : false;
      let noLoading = dataRangeList[0].noLoading
        ? dataRangeList[0].noLoading
        : false;
      function* getter(dataRangeList) {
        if (dataRangeList == null || dataRangeList.length == 0) {
          return;
        }
        let indexList = [];
        if (!dataRangeList[0].data_range_type) {
          for (let index = 0; index < dataRangeList.length; index++) {
            indexList.push(dataRangeList[index].data_range_index_name);
          }
        }
        for (let index = 0; index < dataRangeList.length; index++) {
          let dataRangeOne = dataRangeList[index];
          let from = 0;
          while (true) {
            if (!dataRangeOne.data_range_type) {
              state.dataRangeQueryIndexMaxList.push(
                dataRangeOne.data_range_index_name
              );
              state.dataRangeQueryIndexMaxCount += Number(
                dataRangeOne.total_size
              )
                ? Number(dataRangeOne.total_size)
                : 0;

              if (
                state.dataRangeQueryIndexMaxList.length < state.size &&
                state.dataRangeQueryIndexMaxCount < 300 * 1024 * 1024
              ) {
                if (index >= dataRangeList.length - 1) {
                  let tmpDataRangeDetail = {};
                  // 如果没有获取到有效的index表，不要继续查询表里的数据了
                  if (state.dataRangeQueryIndexMaxList.length <= 0) {
                    break;
                  }
                  tmpDataRangeDetail.data_range_index_name =
                    indexList.join(",");
                  window.main.$store.commit(
                    "newsSearchList/setDataListGetter",
                    tmpDataRangeDetail
                  );
                  if (!noaggs) {
                    setTimeout(() => {
                      window.main.$store.commit(
                        "newsSearchList/setDataListGetterAggs",
                        tmpDataRangeDetail
                      );
                    }, 100);
                  }
                  yield { res: dataRangeOne };
                  state.dataRangeQueryIndexMaxList = [];
                  state.dataRangeQueryIndexMaxCount = 0;
                  break;
                } else {
                  break;
                }
              } else {
                let tmpDataRangeDetail = {};
                tmpDataRangeDetail.data_range_index_name = indexList.join(",");
                window.main.$store.commit(
                  "newsSearchList/setDataListGetter",
                  tmpDataRangeDetail
                );
                if (!noaggs) {
                  setTimeout(() => {
                    window.main.$store.commit(
                      "newsSearchList/setDataListGetterAggs",
                      tmpDataRangeDetail
                    );
                  }, 100);
                }
                yield { res: dataRangeOne };
                state.dataRangeQueryIndexMaxList = [];
                state.dataRangeQueryIndexMaxCount = 0;
                break;
              }
            }
            let dataRangeDetail;
            if (
              state.time_range !== "无" &&
              state.time_range !== "自定义时间"
            ) {
              let searchData;
              if (state.time_range === "180天") {
                searchData = 7;
              } else if (state.time_range === "60天") {
                searchData = 3;
              } else {
                searchData = 2;
              }

              switch (state.time_range) {
                case "":
                  state.time_range_end = 0;
                  state.time_range_begin = 0;
                  state.searchList = "";
                  break;
                case "今天":
                  searchData = 1;
                  break;
                case "本月":
                  searchData = 1;
                  break;
                case "24h":
                  searchData = 2;
                  break;
                case "2天":
                  searchData = 2;
                  break;
                case "3天":
                  searchData = 2;
                  break;
                case "7天":
                  searchData = 2;
                  break;
                case "10天":
                  searchData = 2;
                  break;
                case "30天":
                  searchData = 2;
                  break;
                case "60天":
                  searchData = 3;
                  break;
                case "180天":
                  searchData = 7;
                  break;
                case "365天":
                  searchData = 13;
                  break;
              }

              let data = new Date();
              let year = data.getFullYear(); //获取年
              let mon = data.getMonth() + 1; //获取月
              let arry = [];
              for (let i = searchData; i > 0; i--) {
                if (mon <= 0) {
                  year = year - 1;
                  mon = mon + 12;
                }
                if (mon < 10) {
                  mon = "0" + mon;
                }
                arry[i - 1] = year + "" + mon;
                mon = mon - 1;
              }
              let dataStr = "";
              arry.forEach((item) => {
                dataStr += `${item}|`;
              });
              dataStr = dataStr.substring(0, dataStr.length - 1);
              dataRangeDetail = {
                head: {
                  from: from,
                  size: state.size,
                },
                control: {
                  prefix_filter:
                    "^public_sentiment_prefix_" + "(" + dataStr + ")_.?.?.?_",
                  query_type: state.use_public,
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: "public_sentiment_prefix",
                  task_authority: "username",
                },
              };
            } else if (state.time_range == "自定义时间") {
              const startTime = state.customTime[0];
              const endTime = state.customTime[1];
              const statrData = new Date(Math.min(startTime, endTime));
              const endData = new Date(Math.max(startTime, endTime));
              const result = [];
              let currentYear = statrData.getFullYear();
              let currentMonth = statrData.getMonth();

              const endYear = endData.getFullYear();
              const endMonth = endData.getMonth();

              while (
                currentYear < endYear ||
                (currentYear === endYear && currentMonth <= endMonth)
              ) {
                //格式化成YYYY-MM,月份补零
                const formattedMonth = String(currentMonth + 1).padStart(
                  2,
                  "0"
                );
                result.push(`${currentYear}${formattedMonth}`);

                currentMonth++;
                if (currentMonth > 11) {
                  currentYear++;
                  currentMonth = 0;
                }
              }

              dataRangeDetail = {
                head: {
                  from: from,
                  size: state.size,
                },
                control: {
                  prefix_filter:
                    "^public_sentiment_prefix_" +
                    "(" +
                    result.join("|") +
                    ")_.?.?.?_",
                  query_type: state.use_public,
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: "public_sentiment_prefix",
                },
              };
            } else {
              dataRangeDetail = {
                head: {
                  from: from,
                  size: state.size,
                },
                control: {
                  query_type: state.use_public,
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: "public_sentiment_prefix",
                },
              };
            }

            if (window.main.$route.name == 'personDetails' || window.main.$route.name == "oriDetails") {
              dataRangeDetail = {
                head: {
                  from: from,
                  size: state.size,
                },
                control: {
                  prefix_filter: "public_sentiment_prefix",
                  query_type: state.use_public,
                },
                msg: {
                  data_range_father_path: dataRangeOne.data_range_path,
                  data_range_index_prefix: "public_sentiment_prefix",
                },
              };
            }

            state.dataRangeDetail.data_range_father_path =
              dataRangeOne.data_range_path;
            state.dataRangeDetail.data_range_name = "舆情新闻列表";

            window.main.$store.commit("newsSearchList/clearLoadingBun");
            // if (!noLoading) {
            //     window.main.$store.commit("newsSearchList/setLoadingLayer");
            // }
            let nextValue = yield window.main.$main_socket.sendData(
              "Api.Search.DataRange.ListTrue",
              [dataRangeDetail],
              "newsSearchList/setDataRangeTree"
            );
            console.log("nextValue", nextValue);

            if (nextValue == "nextDataRangePage") {
              from = from + state.size;
              continue;
            }
            yield* getter(nextValue);
            if (nextValue == null || nextValue.length < state.size) {
              from = 0;
              break;
            } else {
              from = from + state.size;
            }
          }
        }
        return;
      }
      state.dataRangeGetter = getter(dataRangeList);
      let nextRes = state.dataRangeGetter.next(dataRangeList);
      if (nextRes.value === undefined && nextRes.done) {
        // window.main.$store.commit("newsSearchList/clearLoadingLayer");
      }
    },
    setDataListGetter(state, data) {
      data.task_authority = "username";
      function* getter(data) {
        if (data == null) {
          return;
        }
        while (true) {
          state.conditions.time_range = state.time_range;

          let control = {
            condition: state.conditions,
            sort: [{ timestamp: { order: "desc" } }],
            query_type: state.use_public,
            highlight_fields: ["title", "content_article"],
          };
          console.log(
            "state.queryString",
            state.queryString,
            state.add_es_query_conditions
          );

          if (state.queryString != "" || state.hasAggs) {
            control.add_es_query_conditions = state.add_es_query_conditions;
          }
          let resData = {
            head: {
              from:
                (state.currentPage - 1) * 20 < 0
                  ? 0
                  : (state.currentPage - 1) * 20,
              size: state.size,
            },
            msg: data,
            control: control,
          };
          state.beginBun = 0;
          let clearReadList = [];
          for (let index = 0; index < 20; index++) {
            clearReadList.push(false);
          }
          Vue.set(state, "readList", clearReadList);
          yield window.main.$main_socket.sendData(
            "Api.Search.SearchList.Query",
            [resData],
            "newsSearchList/setDataList"
          );
          state.from += state.size;
          //let nextValue = yield window.main.$main_socket.sendData('Api.Search.SearchList.Query', [resData], 'newsSearchList/setDataListGetter');
        }

        return;
      }

      state.dataListGetter = getter(data);
      let nextRes = state.dataListGetter.next();
      if (nextRes.value === undefined && nextRes.done) {
        // window.main.$store.commit("newsSearchList/clearLoadingLayer");
      }
    },
    setDataListGetterAggs(state, data) {
      data.task_authority = "username";
      function* getter(data) {
        if (data == null) {
          return;
        }
        while (true) {
          state.conditions.time_range = state.time_range;
          let control = {
            condition: state.conditions,
            sort: [{ timestamp: { order: "desc" } }],
            query_type: state.use_public,
            aggs: {
              type: {
                terms: {
                  field: "type",
                },
              },
              timestamp: {
                range: {
                  field: "timestamp",
                  ranges: window.main.$tools.timeRangeArray(),
                },
              },
              author_id: {
                terms: {
                  field: "author_id",
                },
              },
            },
          };
          if (state.queryString != "" || state.hasAggs) {
            control.add_es_query_conditions = state.add_es_query_conditions;
          }
          let resData = {
            head: {
              from:
                (state.currentPage - 1) * 20 < 0
                  ? 0
                  : (state.currentPage - 1) * 20,
              size: state.size,
            },
            msg: data,
            control: control,
          };
          state.beginBun = 0;
          yield window.main.$main_socket.sendData(
            "Api.Search.SearchList.Query",
            [resData],
            "newsSearchList/setAggsList"
          );
        }

        return;
      }

      state.aggsdataListGetter = getter(data);
      let nextRes = state.aggsdataListGetter.next();
    },
    async setDataList(state, v) {
      // window.main.$store.commit("newsSearchList/clearLoadingLayer");
      window.main.$store.commit("newsSearchList/setFind", true);
      // window.main.$store.commit(
      //   "newsSearchList/clearLoadingLayerForceSet"
      // );
      try {
        if (
          v == null ||
          v == "undefined" ||
          !v.hasOwnProperty("hits") ||
          v.hits == null ||
          !v.hits.hasOwnProperty("hits") ||
          v.hits.hits == null
        ) {
          window.main.$store.commit("newsSearchList/setTimeLoading", false);
          state.mainLoading = false;
          state.req = false;
          state.from = 0;
          state.dataListGetter.return();
          state.dataRangeGetter.next("nextDataRangePage");
          return;
        } else {
          state.req = true;
        }
        // state.tmpDataListRes = v;
        // 叠加查询总数
        state.dataListTotal += v.hits.hits.length;

        // 判断并保存已经取下来的数据
        state.dataList = state.dataList.concat(v.hits.hits);
        window.main.$store.commit("newsSearchList/setTimeLoading", false);
        state.mainLoading = false;
        setTimeout(() => {
          window.main.$store.commit("newsSearchList/setnoloadCache", false);
        }, 500);

        if (state.getRead) {
          if (
            window.main.$store.state.userInfo.userinfo.authority &&
            window.main.$store.state.userInfo.userinfo.username
          ) {
            window.main.$store.commit("newsSearchList/getReadList");
          } else {
            setTimeout(() => {
              window.main.$store.commit("newsSearchList/getReadList");
            }, 3000);
          }
          state.getRead = false;
        }

        state.total = v?.hits.total.value;
        window.main.$store.commit("channelNum/setPublicOpinionTotal", state.total);
        // 判断如果还可以取数据，继续取
        // if ((v.hits.total.value == 0) || (v.hits.hits == null) || (v.hits.hits.length == 0) || (state.dataList.length < state.size)) {

        // if (state.dataList.length < state.size) {
        //   state.from = 0;
        //   state.dataListGetter.return();
        //   state.dataRangeGetter.next(
        //     "nextDataRangePage"
        //   );
        //   return;
        // }

        // // 如果是取满size数据了的情况，而且有新数据，强行关闭遮罩层。
        // window.main.$store.commit(
        //   "newsSearchList/clearLoadingLayerForceSet"
        // );
      } catch (err) {
        //throw err;
      }
      // let vl={arr:state.dataList,attr:'timestamp',flag:false}
      // window.main.$store.commit('newsSearchList/sortArr',vl)
    },
    async setDataRangeTree(state, dataRangeList) {
      console.log("dataRangeList", dataRangeList);

      if (dataRangeList == null || dataRangeList.length == 0) {
        let tmpObj = state.dataRangeGetter.next([]);
      } else {
        let tmpObj = state.dataRangeGetter.next(dataRangeList);
      }
    },
    async setAggsList(state, v) {
      state.hasAggs = false;
      //如果是搜索按钮点的搜索，更新右边所有的aggs
      if (state.agtype && v.aggregations) {
        state.aggsList = v.aggregations;
        state.agList = state.aggsList;
        console.log("state.agList", state.agList);
        state.agtype = false;
      } else if (v.aggregations) {
        //如果只是右上角增加过滤条件，只更新echars图，aggs列表数据来源agList不变
        state.aggsList = v.aggregations;
        state.agtype = false;
      }
    },
  },
};
