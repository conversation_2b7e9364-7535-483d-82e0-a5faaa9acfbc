<template>
  <div>
    <div v-if="dataNode">
      <div class="layout">
        <div class="treeItem" @click="showDuanFn(dataNode, $event)">
          <span :class="dataNode.iconStyle"></span>
          <span>{{ dataNode.columnValues.i.case_dir_name }}</span>
          <span
            class="description"
            style="margin-left: 30px; color: #ccc"
          ></span>

          <el-radio
            v-model="radio"
            :label="dataNode.row"
            @change="chooseCaseFn(dataNode.row)"
            v-show="dataNode.iconStyle === 'el-icon-collection' && addClue"
            >选取案件</el-radio
          >
        </div>
        <tree-box
          :data-node="dataNode"
          sub-component="children-tree"
          :addClue="addClue"
        ></tree-box>
      </div>
    </div>

    <!-- 设置案件目录信息 -->
    <el-dialog title="设置案件目录信息" :visible.sync="dialogEditCaseDir">
      <el-form :model="editSubForm" @submit.native.prevent ref="editSubForm">
        <el-form-item
          label="创建人"
          :label-width="formLabelWidth"
          prop="create_name"
        >
          <el-input
            v-model="editSubForm.create_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="创建人邮箱"
          :label-width="formLabelWidth"
          prop="create_email"
        >
          <el-input
            v-model="editSubForm.create_email"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="相关目标人"
          :label-width="formLabelWidth"
          prop="target_person"
        >
          <el-input
            v-model="editSubForm.target_person"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="相关事件"
          :label-width="formLabelWidth"
          prop="target_event"
        >
          <el-input
            v-model="editSubForm.target_event"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="remarks">
          <el-input v-model="editSubForm.remarks" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditCaseDir = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editBtnFn('editSubForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog title="添加文件夹" :visible.sync="addFilesDialogVisible">
      <el-form
        :model="addFilesForm"
        @submit.native.prevent
        :rules="addFilesFormRules"
        ref="addFilesForm"
      >
        <el-form-item
          label="类型："
          :label-width="formLabelWidth"
          prop="file_type"
        >
          <el-radio-group v-model="addFilesForm.file_type">
            <el-radio :label="'username'">username</el-radio>
            <el-radio :label="'authority'">authority</el-radio>
            <el-radio :label="'public'">public</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="名称："
          :label-width="formLabelWidth"
          prop="file_name"
        >
          <el-input
            v-model="addFilesForm.file_name"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addFilesDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="addFilesBtnFn('addFilesForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="文件详情"
      :visible.sync="handleMoreDialogVisible"
      width="30%"
    >
      <div class="listMoreBOx">
        <div
          class="listMoreRow"
          v-for="(v, k, ind) in $store.state.caseManage.fileMoreData"
          :key="ind"
        >
          <div class="listMoreRowL">{{ k }}</div>
          <div class="listMoreRowR">{{ v }}</div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="重命名" :visible.sync="renameDialog">
      <el-form
        :model="renameForm"
        @submit.native.prevent
        :rules="renameFormRules"
        ref="renameForm"
      >
        <el-form-item
          label="新名字"
          :label-width="formLabelWidth"
          prop="rename"
        >
          <el-input v-model="renameForm.rename" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="renameDialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="reNameBtnFn('renameForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog :title="listName" :visible.sync="caseDialogVisible">
      <div class="dialogTit">
        <!-- <el-button @click="dialogAddCase = true" size="mini" type="primary" v-if="listName==='案件'">添加</el-button> -->
        <el-button
          @click="dialogAddPerson = true"
          size="mini"
          type="primary"
          v-if="listName === '关键人'"
          >添加</el-button
        >
        <el-button
          @click="dialogAddOrganization = true"
          size="mini"
          type="primary"
          v-if="listName === '关键组织'"
          >添加</el-button
        >
        <el-button
          @click="dialogAddClue = true"
          size="mini"
          type="primary"
          v-if="listName === '关键线索'"
          >添加</el-button
        >
        <div style="display: flex">
          <el-form :model="tagSearch" @submit.native.prevent ref="tagSearch">
            <el-input
              placeholder="请输入标签,以‘，’逗号分隔"
              v-model="tagSearch.tagVal"
              class="input-with-select"
              size="mini"
              style="width: 310px"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                size="mini"
                native-type="submit"
                @click="tagSearchFn"
              ></el-button>
            </el-input>
          </el-form>
          <el-button @click="returnHomeFn" size="mini" type="primary"
            >返回首页</el-button
          >
        </div>
      </div>
      <div
        class="relatedRow"
        v-for="(item, index) in $store.state.caseManage.caseList"
        :key="index"
      >
        <div
          class="rowItem"
          v-for="(val, key, i) in item"
          :key="i"
          v-show="
            key !== 'case_id' &&
            key !== 'case_dir_id' &&
            key !== 'key_person_id' &&
            key !== 'key_organization_id' &&
            key !== 'key_clue_id' &&
            key !== 'tags' &&
            key !== 'organization_group' &&
            key !== 'clue_content'
          "
        >
          <div class="rowItemT">{{ $t("caseManage." + key) }}</div>
          <div class="rowItemB">
            {{
              key === "create_timestamp" ||
              key === "update_timestamp" ||
              key === "birthday" ||
              key === "timestamp" ||
              key == "@timestamp" ||
              key == "create_timestamp" ||
              key == "create_time" ||
              key == "timestamp" ||
              key == "update_time" ||
              key == "birthday" ||
              key == "father_birthday" ||
              key == "mather_birthday" ||
              key == "create_time_str"
                ? $tools.timestampToTime(val)
                : val
            }}
          </div>
        </div>
        <div class="rowItem" v-if="item.hasOwnProperty('tags')">
          <div class="rowItemT">标签</div>
          <div class="rowItemB">
            <span
              v-for="(itemTag, ind) in item.tags.split(',')"
              :key="ind"
              class="tagsSty"
              title="删除"
              @click="caseHandleTagDel(item, itemTag)"
              >{{ itemTag }}</span
            >
          </div>
        </div>
        <div class="operation">
          <el-button size="mini" @click="caseHandleEdit(item)">编辑</el-button>
          <el-button size="mini" @click="listMorefn(item)">详情</el-button>
          <el-button size="mini" type="danger" @click="caseHandleDelete(item)"
            >删除</el-button
          >
          <el-dropdown trigger="click" style="margin-left: 10px">
            <span class="el-dropdown-link">
              下拉菜单<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native="relatedCaseFn(item, '相关案件', listName)"
                >相关案件</el-dropdown-item
              >
              <el-dropdown-item
                @click.native="relatedPersonFn(item, '相关联系人', listName)"
                >相关联系人</el-dropdown-item
              >
              <el-dropdown-item
                @click.native="
                  relatedOrganizationFn(item, '相关组织', listName)
                "
                >相关组织</el-dropdown-item
              >
              <el-dropdown-item
                @click.native="relatedClueFn(item, '相关线索', listName)"
                >相关线索</el-dropdown-item
              >
              <el-dropdown-item
                @click.native="tagsFn(item, '添加标签', listName)"
                >添加标签</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <div class="block" style="margin-top: 10px; text-align: center">
        <el-pagination
          :page-size="10"
          @current-change="caseListPageChange"
          background
          :current-page.sync="caseCurrentPage"
          layout="prev, pager, next"
          :total="$store.state.caseManage.caseCount"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog title="添加" :visible.sync="dialogAddCase">
      <el-form
        :model="addCaseForm"
        @submit.native.prevent
        :rules="addCaseFormRulesaaa"
        ref="addCaseForm"
      >
        <el-form-item
          label="案件名称"
          :label-width="formLabelWidth"
          prop="case_name"
        >
          <el-input
            v-model="addCaseForm.case_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="案件描述"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input v-model="addCaseForm.summary" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddCase = false">取 消</el-button>
        <el-button
          type="primary"
          @click="addCaseBtnFn('addCaseForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="添加" :visible.sync="dialogAddPerson" width="50%">
      <el-form
        :model="addPersonForm"
        @submit.native.prevent
        :rules="addPersonFormRules"
        ref="addPersonForm"
      >
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addPersonForm.fullname"
          :label="'关键人名称' + index + 1"
          :key="domain.key"
          :prop="'fullname.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键人名称',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="removeDomain(domain, 'addPersonForm', 'fullname')"
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addPersonForm.nickname"
          :label="'关键人昵称' + index + 1"
          :key="domain.key"
          :prop="'nickname.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键人昵称',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="removeDomain(domain, 'addPersonForm', 'nickname')"
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          label="性别"
          :label-width="formLabelWidth"
          prop="identity_number"
        >
          <el-radio v-model="addPersonForm.sex" label="m">男</el-radio>
          <el-radio v-model="addPersonForm.sex" label="f">女</el-radio>
        </el-form-item>
        <el-form-item
          label="身份证"
          :label-width="formLabelWidth"
          prop="identity_number"
        >
          <el-input
            v-model="addPersonForm.identity_number"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="出生年月日"
          :label-width="formLabelWidth"
          prop="birthday"
        >
          <el-date-picker
            v-model="addPersonForm.birthday"
            type="date"
            placeholder="选择日期"
            format="yyyy 年 MM 月 dd 日"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="地址" :label-width="formLabelWidth" prop="address">
          <el-input
            v-model="addPersonForm.address"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addPersonForm.telephone"
          :label="'关键人电话' + index + 1"
          :key="domain.key"
          :prop="'telephone.' + index + '.value'"
          :rules="[
            {
              required: true,
              message: '请输入关键人电话',
              trigger: 'blur',
            },
            {
              pattern:
                /^(0[0-9]{2,3})?([2-9][0-9]{6,7})+([0-9]{1,4})?$|^1[3|4|5|7|8][0-9]{9}$/,
              message: '请输入正确得手机号',
            },
          ]"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="
                removeDomain(domain, 'addPersonForm', 'telephone')
              "
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addPersonForm.summary"
          :label="'关键人描述' + index + 1"
          :key="domain.key"
          :prop="'summary.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键人描述',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="removeDomain(domain, 'addPersonForm', 'summary')"
              >删除</el-button
            >
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddPerson = false">取 消</el-button>
        <el-button @click="addPersonDomain('summary')"
          >新增关键人描述</el-button
        >
        <el-button @click="addPersonDomain('fullname')"
          >新增关键人名称</el-button
        >
        <el-button @click="addPersonDomain('nickname')"
          >新增关键人昵称</el-button
        >
        <el-button @click="addPersonDomain('telephone')"
          >新增关键人电话</el-button
        >
        <el-button
          type="primary"
          @click="addPersonBtnFn('addPersonForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="添加关键组织"
      :visible.sync="dialogAddOrganization"
      width="50%"
    >
      <el-form
        :model="addOrganizationForm"
        @submit.native.prevent
        :rules="addOrganizationFormRules"
        ref="addOrganizationForm"
      >
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addOrganizationForm.organization_name"
          :label="'关键组织名称' + index + 1"
          :key="domain.key"
          :prop="'organization_name.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键组织名称',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="
                removeDomain(domain, 'addOrganizationForm', 'organization_name')
              "
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addOrganizationForm.summary"
          :label="'描述' + index + 1"
          :key="domain.key"
          :prop="'summary.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键组织描述',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="
                removeDomain(domain, 'addOrganizationForm', 'summary')
              "
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addOrganizationForm.organization_website"
          :label="'网址' + index + 1"
          :key="domain.key"
          :prop="'organization_website.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入关键组织网址',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input v-model="domain.value"></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="
                removeDomain(
                  domain,
                  'addOrganizationForm',
                  'organization_website'
                )
              "
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          v-for="(domain, index) in addOrganizationForm.organization_group"
          :label="'组织群' + index + 1"
          :key="domain.key"
        >
          <div style="display: flex">
            <el-input v-model="domain.type" placeholder="type"></el-input
            ><el-input
              v-model="domain.group_name"
              placeholder="group_name"
            ></el-input
            ><el-input
              v-model="domain.group_nickname"
              placeholder="group_nickname"
            ></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="
                removeDomain(
                  domain,
                  'addOrganizationForm',
                  'organization_group'
                )
              "
              >删除</el-button
            >
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddOrganization = false">取 消</el-button>
        <el-button @click="addOrganizationDomain('organization_name')"
          >新增组织名称</el-button
        >
        <el-button @click="addOrganizationDomain('summary')"
          >新增描述</el-button
        >
        <el-button @click="addOrganizationDomain('organization_website')"
          >新增网址</el-button
        >
        <el-button @click="addOrganizationDomain('organization_group')"
          >新增组织群</el-button
        >
        <el-button
          type="primary"
          @click="addOrganizationBtnFn('addOrganizationForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="添加关键线索" :visible.sync="dialogAddClue">
      <el-form
        :model="addClueForm"
        @submit.native.prevent
        :rules="addClueFormRules"
        ref="addClueForm"
      >
        <el-form-item
          label="关键线索描述"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input v-model="addClueForm.summary" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddClue = false">取 消</el-button>
        <el-button
          type="primary"
          @click="addClueBtnFn('addClueForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="标签" :visible.sync="dialogTagCase">
      <el-form
        :model="tagCaseForm"
        @submit.native.prevent
        :rules="tagCaseFormRules"
        ref="tagCaseForm"
      >
        <el-form-item label="标签" :label-width="formLabelWidth" prop="tags">
          <el-input v-model="tagCaseForm.tags" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTagCase = false">取 消</el-button>
        <el-button
          type="primary"
          @click="tagCaseBtnFn('tagCaseForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="dialogEditCase">
      <el-form
        :model="editCaseForm"
        @submit.native.prevent
        :rules="editCaseFormRules"
        ref="editCaseForm"
      >
        <el-form-item
          label="案件名称"
          :label-width="formLabelWidth"
          prop="case_name"
        >
          <el-input
            v-model="editCaseForm.case_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="创建人"
          :label-width="formLabelWidth"
          prop="create_name"
        >
          <el-input
            v-model="editCaseForm.map.create_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="创建人邮箱"
          :label-width="formLabelWidth"
          prop="create_email"
        >
          <el-input
            v-model="editCaseForm.map.create_email"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="相关目标人"
          :label-width="formLabelWidth"
          prop="target_person"
        >
          <el-input
            v-model="editCaseForm.map.target_person"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="相关事件"
          :label-width="formLabelWidth"
          prop="target_event"
        >
          <el-input
            v-model="editCaseForm.map.target_event"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="remarks">
          <el-input
            v-model="editCaseForm.map.remarks"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditCase = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editCaseBtnFn('editCaseForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="dialogEditPerson">
      <el-form
        :model="editPersonForm"
        @submit.native.prevent
        :rules="editPersonFormRules"
        ref="editPersonForm"
      >
        <el-form-item
          label="关键人描述"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input
            v-model="editPersonForm.summary"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditPerson = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editCaseBtnFn('editPersonForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="dialogEditOrganization">
      <el-form
        :model="editOrganizationForm"
        @submit.native.prevent
        :rules="editOrganizationFormRules"
        ref="editOrganizationForm"
      >
        <el-form-item
          label="关键组织名称"
          :label-width="formLabelWidth"
          prop="organization_name"
        >
          <el-input
            v-model="editOrganizationForm.organization_name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="关键组织描述"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input
            v-model="editOrganizationForm.summary"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditOrganization = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editCaseBtnFn('editOrganizationForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="dialogEditClue">
      <el-form
        :model="editClueForm"
        @submit.native.prevent
        :rules="editClueFormRules"
        ref="editClueForm"
      >
        <el-form-item
          label="关键线索描述"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input
            v-model="editClueForm.summary"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditClue = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editCaseBtnFn('editClueForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="dialogMoreCase" width="50%">
      <div class="listMoreBOx">
        <div
          class="listMoreRow"
          v-for="(v, k, index) in moreDialogData"
          :key="index"
        >
          <div class="listMoreRowL">{{ $t("caseManage." + k) }}:</div>
          <div
            class="listMoreRowR"
            v-if="k !== 'organization_group' && k !== 'clue_content'"
          >
            {{
              key === "create_timestamp" ||
              key === "update_timestamp" ||
              key === "birthday" ||
              key === "timestamp" ||
              key == "@timestamp" ||
              key == "create_timestamp" ||
              key == "create_time" ||
              key == "timestamp" ||
              key == "update_time" ||
              key == "birthday" ||
              key == "father_birthday" ||
              key == "mather_birthday" ||
              key == "create_time_str"
                ? $tools.timestampToTime(v)
                : v
            }}
          </div>
          <div class="listMoreRowR" v-if="k === 'organization_group'">
            <div style="border: 1px solid #ccc">
              <div class="mayBox" v-for="(item, ind) in v" :key="ind">
                <div
                  class="mayRow"
                  v-for="(val, key, inds) in item"
                  :key="inds"
                >
                  <div class="mayTop">{{ $t("caseManage." + key) }}</div>
                  <div class="mayBom">
                    {{
                      key === "create_timestamp" ||
                      key === "update_timestamp" ||
                      key === "birthday" ||
                      key === "timestamp" ||
                      key == "@timestamp" ||
                      key == "create_timestamp" ||
                      key == "create_time" ||
                      key == "timestamp" ||
                      key == "update_time" ||
                      key == "birthday" ||
                      key == "father_birthday" ||
                      key == "mather_birthday" ||
                      key == "create_time_str"
                        ? $tools.timestampToTime(val)
                        : val
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="listMoreRowR" v-if="k === 'clue_content'">
            <div style="border: 1px solid #ccc">
              <div
                v-if="v['_source'].hasOwnProperty('icon')"
                style="padding: 10px"
              >
                <div class="mayTop" style="margin-bottom: 10px">
                  <b>头像</b>
                </div>
                <img
                  :src="
                    '/webhdfs/v1/res/icon/' +
                    v['_source']['icon'] +
                    '?op=OPEN&user.name=admin'
                  "
                  :type="v['_source'].icon_type"
                />
              </div>
              <div
                class="mayBox"
                v-for="(val, key, ind) in v._source"
                :key="ind"
              >
                <div class="mayRow">
                  <div class="mayTop">{{ $t("caseManage." + key) }}</div>
                  <div
                    class="mayBom"
                    style="white-space: pre-line; line-height: 20px"
                  >
                    {{
                      key === "create_timestamp" ||
                      key === "update_timestamp" ||
                      key === "birthday" ||
                      key === "timestamp" ||
                      key == "@timestamp" ||
                      key == "create_timestamp" ||
                      key == "create_time" ||
                      key == "timestamp" ||
                      key == "update_time" ||
                      key == "birthday" ||
                      key == "father_birthday" ||
                      key == "mather_birthday" ||
                      key == "create_time_str"
                        ? $tools.timestampToTime(val)
                        : val
                    }}
                  </div>
                </div>
              </div>
              <div
                v-if="v['_source'].hasOwnProperty('content_file')"
                style="padding: 10px"
              >
                <div class="mayTop" style="margin-bottom: 10px">
                  <b>附件</b>
                </div>
                <a
                  style="color: blue"
                  title="点击下载"
                  v-for="(imgItem, imgindex) in v['_source']['content_file']"
                  :key="imgindex"
                  :href="
                    '/webhdfs/v1/res/content_file/' +
                    imgItem +
                    '?op=OPEN&user.name=admin'
                  "
                  :type="v['_source'].content_file_type"
                  :download="v['_source']['content_file_name'][imgindex]"
                  >{{ v["_source"]["content_file_name"][imgindex] }}</a
                >
              </div>

              <div
                v-if="v['_source'].hasOwnProperty('content_img')"
                style="padding: 10px"
              >
                <div class="mayTop" style="margin-bottom: 10px">
                  <b>图片</b>
                </div>
                <div
                  v-for="(imgItem, imgindex) in v['_source']['content_img']"
                  :key="imgindex"
                >
                  <img
                    :src="
                      '/webhdfs/v1/res/content_img/' +
                      imgItem +
                      '?op=OPEN&user.name=admin'
                    "
                    :type="v['_source'].content_img_type"
                    :alt="imgItem ? imgItem : '图片链接为空'"
                  />

                  <p style="margin-left: 10px">
                    {{ v["_source"]["content_file_name"][imgindex] }}
                  </p>
                </div>
              </div>
              <div
                v-if="v['_source'].hasOwnProperty('content_video')"
                style="padding: 10px"
              >
                <div class="mayTop" style="margin-bottom: 10px">
                  <b>视频</b>
                </div>
                <div
                  v-for="(imgItem, imgindex) in v['_source']['content_video']"
                  :key="imgindex"
                >
                  <span v-if="!imgItem">链接为空</span>
                  <video controls="controls">
                    <source
                      :src="
                        '/webhdfs/v1/res/content_video/' +
                        imgItem +
                        '?op=OPEN&user.name=admin'
                      "
                      :type="v['_source'].content_video_type"
                      :alt="imgItem ? imgItem : '链接为空'"
                    />
                  </video>

                  <p style="margin-left: 10px">
                    {{ v["_source"]["content_file_name"][imgindex] }}
                  </p>
                </div>
              </div>
              <div
                v-if="v['_source'].hasOwnProperty('content_voice')"
                style="padding: 10px"
              >
                <div class="mayTop" style="margin-bottom: 10px">
                  <b>音频</b>
                </div>
                <div
                  v-for="(imgItem, imgindex) in v['_source']['content_voice']"
                  :key="imgindex"
                >
                  <audio
                    controls="controls"
                    :type="v['_source'].content_voice_type"
                    :src="
                      '/webhdfs/v1/res/content_voice/' +
                      imgItem +
                      '?op=OPEN&user.name=admin'
                    "
                    :alt="imgItem ? imgItem : '链接为空'"
                  >
                    {{ v["_source"]["content_file_name"][imgindex] }}
                  </audio>
                  <p style="margin-left: 10px">
                    {{ v["_source"]["content_file_name"][imgindex] }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :title="relatedTitle" :visible.sync="relatedCaseDialogVisible">
      <div class="dialogTit">
        <el-button
          @click="addRelatedCaseFn"
          size="mini"
          type="primary"
          v-if="relatedTitle === '相关案件'"
          >添加相关案件</el-button
        >
        <el-button
          @click="addRelatedPersonFn"
          size="mini"
          type="primary"
          v-if="relatedTitle === '相关联系人'"
          >添加相关联系人</el-button
        >
        <el-button
          @click="addRelatedOrganizationFn"
          size="mini"
          type="primary"
          v-if="relatedTitle === '相关组织'"
          >添加相关组织</el-button
        >
        <el-button
          @click="addRelatedClue"
          size="mini"
          type="primary"
          v-if="relatedTitle === '相关线索'"
          >添加相关线索</el-button
        >
      </div>
      <div
        class="relatedRow"
        v-for="(item, index) in $store.state.caseManage.relatedCaseList"
        :key="index"
      >
        <div
          class="rowItem"
          v-for="(val, key, i) in item"
          :key="i"
          v-show="
            key !== 'case_id' &&
            key !== 'case_dir_id' &&
            key !== 'key_person_id' &&
            key !== 'key_organization_id' &&
            key !== 'key_clue_id' &&
            key !== 'tags' &&
            key !== 'organization_group' &&
            key !== 'clue_content'
          "
        >
          <div class="rowItemT">{{ $t("caseManage." + key) }}</div>
          <div class="rowItemB">
            {{
              key === "create_timestamp" ||
              key === "update_timestamp" ||
              key === "birthday" ||
              key === "timestamp" ||
              key == "@timestamp" ||
              key == "create_timestamp" ||
              key == "create_time" ||
              key == "timestamp" ||
              key == "update_time" ||
              key == "birthday" ||
              key == "father_birthday" ||
              key == "mather_birthday" ||
              key == "create_time_str"
                ? $tools.timestampToTime(val)
                : val
            }}
          </div>
        </div>
        <div class="rowItem" v-if="item.hasOwnProperty('tags')">
          <div class="rowItemT">标签</div>
          <div class="rowItemB">
            <span
              v-for="(itemTag, ind) in item.tags.split(',')"
              :key="ind"
              class="tagsSty"
              >{{ itemTag }}</span
            >
          </div>
        </div>
        <div class="operation">
          <el-button size="mini" @click="listMorefn(item)">详情</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="relatedCaseHandleDelete(item)"
            v-if="relatedTitle === '相关案件'"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="relatedPersonHandleDelete(item)"
            v-if="relatedTitle === '相关联系人'"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="relatedOrganizationHandleDelete(item)"
            v-if="relatedTitle === '相关组织'"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="relatedClueHandleDelete(item)"
            v-if="relatedTitle === '相关线索'"
            >删除</el-button
          >
        </div>
      </div>

      <div class="block" style="margin-top: 10px; text-align: center">
        <el-pagination
          :page-size="10"
          @current-change="relatedCaseListPageChange"
          :current-page.sync="relatedCaseCurrentPage"
          background
          layout="prev, pager, next"
          :total="$store.state.caseManage.relatedCaseCount"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog
      :title="'添加' + relatedTitle"
      :visible.sync="dialogAddRelatedCase"
    >
      <div class="dialogTit">
        <el-form
          :model="tagSearch"
          @submit.native.prevent
          ref="tagSearch"
          style="width: 310px"
        >
          <el-input
            placeholder="请输入标签,以‘，’逗号分隔"
            v-model="tagSearch.tagVal"
            class="input-with-select"
            size="mini"
            style="width: 60%"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              size="mini"
              native-type="submit"
              @click="tagSearchAddRelatedCase"
            ></el-button>
            <!-- <el-button slot="append" icon="el-icon-search" size="mini" native-type="submit" @click="tagSearchAddRelatedCase" v-if="relatedTitle==='相关联系人'"></el-button> -->
          </el-input>
        </el-form>
      </div>
      <div
        class="relatedRow"
        v-for="(item, index) in $store.state.caseManage.addRelatedCaseList"
        :key="index"
      >
        <div
          class="rowItem"
          v-for="(val, key, i) in item"
          :key="i"
          v-show="
            key !== 'case_id' &&
            key !== 'case_dir_id' &&
            key !== 'key_person_id' &&
            key !== 'key_organization_id' &&
            key !== 'key_clue_id' &&
            key !== 'tags' &&
            key !== 'organization_group' &&
            key !== 'clue_content'
          "
        >
          <div class="rowItemT">{{ $t("caseManage." + key) }}</div>
          <div class="rowItemB">
            {{
              key === "create_timestamp" ||
              key === "update_timestamp" ||
              key === "birthday" ||
              key === "timestamp" ||
              key == "@timestamp" ||
              key == "create_timestamp" ||
              key == "create_time" ||
              key == "timestamp" ||
              key == "update_time" ||
              key == "birthday" ||
              key == "father_birthday" ||
              key == "mather_birthday" ||
              key == "create_time_str"
                ? $tools.timestampToTime(val)
                : val
            }}
          </div>
        </div>
        <div class="rowItem" v-if="item.hasOwnProperty('tags')">
          <div class="rowItemT">标签</div>
          <div class="rowItemB">
            <span
              v-for="(itemTag, ind) in item.tags.split(',')"
              :key="ind"
              class="tagsSty"
              >{{ itemTag }}</span
            >
          </div>
        </div>
        <div class="operation">
          <el-button size="mini" @click="listMorefn(item)">详情</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="addNowCase(item)"
            v-if="relatedTitle === '相关案件'"
            >添加</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="addNowPerson(item)"
            v-if="relatedTitle === '相关联系人'"
            >添加联系人</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="addNowOrganization(item)"
            v-if="relatedTitle === '相关组织'"
            >添加组织</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="addNowClue(item)"
            v-if="relatedTitle === '相关线索'"
            >添加线索</el-button
          >
        </div>
      </div>

      <div class="block" style="margin-top: 10px; text-align: center">
        <el-pagination
          :page-size="10"
          @current-change="addRelatedCaseListPageChange"
          :current-page.sync="addRelatedCaseCurrentPage"
          background
          layout="prev, pager, next"
          :total="$store.state.caseManage.addRelatedCaseCount"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <!--详情 -->
    <el-dialog title="详情" :visible.sync="dialogMore" width="50%">
      <div class="listMoreBOx" v-if="moreDialog">
        <div
          class="listMoreRow"
          v-for="(v, k, index) in moreDialog.columnValues.i"
          :key="index"
        >
          <div class="listMoreRowL">{{ $t("caseManage." + k) }}:</div>
          <div class="listMoreRowR">
            {{
              k === "create_timestamp" ||
              k === "update_timestamp" ||
              k === "birthday" ||
              k === "@timestamp"
                ? $tools.timestampToTime(v)
                : v
            }}
          </div>
        </div>
        <div
          class="listMoreRow"
          v-for="(v, k, index) in moreDialog.columnValues.p"
          :key="index"
        >
          <div class="listMoreRowL">{{ $t("caseManage." + k) }}:</div>
          <div class="listMoreRowR">
            {{
              k === "create_timestamp" ||
              k === "update_timestamp" ||
              k === "birthday" ||
              k === "@timestamp"
                ? $tools.timestampToTime(v)
                : v
            }}
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 重置案件目录信息 -->
    <el-dialog title="设置案件目录信息" :visible.sync="dialogUnSetCaseDir">
      <el-form :model="unSetSubForm" @submit.native.prevent ref="unSetSubForm">
        <el-form-item
          label="创建人"
          :label-width="formLabelWidth"
          prop="create_name"
        >
          <el-switch
            v-model="unSetSubForm.create_name"
            active-text="true"
            inactive-text="false"
          >
          </el-switch>
        </el-form-item>
        <el-form-item
          label="创建人邮箱"
          :label-width="formLabelWidth"
          prop="create_email"
        >
          <el-switch
            v-model="unSetSubForm.create_email"
            active-text="true"
            inactive-text="false"
          >
          </el-switch>
        </el-form-item>
        <el-form-item
          label="相关目标人"
          :label-width="formLabelWidth"
          prop="target_person"
        >
          <el-switch
            v-model="unSetSubForm.target_person"
            active-text="true"
            inactive-text="false"
          >
          </el-switch>
        </el-form-item>
        <el-form-item
          label="相关事件"
          :label-width="formLabelWidth"
          prop="target_event"
        >
          <el-switch
            v-model="unSetSubForm.target_event"
            active-text="true"
            inactive-text="false"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="remarks">
          <el-switch
            v-model="unSetSubForm.remarks"
            active-text="true"
            inactive-text="false"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogUnSetCaseDir = false">取 消</el-button>
        <el-button
          type="primary"
          @click="unSetBtnFn('unSetSubForm')"
          native-type="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "AuthorityTree",
  props: ["dataNode", "addClue"],
  components: {
    "tree-box": () => import("./tree_box.vue"),
  },
  data() {
    return {
      dialogUnSetCaseDir: false,
      unSetSubForm: {
        case_dir_father_path: "",
        case_dir_name: "",
        create_name: true,
        create_email: true,
        target_person: true,
        target_event: true,
        remarks: true,
      },
      dialogAddRelatedCase: false,
      addRelatedCaseCurrentPage: 1,
      relatedTitle: "",
      relatedCaseCurrentPage: 1,
      related: {},
      related_case: 0,
      dialogTagCase: false,
      relatedCaseDialogVisible: false,
      tagCaseForm: {
        tags: "",
        id: 0,
      },
      tagCaseFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      moreDialogData: null,
      dialogMoreCase: false,
      dialogMore: false,
      dialogEditCase: false,
      editCaseForm: {
        case_name: "",
        row_key: "",
        map: {
          create_name: "",
          create_email: "",
          target_person: "",
          target_event: "",
          remarks: "",
        },
      },
      editCaseFormRules: {
        case_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogEditPerson: false,
      editPersonForm: {
        summary: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
        key_person_id: 0,
      },
      editPersonFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogEditOrganization: false,
      editOrganizationForm: {
        summary: "",
        organization_name: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
        key_organization_id: 0,
      },
      editOrganizationFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        organization_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogEditClue: false,
      editClueForm: {
        summary: "",

        case_dir_id: this.$store.state.caseManage.case_dir_id,
        key_clue_id: 0,
      },
      editClueFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
      },
      tagSearch: { tagVal: "" },
      listName: "",
      caseDialogVisible: false,
      caseCurrentPage: 1,
      dialogAddCase: false,
      addCaseForm: {
        case_name: "",
        summary: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      addCaseFormRulesaaa: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogAddPerson: false,
      addPersonForm: {
        fullname: [{ value: "" }],
        nickname: [{ value: "" }],
        telephone: [{ value: "" }],
        summary: [{ value: "" }],
        identity_number: "41234341245634262345",
        birthday: 1608638743,
        sex: "m",
        address: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      addPersonFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogAddOrganization: false,
      addOrganizationForm: {
        organization_website: [{ value: "" }],
        organization_name: [{ value: "" }],
        summary: [{ value: "" }],
        organization_group: [
          {
            type: "",
            group_name: "",
            group_nickname: "",
            key: Date.now(),
          },
        ],
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      addOrganizationFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        organization_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogAddClue: false,
      addClueForm: {
        summary: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      addClueFormRules: {
        summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogAddCaseDir: false,
      addCaseDirForm: {
        case_dir_name: "",
        case_dir_summary: "",
        case_dir_id: 0,
      },
      addCaseDirFormRules: {
        case_dir_summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_dir_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      renameDialog: false,
      renameForm: {
        rename: "",
        file_name: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      renameFormRules: {
        rename: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
      },
      handleMoreDialogVisible: false,
      addFilesForm: {
        file_path: "",
        file_type: "username",
        file_name: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      addFilesFormRules: {
        file_type: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
        file_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogAddCaseDir: false,
      formLabelWidth: "120px",
      dialogEditCaseDir: false,
      editSubForm: {
        create_name: "",
        create_email: "",
        target_person: "",
        target_event: "",
        remarks: "",
        case_dir_father_path: "",
        case_dir_name: "",
        // case_dir_name:'',
        // case_dir_summary:'',
        // case_dir_id:0,
      },
      editSubFormRules: {
        create_name: [
          {
            required: true,
            message: "请输入创建人",
            trigger: ["blur", "change"],
          },
        ],
        // case_dir_name:[{ required: true,message: '请输入名称', trigger: ['blur','change'] }],
      },
      addCaseDirForm: {
        case_dir_name: "",
        case_dir_summary: "",
        case_dir_id: 0,
      },
      addCaseDirFormRules: {
        case_dir_summary: [
          {
            required: true,
            message: "请输入描述",
            trigger: ["blur", "change"],
          },
        ],
        case_dir_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      file_path: [],
      file_type: "username",
      file_name: "",
      filesListDialogVisible: false,
      uploadDialogVisible: false,
      uploadForm: {
        file_path: "",
        file_type: "username",
        file_name: "",
        case_dir_id: this.$store.state.caseManage.case_dir_id,
      },
      uploadFormRules: {
        file_type: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
        file_name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      addFilesDialogVisible: false,
      filesTypeOptions: [
        {
          value: "username",
          label: "username",
        },
        {
          value: "authority",
          label: "authority",
        },
        {
          value: "public",
          label: "public",
        },
      ],
    };
  },
  created() {
    //  alert(this.addClue)
  },
  computed: {
    moreDialog: {
      get() {
        return this.$store.state.caseManage.Detail;
      },
      set(val) {},
    },
    currentPage1: {
      get() {
        return this.dataNode.total / 2;
      },
      set(val) {},
    },
    radio: {
      get() {
        return this.$store.state.caseManage.addClueChooseCase_id;
      },
      set(val) {},
    },
  },
  methods: {
    // 添加线索时选取案件
    chooseCaseFn(v) {
      this.$store.commit("caseManage/setaddClueChooseCase_id", v);
    },
    /**重置--dialog */
    showResetCaseCatalogue(v, event) {
      this.unSetSubForm.case_dir_father_path =
        v.columnValues.i.case_dir_father_path;
      this.unSetSubForm.case_dir_name = v.columnValues.i.case_dir_name;
      this.dialogUnSetCaseDir = true;
      event.stopPropagation();
    },
    /**提交重置 */
    unSetBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "caseManage/setCaseDirFather",
            this.unSetSubForm.case_dir_father_path
          );
          this.$store.commit("caseManage/sendUnSetCaseInfo", this.unSetSubForm);
          this.dialogUnSetCaseDir = false;
        }
      });
    },
    /**案件和案件目录详情获取 */
    moreFn(v, event) {
      if (v.iconStyle === "el-icon-collection") {
        this.$store.commit("caseManage/sendCaseDetail", v);
      } else {
        this.$store.commit("caseManage/sendCaseDirDetail", v);
      }

      this.dialogMore = true;
      event.stopPropagation();
    },

    /**添加案件--dialog */
    showAddCaseListDialog(v) {
      this.dialogAddCase = true;
      if (v.columnValues.i.case_dir_father_path.case_dir_father_path === "") {
        this.addCaseForm.case_dir_father_path = "/";
      } else if (v.columnValues.i.case_dir_father_path === "/") {
        this.addCaseForm.case_dir_father_path =
          v.columnValues.i.case_dir_father_path +
          v.columnValues.i.case_dir_name;
      } else {
        this.addCaseForm.case_dir_father_path =
          v.columnValues.i.case_dir_father_path +
          "/" +
          v.columnValues.i.case_dir_name;
      }
    },
    /**提交添加案件 */
    addCaseBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "caseManage/setCaseDirFather",
            this.addCaseForm.case_dir_father_path
          );
          this.$store.commit("caseManage/sendCaseAdd", {
            case_dir_father_path: this.addCaseForm.case_dir_father_path,
            case_name: this.addCaseForm.case_name,
            summary: this.addCaseForm.summary,
          });
          this.dialogAddCase = false;
        }
      });
    },
    keyClue(dataNode, event) {
      this.caseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/setCaseList", []);
      this.listName = "关键线索";
      this.$store.commit("caseManage/setListName", this.listName);
      this.$store.commit("caseManage/setCaseId", dataNode.row);
      //  this.$store.commit('caseManage/setaddhistories',dataNode)
      let case_dir_id = dataNode.case_dir_id;
      this.$store.commit("caseManage/setCaseDirId", Number(case_dir_id));
      this.$store.commit("caseManage/sendPersonList", dataNode);
      // this.caseDialogVisible=true;
      event.stopPropagation();
    },
    keyOrganization(dataNode, event) {
      this.caseCurrentPage = 1;

      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/setCaseList", []);
      this.listName = "关键组织";
      this.$store.commit("caseManage/setListName", this.listName);
      this.$store.commit("caseManage/setCaseId", dataNode.row);
      //  this.$store.commit('caseManage/setaddhistories',dataNode)
      let case_dir_id = dataNode.case_dir_id;
      this.$store.commit("caseManage/setCaseDirId", Number(case_dir_id));
      this.$store.commit("caseManage/sendPersonList", dataNode);
      // this.$store.commit('caseManage/sendOrganizationList')
      // this.$store.commit('caseManage/sendOrganizationCount')
      // this.caseDialogVisible=true;
      event.stopPropagation();
    },
    removeDomain(item, type, key) {
      var index = this[type][key].indexOf(item);
      if (index !== -1) {
        this[type][key].splice(index, 1);
      }
    },

    tagCaseBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit("caseManage/sendCaseAddTag", {
            tags: this.tagCaseForm.tags,
            id: this.tagCaseForm.id,
            listName: this.listName,
          });
          this.dialogTagCase = false;
        } else {
          return false;
        }
      });
    },
    relatedClueHandleDelete(v) {
      this.$store.commit("caseManage/relatedClueHandleDelete", {
        related_case: this.related_case,
        key_clue_id: v.key_clue_id,
      });
    },
    relatedOrganizationHandleDelete(v) {
      this.$store.commit("caseManage/relatedOrganizationHandleDelete", {
        related_case: this.related_case,
        key_organization_id: v.key_organization_id,
      });
    },
    relatedPersonHandleDelete(v) {
      this.$store.commit("caseManage/relatedPersonHandleDelete", {
        related_case: this.related_case,
        key_person_id: v.key_person_id,
      });
    },
    addRelatedClue() {
      this.addRelatedCaseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/sendAddRelatedClueList");
      this.$store.commit("caseManage/sendAddRelatedClueCount");
      this.dialogAddRelatedCase = true;
    },
    addRelatedOrganizationFn() {
      this.addRelatedCaseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/sendAddRelatedOrganizationList");
      this.$store.commit("caseManage/sendAddRelatedOrganizationCount");
      this.dialogAddRelatedCase = true;
    },
    addRelatedPersonFn() {
      this.addRelatedCaseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/sendAddRelatedPersonList");
      this.$store.commit("caseManage/sendAddRelatedPersonCount");
      this.dialogAddRelatedCase = true;
    },
    relatedCaseHandleDelete(v) {
      this.$store.commit("caseManage/relatedCaseHandleDelete", {
        related_case: this.related_case,
        case_id: v.case_id,
      });
    },
    addRelatedCaseListPageChange(val) {
      let related = this.related;
      let obj = { page: val, ...related };
      this.$store.commit("caseManage/sendPageAddRelatedList", obj);
    },
    tagSearchAddRelatedCase() {
      this.addRelatedCaseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", {
        tags: this.tagSearch.tagVal.split(","),
      });
      this.$store.commit("caseManage/tagSearchAddRelatedCase", {
        tags: this.tagSearch.tagVal.split(","),
        listName: this.listName,
      });
      this.tagSearch.tagVal = "";
    },
    relatedCaseListPageChange(v) {
      let related = this.related;
      this.$store.commit("caseManage/setNowRelatedCasePage", v);
      this.$store.commit("caseManage/sendPageRelatedCase", {
        page: v,
        ...related,
      });
    },
    addNowPerson(v) {
      let obj = new Object();
      if (this.listName === "案件") {
        obj = {
          related_case: this.related_case,
          key_person_id: v.key_person_id,
        };
      }
      if (this.listName === "关键人") {
        obj = {
          related_person: this.related_case,
          key_person_id: v.key_person_id,
        };
      }
      if (this.listName === "关键组织") {
        obj = {
          related_organization: this.related_case,
          key_person_id: v.key_person_id,
        };
      }
      if (this.listName === "关键线索") {
        obj = {
          related_clue: this.related_case,
          key_person_id: v.key_person_id,
        };
      }
      this.$store.commit("caseManage/sendAddRelatedPerson", obj);
    },
    addNowOrganization(v) {
      let obj = new Object();
      if (this.listName === "案件") {
        obj = {
          related_case: this.related_case,
          key_organization_id: v.key_organization_id,
        };
      }
      if (this.listName === "关键人") {
        obj = {
          related_person: this.related_case,
          key_organization_id: v.key_organization_id,
        };
      }
      if (this.listName === "关键组织") {
        obj = {
          related_organization: this.related_case,
          key_organization_id: v.key_organization_id,
        };
      }
      if (this.listName === "关键线索") {
        obj = {
          related_clue: this.related_case,
          key_organization_id: v.key_organization_id,
        };
      }
      this.$store.commit("caseManage/sendAddRelatedOrganization", obj);
    },
    addNowClue(v) {
      let obj = new Object();
      if (this.listName === "案件") {
        obj = { related_case: this.related_case, key_clue_id: v.key_clue_id };
      }
      if (this.listName === "关键人") {
        obj = { related_person: this.related_case, key_clue_id: v.key_clue_id };
      }
      if (this.listName === "关键组织") {
        obj = {
          related_organization: this.related_case,
          key_clue_id: v.key_clue_id,
        };
      }
      if (this.listName === "关键线索") {
        obj = { related_clue: this.related_case, key_clue_id: v.key_clue_id };
      }
      this.$store.commit("caseManage/sendAddRelatedClue", obj);
    },
    addNowCase(v) {
      let obj = new Object();
      if (this.listName === "案件") {
        obj = { related_case: this.related_case, case_id: v.case_id };
      }
      if (this.listName === "关键人") {
        obj = { related_person: this.related_case, case_id: v.case_id };
      }
      if (this.listName === "关键组织") {
        obj = { related_organization: this.related_case, case_id: v.case_id };
      }
      if (this.listName === "关键线索") {
        obj = { related_clue: this.related_case, case_id: v.case_id };
      }

      this.$store.commit("caseManage/sendAddRelatedCase", obj);
    },
    addRelatedCaseFn() {
      this.addRelatedCaseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/sendAddRelatedCaseList");
      this.$store.commit("caseManage/sendAddRelatedCaseCount");
      this.dialogAddRelatedCase = true;
    },
    editCaseBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          /**提交案件编辑 */

          this.$store.commit("caseManage/sendCaseEdit", this.editCaseForm);
          let tmp = {
            row_key: this.editCaseForm.row_key,
            case: this.editCaseForm.map,
          };
          this.$store.commit("caseManage/sendInfoCase", tmp);
          let obj = {};
          for (let str in this.editCaseForm.map) {
            if (this.editCaseForm.map[str] === "") {
              obj[str] = "";
            }
          }
          this.$store.commit("caseManage/sendUnSetCaseInfo", {
            caseHead: tmp,
            msg: obj,
          });
          this.dialogEditCase = false;
        } else {
          return false;
        }
      });
    },
    relatedPersonFn(v, name, listName) {
      this.relatedTitle = name;
      let obj = new Object();
      this.$store.commit("caseManage/setRelatedTitle", name);
      this.relatedCaseCurrentPage = 1;
      this.related = { related_person: v.key_person_id };
      if (listName === "案件") {
        obj = { related_case: v.case_id };
        this.related_case = v.case_id;
      }
      if (listName === "关键人") {
        obj = { related_person: v.key_person_id };
        this.related_case = v.key_person_id;
      }
      if (listName === "关键组织") {
        obj = { related_organization: v.key_organization_id };
        this.related_case = v.key_organization_id;
      }
      if (listName === "关键线索") {
        obj = { related_clue: v.key_clue_id };
        this.related_case = v.key_clue_id;
      }
      this.$store.commit("caseManage/sendRelatedPerson", obj);
      this.relatedCaseDialogVisible = true;
    },
    relatedOrganizationFn(v, name, listName) {
      this.relatedTitle = name;
      let obj = new Object();
      this.$store.commit("caseManage/setRelatedTitle", name);
      this.relatedCaseCurrentPage = 1;
      this.related = { related_organization: v.key_organization_id };
      if (listName === "案件") {
        obj = { related_case: v.case_id };
        this.related_case = v.case_id;
      }
      if (listName === "关键人") {
        obj = { related_person: v.key_person_id };
        this.related_case = v.key_person_id;
      }
      if (listName === "关键组织") {
        obj = { related_organization: v.key_organization_id };
        this.related_case = v.key_organization_id;
      }
      if (listName === "关键线索") {
        obj = { related_clue: v.key_clue_id };
        this.related_case = v.key_clue_id;
      }

      this.$store.commit("caseManage/sendRelatedOrganization", obj);
      this.relatedCaseDialogVisible = true;
    },
    relatedClueFn(v, name, listName) {
      this.relatedTitle = name;
      this.$store.commit("caseManage/setRelatedTitle", name);
      this.relatedCaseCurrentPage = 1;
      let obj = new Object();
      this.related = { related_clue: v.key_clue_id };
      if (listName === "案件") {
        obj = { related_case: v.case_id };
        this.related_case = v.case_id;
      }
      if (listName === "关键人") {
        obj = { related_person: v.key_person_id };
        this.related_case = v.key_person_id;
      }
      if (listName === "关键组织") {
        obj = { related_organization: v.key_organization_id };
        this.related_case = v.key_organization_id;
      }
      if (listName === "关键线索") {
        obj = { related_clue: v.key_clue_id };
        this.related_case = v.key_clue_id;
      }
      this.$store.commit("caseManage/sendRelatedClue", obj);
      this.relatedCaseDialogVisible = true;
    },
    tagsFn(v, name, listName) {
      if (this.listName === "案件") {
        this.tagCaseForm.id = v.case_id;
      }
      if (this.listName === "关键人") {
        this.tagCaseForm.id = v.key_person_id;
      }
      if (this.listName === "关键组织") {
        this.tagCaseForm.id = v.key_organization_id;
      }
      if (this.listName === "关键线索") {
        this.tagCaseForm.id = v.key_clue_id;
      }
      this.dialogTagCase = true;
    },
    relatedCaseFn(v, name, listName) {
      this.relatedTitle = name;
      this.$store.commit("caseManage/setRelatedTitle", name);
      this.relatedCaseCurrentPage = 1;
      let obj = new Object();
      this.related = { related_case: v.case_id };
      if (listName === "案件") {
        obj = { related_case: v.case_id };
        this.related_case = v.case_id;
      }
      if (listName === "关键人") {
        obj = { related_person: v.key_person_id };
        this.related_case = v.key_person_id;
      }
      if (listName === "关键组织") {
        obj = { related_organization: v.key_organization_id };
        this.related_case = v.key_organization_id;
      }
      if (listName === "关键线索") {
        obj = { related_clue: v.key_clue_id };
        this.related_case = v.key_clue_id;
      }

      this.$store.commit("caseManage/sendRelatedCase", obj);
      this.relatedCaseDialogVisible = true;
    },
    caseHandleDelete(v1) {
      this.$confirm("此操作将永久删除该权限, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let obj = new Object();
          if (this.listName === "案件") {
            obj = { case_id: v1.case_id };
          }
          if (this.listName === "关键人") {
            obj = { key_person_id: v1.key_person_id };
          }
          if (this.listName === "关键组织") {
            obj = { key_organization_id: v1.key_organization_id };
          }
          if (this.listName === "关键线索") {
            obj = { key_clue_id: v1.key_clue_id };
          }
          this.$store.commit("caseManage/sendDelCase", obj);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    caseHandleTagDel(v1, v2) {
      let obj = {};
      this.$confirm("此操作将永久删除该权限, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (this.listName === "案件") {
            obj = {
              id: Number(v1.case_id),
              tags: new Array(v2),
              listName: this.listName,
            };
          }
          if (this.listName === "关键人") {
            obj = {
              id: Number(v1.key_person_id),
              tags: new Array(v2),
              listName: this.listName,
            };
          }
          if (this.listName === "关键组织") {
            obj = {
              id: Number(v1.key_organization_id),
              tags: new Array(v2),
              listName: this.listName,
            };
          }
          if (this.listName === "关键线索") {
            obj = {
              id: Number(v1.key_clue_id),
              tags: new Array(v2),
              listName: this.listName,
            };
          }
          this.$store.commit("caseManage/sendDelCaseTag", obj);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addClueBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit("caseManage/sendClueAdd", {
            summary: this.addClueForm.summary,
          });
          this.dialogAddClue = false;
        } else {
          return false;
        }
      });
    },
    addOrganizationBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let summaryArr = [];
          summaryArr = this.addOrganizationForm.summary.map((item, index) => {
            return item.value;
          });
          let organizationNameArr = [];
          organizationNameArr = this.addOrganizationForm.organization_name.map(
            (item, index) => {
              return item.value;
            }
          );
          let organizationWebsiteArr = [];
          organizationWebsiteArr =
            this.addOrganizationForm.organization_website.map((item, index) => {
              return item.value;
            });
          this.$store.commit("caseManage/sendOrganizationAdd", {
            summary: summaryArr[0],
            name: organizationNameArr,
            organization_website: organizationWebsiteArr,
            organization_group: this.addOrganizationForm.organization_group,
          });
          this.dialogAddOrganization = false;
        } else {
          return false;
        }
      });
    },
    addOrganizationDomain(v) {
      if (v === "organization_group") {
        this.addOrganizationForm[v].push({
          type: "",
          group_name: "",
          group_nickname: "",
          key: Date.now(),
        });
      } else {
        this.addOrganizationForm[v].push({
          value: "",
          key: Date.now(),
        });
      }
    },
    addPersonBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let summaryArr = [];
          summaryArr = this.addPersonForm.summary.map((item, index) => {
            return item.value;
          });
          let fullnameArr = [];
          fullnameArr = this.addPersonForm.fullname.map((item, index) => {
            return item.value;
          });
          let nicknameArr = [];
          nicknameArr = this.addPersonForm.nickname.map((item, index) => {
            return item.value;
          });
          let telephoneArr = [];
          telephoneArr = this.addPersonForm.telephone.map((item, index) => {
            return item.value;
          });
          this.$store.commit("caseManage/sendPersonAdd", {
            summary: this.addPersonForm.summary.value,
            name: fullnameArr,
            nickname: nicknameArr,
            telephone: telephoneArr,
            identity_number: [this.addPersonForm.identity_number],
            birthday: this.addPersonForm.birthday,
            sex: this.addPersonForm.sex,
            address: this.addPersonForm.address.split(","),
          });
          this.dialogAddPerson = false;
        } else {
          return false;
        }
      });
    },
    addPersonDomain(v) {
      this.addPersonForm[v].push({
        value: "",
        key: Date.now(),
      });
    },
    listMorefn(v) {
      this.moreDialogData = v;
      this.dialogMoreCase = true;
    },
    caseHandleEdit(v1) {
      if (this.listName === "案件") {
        this.dialogEditCase = true;
        this.editCaseForm.case_id = v1.case_id;
        if (v1.summary.indexOf(",") > -1) {
          this.editCaseForm.summary = v1.summary.join(",");
        } else {
          this.editCaseForm.summary = v1.summary;
        }

        this.editCaseForm.case_name = v1.case_name;
      }
      if (this.listName === "关键人") {
        this.dialogEditPerson = true;
        this.editPersonForm.key_person_id = v1.key_person_id;

        if (v1.summary.indexOf(",") > -1) {
          this.editPersonForm.summary = v1.summary.join(",");
        } else {
          this.editPersonForm.summary = v1.summary;
        }
      }
      if (this.listName === "关键组织") {
        this.dialogEditOrganization = true;
        this.editOrganizationForm.key_organization_id = v1.key_organization_id;

        if (v1.summary.indexOf(",") > -1) {
          this.editOrganizationForm.summary = v1.summary.join(",");
        } else {
          this.editOrganizationForm.summary = v1.summary;
        }
        if (v1.organization_name.indexOf(",") > -1) {
          this.editOrganizationForm.organization_name =
            v1.organization_name.join(",");
        } else {
          this.editOrganizationForm.organization_name = v1.organization_name;
        }
      }
      if (this.listName === "关键线索") {
        this.dialogEditClue = true;
        this.editClueForm.key_clue_id = v1.key_clue_id;

        if (v1.summary.indexOf(",") > -1) {
          this.editClueForm.summary = v1.summary.join(",");
        } else {
          this.editClueForm.summary = v1.summary;
        }
      }
    },
    caseListPageChange(val) {
      this.$store.commit("caseManage/setListName", this.listName);
      this.$store.commit("caseManage/setNowCasePage", val);
    },
    returnHomeFn() {
      if (this.listName === "关键人") {
        this.$store.commit("caseManage/sendPersonList");
        this.$store.commit("caseManage/sendPersonCount");
      }
      if (this.listName === "关键组织") {
        this.$store.commit("caseManage/sendOrganizationList");
        this.$store.commit("caseManage/sendOrganizationCount");
      }
      if (this.listName === "关键线索") {
        this.$store.commit("caseManage/sendClueList");
        this.$store.commit("caseManage/sendClueCount");
      }
      if (this.listName === "案件") {
        this.$store.commit("caseManage/sendCaseList");
        this.$store.commit("caseManage/sendCaseCount");
      }
      let tmpThis = this;
      let timer = null;
      let beginBun = 0;
      if (timer) {
        clearInterval(timer);
      }
      const loading = this.$loading({
        lock: true,
        text: "正在加载",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        beginBun++;
        if (tmpThis.$store.state.caseManage.req) {
          loading.close();
          clearInterval(timer);

          tmpThis.$store.commit("caseManage/setReq", false);
        } else {
        }
      }, 300);
      this.caseCurrentPage = 1;
    },
    tagSearchFn() {
      this.caseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", {
        tags: this.tagSearch.tagVal.split(","),
      });
      this.$store.commit("caseManage/tagCaseSearch", {
        tags: this.tagSearch.tagVal,
        listName: this.listName,
      });
      this.tagSearch.tagVal = "";
      let tmpThis = this;
      let timer = null;
      let beginBun = 0;
      if (timer) {
        clearInterval(timer);
      }
      const loading = this.$loading({
        lock: true,
        text: "正在加载",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        beginBun++;
        if (tmpThis.$store.state.caseManage.req) {
          loading.close();
          clearInterval(timer);
          tmpThis.$store.commit("caseManage/setReq", false);
        } else {
        }
      }, 300);
    },
    casefn(nodedata, event) {
      this.caseCurrentPage = 1;
      this.$store.commit("caseManage/setTags", { tags: [] });
      this.$store.commit("caseManage/setCaseList", []);
      this.listName = "案件";
      let case_dir_id = nodedata.case_dir_id;
      this.$store.commit("caseManage/setCaseDirId", Number(case_dir_id));
      this.$store.commit("caseManage/sendCaseList");
      this.$store.commit("caseManage/sendCaseCount");
      this.caseDialogVisible = true;
      let tmpThis = this;
      let timer = null;
      let beginBun = 0;
      if (timer) {
        clearInterval(timer);
      }

      const loading = this.$loading({
        lock: true,
        text: "正在加载",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        beginBun++;
        if (tmpThis.$store.state.caseManage.req) {
          loading.close();
          clearInterval(timer);
          tmpThis.$store.commit("caseManage/setReq", false);
        } else {
        }
      }, 300);
      event.stopPropagation();
    },
    addFilesBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit("caseManage/sendAddFile", {
            file_path: this.file_path.join("/"),
            file_name: this.addFilesForm.file_name,
            file_type: this.addFilesForm.file_type,
            case_dir_id: this.$store.state.caseManage.case_dir_id,
          });
          this.addFilesForm.file_name = "";
          this.addFilesDialogVisible = false;
        } else {
          return false;
        }
      });
    },
    async uploadfn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let tmpThis = this;
          let file_path = "";

          if (this.file_path.length === 0) {
            file_path = "";
          } else {
            file_path = this.file_path.join("/");
          }
          let file = this.$refs.file.files[0];
          if (file) {
            let formData = new FormData();
            formData.append("put_case_file", file);
            formData.append(
              "session_id",
              window.main.$store.state.userInfo.session_id
            );
            formData.append("file_type", this.uploadForm.file_type);
            formData.append("file_path", file_path);
            formData.append("file_name", this.uploadForm.file_name);
            formData.append(
              "case_dir_id",
              this.$store.state.caseManage.case_dir_id
            );
            this.progressDialogVisible = true; //tmpThis.percentage

            this.$axios
              .post("/filesystem/api/rest/v2/case_file/put", formData, {
                headers: {
                  "Content-Type": "multipart/form-data",
                },
                onUploadProgress: (progressEvent) => {
                  if (progressEvent.lengthComputable) {
                    let num = Math.round(
                      (progressEvent.loaded / progressEvent.total) * 100
                    );
                    this.percentage = num;
                  }
                },
              })
              .then((response) => {
                if (response.data.status === "ok") {
                  let file_path = "";

                  if (tmpThis.file_path.length === 0) {
                    file_path = "";
                  } else {
                    file_path = tmpThis.file_path.join("/");
                  }
                  tmpThis.$store.commit("caseManage/sendFilesList", {
                    file_path: file_path,
                    file_name: tmpThis.file_name,
                    file_type: tmpThis.file_type,
                  });
                  tmpThis.progressDialogVisible = false;
                  this.uploadDialogVisible = false;
                  this.uploadForm.file_name = "";
                  this.$refs.file.value = "";
                } else {
                  alert("创建工作失败");
                }
              });
          } else {
            alert("请上传文件");
          }
        } else {
          return false;
        }
      });
    },
    handleMore(ind, v) {
      let file_path = "";

      if (this.file_path.length === 0) {
        file_path = "";
      } else {
        file_path = this.file_path.join("/");
      }
      this.$store.commit("caseManage/sendMoreData", {
        case_dir_id: this.$store.state.caseManage.case_dir_id,
        obj: v,
        file_path: file_path,
        file_type: this.file_type,
      });
      this.handleMoreDialogVisible = true;
    },
    handleReName(ind, v) {
      this.renameForm.file_name = v.pathSuffix;
      this.renameForm.rename = v.pathSuffix;
      this.renameDialog = true;
    },
    reNameBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let file_path = "";
          if (this.file_path.length === 0) {
            file_path = "";
          } else {
            file_path = this.file_path.join("/");
          }
          this.$store.commit("caseManage/sendRenameData", {
            case_dir_id: this.$store.state.caseManage.case_dir_id,
            rename: this.renameForm.rename,
            file_path: file_path,
            file_type: this.file_type,
            file_name: this.renameForm.file_name,
          });
          this.renameDialog = false;
        } else {
          return false;
        }
      });
    },
    handleDel(ind, row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let file_path = "";
          if (this.file_path.length === 0) {
            file_path = "";
          } else {
            file_path = this.file_path.join("/");
          }
          this.$store.commit("caseManage/sendFileDel", {
            case_dir_id: this.$store.state.caseManage.case_dir_id,
            file_path: file_path,
            file_type: this.file_type,
            file_name: row.pathSuffix,
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    downfile(ind, row) {
      var tempwindow = window.open("_blank");
      tempwindow.location.href =
        "/filesystem/api/rest/v2/case_file/get/" +
        this.$store.state.caseManage.case_dir_id +
        "/" +
        "/username" +
        "/" +
        row.pathSuffix;
    },
    openUpload() {
      this.uploadDialogVisible = true;
    },
    thisTileFn(v) {
      if (v.type === "DIRECTORY") {
        let file_path = this.file_path.join("/");

        this.$store.commit("caseManage/sendThisFilesList", {
          case_dir_id: this.$store.state.caseManage.case_dir_id,
          obj: v,
          file_path: file_path,
          file_type: this.file_type,
        });
        this.file_path.push(v.pathSuffix);
        this.$store.commit("caseManage/setFilePath", this.file_path);
        this.$store.commit("caseManage/setNowFileName", v.pathSuffix);
        let tmpThis = this;
        let timer = null;
        let beginBun = 0;
        if (timer) {
          clearInterval(timer);
        }
        const loading = this.$loading({
          lock: true,
          text: "正在加载",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.7)",
        });
        timer = setInterval(() => {
          beginBun++;
          if (tmpThis.$store.state.caseManage.req) {
            loading.close();
            clearInterval(timer);
            tmpThis.$store.commit("caseManage/setReq", false);
          } else {
          }
        }, 300);
      }
    },
    addMkdirFn() {
      this.addFilesDialogVisible = true;
      this.addFilesForm.file_path = "";
    },
    openUpload() {
      this.uploadDialogVisible = true;
    },
    filesTypeChange(v) {
      let file_path = "";

      if (this.file_path.length === 0) {
        file_path = "";
      } else {
        file_path = this.file_path.join("/");
      }
      this.file_type = v;
      this.$store.commit("caseManage/setFileType", this.file_type);
      this.$store.commit("caseManage/sendFilesList", {
        file_path: file_path,
        file_name: this.file_name,
        file_type: this.file_type,
      });

      let tmpThis = this;
      let timer = null;
      let beginBun = 0;
      if (timer) {
        clearInterval(timer);
      }
      const loading = this.$loading({
        lock: true,
        text: "正在加载",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        beginBun++;
        if (tmpThis.$store.state.caseManage.req) {
          loading.close();
          clearInterval(timer);
          tmpThis.$store.commit("caseManage/setReq", false);
        } else {
        }
      }, 300);
    },
    returnFn() {
      if (this.file_path.length > 0) {
        this.file_path.pop();
        this.$store.commit("caseManage/setFilePath", this.file_path);
        this.$store.commit("caseManage/setNowFileName", this.file_name);
        let file_path = "";
        if (this.file_path.length === 0) {
          file_path = "";
        } else {
          file_path = this.file_path.join("/");
        }
        this.$store.commit("caseManage/sendFilesList", {
          file_path: file_path,
          file_name: this.file_name,
          file_type: this.file_type,
        });
        let tmpThis = this;
        let timer = null;
        let beginBun = 0;
        if (timer) {
          clearInterval(timer);
        }
        const loading = this.$loading({
          lock: true,
          text: "正在加载",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.7)",
        });
        timer = setInterval(() => {
          beginBun++;
          if (tmpThis.$store.state.caseManage.req) {
            loading.close();
            clearInterval(timer);
            tmpThis.$store.commit("caseManage/setReq", false);
          } else {
          }
        }, 300);
      }
    },
    filefn(nodedata, event) {
      let tmpThis = this;
      let timer = null;
      let beginBun = 0;
      if (timer) {
        clearInterval(timer);
      }
      const loading = this.$loading({
        lock: true,
        text: "正在加载",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        beginBun++;
        if (tmpThis.$store.state.caseManage.req) {
          loading.close();
          clearInterval(timer);
          tmpThis.$store.commit("caseManage/setReq", false);
        } else {
        }
      }, 300);
      let file_path = "";
      this.file_path = [];
      this.$store.commit("caseManage/setFilePath", this.file_path);
      if (this.file_path.length === 0) {
        file_path = "";
      } else {
        file_path = this.file_path.join("/");
      }
      let case_dir_id = Number(nodedata.case_dir_id);
      this.$store.commit("caseManage/setCaseDirId", Number(case_dir_id));
      this.$store.commit("caseManage/sendFilesList", {
        file_path: file_path,
        file_name: this.file_name,
        file_type: this.file_type,
      });

      this.filesListDialogVisible = true;
      event.stopPropagation();
    },
    addCaseDir(v, event) {
      this.dialogAddCaseDir = true;

      if (v.columnValues.i.case_dir_father_path.case_dir_father_path === "") {
        this.addCaseDirForm.case_dir_father_path = "/";
      } else if (v.columnValues.i.case_dir_father_path === "/") {
        this.addCaseDirForm.case_dir_father_path =
          v.columnValues.i.case_dir_father_path +
          v.columnValues.i.case_dir_name;
      } else {
        this.addCaseDirForm.case_dir_father_path =
          v.columnValues.i.case_dir_father_path +
          "/" +
          v.columnValues.i.case_dir_name;
      }
      event.stopPropagation();
    },
    addCaseDirBtnFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "caseManage/setCaseDirFather",
            this.addCaseDirForm.case_dir_father_path
          );
          window.main.$case_socket.sendData(
            "Api.CaseDir.Add",
            [
              {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                },
                msg: {
                  case_dir_father_path:
                    this.addCaseDirForm.case_dir_father_path,
                  case_dir_name: this.addCaseDirForm.case_dir_name,
                  case_dir_summary: this.addCaseDirForm.case_dir_summary,
                },
              },
            ],
            "caseManage/setAddCaseDir"
          );
          this.addCaseDirForm.case_dir_father_path = "";
          this.addCaseDirForm.case_dir_name = "";
          this.addCaseDirForm.case_dir_summary = "";
          this.dialogAddCaseDir = false;
        } else {
          return false;
        }
      });
    },
    handleClick(event) {
      event.stopPropagation();
    },
    removeFn(nodedata, event) {
      this.$confirm("此操作将永久删除该权限, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit(
            "caseManage/setCaseDirFather",
            nodedata.columnValues.i.case_dir_father_path
          );
          if (nodedata.iconStyle === "el-icon-collection") {
            this.$store.commit("caseManage/sendDelCase", nodedata);
          } else {
            window.main.$case_socket.sendData(
              "Api.CaseDir.Del",
              [
                {
                  head: {
                    session_id: window.main.$store.state.userInfo.session_id,
                  },
                  msg: {
                    case_dir_father_path:
                      nodedata.columnValues.i.case_dir_father_path,
                    case_dir_name: nodedata.columnValues.i.case_dir_name,
                  },
                },
              ],
              "caseManage/setDelCaseDir"
            );
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
      event.stopPropagation();
    },
    editFn(nodedata, event) {
      this.$store.commit(
        "caseManage/setCaseDirFather",
        nodedata.columnValues.i.case_dir_father_path
      );
      if (nodedata.iconStyle === "el-icon-collection") {
        this.dialogEditCase = true;
        this.editCaseForm.row_key = nodedata.row;
        this.editCaseForm.case_name = nodedata.columnValues.i.case_dir_name;
      } else {
        this.dialogEditCaseDir = true;
        this.editSubForm.case_dir_father_path =
          nodedata.columnValues.i.case_dir_father_path;
        this.editSubForm.case_dir_name = nodedata.columnValues.i.case_dir_name;
      }
      event.stopPropagation();
    },
    editBtnFn(formName, dataNode, event) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.commit(
            "caseManage/setCaseDirFather",
            this.editSubForm.case_dir_father_path
          );
          window.main.$case_socket.sendData(
            "Api.CaseDir.Set",
            [
              {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                },
                msg: {
                  case_dir_father_path: this.editSubForm.case_dir_father_path,
                  case_dir_name: this.editSubForm.case_dir_name,
                  map: {
                    create_name: this.editSubForm.create_name,
                    create_email: this.editSubForm.create_email,
                    target_person: this.editSubForm.target_person,
                    target_event: this.editSubForm.target_event,
                    remarks: this.editSubForm.remarks,
                  },
                },
              },
            ],
            "caseManage/setEditCaseDir"
          );
          let obj = {};

          for (let str in this.editSubForm) {
            if (this.editSubForm[str] === "") {
              obj[str] = "";
            }
          }
          this.$store.commit("caseManage/sendUnSetCaseInfo", {
            case_dir: {
              case_dir_father_path: this.editSubForm.case_dir_father_path,
              case_dir_name: this.editSubForm.case_dir_name,
            },
            unset: obj,
          });
        } else {
          return false;
        }
      });
    },
    fn(v, event) {
      this.$store.commit("task/setUserListNowAuthority", v);
      this.$store.commit("task/setDialogUserListVisible", true);
      event.stopPropagation();
    },
    handleCurrentChange(id, page) {
      this.dataNode.startCount = page;
      this.$store.commit("caseManage/setCaseDirFatherList", [Number(id)]);
      this.$store.commit("caseManage/setStartCount", page);
      window.main.$store.commit("caseManage/sendCaseDirCount");
      this.$store.commit("caseManage/sendCaseDirList");
      let tmpThis = this;
      let timer = null;
      if (timer) {
        clearInterval(timer);
      }
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.7)",
      });
      timer = setInterval(() => {
        if (tmpThis.$store.state.caseManage.req) {
          clearInterval(timer);
          loading.close();
          this.$store.commit("caseManage/setReq", false);
        }
      }, 200);
    },
    evenfn(event, id) {
      event.stopPropagation();
    },
    showDuanFn(dataNode, event) {
      if (
        dataNode.hasOwnProperty("iconStyle") &&
        dataNode.iconStyle != "el-icon-collection"
      ) {
        this.$store.commit("caseManage/setnoneData", false);
        this.$store.commit("caseManage/setReq", false);
        if (dataNode.iconStyle === "triangle") {
          dataNode.iconStyle = "bottomsty";
        } else if (dataNode.iconStyle === "bottomsty") {
          dataNode.iconStyle = "triangle";
          if (dataNode.columnValues.i.case_dir_father_path == "") {
            window.main.$pki_socket.sendData(
              "Api.SubAuthority.List",
              [
                {
                  session_id: window.main.$store.state.userInfo.session_id,
                  authority:
                    window.main.$store.state.userInfo.userinfo.authority,
                  sub_authority_father: "/63617365",
                  from: 0,
                  size: 100,
                },
              ],
              "caseManage/setCaseDirFatherArrData"
            );
          }
        }
        window.main.$store.commit("caseManage/sendCaseDirList", dataNode);
        this.$store.commit("caseManage/sendCaseList", dataNode);
        this.$store.commit("caseManage/setcasedirDetail", [dataNode]);
      }

      if (dataNode.case_name == "case") {
        this.$store.commit("caseManage/clearCaseRelateOneList");
        this.$store.commit("caseManage/setListName", "案件");
        this.$store.commit("caseManage/setCaseId", dataNode.row);
        this.$store.commit("caseManage/setNowInfo", dataNode);

        this.$store.commit("caseManage/sendCaseDetail", dataNode);
        this.$store.commit("caseManage/setCurrentView", "caseList");
        this.$store.commit("caseManage/setNowCasePage", 1);
      } else {
        this.$store.commit("caseManage/sendCaseDirList", dataNode);
        this.$store.commit("caseManage/setCurrentView", "caseDir");

        if (dataNode.columnValues.i.case_dir_father_path == "") {
          this.$store.commit("caseManage/setcasedirDetail", [dataNode]);
          return;
        } else {
          this.$store.commit("caseManage/sendCaseList", dataNode);
          this.$store.commit("caseManage/sendCaseDirDetail", dataNode);
        }
      }

      event.stopPropagation();
    },
  },
};
</script>
