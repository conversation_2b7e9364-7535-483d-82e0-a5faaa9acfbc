<template>
  <div class="global-robot-container" v-show="visible">
    <div class="robot-wrapper">
      <!-- 纯文本提示 -->
      <div
        class="robot-tip"
        v-if="showTip && !isHtmlContent"
        @mouseenter="pauseAutoHide"
        @mouseleave="resumeAutoHide"
      >
        <div class="tip-content">{{ currentTip }}</div>
        <div class="tip-arrow"></div>
      </div>

      <!-- HTML内容提示 -->
      <div
        class="robot-tip html-tip"
        v-if="showTip && isHtmlContent"
        @mouseenter="pauseAutoHide"
        @mouseleave="resumeAutoHide"
      >
        <div class="tip-prefix" v-if="tipPrefix">{{ tipPrefix }}</div>
        <div class="tip-content html-content" v-html="currentHtmlTip"></div>
        <div class="tip-arrow"></div>
      </div>

      <!-- AI+ 风格菜单 -->
      <div
        class="ai-menu"
        :class="{ 'ai-search-route': isAiSearchRoute }"
        v-show="showMenu"
      >
        <div class="menu-item ai-plus" v-if="!isAiSearchRoute">
          <div class="menu-icon ai-icon" @click="handleMenuItem('AI')">
            <i class="custom-icon" title="AI对话框"></i>
          </div>
        </div>
        <div class="menu-item notification">
          <div
            class="menu-icon notification-icon"
            @click="handleMenuItem('messageSystem')"
          >
            <i class="el-icon-bell" title="通知"></i>
          </div>
        </div>
        <div class="menu-item telegram">
          <div class="menu-icon telegram-icon" @click="handleMenuItem('email')">
            <i class="el-icon-position" title="邮箱"></i>
          </div>
        </div>
      </div>

      <img
        class="robot-image"
        :src="robotImage"
        alt="AI助手"
        @click="handleRobotClick"
      />
    </div>
    <message_system
      ref="messageRef"
      :mainSocket="this.$main_socket"
      :messageSocket="this.$emlmsgs_socket"
      v-if="messageDialogVisible"
      :getContacts="true"
    />
    <el-dialog
      title="ai助手"
      :close-on-click-modal="false"
      :visible.sync="dialogAiVisible"
      width="80%"
      top="40px"
      :modal="true"
      append-to-body
      :lock-scroll="false"
      :fullscreen="false"
      class="ai-assistant-dialog"
    >
      <div style="height: 780px" class="dia_box">
        <IndexChat ref="sessionIndex" :window-data="windowData"></IndexChat>
      </div>
    </el-dialog>
  </div>
</template>

<style>
/* 注意：这里不使用scoped，确保样式可以影响到全局弹窗 */
.ai-assistant-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-assistant-dialog .el-dialog {
  margin: 0 auto !important;
  position: relative;
  display: flex;
  flex-direction: column;
}

.ai-assistant-dialog .el-dialog__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 9999 !important;
}

.ai-assistant-dialog .v-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
  z-index: 9998 !important;
}

/* 确保内容区域在遮罩之上 */
.ai-assistant-dialog .dia_box {
  position: relative;
  z-index: 10000 !important;
}
</style>

<script>
import SessionTypeConstant from "/src/common/constants/SessionType";
import ContentShowType from "@/common/constants/ContentShowType";
import IndexChat from "/src/components/session/IndexChat";
import { dialog } from "@/i18n/zh/information";
const windowData = {
  title: "Ai聊天室",
  description: "与Ai一起畅所欲言",
  sessionType: SessionTypeConstant.CHAT,
  contentShowType: ContentShowType.Markdown,
};
export default {
  name: "GlobalRobot",
  data() {
    return {
      windowData,
      dialogAiVisible: false,
      robotImage: require("@/assets/images/机器人.gif"),
      showTip: false,
      messageDialogVisible: false,
      currentTip: "",
      currentHtmlTip: "",
      tipPrefix: "",
      isHtmlContent: false,
      tipIndex: 0,
      tips: [
        "有什么可以帮您的吗？",
        "点击我可以获取帮助",
        "有新的通知需要查看",
        "欢迎使用智能系统",
      ],
      showMenu: false,
      visible: true,
      hideTimer: null, // 用于存储隐藏定时器
      isPaused: false, // 用于标记是否暂停自动隐藏
      defaultShowTime: 2000, // 默认显示时间为2秒
    };
  },
  components: {
    IndexChat,
  },
  created() {
    // 监听事件，允许其他组件控制机器人的显示/隐藏
    this.$bus.$on("toggleRobot", (status) => {
      console.log(
        "GlobalRobot received toggleRobot event:",
        status,
        "current visible:",
        this.visible
      );
      if (status !== undefined) {
        this.visible = status;
      } else {
        this.visible = !this.visible;
      }
      console.log("GlobalRobot visible changed to:", this.visible);
    });

    // 监听显示特定提示的事件
    this.$bus.$on("showRobotTip", (tip) => {
      this.showCustomTip(tip);
    });

    // 监听显示HTML提示的事件
    this.$bus.$on("showHtmlTip", (data) => {
      this.showHtmlTip(data);
    });
  },
  mounted() {
    this.startTipInterval();
    // 添加全局点击事件监听器
    document.addEventListener("click", this.handleGlobalClick);
  },
  beforeDestroy() {
    if (this.tipTimer) {
      clearInterval(this.tipTimer);
    }
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
    }
    // 移除全局点击事件监听器
    document.removeEventListener("click", this.handleGlobalClick);
    // 取消事件监听
    this.$bus.$off("toggleRobot");
    this.$bus.$off("showRobotTip");
    this.$bus.$off("showHtmlTip");
  },
  methods: {
    startTipInterval() {
      // 初始展示第一条提示
      this.showTipMessage();

      // 每10秒显示一次提示，持续2秒
      this.tipTimer = setInterval(() => {
        this.showTipMessage();
      }, 10000);
    },
    showTipMessage() {
      // 不在显示菜单时显示提示
      if (this.showMenu) return;

      // 更新提示文本
      this.currentTip = this.tips[this.tipIndex];
      this.tipIndex = (this.tipIndex + 1) % this.tips.length;

      // 显示纯文本提示
      this.isHtmlContent = false;
      this.showTip = true;

      // 设置自动隐藏
      this.setAutoHide(this.defaultShowTime);
    },
    showCustomTip(tip) {
      // 隐藏菜单
      this.showMenu = false;

      // 显示纯文本提示
      this.isHtmlContent = false;
      this.currentTip = tip;
      this.showTip = true;

      // 设置自动隐藏 (2秒)
      this.setAutoHide(this.defaultShowTime);
    },
    showHtmlTip(data) {
      // 隐藏菜单
      this.showMenu = false;

      // 显示HTML提示
      this.isHtmlContent = true;
      this.tipPrefix = data.prefix || "";
      this.currentHtmlTip = data.content || "";
      this.showTip = true;

      // 判断内容类型，如果是预警消息，显示时间更长
      let showTime = this.defaultShowTime;
      if (this.tipPrefix.includes("预警")) {
        showTime = 8000; // 预警显示更长时间（8秒）
      }

      // 设置自动隐藏
      this.setAutoHide(showTime);
    },
    setAutoHide(delay) {
      // 清除之前的定时器
      if (this.hideTimer) {
        clearTimeout(this.hideTimer);
      }

      // 设置新的定时器
      this.isPaused = false;
      this.hideTimer = setTimeout(() => {
        if (!this.isPaused) {
          this.showTip = false;
        }
      }, delay);
    },
    pauseAutoHide() {
      // 鼠标移入时暂停自动隐藏
      this.isPaused = true;
      if (this.hideTimer) {
        clearTimeout(this.hideTimer);
      }
    },
    resumeAutoHide() {
      // 鼠标移出时恢复自动隐藏
      this.isPaused = false;
      this.setAutoHide(1000); // 移出后1秒隐藏
    },
    handleRobotClick() {
      // 切换帮助菜单的显示状态
      this.showMenu = !this.showMenu;
      console.log(this.showMenu);

      // 隐藏提示
      this.showTip = false;
    },
    handleGlobalClick(event) {
      // 检查点击是否在机器人组件区域之外
      if (this.showMenu && !this.$el.contains(event.target)) {
        this.showMenu = false;
      }
    },
    handleMenuItem(action) {
      switch (action) {
        case "AI":
          this.$message.info("正在打开AI功能...");
          this.dialogAiVisible = true;
          break;
        case "messageSystem":
          this.$message.info("正在打开消息系统...");
          this.messageDialogVisible = true;
          this.$nextTick(() => {
            if (this.$refs.messageRef) {
              this.$refs.messageRef.showMessageSystem();
            }
          });
          break;
        case "email":
          this.$message.info("正在打开邮箱...");
          this.$router.push({ name: "mailbox" });
          break;
      }

      // 关闭菜单
      this.showMenu = false;
    },
  },
  computed: {
    isAiSearchRoute() {
      return this.$route.path === "/aiSeach";
    },
  },
};
</script>

<style scoped>
.custom-icon {
  display: inline-block;
  width: 24px;
  height: 14px;
  background-image: url("../assets/images/AI.png");
  background-size: cover;
}
.global-robot-container {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 2000;

  .robot-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px; /* 恢复为200px的宽度 */
  }

  .robot-image {
    width: 150px;
    height: 150px;
    cursor: pointer;
    transition: transform 0.3s;

    &:hover {
      transform: scale(1.1);
    }
  }

  .robot-tip {
    position: absolute;
    bottom: 130px;
    right: 10px;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px 15px;
    max-width: 250px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 2;

    &.html-tip {
      max-width: 350px; /* 增加HTML提示的最大宽度 */
      width: 350px;
    }

    .tip-prefix {
      font-weight: bold;
      margin-bottom: 5px;
      color: #409eff;
    }

    .tip-content {
      font-size: 14px;
      color: #333;

      &.html-content {
        max-height: 300px;
        overflow-y: auto;
        padding-right: 5px;

        &::-webkit-scrollbar {
          width: 5px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #ddd;
          border-radius: 5px;
        }
      }
    }

    .tip-arrow {
      position: absolute;
      bottom: -10px;
      right: 30px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid #fff;
      filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
    }
  }
  .dia_box {
    ::v-deep(.el-dialog__body) {
      padding: 0px;
    }
  }
  /* AI+ 风格菜单 - 根据图片布局 */
  .ai-menu {
    position: absolute;
    width: 250px;
    height: 250px;
    bottom: 0;
    right: -70px;
    pointer-events: none;

    &.ai-search-route {
      .menu-item {
        &.notification {
          top: 40px !important;
          left: 100px !important;
          right: auto !important;
          z-index: 3;
        }

        &.telegram {
          top: 80px !important;
          right: 60px !important;
          bottom: auto !important;
          z-index: 2;
        }
      }
    }

    .menu-item {
      position: absolute;
      pointer-events: auto;

      &.ai-plus {
        top: 75px;
        left: 110px;
        z-index: 3;
      }

      &.notification {
        top: 120px;
        right: 60px;
        z-index: 2;
      }

      &.telegram {
        bottom: 20px;
        right: 80px;
        z-index: 1;
      }
    }

    .menu-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #f5f5f5; /* 统一默认灰色背景 */
      color: #666; /* 统一默认图标颜色 */

      &:hover {
        transform: scale(1.5);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.ai-icon {
        width: 40px;
        height: 40px;
        font-weight: bold;
        font-size: 14px;
      }

      &.notification-icon {
        font-size: 18px;
      }

      &.telegram-icon {
        font-size: 18px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px) translateX(-50%);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0) translateX(-50%);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
.dia_box {
  position: relative;
  z-index: 2001;
}

::v-deep .el-dialog__wrapper {
  z-index: 2000 !important;
}

::v-deep .v-modal {
  z-index: 1999 !important;
}
</style>
