<template>
  <div class="model_lay" id="modelChartLay">
    <el-row>
      <el-col :span="24">
        <div class="list_title" style="display: block">
          已搜索表数量：<span>{{
            $store.state.telegramSearch.telegramSearchChartRange.tmpDataList[myname].searchedTableTotal
          }}</span>
          已搜索总数据：<span>{{
            $store.state.telegramSearch.telegramSearchChartRange.tmpDataList[myname].searchedDataTotal
          }}</span>
          已搜索命中数据：<span>{{
            $store.state.telegramSearch.telegramSearchChartData.tmpDataList[myname].dataListTotal
          }}</span>
          正在搜索库路径：<span>{{
            $store.state.telegramSearch.telegramSearchChartRange.tmpDataList[myname].dataRangeDetail
              .data_range_father_path
          }}</span>
          正在搜索表名：<span
            style="
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
            "
            >{{
              $store.state.telegramSearch.telegramSearchChartRange.tmpDataList[myname].dataRangeDetail
                .data_range_name
            }}</span
          >
          <!-- <router-link :to="'/index/comparedList'" class="cartLink">目标分析<i class="icon iconfont" >&#xe60b;</i></router-link> -->
        </div>
      </el-col>
    </el-row>
    <el-row style="position: absolute; top: 65px; bottom: 0px; width: 100%">
      <el-col :span="24" style="height: 100%; display: flex">
        <el-container class="list_lay" style="">
          <el-main style="height:100%;border:padding:0px 10px;">
            <el-row style="">
              <el-col :span="24" style="">
                <!-- myid{{myId}} -->
                <div
                  style="
                    padding: 10px;
                    background: rgba(0, 0, 0, 0.05);
                    border: 1px solid #ccc;
                  "
                >
                  <b>{{ tit }}列表展示</b>
                </div>
                <!-- <scroll @scroll-to-botton='loadMore(true)'  @scroll-to-top='loadMore(false)' :realMinHeight="100"> -->
                <scrollB
                  :myId="myId"
                  @scroll-to-botton="
                    $tools.chartloadMore(
                      false,
                      'searchList',
                      $store.state.telegramSearch.telegramSearchChartData.storeMyname,
                      $store.state.telegramSearch.telegramSearchChartData.tmpDataList.req
                    )
                  "
                  :realMinHeight="100"
                >
                  <div
                    class="unit"
                    v-for="(val, index) in $store.state.telegramSearch.telegramSearchChartData.tmpDataList[
                      myname
                    ].dataList"
                    :key="index"
                  >
                    <!-- <div class="unit_l"  v-if="(!(val == null))&&val.hasOwnProperty ('_source')&&val._source.hasOwnProperty('icon')"  > -->
                    <div
                      class="unit_l"
                      v-if="
                        !(val == null) &&
                        val.hasOwnProperty('_source') &&
                        val._source.hasOwnProperty('icon')
                      "
                    >
                      <!-- <img @error="errorFn()" :src="val['_source'].icon&&'/webhdfs/v1/res/icon/'+val._source.icon+ '?op=OPEN&user.name=admin'?'/webhdfs/v1/res/icon/'+val._source.icon+ '?op=OPEN&user.name=admin':require('../assets/images/winter.jpg')" :type="val['_source'].icon_type?val['_source'].icon_type:''"/> -->
                      <img @error="errorFn($event)"
                      :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + val._source.icon[0].sha512_hash + '?session_id='+$store.state.userInfo.session_id" :type='val._source.icon[0].icon_type' />
                    </div>
                    <div class="unit_l" v-else>
                      <img :src="require('../../../../assets/images/winter.jpg')" />
                    </div>
                    <div class="unit_m" v-if="!(val == null)">
                      <div
                        class="unit_m_table"
                        v-if="val.hasOwnProperty('_source')"
                      >
                        <div class="list_table">
                          <div><b>搜索命中评分</b></div>
                          <div
                            class="omitted"
                            v-if="val.hasOwnProperty('_score')"
                          >
                            {{ val["_score"] }}
                          </div>
                        </div>
                        <div
                          class="list_table"
                          v-for="(table_val, table_name, table_index) in val[
                            '_source'
                          ]"
                          :key="table_index"
                          v-if="
                            table_name != 'content' &&
                            table_name != 'analysis_doc' &&
                            table_name != 'content' &&
                            table_name != 'content_article' &&
                            table_name != 'content_img' &&
                            table_name != 'content_video' &&
                            table_name != 'content_voice' &&
                            table_name != 'content_file' &&
                            table_name != 'icon' &&
                            table_name != 'icon_type'
                          "
                        >
                          <div>
                            <b>{{ $t("list." + table_name) }}</b>
                          </div>
                          <div
                            class="omitted"
                            :title="
                              table_name == '@timestamp'
                                ? $tools.timestampToTime(table_val)
                                : table_val
                            "
                          >
                            {{
                              table_name == "@timestamp" ||
                              table_name == "create_timestamp" ||
                              table_name == "create_time" ||
                              table_name == "timestamp" ||
                              table_name == "update_time" ||
                              table_name == "birthday" ||
                              table_name == "father_birthday" ||
                              table_name == "mather_birthday" ||
                              table_name == "create_time_str"
                                ? $tools.timestampToTime(table_val)
                                : table_val
                            }}
                          </div>
                        </div>
                      </div>
                      <div
                        class="unit_m_marka"
                        v-if="val['_source'].hasOwnProperty('content_article')"
                        style="word-break: break-all; white-space: pre-line"
                      >
                        {{ val["_source"].content_article }}
                      </div>
                    </div>
                    <div class="unit_r" v-if="!(val == null)">
                      <el-button
                        type="primary"
                        round
                        size="mini"
                        @click.native="addComparedList(val)"
                        ><i class="icon iconfont" style="margin-right: 5px"
                          >&#xe60b;</i
                        >加入分析</el-button
                      >
                      <!-- <el-button type="success" round size="mini" @click.native="addCollectionList(val)">收藏</el-button> -->
                      <el-button
                        type="success"
                        round
                        size="mini"
                        @click.native="openClue(val)"
                        >添加线索</el-button
                      >
                      <el-button
                        type="warning"
                        round
                        size="mini"
                        @click.native="morefn(val)"
                        >详情</el-button
                      >
                    </div>
                  </div>
                </scrollB>
              </el-col>
            </el-row>
          </el-main>
        </el-container>
      </el-col>
    </el-row>
    <el-dialog
      title="详情"
      :visible.sync="$store.state.telegramSearch.telegramSearchDataDetail.dialogVisibleB"
      v-if="$store.state.telegramSearch.telegramSearchDataDetail.dialogVisibleB"
      :before-close="handleClose"
      width="80%"
      style="margin-top: -10vh !important"
      append-to-body
    >
      <el-tabs
        type="border-card"
        @tab-click="handleClick"
        v-model="tabactiveName"
      >
        <div class="list_title">
          {{ bian
          }}<span>{{
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
              "_index"
            ]
          }}</span>
          数据编号：<span>{{
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
              "_id"
            ]
          }}</span>
        </div>

        <el-tab-pane
          v-if="
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
          "
          name="baseData"
        >
          <span slot="label">{{ $t("common." + "elasticsearch_data") }}</span>
          <div>
            <el-collapse
              v-model="activeName"
              accordion
              style="margin-top: 20px"
            >
              <el-collapse-item name="1">
                <template slot="title"> 基础信息 </template>
                <div class="morInfor">
                  <div
                    class="morInfor_l"
                    v-if="
                      !(
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                          .elasticsearch_dataB == null
                      ) &&
                      $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB.hasOwnProperty(
                        '_source'
                      ) &&
                      $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB._source.hasOwnProperty(
                        'icon'
                      )
                    "
                  >
                    <img
                      :src="
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                          .elasticsearch_dataB._source.icon[0].sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id
                      "
                      :type="
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                          .elasticsearch_dataB._source.icon[0].icon_type
                      "
                      :onerror="defaultImg"
                    />
                  </div>
                  <div class="morInfor_r">
                    <ul>
                      <li
                        class="morInfor_r_li"
                        v-for="(lival, liname, liindex) in $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                          '_source'
                        ]"
                        :key="liindex"
                        v-show="
                          liname != 'analysis_doc' &&
                          liname != 'content' &&
                          liname != 'content_article' &&
                          liname != 'content_img' &&
                          liname != 'content_video' &&
                          liname != 'content_voice' &&
                          liname != 'content_file'
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list." + liname) }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            liname == "@timestamp" ||
                            liname == "create_timestamp" ||
                            liname == "create_time" ||
                            liname == "timestamp" ||
                            liname == "update_time" ||
                            liname == "birthday" ||
                            liname == "father_birthday" ||
                            liname == "mather_birthday" ||
                            liname == "create_time_str"
                              ? $tools.timestampToTime(lival)
                              : lival
                          }}
                        </div>
                      </li>
                      <li
                        class="morInfor_r_li"
                        v-if="
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp.hanlp_server &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp.hanlp_server
                            .keyword_list
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list.keyword_list") }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                              .base_dataB.columnValues.nlp.hanlp_server
                              .keyword_list
                          }}
                        </div>
                      </li>
                      <li
                        class="morInfor_r_li"
                        v-if="
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp.hanlp_server &&
                          $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                            .base_dataB.columnValues.nlp.hanlp_server
                            .sentence_list
                        "
                      >
                        <div class="morInfor_r_li_h">
                          {{ $t("list.sentence_list") }}:
                        </div>
                        <div class="morInfor_r_li_c">
                          {{
                            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                              .base_dataB.columnValues.nlp.hanlp_server
                              .sentence_list
                          }}
                        </div>
                      </li>
                    </ul>
                    <div
                      v-if="
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                          .columnValues &&
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                          .columnValues.nlp &&
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                          .columnValues.nlp.hanlp_server &&
                        $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                          .columnValues.nlp.hanlp_server.analysis_doc
                      "
                    >
                      <div id="myChartB" :style="{ height: '400px' }"></div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="正文"
                name="2"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_article')
                "
              >
                <div style="white-space: pre-line; line-height: 20px">
                  {{
                    $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                      .elasticsearch_dataB["_source"].content_article
                  }}
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="图片"
                name="3"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_img')
                "
              >
                <div class="imgAllBox">
                  <div
                    v-for="(imgItem, imgindex) in $store.state.telegramSearch.telegramSearchDataDetail
                      .tmpDataDetail.elasticsearch_dataB['_source'][
                      'content_img'
                    ]"
                    :key="imgindex"
                    style="width: 300px; margin-top: 20px"
                  >
                    <el-image
                      style="width: 300px; height: 300px"
                      :fit="'cover'"
                      :src="
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id
                      "
                      :preview-src-list="[
                        '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                          imgItem.sha512_hash +
                          '?session_id=' +
                          $store.state.userInfo.session_id,
                      ]"
                    >
                    </el-image>
                    <p style="fong-weight: bold">{{ imgItem.file_name }}</p>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="视频"
                name="4"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_video')
                "
              >
                <div
                  v-for="(imgItem, imgindex) in $store.state.telegramSearch.telegramSearchDataDetail
                    .tmpDataDetail.elasticsearch_dataB['_source'][
                    'content_video'
                  ]"
                  :key="imgindex"
                >
                  <span v-if="!imgItem">链接为空</span>
                  <video
                    controls="controls"
                    preload="auto"
                    muted
                    loop
                    :ref="'videoB' + imgindex"
                    :id="'videoB' + imgindex"
                  >
                    <source
                      :src="
                        '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id
                      "
                      :type="imgItem.content_video_type"
                      :alt="imgItem ? imgItem : '链接为空'"
                    />
                  </video>
                  <p>
                    选择播放速率：<select
                      ref="selRate"
                      @change="videoFn('videoB' + imgindex)"
                    >
                      <option value="0.5">0.5</option>
                      <option value="1" selected>1.0</option>
                      <option value="1.25">1.25</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2.0</option>
                      <option value="2">3.0</option>
                      <option value="2">4.0</option>
                    </select>
                  </p>
                  <p>{{ imgItem.file_name }}</p>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="音频"
                name="5"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_voice')
                "
              >
                <div
                  v-for="(imgItem, imgindex) in $store.state.telegramSearch.telegramSearchDataDetail
                    .tmpDataDetail.elasticsearch_dataB['_source'][
                    'content_voice'
                  ]"
                  :key="imgindex"
                >
                  <video
                    controls="controls"
                    preload="auto"
                    muted
                    loop
                    autoplay
                    :type="
                      $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                        .elasticsearch_dataB['_source'].content_voice_type
                    "
                    :ref="'videoB' + imgindex"
                    :id="'videoB' + imgindex"
                    :src="
                      '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_voice/' +
                      imgItem.sha512_hash +
                      '?session_id=' +
                      $store.state.userInfo.session_id
                    "
                    :alt="imgItem ? imgItem : '链接为空'"
                  >
                    {{ imgItem.file_name }}
                  </video>
                  <p>
                    选择播放速率：<select
                      ref="selRate"
                      @change="videoFn('videoB' + imgindex)"
                    >
                      <option value="0.5">0.5</option>
                      <option value="1" selected>1.0</option>
                      <option value="1.25">1.25</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2.0</option>
                      <option value="2">3.0</option>
                      <option value="2">4.0</option>
                    </select>
                  </p>
                  <p>{{ imgItem.file_name }}</p>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="附件"
                name="6"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_file')
                "
              >
                <a
                  style="color: blue"
                  title="点击下载"
                  v-for="(imgItem, imgindex) in $store.state.telegramSearch.telegramSearchDataDetail
                    .tmpDataDetail.elasticsearch_dataB['_source'][
                    'content_file'
                  ]"
                  :key="imgindex"
                  :href="
                    '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_file/' +
                    imgItem.sha512_hash +
                    '?session_id=' +
                    $store.state.userInfo.session_id
                  "
                  :type="imgItem.content_file_type"
                  :download="imgItem['content_file_name']"
                  >{{ imgItem["content_file_name"] }}</a
                >
              </el-collapse-item>
              <el-collapse-item
                title="pdf预览"
                name="8"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content_pdf')
                "
              >
                <div
                  id="pdfBox"
                  v-for="(imgItem, imgindex) in $store.state.telegramSearch.telegramSearchDataDetail
                    .tmpDataDetail.elasticsearch_dataB['_source'][
                    'content_pdf'
                  ]"
                  :key="imgindex"
                  style="overflow: auto; height: 600px"
                >
                  <p
                    style="
                      margin-top: 10px;
                      font-weight: bold;
                      text-align: center;
                      margin-bottom: 10px;
                      font-size: 16px;
                    "
                  >
                    {{ imgItem.file_name }}
                  </p>
                  <p
                    @click="pdfFn"
                    v-if="!$store.state.telegramSearch.telegramSearchDataDetail.Pdf"
                    style="text-align: center; cursor: pointer; color: #409eff"
                  >
                    PDF走丢了ㄒoㄒㄒoㄒ，点击一下把他找回来吧~~~~！
                  </p>
                  <!-- <div style="text-align:center"><el-button type="primary" @click="pdfFn" v-if="!$store.state.telegramSearch.telegramSearchDataDetail.Pdf" size="mini">浏览PDF详情>></el-button></div> -->
                  <div v-if="pdferror" style="text-align: center">
                    <b>not found</b>
                  </div>
                  <div>
                    <a
                      :href="
                        '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                        imgItem.sha512_hash +
                        '?session_id=' +
                        $store.state.userInfo.session_id
                      "
                      target="_blank"
                    >
                      <pdf
                        v-for="i in numPages[imgindex]"
                        ref="pdf"
                        :key="i"
                        :src="
                          '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/' +
                          imgItem.sha512_hash +
                          '?session_id=' +
                          $store.state.userInfo.session_id
                        "
                        :page="i"
                      ></pdf>
                    </a>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="原始正文"
                name="7"
                v-if="
                  $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB[
                    '_source'
                  ].hasOwnProperty('content')
                "
              >
                <div style="white-space: pre-line; line-height: 20px">
                  {{
                    $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
                      .elasticsearch_dataB["_source"].content
                  }}
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
        <el-tab-pane
          name="groupContentData"
          v-if="
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source &&
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source.group_name != null
          "
        >
          <span slot="label">{{ $t("common." + "group_content_data") }}</span>
          <div
            class="groupTit"
            v-if="
              $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data &&
              JSON.stringify(
                $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data
              ) !== '{}'
            "
          >
            {{
              $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data[
                "group_nickname"
              ]
            }}
            <p>
              {{
                $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data[
                  "group_name"
                ]
              }}
            </p>
          </div>
          <div class="group_lay">
            <scrolldetail
              @scroll-to-botton="loadGroupContentData(false)"
              @scroll-to-top="loadGroupContentData(true)"
              :realMinHeight="100"
            >
              <ul
                v-if="
                  $store.state.telegramSearch.telegramSearchChartData.tmpDataList[
                    'groupContentData'
                  ].hasOwnProperty('dataList')
                "
              >
                <li
                  v-for="(item, groupindex) in $store.state.telegramSearch.telegramSearchChartData
                    .tmpDataList['groupContentData'].dataList"
                  :key="groupindex"
                >
                  <div class="group_l_img">
                    <img
                      :src="
                        item['_source'].icon
                          ? require(item['_source'].icon +
                              '?op=OPEN&user.name=admin')
                          : require('../../../../assets/images/winter.jpg')
                      "
                      :type="
                        item['_source'].icon_type
                          ? item['_source'].icon_type
                          : ''
                      "
                    />
                  </div>
                  <div class="group_r">
                    <p class="rgroupMember">
                      {{ item["_source"].group_member }}
                    </p>
                    <div class="group_r_content">
                      {{ item["_source"].content_article }}
                    </div>
                    <div
                      class="imgAllBox"
                      v-if="item['_source'].hasOwnProperty('content_img')"
                    >
                      <div
                        v-for="(imgItem, imgindex) in item['_source'][
                          'content_img'
                        ]"
                        :key="imgindex"
                        style="width: 300px; margin-top: 20px"
                      >
                        <el-image
                          style="width: 300px; height: 300px"
                          :fit="'cover'"
                          :src="
                            '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                            imgItem.sha512_hash +
                            '?session_id=' +
                            $store.state.userInfo.session_id
                          "
                          :preview-src-list="[
                            '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_img/' +
                              imgItem.sha512_hash +
                              '?session_id=' +
                              $store.state.userInfo.session_id,
                          ]"
                        >
                        </el-image>
                        <p style="fong-weight: bold">{{ imgItem.file_name }}</p>
                      </div>
                    </div>
                    <div
                      class="imgAllBox"
                      v-if="item['_source'].hasOwnProperty('content_video')"
                    >
                      <div
                        v-for="(imgItem, imgindex) in item['_source'][
                          'content_video'
                        ]"
                        :key="imgindex"
                      >
                        <span v-if="!imgItem">链接为空</span>
                        <video
                          controls="controls"
                          preload="auto"
                          muted
                          loop
                          :ref="'videoB' + imgindex"
                          :id="'videoB' + imgindex"
                        >
                          <source
                            :src="
                              '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_video/' +
                              imgItem.sha512_hash +
                              '?session_id=' +
                              $store.state.userInfo.session_id
                            "
                            :type="imgItem.content_video_type"
                            :alt="imgItem ? imgItem : '链接为空'"
                          />
                        </video>
                        <p>
                          选择播放速率：<select
                            ref="selRate"
                            @change="videoFn('videoB' + imgindex)"
                          >
                            <option value="0.5">0.5</option>
                            <option value="1" selected>1.0</option>
                            <option value="1.25">1.25</option>
                            <option value="1.5">1.5</option>
                            <option value="2">2.0</option>
                            <option value="2">3.0</option>
                            <option value="2">4.0</option>
                          </select>
                        </p>
                        <p>{{ imgItem.file_name }}</p>
                      </div>
                    </div>
                    <div
                      class="imgAllBox"
                      v-if="item['_source'].hasOwnProperty('content_voice')"
                    >
                      <div
                        v-for="(imgItem, imgindex) in item['_source'][
                          'content_voice'
                        ]"
                        :key="imgindex"
                      >
                        <video
                          controls="controls"
                          preload="auto"
                          muted
                          loop
                          autoplay
                          :type="item['_source'].content_voice_type"
                          :ref="'videoB' + imgindex"
                          :id="'videoB' + imgindex"
                          :src="
                            '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/content_voice/' +
                            imgItem.sha512_hash +
                            '?session_id=' +
                            $store.state.userInfo.session_id
                          "
                          :alt="imgItem ? imgItem : '链接为空'"
                        >
                          {{ imgItem.file_name }}
                        </video>
                        <p>
                          选择播放速率：<select
                            ref="selRate"
                            @change="videoFn('videoB' + imgindex)"
                          >
                            <option value="0.5">0.5</option>
                            <option value="1" selected>1.0</option>
                            <option value="1.25">1.25</option>
                            <option value="1.5">1.5</option>
                            <option value="2">2.0</option>
                            <option value="2">3.0</option>
                            <option value="2">4.0</option>
                          </select>
                        </p>
                        <p>{{ imgItem.file_name }}</p>
                      </div>
                    </div>
                    <div
                      class="imgAllBox"
                      v-if="item['_source'].hasOwnProperty('content_file')"
                    >
                      <a
                        style="color: blue; display: block; line-height: 50px"
                        title="点击下载"
                        v-for="(imgItem, imgindex) in item['_source'][
                          'content_file'
                        ]"
                        :key="imgindex"
                        :href="
                          '/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_file/' +
                          imgItem.sha512_hash +
                          '?session_id=' +
                          $store.state.userInfo.session_id
                        "
                        :type="imgItem.content_file_type"
                        :download="imgItem['file_name']"
                        >{{ imgItem["file_name"] }}</a
                      >
                    </div>
                    <div class="group_r_timestamp">
                      {{ $tools.timestampToTime(item["_source"].timestamp) }}
                    </div>
                  </div>
                </li>
              </ul>
            </scrolldetail>
          </div>
        </el-tab-pane>
        <el-tab-pane
          name="groupData"
          v-if="
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source &&
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source.group_name != null
          "
        >
          <span slot="label">{{ $t("common." + "group_data") }}</span>
          <div
            class="group_data"
            v-if="$store.state.telegramSearch.telegramSearchChartData.tmpDataList['groupData'].dataList"
          >
            <div
              class="morInfor"
              v-for="(item, liindex) in $store.state.telegramSearch.telegramSearchChartData.tmpDataList[
                'groupData'
              ].dataList"
              :key="liindex"
              style="margin-top: 20px; border-bottom: 1px solid #ccc"
            >
              <div
                class="morInfor_l"
                v-if="item._source.hasOwnProperty('icon')"
              >
                <img
                  @error="errorFn($event)"
                  :src="
                    '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                    item._source.icon[0].sha512_hash +
                    '?session_id=' +
                    $store.state.userInfo.session_id
                  "
                  :type="item._source.icon[0].icon_type"
                />
              </div>
              <div class="morInfor_r">
                <ul v-for="(lival, a, ind) in item._source" :key="ind">
                  <li class="morInfor_r_li">
                    <div class="morInfor_r_li_h">{{ $t("list." + a) }}:</div>
                    <div class="morInfor_r_li_c">
                      {{
                        a == "create_time" ||
                        a == "@timestamp" ||
                        a == "timestamp" ||
                        a == "@timestamp" ||
                        a == "create_timestamp" ||
                        a == "create_time" ||
                        a == "timestamp" ||
                        a == "update_time" ||
                        a == "birthday" ||
                        a == "father_birthday" ||
                        a == "mather_birthday" ||
                        a == "create_time_str"
                          ? $tools.timestampToTime(lival)
                          : lival
                      }}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          name="groupMemberData"
          v-if="
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source &&
            $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_dataB
              ._source.group_name != null
          "
        >
          <span slot="label">{{ $t("common." + "group_member_data") }}</span>
          <div
            class="groupTit"
            v-if="
              $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data &&
              JSON.stringify(
                $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data
              ) !== '{}'
            "
          >
            {{
              $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data[
                "group_nickname"
              ]
            }}
            <p>
              {{
                $store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.group_data[
                  "group_name"
                ]
              }}
            </p>
          </div>
          <scrolldetailc
            @scroll-to-botton="loadGroupMemberData(false)"
            :realMinHeight="100"
          >
            <div
              class="group_member_data"
              style="max-height: 600px; overflow: auto"
            >
              <div
                class="morInfor group_member_data_unit"
                v-for="(item, liindex) in $store.state.telegramSearch.telegramSearchDataDetail
                  .group_member_data"
                :key="liindex"
              >
                <div class="morInfor_l" v-if="item['_source']['icon']">
                  <img
                    :src="
                      '/webhdfs/v1/res/content_img/' + item['_source']['icon']
                    "
                    :type="item['_source'].icon_type"
                  />
                </div>
                <div class="morInfor_r">
                  <div
                    class="morInfor_r_box"
                    v-if="item['_source']['type'] == 'whatsapp'"
                  >
                    <div class="morInfor_r_box_t">
                      {{ item["_source"]["group_member"] }}
                    </div>
                    <div class="morInfor_r_box_m">
                      {{ item["_source"]["content"] }}
                    </div>
                    <div class="morInfor_r_box_b">
                      <div>
                        <span class="tit">权限：</span
                        ><span>{{ item["_source"]["group_authority"] }}</span>
                      </div>
                      <div>
                        <span class="tit">类型：</span
                        ><span>{{ item["_source"]["type"] }}</span>
                      </div>
                      <div>
                        <span>{{
                          $tools.timestampToTime(item["_source"]["@timestamp"])
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <ul v-else>
                    <li
                      class="morInfor_r_li"
                      v-for="(mval, mname, mindex) in item['_source']"
                      :key="mindex"
                      v-show="mname != 'icon' && mname != 'icon_type'"
                    >
                      <div class="morInfor_r_li_h">
                        {{ $t("list." + mname) }}:
                      </div>
                      <div class="morInfor_r_li_c">
                        {{
                          mname == "create_time" ||
                          mname == "@timestamp" ||
                          mname == "@timestamp" ||
                          mname == "create_timestamp" ||
                          mname == "create_time" ||
                          mname == "timestamp" ||
                          mname == "update_time" ||
                          mname == "birthday" ||
                          mname == "father_birthday" ||
                          mname == "mather_birthday" ||
                          mname == "create_time_str"
                            ? $tools.timestampToTime(mval)
                            : mval
                        }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </scrolldetailc>
        </el-tab-pane>
        <!-- <el-tab-pane  name="articleCount"  v-if="$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.type === 'twitter'&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.user_id!= null&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.article_count != null&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.article_count != 0">
					<span slot="label" >{{$t('common.' + 'article_count')}}</span>
					<div style="height:800px;" >
						<component v-bind:is="'chartList'" :myname="'article_count'"  :myId="'article_count'" ></component>
					</div>
				</el-tab-pane>
				<el-tab-pane  name="likesCount "  v-if="$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.type === 'twitter'&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.user_id!= null&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.likes_count  != null&&$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.likes_count  != 0">
					<span slot="label" >{{$t('common.' + 'likes_count ')}}</span>
					<div style="height:800px;">
						<component v-bind:is="'chartList'" :myname="'likes_count'"  :myId="'likes_count'" ></component>
					</div>
				</el-tab-pane> -->
      </el-tabs>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogCaseTree"
      width="80%"
      append-to-body
    >
      <div class="caseDirBox" ref="caseDirBox">
        {{ $store.state.telegramSearch.telegramSearchCaseManage.caseDirCount }}
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCaseTree = false">取 消</el-button>
        <el-button type="primary" @click="addClue()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import pdf from "vue-pdf";
export default {
  data() {
    return {
      tabactiveName: "baseData",
      clue_content: {},
      case_dir_id: 0,
      dialogCaseTree: false,
      bian: "",
      activeName: "1",
      advanced: false,
      loading: false,
      date1: "",
      date2: "",
      timeRange: "无",
      queryMode: "match",
      queryString: "",
      usePublic: "public",
      numPages: null,
      defaultImg: 'this.src="' + require("../../../../assets/images/winter.jpg") + '"',
    };
  },
  props: {
    myname: String,
    tit: String,
    myId: String,
  },
  watch: {
    date1: function (v) {
      if (v) {
        this.$store.commit(
          "telegramSearch/telegramSearchCondition/setTimeRangeBegin",
          parseInt(v.getTime() / 1000)
        );
      }
    },
    date2: function (v) {
      if (v) {
        this.$store.commit(
          "telegramSearch/telegramSearchCondition/setTimeRangeEnd",
          parseInt(v.getTime() / 1000)
        );
      }
    },
    caseDirList: {
      handler(newVal, oldVal) {
        let str = '<div class="layout">';
        let tmpThis = this;

        //设置分页
        let pageSize = 5;
        //设置一次显示多少页
        let pageLimit = 5;
        //当前页数
        let currentPage = 0;
        //临时变量，比较数目大小
        for (let i = 0; i < newVal.length; i++) {
          str +=
            `<div class="treeItem" onclick="showFn(this` +
            `)"><span class='triangle'></span><input name="Fruit" type="radio" style="margin-right:10px;" value="${newVal[i].case_dir_id}" onclick="chooseDir(${newVal[i].case_dir_id})"/><span class="case_dir_name">${newVal[i].case_dir_name}</span><span style="margin-left:10px;color:#999">(ID:</span><span class="case_dir_id" style="color:#999;">${newVal[i].case_dir_id}</span><span style="margin-left:10px;color:#999;">描述:</span><span style="color:#999;" class="case_dir_summary" >${newVal[i].case_dir_summary}</span>)<div class="case_dir_father" style="display:none">${newVal[i].case_dir_father}</div>`;
          str += `</div>`;
        }
        str += `</div>`;
        str += `<div class="treePage" >`;
        let resultsCount = 1;
        if (newVal.length > 0 && newVal[0].count) {
          resultsCount = newVal[0].count;
          //计算当前有多少页
          let pageTotal = Math.ceil(resultsCount / pageSize);
          let count = 0;
          //比较当前应显示多少数据
          if (resultsCount <= pageSize) {
            count = resultsCount;
          } else {
            count = pageSize;
          }
          //如果总页数大于设置的页数，则
          if (pageTotal > pageLimit) {
            for (var j = 0; j < pageLimit; j++) {
              str +=
                '<a href="javascript:void(0)" onclick="changePage(' +
                j * pageSize +
                "," +
                pageTotal +
                "," +
                j +
                "," +
                'this)">' +
                (j + 1) +
                "</a>";
            }
            str += '<a style="cursor: not-allowed;">......</a>';
          } else {
            for (var j = 0; j < pageTotal; j++) {
              str +=
                '<a href="javascript:void(0)" onclick="changePage(' +
                j * pageSize +
                "," +
                pageTotal +
                "," +
                j +
                ',this)">' +
                (j + 1) +
                "</a>";
            }
          }
          str += `</div>`;
        }
        if (this.$refs.caseDirBox) {
          this.$refs.caseDirBox.innerHTML = str;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    dataList() {
      setTimeout(() => {
        return this.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[this.myname].dataList;
      }, 1000);
    },
    caseDirList() {
      return this.$store.state.telegramSearch.telegramSearchCaseManage.caseDirList;
    },
    groupContentDataList: function () {
      return this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail
        .group_content_data;
    },
    contentImgSrc: function () {
      return (
        "/webhdfs/v1/res/content_img/" +
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data[
          "_source"
        ].content_img +
        "?op=OPEN&user.name=admin"
      );
    },
    contentVoiceSrc: function () {
      return (
        "/webhdfs/v1/res/content_voice/" +
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data[
          "_source"
        ].content_voice +
        "?op=OPEN&user.name=admin"
      );
    },
    contentVideoSrc: function () {
      return (
        "/webhdfs/v1/res/content_video/" +
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data[
          "_source"
        ].content_video +
        "?op=OPEN&user.name=admin"
      );
    },
    contentFileSrc: function () {
      return (
        "/webhdfs/v1/res/content_file/" +
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data[
          "_source"
        ].content_file +
        "?op=OPEN&user.name=admin"
      );
    },
  },
  mounted() {},
  methods: {
    handleClick(tab, event) {
      let tmpThis = this;
      if (tab.name !== "baseData") {
        let data_range_path = "";
        let data_range_index_prefix = "";
        if (tab.name === "groupContentData") {
          data_range_path = "/group_data/whatsapp/group_content_data";
          data_range_index_prefix = "group_content_data_prefix_whatsapp";
        }
        if (tab.name === "groupData") {
          data_range_path = "/group_data/whatsapp/group_data";
          data_range_index_prefix = "group_data_prefix_whatsapp";
        }
        if (tab.name === "groupMemberData") {
          data_range_path = "/group_data/whatsapp/group_member_data";
          data_range_index_prefix = "group_member_data_prefix_whatsapp";
        }
        tmpThis.$store.commit("telegramSearch/telegramSearchChartData/chartOncklickClear");
        tmpThis.$store.commit("telegramSearch/telegramSearchChartRange/clearDataRangeTree", tab.name); //清除之前的查询数据范围
        tmpThis.$store.commit("telegramSearch/telegramSearchChartData/clearSearchList", tab.name); //清除之前的查询结果数据
        tmpThis.$store.commit("telegramSearch/telegramSearchChartData/setStoreMyname", tab.name);
        tmpThis.$store.commit("telegramSearch/telegramSearchChartData/setusePublic", {
          data: tmpThis.usePublic,
          name: tab.name,
        });
        tmpThis.$store.commit("telegramSearch/telegramSearchChartData/setSearch", {
          data: true,
          name: tab.name,
        });
        window.main.$store.commit(`telegramSearch/telegramSearchChartRange/setDataRangeGetter`, {
          dataRangeList: [
            {
              data_range_index_prefix: data_range_index_prefix,
              data_range_path: data_range_path,
              data_range_type: true,
            },
          ],
          name: tab.name,
          moreData: "moreData",
        });
      }
    },
    openClue(v) {
      this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFatherList", []);
      this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFrom", 0);
      this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFrom2", 0);
      this.$store.commit("telegramSearch/telegramSearchCaseManage/sendCaseDirFatherArr");
      this.clue_content = v;
      this.dialogCaseTree = true;
    },
    chooseDir(id) {
      this.case_dir_id = id;
      event.stopPropagation();
    },
    addClue() {
      window.main.$case_socket.sendData(
        "Api.KeyClue.Add",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              case_dir_id: this.case_dir_id,
              clue_content: this.clue_content,
            },
          },
        ],
        "telegramSearch/telegramSearchCaseManage/setList2AddRelatedClue"
      );
    },
    showFn(dom) {
      let str = "";
      let layoutStr = "";
      let pageStr = "";
      let tmpThis = this;
      let timer = null;
      if (timer) {
        clearInterval(timer);
      }

      let case_dir_id = dom.getElementsByClassName("case_dir_id")[0].innerHTML;
      this.$store.commit(
        "telegramSearch/telegramSearchCaseManage/setCaseDirFatherList",
        Number(case_dir_id)
      );
      if (dom.firstChild.className === "triangle") {
        window.main.$case_socket.sendData(
          "Api.CaseDir.Count",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
              },
              msg: { case_dir_father_list: [Number(case_dir_id)] },
            },
          ],
          "telegramSearch/telegramSearchCaseManage/setCaseDirCount"
        );
        window.main.$case_socket.sendData(
          "Api.CaseDir.List",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                from: 0,
                size: 5,
              },
              msg: { case_dir_father_list: [Number(case_dir_id)] },
            },
          ],
          "telegramSearch/telegramSearchCaseManage/setPageCaseList"
        );

        timer = setInterval(() => {
          const loading = this.$loading({
            lock: true,
            text: "Loading",
            spinner: "el-icon-loading",
            background: "rgba(255, 255, 255, 0.7)",
          });

          if (tmpThis.$store.state.telegramSearch.telegramSearchCaseManage.req) {
            clearInterval(timer);
            loading.close();

            if (tmpThis.$store.state.telegramSearch.telegramSearchCaseManage.caseDirPageList.length > 0) {
              let newVal = tmpThis.$store.state.telegramSearch.telegramSearchCaseManage.caseDirPageList;
              //设置分页
              let pageSize = 5;
              //设置一次显示多少页
              let pageLimit = 5;
              //当前页数
              let currentPage = 0;
              //临时变量，比较数目大小
              for (let i = 0; i < newVal.length; i++) {
                layoutStr +=
                  `<div class="treeItem" onclick="showFn(this` +
                  `)"><span class='triangle'></span> <input name="Fruit" type="radio" style="margin-right:10px;" value="${newVal[i].case_dir_id}" onclick="chooseDir(${newVal[i].case_dir_id})"/><span class="case_dir_name">${newVal[i].case_dir_name}</span><span style="margin-left:10px;color:#999">(ID:</span><span class="case_dir_id" style="color:#999;">${newVal[i].case_dir_id}</span><span style="margin-left:10px;color:#999;">描述:</span><span style="color:#999;" class="case_dir_summary" >${newVal[i].case_dir_summary}</span>)<div class="case_dir_father" style="display:none">${newVal[i].case_dir_father}</div>`;
                layoutStr += `</div>`;
              }
              let resultsCount = 1;
              if (newVal.length > 0 && newVal[0].count) {
                resultsCount = newVal[0].count;
                //计算当前有多少页
                let pageTotal = Math.ceil(resultsCount / pageSize);
                let count = 0;
                //比较当前应显示多少数据
                if (resultsCount <= pageSize) {
                  count = resultsCount;
                } else {
                  count = pageSize;
                }
                //如果总页数大于设置的页数，则
                if (pageTotal > pageLimit) {
                  for (var j = 0; j < pageLimit; j++) {
                    pageStr +=
                      '<a href="javascript:void(0)" onclick="changePage(' +
                      j * pageSize +
                      "," +
                      pageTotal +
                      "," +
                      j +
                      "," +
                      'this)">' +
                      (j + 1) +
                      "</a>";
                  }
                  pageStr += '<a style="cursor: not-allowed;">......</a>';
                } else {
                  for (var j = 0; j < pageTotal; j++) {
                    pageStr +=
                      '<a href="javascript:void(0)" onclick="changePage(' +
                      j * pageSize +
                      "," +
                      pageTotal +
                      "," +
                      j +
                      ',this)">' +
                      (j + 1) +
                      "</a>";
                  }
                }
              }
              if (dom.getElementsByClassName("layout")[0]) {
                dom.removeChild(dom.getElementsByClassName("layout")[0]);
                if (dom.getElementsByClassName("treePage")[0]) {
                  dom.removeChild(dom.getElementsByClassName("treePage")[0]);
                }
              } else {
                dom.innerHTML =
                  domStr +
                  `<div class="layout">` +
                  layoutStr +
                  `</div>` +
                  `<div class="treePage" >` +
                  pageStr +
                  `</div>`;
              }
              this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFather", 0);
              tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setPageCaseList", []);
              tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setReq", false);
            } else if (
              tmpThis.$store.state.telegramSearch.telegramSearchCaseManage.caseDirPageList.length === 0
            ) {
              if (dom.getElementsByClassName("layout")[0]) {
                dom.removeChild(dom.getElementsByClassName("layout")[0]);
                if (dom.getElementsByClassName("treePage")[0]) {
                  dom.removeChild(dom.getElementsByClassName("treePage")[0]);
                }
              } else {
                dom.innerHTML =
                  domStr +
                  `<div class="layout" >` +
                  '<div style="color:#999;text-align:center;">无子项</div>' +
                  `</div>`;
              }
              this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFather", 0);
              tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setPageCaseList", []);
              tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setReq", false);
            }
          }
        }, 200);
        dom.getElementsByClassName("triangle")[0].className = "bottomsty";
      } else if (dom.firstChild.className === "bottomsty") {
        if (dom.getElementsByClassName("layout")[0]) {
          dom.removeChild(dom.getElementsByClassName("layout")[0]);
          if (dom.getElementsByClassName("treePage")[0]) {
            dom.removeChild(dom.getElementsByClassName("treePage")[0]);
          }
        }
        this.$store.commit("telegramSearch/telegramSearchCaseManage/setCaseDirFather", 0);
        tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setPageCaseList", []);
        tmpThis.$store.commit("telegramSearch/telegramSearchCaseManage/setReq", false);
        dom.getElementsByClassName("bottomsty")[0].className = "triangle";
      }

      // this.$store.commit('caseManage/setCaseDirFather',Number(case_dir_id))
      let domStr = dom.innerHTML;

      event.stopPropagation();
    },
    usePublicFn2(v) {},
    errorFn(even) {
      even.target.src = require("../../../../assets/images/winter.jpg");
    },
    loadGroupMemberData(direction) {
      this.$store.commit(
        "telegramSearch/telegramSearchDataDetail/telegramGroupMemberData/sendGetDataDetailGroupMemberData",
        direction
      );
    },
    loadGroupContentData(direction) {
      this.$store.commit(
        "telegramSearch/telegramSearchDataDetail/telegramGroupContentData/setDirection",
        direction
      );
      this.$store.commit(
        "telegramSearch/telegramSearchDataDetail/telegramGroupContentData/sendGetDataDetailGroupContentData",
        direction
      );
    },
    addCollectionList(v) {
      this.$store.commit("telegramSearch/telegramSearchCollection/sendAddCollectionList", v);
    },
    handleClose() {
      this.activeName = "1";
      this.$store.commit("telegramSearch/telegramSearchDataDetail/setdialogVisibleB", false);
    },

    advancedfn() {
      if (this.$store.state.telegramSearch.telegramSearchCondition.time_range_begin) {
        this.date1 = new Date(
          this.$store.state.telegramSearch.telegramSearchCondition.time_range_begin * 1000
        );
      } else {
        this.date1 = new Date();
      }
      if (this.$store.state.telegramSearch.telegramSearchCondition.time_range_end) {
        this.date2 = new Date(
          this.$store.state.telegramSearch.telegramSearchCondition.time_range_end * 1000
        );
      } else {
        this.date2 = new Date();
      }
      this.queryMode = this.$store.state.telegramSearch.telegramSearchCondition.query_mode;
      this.timeRange = this.$store.state.telegramSearch.telegramSearchCondition.time_range;
      this.advanced = !this.advanced;
    },
    onSubmit() {
      this.advanced = false;
      this.$store.commit("telegramSearch/telegramSearchCondition/setTimeRange", this.timeRange);
      this.$store.commit("telegramSearch/telegramSearchCondition/setQueryMode", this.queryMode);
    },
    onSubmitRef() {
      if (this.$store.state.telegramSearch.telegramSearchCondition.time_range_begin) {
        this.date1 = new Date(
          this.$store.state.telegramSearch.telegramSearchCondition.time_range_begin * 1000
        );
      } else {
        this.date1 = new Date();
      }
      if (this.$store.state.telegramSearch.telegramSearchCondition.time_range_end) {
        this.date2 = new Date(
          this.$store.state.telegramSearch.telegramSearchCondition.time_range_end * 1000
        );
      } else {
        this.date2 = new Date();
      }
      this.queryMode = this.$store.state.telegramSearch.telegramSearchCondition.query_mode;
      this.timeRange = this.$store.state.telegramSearch.telegramSearchCondition.time_range;
      this.$store.commit("telegramSearch/telegramSearchCondition/setTimeRange", this.timeRange);
      this.$store.commit("telegramSearch/telegramSearchCondition/setQueryMode", this.queryMode);
    },
    async gofn() {
      this.$store.commit("telegramSearch/telegramSearchDataRange/setChartOn", false);
      this.$store.commit("telegramSearch/telegramSearchDataRange/clearDataRangeTree"); //清除之前的查询数据范围
      this.$store.commit("telegramSearch/telegramSearchList/clearSearchList"); //清除之前的查询结果数据
      await this.$store.commit(
        "telegramSearch/telegramSearchList/setQueryString",
        this.queryString
      );
      // this.$router.push("/index/search/list/loading");
      // this.$store.commit("telegramSearch/telegramSearchList/setusePublic", this.usePublic);
    },
    morefn(v) {
      this.bian = "";
      this.tabactiveName = "baseData";
      this.$store.commit("telegramSearch/telegramSearchDataDetail/clearDataDetailB");
      this.$store.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "elasticsearch_dataB",
        value: v,
      });
      this.$store.commit(
        "telegramSearch/telegramSearchDataDetail/telegramBaseData/sendGetDataDetailBaseDataB",
        v
      );
      let tmpThis = this;
      setTimeout(function () {
        tmpThis.bian = "数据库：";
      }, 1000);
    },
    pdfFn() {
      this.$store.commit("telegramSearch/telegramSearchDataDetail/setPdf", true);
      document.getElementById("pdfBox").style.cssText =
        "height:600px;overflow: auto;";
      this.pdferror = false;
      this.numPages = [];
      if (
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.hasOwnProperty(
          "content_pdf"
        )
      ) {
        this.$store.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.elasticsearch_data._source.content_pdf.forEach(
          (element, index) => {
            let loadingTask = pdf.createLoadingTask(
              "/filesystem/api/rest/v2/node-0/big_file/get_sha512_file/content_pdf/" +
                element.sha512_hash +
                "?session_id=" +
                this.$store.state.userInfo.session_id
            );

            loadingTask.promise
              .then((pdf) => {
                this.numPages.push(pdf.numPages);
              })
              .catch((err) => {
                document.getElementById("pdfBox").style.cssText =
                  "height:40px;overflow: auto;";
                this.pdferror = true;
              });
          }
        );
      }
    },
    collapseFn(v) {},
    addComparedList(v) {
      this.$store.commit("comparedList/sendAddComparedList", v);
    },
  },
  created() {
    this.onSubmitRef();
    window.chooseDir = this.chooseDir;
    window.showFn = this.showFn;
    this.queryString = this.$store.state.telegramSearch.telegramSearchList.queryString;
  },
  components: {
    scrollB: () => import("./scrollmore"),
    scroll: () => import("./scroll"),
    scrolldetail: () => import("./scrolldetail"),
    scrolldetailc: () => import("./scrolldetailc"),
    pdf,
  },
};
</script>

<style scoped src="../../../../assets/css/list.css">
.collColor >>> b {
  display: block;
  background: #ccc;
}
.el-tab-pane .el-dialog__wrapper {
  position: relative !important;
  margin-top: 100px !important;
}
</style>
<style lang="scss">
.relatedRow {
  margin-bottom: 15px;
  display: flex;
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  .rowItem {
    flex: 1 1;
    border-right: 1px solid #ddd;
    .rowItemT {
      padding: 10px;
      text-align: center;
      font-weight: bold;
      border-top: 1px solid #ddd;
    }
    .rowItemB {
      padding: 10px;
      text-align: center;
      border-top: 1px solid #ddd;
    }
  }
  .operation {
    border-right: 1px solid #ddd;
    border-top: 1px solid #ddd;
  }
  .operation {
    padding: 0 10px;
    display: flex;
    align-items: center;
  }
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}
.el-dialog {
  width: 85%;
}
.dialogTit {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}
.treeItem {
  color: #666;
  padding: 20px;

  cursor: pointer;
  font-size: 16px;
  position: relative;
  .btns {
    position: absolute;
    right: 20px;
    top: 20px;
    span {
      margin-left: 10px;
    }
  }
}
.treeItem:hover > .case_dir_name,
.treeItem:hover > .btns {
  color: #409eff;
}

.triangle {
  margin-right: 4px;
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-left: 7px solid #c0c4cc;
  border-bottom: 5px solid transparent;
}
.bottomsty {
  margin-right: 4px;
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 7px solid #c0c4cc;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}
.caseDirTit {
  padding: 10px 20px;
  font-weight: bold;
}
.treePage {
  text-align: center;
  a {
    margin: 0 10px;
    border: 1px solid #f5f5f5;
    background: #f2f2f2;
    padding: 0 2px;
    &:hover {
      color: #409eff;
    }
  }
}
.tagsSty {
  margin-right: 10px;
  background: #52a04f;
  padding: 2px 3px;
  cursor: pointer;
  color: #fff;
}
.el-collapse-item__header {
  font-weight: bold !important;
  background: #eee;
  text-indent: 10px;
  border-bottom: 1px solid #fff;
}
</style>
