<template>
  <div 
    class="details" 
    v-loading="personDetailLoading"
    element-loading-text="正在加载人员详情,请稍后..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <!-- 左侧 -->
    <el-card class="box-card">
      <div class="left">
        <!-- 个人信息 -->
        <div class="person-card">
          <div class="avatar">
            <div class="avatar-img">
              <el-avatar
                shape="square"
                :src="getAvatarSrc(personInfo.avatar)"
                class="custom-avatar"
              ></el-avatar>
            </div>
            <div class="avatar-info">
              <div class="info-row">
                <span class="info-label">姓名：</span
                ><span class="info-value">{{ personInfo.name }}</span>
              </div>
              <div class="info-row sex-age-row">
                <span>
                  <span class="info-label">性别：</span>
                  <span class="info-value">{{ personInfo.sex }}</span>
                </span>
                <span class="info-age">
                  <span class="info-label">年龄：</span>
                  <span class="info-value">{{ personInfo.age }}</span>
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">手机号：</span
                ><span class="info-value">{{ personInfo.phone }}</span>
              </div>
              <div class="avatar-actions">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="编辑"
                  placement="top"
                >
                  <el-button
                    type="primary"
                    icon="el-icon-edit"
                    circle
                    size="mini"
                    @click="editPerson"
                  ></el-button>
                </el-tooltip>
                <!-- <el-tooltip
                  class="item"
                  effect="dark"
                  content="拓扑关系"
                  placement="top"
                >
                  <el-button
                    type="success"
                    icon="el-icon-share"
                    circle
                    size="mini"
                    @click="showRelation"
                  ></el-button>
                </el-tooltip> -->
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除"
                  placement="top"
                >
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    size="mini"
                    @click="deletePerson(personId)"
                  ></el-button>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="other relation-collapse-content">
            <div class="relation-row">
              <span class="relation-label">出生日期：</span>
              <span class="relation-value">{{ personInfo.dateBirth }}</span>
            </div>
            <div class="relation-row">
              <span class="relation-label">身份证号：</span>
              <span class="relation-value">{{ personInfo.identity }}</span>
            </div>
            <div class="relation-row">
              <span class="relation-label">邮箱：</span>
              <span class="relation-value">{{ personInfo.email }}</span>
            </div>
            <div
              v-for="([key, value], index) in Object.entries(otherInfo)"
              :key="'other' + index"
              class="relation-row"
            >
              <span class="relation-label">{{ key }}：</span>
              <span class="relation-value">{{ value }}</span>
            </div>
          </div>

          <!-- 社会关系分组 -->
          <!-- <div class="relation-section">
            <el-collapse v-if="socializeMsg && socializeMsg.length">
              <el-collapse-item
                v-for="(item, idx) in socializeMsg"
                :key="'relation' + idx"
                :title="item.relationName + '：' + item.relationInfo.name"
              >
                <div class="relation-collapse-content">
                  <div class="relation-row">
                    <span class="relation-label">性别：</span
                    ><span class="relation-value">{{
                      item.relationInfo.sex
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">出生日期：</span
                    ><span class="relation-value">{{
                      item.relationInfo.dateBirth
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">年龄：</span
                    ><span class="relation-value">{{
                      item.relationInfo.age
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">备注：</span
                    ><span class="relation-value">{{
                      item.relationInfo.remark
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">身份证号：</span
                    ><span class="relation-value">{{
                      item.relationInfo.identity
                    }}</span>
                  </div>
                  <div class="relation-row">
                    <span class="relation-label">手机号：</span
                    ><span class="relation-value">{{
                      item.relationInfo.phone
                    }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div
              v-else
              style="color: #bbb; font-size: 14px; padding: 8px 0 0 8px"
            >
              暂无社会关系
            </div>
          </div> -->

          <!-- 社交媒体分组 -->
          <!-- <div class="media-section">
            <el-collapse>
              <el-collapse-item
                title="Telegram"
                name="telegram"
                v-if="
                  socialMedia &&
                  socialMedia.telegram &&
                  socialMedia.telegram.length
                "
              >
                <div
                  v-for="(item, idx) in socialMedia.telegram"
                  :key="'media-tg' + idx"
                  class="media-collapse-content"
                >
                  <div class="media-row">
                    <span class="media-label">用户名：</span
                    ><span class="media-value">{{ item.name }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span
                    ><span class="media-value">{{ item.idNum }}</span>
                  </div>
                  <div class="media-row" v-if="item.nickName">
                    <span class="media-label">昵称：</span
                    ><span class="media-value">{{ item.nickName }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">手机号：</span
                    ><span class="media-value">{{ item.phone }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">邮箱：</span
                    ><span class="media-value">{{ item.email }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">备注：</span
                    ><span class="media-value">{{ item.remark }}</span>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="Twitter"
                name="twitter"
                v-if="
                  socialMedia &&
                  socialMedia.twitter &&
                  socialMedia.twitter.length
                "
              >
                <div
                  v-for="(item, idx) in socialMedia.twitter"
                  :key="'media-tw' + idx"
                  class="twitter-card-box"
                >
                  <div class="twitter-card-main">
                    <el-avatar
                      :src="item._source.avatar_url || ''"
                      size="large"
                      class="twitter-avatar"
                    >
                      <i class="el-icon-user-solid" style="font-size:32px;color:#ccc" v-if="!item._source.avatar_url"></i>
                    </el-avatar>
                    <div class="twitter-info-right">
                      <div class="twitter-username-row">
                        <span class="twitter-label">用户名：</span>
                        <span class="twitter-username-value ellipsis" :title="item._source.nickname && item._source.nickname[0] ? item._source.nickname[0] : '推特用户'">
                          {{ item._source.nickname && item._source.nickname[0] ? item._source.nickname[0] : '推特用户' }}
                        </span>
                      </div>
                      <div class="twitter-summary-row">
                        <span class="twitter-label">简介：</span>
                        <span class="twitter-summary-value ellipsis" :title="item._source.summary || '暂无简介'">
                          {{ item._source.summary || '暂无简介' }}
                        </span>
                      </div>
                      <div class="twitter-stats-row">
                        <div class="twitter-stat-item">
                          <i class="el-icon-star-on twitter-stat-icon like"></i>
                          <span class="twitter-stat-num">{{ item._source.likes_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-user twitter-stat-icon follower"></i>
                          <span class="twitter-stat-num">{{ item._source.followers_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-user-solid twitter-stat-icon following"></i>
                          <span class="twitter-stat-num">{{ item._source.following_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-document twitter-stat-icon article"></i>
                          <span class="twitter-stat-num">{{ item._source.article_count || 0 }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="Linkedin"
                name="linkedin"
                v-if="
                  socialMedia &&
                  socialMedia.linkedin &&
                  socialMedia.linkedin.length
                "
              >
                <div
                  v-for="(item, idx) in socialMedia.linkedin"
                  :key="'media-li' + idx"
                  class="media-collapse-content"
                >
                  <div class="media-row">
                    <span class="media-label">用户名：</span
                    ><span class="media-value">{{ item.name }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span
                    ><span class="media-value">{{ item.idNum }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">手机号：</span
                    ><span class="media-value">{{ item.phone }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">邮箱：</span
                    ><span class="media-value">{{ item.email }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">备注：</span
                    ><span class="media-value">{{ item.remark }}</span>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                title="Facebook"
                name="facebook"
                v-if="
                  socialMedia &&
                  socialMedia.facebook &&
                  socialMedia.facebook.length
                "
              >
                <div
                  v-for="(item, idx) in socialMedia.facebook"
                  :key="'media-fb' + idx"
                  class="media-collapse-content"
                >
                  <div class="media-row">
                    <span class="media-label">用户名：</span
                    ><span class="media-value">{{ item.name }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span
                    ><span class="media-value">{{ item.idNum }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">手机号：</span
                    ><span class="media-value">{{ item.phone }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">邮箱：</span
                    ><span class="media-value">{{ item.email }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">备注：</span
                    ><span class="media-value">{{ item.remark }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div
              v-if="
                !(
                  socialMedia &&
                  (socialMedia.telegram ||
                    socialMedia.twitter ||
                    socialMedia.linkedin ||
                    socialMedia.facebook)
                )
              "
              style="color: #bbb; font-size: 14px; padding: 8px 0 0 8px"
            >
              暂无社交媒体信息
            </div>
          </div> -->
          <!-- 社交媒体分组 -->
          <div class="media-section">
            <el-collapse>
              <el-collapse-item
                v-for="(item, idx) in telegramSearchList"
                :key="'media-tg' + idx"
                :title="item.username && item.username.username ? 'Telegram ID：' + item.username.username : 'Telegram ID: 无'"
                :name="'telegram-' + idx"
              >
                <div class="media-collapse-content">
                  <div class="media-row">
                    <span class="media-label">用户名：</span>
                    <span class="media-value" :title="item.username && item.username.username ? item.username.username : '无'">{{ item.username && item.username.username ? item.username.username : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span>
                    <span class="media-value" :title="item.user_id && item.user_id.user_id ? item.user_id.user_id : '无'">{{ item.user_id && item.user_id.user_id ? item.user_id.user_id : '无' }}</span>
                  </div>
                  <div class="media-row" v-if="item.nickname && item.nickname.nickname">
                    <span class="media-label">昵称：</span>
                    <span class="media-value" :title="item.nickname.nickname">{{ item.nickname.nickname }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">手机号：</span>
                    <span class="media-value" :title="item.telephone && item.telephone.telephone ? item.telephone.telephone : '无'">{{ item.telephone && item.telephone.telephone ? item.telephone.telephone : '无' }}</span>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                v-for="(item, idx) in twitterSearchList"
                :key="'media-tw' + idx"
                :title="item.nickname && item.nickname.nickname ? 'Twitter ID：' + item.nickname.nickname : 'Twitter ID: 无'"
                :name="'twitter-' + idx"
              >
                <div class="twitter-card-box">
                  <div class="twitter-card-main">
                    <el-avatar size="large" class="twitter-avatar">
                      <i class="el-icon-user-solid" style="font-size:32px;color:#ccc"></i>
                    </el-avatar>
                    <div class="twitter-info-right">
                      <div class="twitter-username-row">
                        <span class="twitter-label">昵称：</span>
                        <span class="twitter-username-value ellipsis" :title="item.nickname && item.nickname.nickname ? item.nickname.nickname : '无'">
                          {{ item.nickname && item.nickname.nickname ? item.nickname.nickname : '无' }}
                        </span>
                      </div>
                      <div class="twitter-summary-row">
                        <span class="twitter-label">简介：</span>
                        <span class="twitter-summary-value ellipsis" :title="item.summary || '暂无简介'">
                          {{ item.summary || '暂无简介' }}
                        </span>
                      </div>
                      <div class="twitter-stats-row">
                        <div class="twitter-stat-item">
                          <i class="el-icon-star-on twitter-stat-icon like"></i>
                          <span class="twitter-stat-num">{{ item.likes_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-user twitter-stat-icon follower"></i>
                          <span class="twitter-stat-num">{{ item.followers_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-user-solid twitter-stat-icon following"></i>
                          <span class="twitter-stat-num">{{ item.following_count || 0 }}</span>
                        </div>
                        <div class="twitter-stat-item">
                          <i class="el-icon-document twitter-stat-icon article"></i>
                          <span class="twitter-stat-num">{{ item.article_count || 0 }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                v-for="(item, idx) in linkedinSearchList"
                :key="'media-li' + idx"
                :title="item._source && item._source.nickname ? 'Linkedin ID：' + item._source.nickname : 'Linkedin ID: 无'"
                :name="'linkedin-' + idx"
              >
                <div class="media-collapse-content">
                  <div class="media-row">
                    <span class="media-label">昵称：</span>
                    <span class="media-value" :title="item._source && item._source.nickname ? item._source.nickname : '无'">{{ item._source && item._source.nickname ? item._source.nickname : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span>
                    <span class="media-value" :title="item._source && item._source.user_id ? item._source.user_id : '无'">{{ item._source && item._source.user_id ? item._source.user_id : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">内容：</span>
                    <span class="media-value" :title="item._source && item._source.content ? item._source.content : '无'">{{ item._source && item._source.content ? item._source.content : '无' }}</span>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                v-for="(item, idx) in facebookSearchList"
                :key="'media-fb' + idx"
                :title="item.name ? 'Facebook ID：' + item.name : 'Facebook ID: 无'"
                :name="'facebook-' + idx"
              >
                <div class="media-collapse-content">
                  <div class="media-row">
                    <span class="media-label">用户名：</span>
                    <span class="media-value" :title="item.name ? item.name : '无'">{{ item.name ? item.name : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">ID：</span>
                    <span class="media-value" :title="item.idNum ? item.idNum : '无'">{{ item.idNum ? item.idNum : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">手机号：</span>
                    <span class="media-value" :title="item.phone ? item.phone : '无'">{{ item.phone ? item.phone : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">邮箱：</span>
                    <span class="media-value" :title="item.email ? item.email : '无'">{{ item.email ? item.email : '无' }}</span>
                  </div>
                  <div class="media-row">
                    <span class="media-label">备注：</span>
                    <span class="media-value" :title="item.remark ? item.remark : '无'">{{ item.remark ? item.remark : '无' }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div
              v-if="
                !(
                  telegramSearchList &&
                  twitterSearchList &&
                  linkedinSearchList &&
                  facebookSearchList
                )
              "
              style="color: #bbb; font-size: 14px; padding: 8px 0 0 8px"
            >
              暂无社交媒体信息
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 右侧 -->
    <div class="right">
      <el-tabs v-model="activeName" @tab-click="handleClickTab">
        <el-tab-pane
          v-for="tab in personBar"
          :key="tab.value"
          :name="tab.value"
        >
          <template #label>
            <span>{{ tab.key }}</span>
            <el-badge v-if="tab.value !== '关系拓扑'" class="badge-num" :value="getBadgeValue(tab.value)" />
          </template>
          <component
            :is="componentMap[tab.value]"
            v-if="componentMap[tab.value] && activeName === tab.value"
            :key="`${tab.value}-${componentKey}`"
            :info="info"
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 引入组织人员关系组件 -->
      <personnel-organization
        v-if="organizationData.flag"
        :data="this.organizationData"
        :personList="this.personList"
        :info="info"
      />
    </div>
    <el-dialog
      :visible.sync="topologicalRelationshipData.flag"
      :modal="false"
      :title="topologicalRelationshipData.title"
      width="50%"
    >
      <personnel-topological-relationship :info="info" />
    </el-dialog>
  </div>
</template>
<script>
import { mapMutations, mapState } from "vuex";
export default {
  name: "Details",
  data() {
    return {
      activeName: "关系拓扑",
      personid: "",
      // 人员基本信息
      personInfo: {},
      // 组织信息
      personOrg: {},
      // 人员中的关系信息
      personRelation: [],
      // 其他信息
      otherInfo: [],
      // 社会关系
      socializeMsg: [],
      // 社交媒体信息
      socialMedia: [],
      // 编辑
      organizationData: {
        flag: false,
        title: "",
      },
      // 拓扑关系图
      topologicalRelationshipData: {
        flag: false,
        title: "",
      },
      info: {},
      time: "无",
      // 组件映射表
      componentMap: {
        关系拓扑: "relationsship-topology-diagram",
        facebook: "Facebook",
        linkedin: "Linkedin",
        telegram: "Telegram",
        twitter: "Twitter",
        intelligence: "intelligence",
        relevantPerson: "relevantPerson",
        relevantOriganization: "relevantOriganization",
        public_opinion: "public_opinion"
      },
      // 组件重新渲染的key
      componentKey: 0,
    };
  },
  computed: {
    ...mapState({
      numberOfChannels: (state) => state.channelNum.numberOfChannels,
      conditionsData: (state) => state.search.conditions.conditionsData,
      personList: (state) => state.person.personList,
      personBar: (state) => state.person.personBar,
      twitter: (state) => state.person.twitter,
      telegram: (state) => state.person.telegram,
      facebook: (state) => state.person.facebook,
      linkedin: (state) => state.person.linkedin,
      identity: (state) => state.person.identity,
      phone: (state) => state.person.phone,
      email: (state) => state.person.email,
      personName: (state) => state.person.personName,
      personDetailLoading: (state) => state.person.personDetailLoading,
      selectPersonValue: (state) => state.person.selectPerson,
      twitterSearchList: (state) => state.relationsshipTopologyDiagram.twitterSearchList,
      telegramSearchList: (state) => state.relationsshipTopologyDiagram.telegramSearchList,
      linkedinSearchList: (state) => state.relationsshipTopologyDiagram.linkedinSearchList,
      facebookSearchList: (state) => state.relationsshipTopologyDiagram.facebookSearchList,
      personalRelations: (state) => state.relationsshipTopologyDiagram.personalRelations,
      organizationalRelations: (state) => state.relationsshipTopologyDiagram.organizationalRelations,
    }),
  },
  components: {
    "personnel-organization": () =>
      import("@/layout/components/archives/PersonnelOrganization.vue"), // 组织人员关系
    "personnel-topological-relationship": () =>
      import(
        "@/layout/components/archives/personnel-topological-relationship.vue"
      ), // 拓扑关系图
    "relationsship-topology-diagram": () =>
      import(
        "@/layout/components/archives/person-detail-components/relationsship-topology-diagram.vue"
      ), // 关系拓扑图
    Facebook: () =>
      import(
        "@/layout/components/searchListComponent/facebook-search-index.vue"
      ), // facebook
    Linkedin: () =>
      import("@/layout/components/searchListComponent/linkedin.vue"), // linkedin
    Telegram: () =>
      import(
        "@/layout/components/telegram-search-components/telegram-search-index.vue"
      ), // telegram
    Twitter: () =>
      import("@/layout/components/searchListComponent/twitter.vue"), // twitter
    intelligence: () =>
      import("@/layout/components/archives/detailIntelligence.vue"), // intelligence
    public_opinion: () =>
      import("@/layout/components/searchListComponent/publicOpinion.vue"), // publicOpinion
    relevantPerson: () =>
      import("@/layout/components/archives/relevantPerson.vue"), // 关系人
    relevantOriganization: () =>
      import("@/layout/components/archives/relevantOriganization.vue"), // 关系组织
  },

  created() {
    this.$store.commit("person/sendPersonBar");
    this.setTimeRange(this.time);
    this.$store.commit("person/resetPersonDetailData");
    const queryData = this.$route.query.data;
    console.log("queryData", queryData);
    if (queryData) {
      const data = JSON.parse(queryData);
      this.selectPerson(data);
      this.sendChannelData();
    }
  },

  watch: {
    personDetailLoading: {
      handler(newVal) {
        console.log("personDetailLoading", newVal);
        this.$nextTick(() => {
          !newVal && this.initData();
        });
      },
    },
  },

  methods: {
    ...mapMutations({
      sendChannelData: "channelNum/sendChannelData",
      selectPerson: "person/selectPerson",
      setConditionsData: "search/conditions/setConditionsData",
      setClearStatisticalList:
        "telegramSearch/telegramSearchList/setClearStatisticalList",
      clearSearchList: "telegramSearch/telegramSearchList/clearSearchList",
      setQueryString: "telegramSearch/telegramSearchList/setQueryString",
      setSearch: "telegramSearch/telegramSearchList/setSearch",
      setChartOn: "telegramSearch/telegramSearchDataRange/setChartOn",
      setMaxInit: "telegramSearch/telegramSearchDataRange/setMaxInit",
      clearDataRangeTree:
        "telegramSearch/telegramSearchDataRange/clearDataRangeTree",
      setTimeRange: "telegramSearch/telegramSearchConditions/setTimeRange",
      setQueryMode: "telegramSearch/telegramSearchConditions/setQueryMode",
      getListTrue: "telegramSearch/telegramSearchList/getListTrue",
    }),

    getBadgeValue(tabValue) {
      const valueMap = {
        '关系拓扑': 0, // 默认值，因为没有对应的数据
        'intelligence': this.numberOfChannels.intelligence || 0,
        'relevantPerson': this.numberOfChannels.relevantPerson || 0,
        'relevantOriganization': this.numberOfChannels.relevantOriganization || 0,
        'public_opinion': this.numberOfChannels.publicOpinion || 0,
        'telegram': this.numberOfChannels.Telegram || 0,
        'twitter': this.numberOfChannels.Twitter || 0,
        'facebook': this.numberOfChannels.Facebook || 0,
        'linkedin': this.numberOfChannels.LinkedIn || 0
      };
      
      return valueMap[tabValue] || 0;
    },

    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require('@/assets/images/user.png');
    },

    initData(d) {
      try {
        let data = null
        if (d) {
          data = d;
        } else {
          data = this.selectPersonValue;
        }
        console.log("接收到的数据:a", data);
        this.info = data;
        this.personId = data._id;
        // 适配后的数据结构
        this.personInfo = { ...data._source.params.basic };
        // 适配后的自定义字段
        this.otherInfo = data._source.params.customFields;
        // 社交媒体信息
        this.socialMedia = { ...data._source.params.media };
        // 人员关系
        // this.personRelation = data._source.params.basic.relation; // basic中的关系
        // 社会关系
        console.log("data._source.params.socialize", data._source.params.socialize);
        if (data._source.params.socialize && data._source.params.socialize.relation && data._source.params.socialize.relation[0].relationName != "") {
          this.socializeMsg = data._source.params.socialize.relation[0].relationName;
        } else {
          this.socializeMsg = [];
        }
        console.log("personRelation", this.personRelation);
      } catch (error) {
        console.error("解析数据时出错:", error);
      }
    },

    // 切换tab
    handleClickTab(tab, event) {
      this.activeName = tab.name;
      if (tab.name == "public_opinion") {
        this.setConditionsData(this.personName);
      }
    },

    editPerson() {
      this.organizationData = {
        flag: true,
        title: "编辑人员",
      };
    },
    showRelation() {
      this.topologicalRelationshipData = {
        flag: true,
        title: "拓扑关系",
      };
    },
    deletePerson(data) {
      this.$confirm("确定删除该目标人吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        window.main.$store.commit("person/deletePer", data);
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    },
  },
};
</script>
<style src="../../../assets/scss/targetDetail.scss" lang="scss"></style>
<style scoped>
::v-deep .el-tabs__header {
  margin: 0;
}

::v-deep .el-tabs__content {
  padding: 0;
  height: calc(100vh - 100px);
}

::v-deep .el-tab-pane {
  height: 100%;
}

/* 禁用 tab 的样式 */
::v-deep .el-tabs__item.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;

  &:hover {
    color: #c0c4cc !important;
  }
}

::v-deep .el-collapse-item__header {
  font-size: 14px;
}

::v-deep .custom-avatar {
  /* 基础尺寸（保持1:1.4比例） */
  width: 150px;
  height: 210px;
  /* 150 * 1.4 = 210 */

  /* 容器样式 */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  /* 超出部分隐藏 */
}

::v-deep .custom-avatar .el-avatar__image {
  /* 图片适配 */
  object-fit: cover;
  /* 保持比例填充容器 */
  width: 100%;
  height: 100%;
}
::v-deep .el-collapse-item__header {
  font-size: 12px;
  font-weight: 600;
  padding: 5px;
}
.section-title {
  font-size: 17px;
  font-weight: 700;
  color: #333;
  margin: 18px 0 8px 8px;
  border-left: 3px solid #409eff;
  padding-left: 8px;
}
.media-card {
  border-radius: 8px;
  margin-bottom: 10px;
  background: #f8fafd;
  border: 1px solid #e6e8eb;
  box-shadow: none;
  padding: 10px 14px;
}
.media-title {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}
.media-info {
  font-size: 14px;
  color: #444;
  margin-bottom: 2px;
}
.twitter-card-box {
  background: #f8fafd;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}
.twitter-card-main {
  display: flex;
  align-items: center;
  gap: 12px;
}
.twitter-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50%;
  flex-shrink: 0;
  background: #f0f0f0;
}
.twitter-info-right {
  flex: 1;
  min-width: 0;
}
.twitter-username-row, .twitter-summary-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  height: 20px;
}
.twitter-label {
  color: #666;
  font-weight: 600;
  min-width: 50px;
  font-size: 12px;
  flex-shrink: 0;
}
.twitter-username-value, .twitter-summary-value {
  color: #222;
  margin-left: 8px;
  font-weight: 500;
  font-size: 12px;
  flex: 1;
  max-width: calc(100% - 58px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.twitter-stats-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 4px;
}
.twitter-stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}
.twitter-stat-icon {
  font-size: 14px;
  margin-right: 4px;
}
.twitter-stat-icon.like {
  color: #ff6b6b;
}
.twitter-stat-icon.follower {
  color: #4dabf7;
}
.twitter-stat-icon.following {
  color: #69db7c;
}
.twitter-stat-icon.article {
  color: #ffd43b;
}
.twitter-stat-num {
  font-weight: 500;
  color: #444;
  font-size: 12px;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
