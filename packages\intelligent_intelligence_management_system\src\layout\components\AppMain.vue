<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
    <global-robot />
  </section>
</template>

<script>
import GlobalRobot from "@/components/GlobalRobot.vue";
export default {
  name: "AppMain",
  components: {
    GlobalRobot,
  },
  computed: {
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style scoped>
.app-main {
  min-height: calc(100vh - 130px);
  position: relative;
  overflow: hidden;
  height: 100%;
  margin-right: 1vh;
  margin-bottom: 1vh;
  /*  width: 98%; */
  margin: 0 auto;
}
</style>
