<template>
  <div>
    <el-card shadow="always">
      <div slot="header">
        <span>节点状态</span>
      </div>
      <div class="node_body" v-if="nodeTaskList.length">
        <div v-for="item in nodeTaskList" class="node_box">
          <div
            class="circle"
            :style="
              item.task_status === 'monitor'
                ? 'background:green'
                : 'background:red'
            "
          ></div>
          <div class="nide_page">
            <img
              v-if="
                nodeUserInfo &&
                nodeUserInfo[item.telephone] &&
                nodeUserInfo[item.telephone].iconSha512
              "
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                nodeUserInfo[item.telephone].iconSha512 +
                '?session_id=' +
                $store.state.userInfo.session_id
              "
            />
            <img v-else src="@/assets/images/user.png" />
          </div>
          <div
            class="node_info"
            v-if="
              nodeUserInfo && item.telephone && nodeUserInfo[item.telephone]
            "
          >
            <p v-if="nodeUserInfo[item.telephone].username.length">
              <b>用户名：</b
              ><span v-for="item in nodeUserInfo[item.telephone].username"
                >({{ item }})
              </span>
            </p>
            <p v-if="nodeUserInfo[item.telephone].nickname.length">
              <b>昵称：</b
              ><span v-for="item in nodeUserInfo[item.telephone].nickname"
                >({{ item }})</span
              >
            </p>
            <p v-if="nodeUserInfo[item.telephone].user_id.length">
              <b>用户ID：</b
              ><span v-for="item in nodeUserInfo[item.telephone].user_id"
                >({{ item }})</span
              >
            </p>
            <p v-if="nodeUserInfo[item.telephone].location">
              <b>归属地：</b>{{ nodeUserInfo[item.telephone].location }}
            </p>
            <p><b>手机号：</b>{{ item.telephone }}</p>
            <p><b>节点IP：</b>{{ item.vps_node }}</p>
            <p>
              <b>更新时间：</b
              >{{ $tools.timestampToTime(Math.trunc(item.timestamp)) }}
            </p>
          </div>
        </div>
      </div>
      <div class="node_null">
        <p>暂无节点在线。</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      nodeTaskList: [1, 2, 3, 4, 5, 6, 7],
      nodeUserInfo: {},
    };
  },
  created() {
    this.sendNodeList();
  },
  mounted() {
    this.nodeTime = setInterval(() => {
      this.sendNodeList();
    }, 60000);
  },
  beforeDestroy() {
    clearInterval(this.nodeTime);
    this.nodeTime = null;
  },
  methods: {
    // 获取节点列表
    sendNodeList() {
      this.nodeTaskList = [];
      window.main.$constant_socket.sendData(
        "Api.Node.ChildNodeList",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              nodePath:
                "/tmp/vps/telegram_spider_manager_system/telegram_spider_status",
            },
          },
        ],
        (res) => {
          res?.forEach((item) => {
            this.sendNodeTaskState(item);
          });
        }
      );
    },
    //获取节点任务
    sendNodeTaskState(item) {
      window.main.$constant_socket.sendData(
        "Api.Node.ChildNodeList",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              nodePath:
                "/tmp/vps/telegram_spider_manager_system/telegram_spider_status/" +
                item,
            },
          },
        ],
        (res) => {
          let msg = {};
          res?.forEach((value) => {
            msg[
              "/tmp/vps/telegram_spider_manager_system/telegram_spider_status/" +
                item +
                "/" +
                value
            ] = "";
          });
          if (Object.keys(msg).length) {
            this.setNodeTaskState(msg);
          }
        }
      );
    },
    setNodeTaskState(msg) {
      if (Object.keys(msg).length) {
        window.main.$constant_socket.sendData(
          "Api.Node.NodeData",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
              },
              msg: msg,
            },
          ],
          (res) => {
            let arr = [];
            for (const key in res) {
              if (Object.hasOwnProperty.call(res, key)) {
                for (const key1 in res[key]) {
                  if (Object.hasOwnProperty.call(res[key], key1)) {
                    const item = res[key][key1];
                    arr.push(item);
                  }
                }
              }
            }
            this.nodeTaskList.push(...arr);
            console.log("this.nodeTaskList", this.nodeTaskList);
            this.sendTaskUserInfo(arr);
          }
        );
      }
    },
    // 获取账号信息
    sendTaskUserInfo(item) {
      let phoneRow = [];
      const sha512 = require("sha512");
      const hash =
        sha512("p;/instant_msg/telegram/telephone").toString("hex") +
        ";p;/instant_msg/telegram/telephone;";
      item?.forEach((item) => {
        phoneRow.push(hash + item.telephone);
      });
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: phoneRow,
            },
            msg: {
              type: "public",
              path: "/instant_msg/telegram/telephone",
              relation: "",
              prefix: "",
            },
          },
        ],
        (res) => {
          res?.forEach((item) => {
            let userInfo = {
              user_id: [],
              location: "",
              nickname: [],
              iconSha512: "",
              username: [],
            };
            const info = item.columnValues.d;
            for (const key in info) {
              if (key.startsWith("user_id") && info[key].user_id) {
                userInfo.user_id.push(info[key].user_id);
              }
              if (key.startsWith("location") && info[key].location) {
                userInfo.location = info[key].location;
              }
              if (key.startsWith("nickname") && info[key].nickname) {
                userInfo.nickname.push(info[key].nickname);
              }
              if (key.startsWith("username") && info[key].username) {
                userInfo.username.push(info[key].username);
              }
              if (key.startsWith("icon") && info[key].icon.sha512_hash) {
                userInfo.iconSha512 = info[key].icon.sha512_hash;
              }
            }
            if (item.columnValues.hasOwnProperty("d")) {
              this.$set(this.nodeUserInfo, info._._, userInfo);
            } else {
              console.log("this.nodeUserInfo", this.nodeUserInfo);
            }
          });
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.node_body {
  height: 85vh;
  overflow: auto;
  background-color: #f5f3f3;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  .node_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 315px;
    height: 160px;
    background-image: linear-gradient(
      91deg,
      #3d6d83,
      #829396,
      #c0baa9,
      #ffe3bc
    );
    background-color: rgb(255, 255, 255);
    border: 1px solid #eee;
    box-shadow: 0 0 10px #eee;
    border-radius: 15px;
    padding: 10px;
    margin: 10px;
    .nide_page {
      img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
      }
    }
    .node_info {
      font-size: 14px;
      color: #000;
      p {
        margin-bottom: 5px;
      }
    }
    position: relative;
    .circle {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 10px;
      height: 10px;
      border-radius: 10px;
      animation: flicker 1500ms infinite;
    }

    @keyframes flicker {
      from {
        opacity: 1;
      }

      25% {
        opacity: 0.7;
      }

      50% {
        opacity: 0.5;
      }

      70% {
        opacity: 0.7;
      }

      to {
        opacity: 1;
      }
    }
  }
}
.node_null {
  padding-top: 50px;
  height: 85vh;
  font-size: 28px;
  text-align: center;
  background-color: #f5f3f3;
}
</style>
