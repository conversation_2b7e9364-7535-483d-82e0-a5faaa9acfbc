import { case_id } from "@/i18n/zh/analysis";

export default {
  namespaced: true,
  state: {
    from: 0,
    size: 20,
    task_status: "all", // 任务状态
    allTaskList: [], // 所有任务
    allTaskListCount: 0, // 总大小
    taskLoading: false,
    parseReadyTaskList: [],
    parseReadyTaskListCount: 0,
    parseEndTaskList: [],
    parseEndTaskListCount: 0,
    parseErrorTaskList: [],
    parseErrorTaskListCount: 0,
    showTaskList: [], //当前展示数据
    logList: [],
    logTemporaryList: [],
    logLastRowKey: [],
    logsDialogVisible: false,
    task_id: "",
    taskDetail: {},
    taskMethodDetail: null,
    intelligenceId: "",
  },
  mutations: {
    // 重置所有数据
    resetTaskState(state) {
      state.from = 0;
      state.task_status = "all";
      state.allTaskList = [];
      state.allTaskListCount = 0;
      state.taskLoading = false;
      state.parseReadyTaskList = [];
      state.parseReadyTaskListCount = 0;
      state.parseEndTaskList = [];
      state.parseEndTaskListCount = 0;
      state.parseErrorTaskList = [];
      state.parseErrorTaskListCount = 0;
      state.showTaskList = [];
    },
    setLogsDialogVisible(state, v) {
      state.logsDialogVisible = v;
    },
    // 设置任务类型,根据总数据进行过滤
    setTaskStatus(state, v) {
      state.task_status = v;
      // 先清空所有分类数组和计数器
      state.parseEndTaskList = [];
      state.parseEndTaskListCount = 0;
      state.parseReadyTaskList = [];
      state.parseReadyTaskListCount = 0;
      state.parseErrorTaskList = [];
      state.parseErrorTaskListCount = 0;
      // 遍历所有任务并分类
      state.allTaskList.forEach((item) => {
        const status = item.columnValues.info.status;
        if (
          status === "parse_ready" ||
          status === "parse_pause" ||
          status === "initializing" ||
          status === "parsing"
        ) {
          state.parseReadyTaskList.push(item);
          state.parseReadyTaskListCount++;
        } else if (status === "error") {
          state.parseErrorTaskList.push(item);
          state.parseErrorTaskListCount++;
        } else if (status === "parse_end") {
          state.parseEndTaskList.push(item);
          state.parseEndTaskListCount++;
        }
      });
      window.main.$store.commit("aiTaskQueue/setPageAndData", 1);
    },
    //储存任务详情结果
    setTaskDetail(state, taskDetail) {
      console.log("setTaskDetail", taskDetail);
      state.taskDetail = taskDetail;
    },

    // 翻页，进行数据截取
    setPageAndData(state, page) {
      let sourceList = [];
      switch (state.task_status) {
        case "parse_end":
          sourceList = state.parseEndTaskList;
          break;
        case "processing":
          sourceList = state.parseReadyTaskList;
          break;
        case "error":
          sourceList = state.parseErrorTaskList;
          break;
        default:
          sourceList = state.allTaskList;
      }
      // page=1: slice(20,40) → items 20-39
      state.showTaskList = sourceList.slice(
        (page - 1) * state.size,
        page * state.size
      );
    },

    // 请求总数据
    sendListAnalysisList(state) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.List",
        [
          {
            head: {
              from: state.from,
              size: state.size,
            },
            msg: {
              task_authority: "username",
              task_type: "ai_workflow_task", //"intelligence_task",
              familys: ["info", "parm"],
            },
          },
        ],
        "aiTaskQueue/setAllAnalysisList"
      );
    },
    // 设置总数据，当数据返回数量等于20条，继续请求
    setAllAnalysisList(state, v) {
      state.allTaskList = state.allTaskList.concat(v);
      if (v.length >= 20) {
        state.from += 20;
        window.main.$store.commit("aiTaskQueue/sendListAnalysisList");
      } else {
        state.allTaskListCount = state.allTaskList.length;
        window.main.$store.commit(
          "aiTaskQueue/setTaskStatus",
          state.task_status
        );
      }
    },
    //获取任务日志
    sendGetTaskLog(state, v) {
      state.task_id = v;
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListLogs",
        [
          {
            head: {
              row_key: state.logLastRowKey,
              size: 200,
              //qualifier:state.logQualifier,
            },
            msg: {
              task_authority: "username",
              task_id: v,
              task_type: "ai_workflow_task",
            },
            //msg:{task_type:['social_platform_task'], familys:['logs']}
          },
        ],
        "aiTaskQueue/setGetTaskLog"
      );
    },
    setGetTaskLog(state, res) {
      function processThinkTags(html) {
        // const thinkRegex = /<think>(.*?)<\/think>/g;
        return html.replace(
          /<think>/g,
          `<div style="color:red;" class="think-block">`
        );
      }
      function processThinkTagsa(html) {
        // const thinkRegex = /<think>(.*?)<\/think>/g;
        return html.replace(/<\/think>/g, (match, p1) => {
          return `</div>`;
        });
      }

      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          let myMessage = res[i].columnValues.info.msg.message;
          let thinkData = "";
          if (res[i].columnValues.info.msg.think.length > 0) {
            thinkData = "<div>" + res[i].columnValues.info.msg.think + "</div>";
          }

          if (myMessage.role === "user") {
            let render = myMessage.content;
            var htmlChar = render.replace(/&lt;/g, "<");
            htmlChar = htmlChar.replace(/&gt;/g, ">");
            const processedHtml = processThinkTags(htmlChar);
            const processedHtmla = processThinkTagsa(processedHtml);
            let userData = {
              role: "user",
              content: processedHtmla,
            };
            state.logTemporaryList.push(userData);
          } else if (myMessage.role === "assistant") {
            let render = myMessage.content;
            let assistantData = null;
            if (render) {
              assistantData = {
                role: "assistant",
                content: thinkData + render,
              };
              state.logTemporaryList.push(assistantData);
            }
          } else if (myMessage.role === "tool") {
            let render = myMessage.content;
            let fun = res[i].columnValues.info.msg.extra.function;
            let toolData = {
              role: "tool",
              content: render,
              tool_call_id: myMessage.tool_call_id,
              tool_calls: [
                {
                  function: {
                    arguments: fun.arguments,
                    name: fun.name,
                  },
                },
              ],
            };
            state.logTemporaryList.push(toolData);
          }
        }

        let arr = [];
        arr.push(res[res.length - 1].row);
        state.logLastRowKey = arr;
        window.main.$store.commit("aiTaskQueue/sendGetTaskLog", state.task_id);
      } else {
        if (state.logTemporaryList.length > 0) {
          state.logList = state.logTemporaryList.reverse();
        }
      }

      state.logsDialogVisible = true;
      console.log("日志", state.logList);
    },
    setClearLastRowKey(state) {
      state.logLastRowKey = [];
    },
    setClearLogList(state) {
      state.logList = [];
    },
    setClearLogTemporaryList(state) {
      state.logTemporaryList = [];
    },
    // 删除任务
    sendDelTask(state, v) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Del",
        [
          {
            head: {
              row_key: v,
            },
            msg: {
              task_authority: "username",
              task_type: "ai_workflow_task",
            },
          },
        ],
        "aiTaskQueue/getDel"
      );
    },
    getDel(state, v) {
      if (v.status === "ok") {
        window.main.$message({
          message: "删除成功",
          type: "success",
        });
        window.main.$store.commit("aiTaskQueue/resetTaskState");
        window.main.$store.commit("aiTaskQueue/sendListAnalysisList");
      }
    },
    //添加情报
    addIntelligence(state, data) {
      //使用当前时间戳与随机数结合当作预警词的唯一id
      function reduceNumber() {
        let soleValue = Math.round(new Date().getTime() / 1000).toString();
        let random = new Array(
          "a",
          "b",
          "c",
          "d",
          "e",
          "f",
          "g",
          "h",
          "i",
          "j",
          "k",
          "l",
          "m",
          "n"
        );
        for (let i = 0; i < 6; i++) {
          let index = Math.floor(Math.random() * 13);
          soleValue += random[index];
        }
        return soleValue;
      }
      console.log(
        "添加情报",
        data,
        window.main.$store.state.intellManageTree.caseDetail.row
      );
      state.intelligenceId = reduceNumber();
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "case",
              index: "key_intelligence",

              id: state.intelligenceId,
            },
            msg: {
              type: "case",
              case_id: window.main.$store.state.intellManageTree.caseDetail.row,
              content_article: data.content,
              title: data.name,
              params: [
                { k: "type", v: [data.type] },
                { k: "content_article", v: [data.content] },
                { k: "id", v: [state.intelligenceId] },
              ],
            },
          },
        ],
        "organization/addIntelligenceCallBack"
      );
    },
    addIntelligenceCallBack(state, data) {
      if (data.status == "ok") {
        window.main.$message.success("添加情报成功");
        /*   window.main.$store.commit("organization/getOrgani"); */
      }
    },
  },
  actions: {
    //获取任务的method
    getTaskMethod({ state, commit }, v) {
      let arr = v.split(";");
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              row_key: [v],
            },
            msg: {
              task_authority: "username",
              task_type: arr[1],
            },
          },
        ],
        (res) => {
          console.log("获取任务的method", res);
          state.taskMethodDetail = res[0];
        }
      );
    },
  },
};
