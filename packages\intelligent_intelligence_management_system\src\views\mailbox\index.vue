<template>
  <div>
    <el-card shadow="always">
      <div slot="header" style="display: flex; justify-content: space-between">
        <span>站内邮箱</span>
        <el-button v-show="afterLogin" type="text" icon="el-icon-user"
          style="font-size: 20px; color: #fff; padding: 0px" @click="changePasswd">
        </el-button>
      </div>
      <div v-if="begRender" class="model_lay">
        <el-dialog title="修改密码" :visible.sync="showChangePasswd" width="30%">
          <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px">
            <!-- <div class="title">
          <span style="margin-left:10px;font-size: 20px;">修改密码 </span>
        </div> -->
            <el-form-item label="邮箱" prop="mail" style="margin-right: 20px">
              <!-- <el-input v-model.number="ruleForm.age"></el-input> -->
              <el-input v-model="mailUsername" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="pass" style="margin-right: 20px">
              <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="checkPass" style="margin-right: 20px">
              <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePasswdFn('ruleForm')">修改密码</el-button>
              <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-form v-show="regist" :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px"
          class="mailForm">
          <div class="title">
            <span style="margin-left: 10px; font-size: 20px">注册 </span>
          </div>
          <el-form-item label="邮箱" prop="mail" style="margin-right: 20px">
            <!-- <el-input v-model.number="ruleForm.age"></el-input> -->
            <el-input v-model="mailUsername" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="pass" style="margin-right: 20px">
            <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="checkPass" style="margin-right: 20px">
            <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="registAccount('ruleForm')">注册</el-button>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>

        <el-form v-show="logIn" v-loading="loginLoading" element-loading-text="登录中，请稍候..." :model="ruleForm" status-icon
          :rules="rules" ref="ruleForm" label-width="100px" class="mailForm">
          <div class="title">
            <span style="margin-left: 10px; font-size: 20px">登录 </span>
          </div>
          <el-form-item label="邮箱" prop="mail" style="margin-right: 20px">
            <el-input v-model="mailUsername" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="pass" style="margin-right: 20px">
            <el-input type="password" v-model="ruleForm.pass" autocomplete="off"
              @keyup.enter.native="logInBtn('ruleForm')"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="logInBtn('ruleForm')">登录</el-button>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>




        <el-tabs type="" v-show="afterLogin" v-model="tabActive" tab-position="left" style="height: 85vh"
          @tab-click="handleTabClick">
          <el-tab-pane name="Sent" style="">
            <span slot="label" style="font-size: 15px; float: left; margin-left: 25px">
              发 信
            </span>
            <el-row>
              <el-col :span="16">
                <el-row class="mailRow">
                  <el-col :span="2" class="mailCol">
                    <div class="">收件人</div>
                  </el-col>
                  <el-col :span="22">
                    <div class="">
                      <el-input v-model="sendMailTo" placeholder=""></el-input>
                    </div>
                    <el-tag v-for="tag in checkList" :key="tag" style="margin-right: 10px">
                      {{ tag }}
                    </el-tag>
                  </el-col>
                </el-row>
                <!-- <el-row v-if="checkList.length != 0" style="margin-left:20px">
              <el-col :span="2">
                <div class=""><br></div>
              </el-col>
              <el-col :span="22">
                <el-tag
                  v-for="tag in checkList" :key="tag"
                  style="margin-right: 10px"
                >
                  {{tag}}
                </el-tag>
              </el-col>
            </el-row> -->

                <el-row class="mailRow">
                  <el-col :span="2" class="mailCol">
                    <div class="">主题</div>
                  </el-col>
                  <el-col :span="22">
                    <div class="">
                      <el-input v-model="sendMail.subject" placeholder=""></el-input>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mailRow">
                  <el-col :span="2" class="mailCol">
                    <div class="">正文</div>
                  </el-col>
                  <el-col :span="22" style="border: 1px solid #bbb">
                    <el-upload ref="uploadFile" class="upload-demo" :auto-upload="false" action="" :limit="2" multiple
                      style="margin: 20px 0 10px 0; border: 0px solid #bbb" :on-exceed="handleExceed"
                      :on-change="handleChange" :before-remove="beforeRemove" :on-remove="handleRemove">
                      <el-button size="small" type="primary">添加附件</el-button>
                      <!-- <div slot="tip" class="el-upload__tip">只能添加2个不超过1MB的附件。</div> -->
                    </el-upload>
                    <!-- <el-input type="textarea" :autosize="{ minRows: 10}"  placeholder="" v-model="sendMail.text" style="margin: 10px 0 10px 0;border: 1px solid #bbb;"/> -->
                    <div style="border: 1px solid #ccc">
                      <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                        :mode="mode" />
                      <Editor style="height: 300px; overflow-y: hidden" v-model="html" :defaultConfig="editorConfig"
                        :mode="mode" @onCreated="onCreated" @onChange="onChange" />
                    </div>
                    <!-- <el-upload ref="uploadPicture" action="#" :auto-upload="false" list-type="picture-card" :limit="2" multiple style="margin: 10px 0 20px 0;border: 0px solid #bbb;"
                            :on-exceed="handlePictureExceed"
                            :on-change="handlePictureChange"
                            :on-preview="handlePictureCardPreview"
                            :before-remove="beforePictureRemove"
                            :on-remove="handlePictureRemove">
                  <i class="el-icon-plus"></i>
                  <div slot="tip" class="el-upload__tip">只能添加2个不超过1MB的图片。</div>
                </el-upload> -->
                    <!-- <el-upload
                    ref="sendImage"
                    class="upload-demo"
                    :auto-upload="false"
                    action=""
                    multiple
                    :limit="1"
                    :on-change="handlePictureChange">
                    <el-button icon="el-icon-picture-outline"
                      style="font-size:20px; padding:12px 5px 0px 5px;border:0px"></el-button>
                  </el-upload> -->
                    <el-dialog :visible.sync="dialogVisible">
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2" class="mailCol">
                    <div class=""><br /></div>
                  </el-col>
                  <el-col :span="22" style="padding: 20px">
                    <el-button type="primary" icon size="mini" @click="sendBtnClick">
                      发送
                      <i class="el-icon-upload el-icon--right" />
                    </el-button>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="4" style="">
                <div style="
                    border: 1px solid #eee;
                    padding: 10px 0px 10px 20px;
                    margin-top: 20px;
                    background-color: #304156;
                    color: #fff;
                  ">
                  联系人列表
                </div>
                <el-checkbox-group v-model="checkList" style="
                    border: 1px solid #eee;
                    margin-top: 0px;
                    padding: 20px;
                    height: 705px;
                  ">
                  <el-checkbox v-for="(listV, listK, listI) in contactList" :key="`list${listI}`"
                    style="margin-bottom: 10px" :label="listV.remark
                      ? listV.remark + ' [ ' + listV.account + ' ]'
                      : listV.username + ' [ ' + listV.account + ' ]'
                      " :value="listV.account">
                  </el-checkbox>
                  <!-- </div> -->
                </el-checkbox-group>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane name="INBOX">
            <span slot="label">
              <!-- 收件箱左边树 -->
              <el-tree :data="inboxList" node-key="id" default-expand-all :expand-on-click-node="false">
                <div class="custom-tree-node" slot-scope="{ node, data }" @mouseenter="mouseEnterEven(data)"
                  @mouseleave="mouseLeaveEven(data)" :id="treeId" @click="inboxListClick(data)">
                  <span :style="data.id == curMailboxId ? 'color:#409EFF' : ''">
                    {{ node.label }}
                  </span>
                  <span v-show="treeButtonShow[data.id]" style="margin-left: 10px">
                    <el-button v-show="data.id != 'INBOX'" type="text" @click.stop="MailListRemove(node, data)"
                      icon="el-icon-delete-solid">
                    </el-button>
                    <el-button type="text" @click.stop="MailListAdd(data)" icon="el-icon-circle-plus">
                    </el-button>
                  </span>
                </div>
              </el-tree>
            </span>

            <!-- 收件箱列表 -->
            <el-table ref="multipleTable" :data="curInbox" tooltip-effect="dark" style="width: 100%" height="800px">
              <el-table-column prop="from" label="发件人" width="400">
              </el-table-column>
              <el-table-column prop="subject" label="主题" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="date" label="日期" width="400">
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="success" @click="moveMail(scope.row)" size="mini">移动<i
                      class="el-icon-rank el-icon--right"></i></el-button>
                  <el-button type="primary" @click="handleRowClick(scope.row)" size="mini">查看<i
                      class="el-icon-view el-icon--right"></i></el-button>
                  <el-button type="danger" @click="delMail(scope.row)" size="mini">删除<i
                      class="el-icon-delete el-icon--right"></i></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <el-dialog :visible.sync="dirDialogVisible" width="10%" @closed="dirDialogCancle">
          <el-input v-model="newDirInput" placeholder="请输入目录名称" @keyup.enter.native="dirDialogConfirm">
          </el-input>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="dirDialogCancle">取 消</el-button>
            <el-button size="mini" type="primary" @click="dirDialogConfirm">确 定</el-button>
          </span>
        </el-dialog>

        <el-dialog title="选择目录" :visible.sync="moveDialogVisible" width="20%">
          <el-tree :data="inboxList" node-key="id" default-expand-all :expand-on-click-node="false">
            <div class="custom-tree-node" slot-scope="{ node, data }" @click="moveDirSelect(data)">
              <span style="padding-right: 0px">
                {{ node.label }}
              </span>
            </div>
          </el-tree>
        </el-dialog>

        <!-- 邮件详情 -->
        <el-dialog title="邮件详情" :visible.sync="showDetail" :before-close="handleDlgClose" width="100%" append-to-body
          class="mailDetail" top="5px">
          <div v-loading="loading" element-loading-text="接收消息中" element-loading-spinner="el-icon-loading"
            style="overflow: auto">
            <!-- <div class="analisis" style="display: flex; justify-content: space-between">
          <span>
            <i class="el-icon-message" style="" />
            消息
          </span>
        </div> -->
            <el-tabs type="card">
              <el-tabs style="height: 100%; margin-top: -60px">
                <el-tab-pane>
                  <el-row class="detailRow">
                    <el-col :span="6" class="detailCol">
                      <div>
                        主题:
                        <span style="font-weight: bold">{{
                          headersData.test
                          }}</span>
                      </div>
                    </el-col>
                    <el-col :span="6" class="detailCol">
                      <div>
                        发件人: <span>{{ headersData.from }}</span>
                      </div>
                    </el-col>
                    <el-col :span="6" class="detailCol">
                      <div>
                        时间: <span>{{ headersData.time }}</span>
                      </div>
                    </el-col>
                    <el-col :span="6" class="detailCol">
                      <div>
                        收件人: <span>{{ headersData.to }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </el-tab-pane>
              </el-tabs>
            </el-tabs>
            <!-- <mhtml class="emailbody">
          <div> <span class="mm" >Content-Type:</span>{{mhtmldata.ContentType}}</div>
          <div><span class="mm" >Content-Language:</span>{{mhtmldata.ContentLanguage}}</div>
          <div><span class="mm" >To:</span>{{mhtmldata.To}}</div>
          <div><span class="mm" >From:</span>{{mhtmldata.From}}</div>
          <div><span class="mm" >Message-Id:</span>{{mhtmldata.MessageId}}</div>
          <div><span class="mm" >Date:</span>{{mhtmldata.Date}}</div>
          <div><span class="mm" >Mime-Version:</span>{{mhtmldata.MimeVersion}}</div>
        </mhtml> -->

            <!-- <div class="emailbody" v-html="htmlData" style="white-space: pre-wrap"></div> -->
            <!-- <div  v-if="fileDataAll.length>0" class="file">
            <p>附件:</p>
            <a v-for='(item,index) in fileDataAll' :key="index" :href="item.fileblob" :download="item.filename" title="点击保存">{{item.filename}}</a>
          </div> -->


            <!-- 我写的，邮件展示内容 -->
            <iframe :src="mailBoxUrl" width="100%" height="400" frameborder="0"
              style="margin-top: 20px; border: 1px solid #eee;"
              sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-downloads"></iframe>
          </div>
        </el-dialog>

        <!-- <el-container>
  		<el-aside width="auto" style="min-width:200px;padding:20px;background-color:#fff;border-right:1px solid #eee;height:100%">

      </el-aside>
  		<el-container>
    		<el-header>Header</el-header>
    		<el-main>Main</el-main>
  		</el-container>
		</el-container> -->
      </div>
    </el-card>
  </div>
</template>
<script>
import { mapActions, mapState } from "vuex";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { DomEditor } from "@wangeditor/editor";
import { IToolbarConfig, createToolbar } from "@wangeditor/editor";
import mailbox from "@/store/modules/mailbox";

export default {
  components: { Editor, Toolbar },

  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      selectMailId: 0,
      dirDialogVisible: false,
      moveDialogVisible: false,
      newDirInput: "",
      curMailboxId: "",
      addMailboxId: "",
      //hash
      treeId: 0,
      treeButtonShow: {},
      hash: "",
      htmltext: "",
      bigp: [],
      smallp: [],
      attachments: [],
      bigattachments: [],
      smallattachments: [],
      texth: [],
      checkList: [],
      content_text: "",
      urlss: "",

      showChangePasswd: false,
      editor: null,
      html: "",
      toolbarConfig: {
        toolbarKeys: [
          "undo",
          "redo",
          "|",
          "headerSelect",
          "|",
          "bold",
          "underline",
          "italic",
          "color",
          "bgColor",
          "|",
          "justifyLeft",
          "justifyRight",
          "justifyCenter",
          "justifyJustify",
          "|",
          "indent",
          "delIndent",
          "|",
          "emotion",
          "uploadImage",
        ],
      },

      editorConfig: {
        placeholder: "",

        hoverbarKeys: {
          text: {
            menuKeys: [
              "headerSelect",
              "bold",
              "color",
              "bgColor",
              "clearStyle",
            ],
          },
          image: {
            menuKeys: [
              "imageWidth30",
              "imageWidth50",
              "imageWidth100",
              "deleteImage",
            ],
          },
        },
        MENU_CONF: {
          uploadImage: {
            server: "#",
            // uploadImgMaxSize:1 *1024,
            maxFileSize: 1 * 1024 * 1024,
            maxNumberFiles: 1,
            base64LimitSize: 10 * 1024 * 1024,

            onBeforeUpload(file) {

              return file;
            },
            onSuccess(file, res) {

            },

            onError(file, err, res) {
              alert("最大上传1MB的图片");

              // insertFn(url, alt, href)
            },
          },
        },
      },

      mode: "default", // or 'simple'
      tabActive: "Sent",
      dialogImageUrl: "",
      dialogVisible: false,
      spanDisabled: false,
      loading: false,
      begRender: false,
      mailUsername: "",
      regist: false,
      logIn: false,
      afterLogin: false,
      showDetail: false,
      ruleForm: {
        pass: "",
        checkPass: "",
      },
      rules: {
        pass: [{ validator: validatePass, trigger: "blur" }],
        checkPass: [{ validator: validatePass2, trigger: "blur" }],
      },
      curInbox: [],
      sendMail: {
        to: [],
        subject: "",
        text: "",
        picture: [],
        attachment: [],
      },
      sendMailTo: "",


      //我自己写的
      loginLoading: false,//增加登录中的状态显示
    };
  },
  created() {
    this.loading = true;
    this.regist = false;
    this.logIn = false;
    this.afterLogin = false;
    this.begRender = false;
    this.$store.state.mailbox.getParm = false;
    this.$store.state.mailbox.loginSucc = false;
    this.$store.state.mailbox.isRegist = false;
    this.$store.commit("home/sendMessageSystem");
    this.$store.commit("mailbox/checkUser");
    // const toolbar = DomEditor.getToolbar()

    // const curToolbarConfig = toolbar.getConfig()
    //


  },

  computed: {
    getParm() {

      return this.$store.state.mailbox.getParm;
    },
    loginState() {

      return this.$store.state.mailbox.loginSucc;
    },
    getInbox() {

      return this.$store.state.mailbox.curInbox;
    },
    // htmlData() {
    //   return decodeURI(this.$store.state.mailbox.htmlData)
    // },
    // 从vuex中得到的数据
    ...mapState({
      headersData: (state) => state.mailbox.headersData,
      fileDataAll: (state) => state.mailbox.fileDataAll,
      mhtmldata: (state) => state.mailbox.mhtmldata,
      contactList: (state) => state.mailbox.contactList,
      inboxList: (state) => state.mailbox.inboxList,
      basestrs: (state) => state.mailbox.basestrs,
      htmlData: (state) => state.mailbox.htmlData,
      passwd: (state) => state.mailbox.passwd,
      // blobstrs:(state)=> state.mailbox.blobstrs,
      // urls:(state)=>state.mailbox.urls

      //我自己写的
      mailBoxUrl: (state) => state.mailbox.mailBoxUrl,
    }),
  },
  watch: {
    getParm: {
      handler(newVal, oldVal) {
        if (this.$store.state.mailbox.isRegist) {
          setTimeout(() => {
            this.mailUsername =
              this.$store.state.userInfo.userinfo.username +
              "@" +
              this.$store.state.userInfo.userinfo.authority +
              ".com";
          }, 100);

          this.regist = false;
          this.logIn = true;
        } else {
          this.regist = true;
          this.logIn = false;
        };

        // while (1) {
        //
        //   if (this.$store.state.userInfo.userinfo.username) {
        //     break
        //   }
        // }

        this.begRender = true;
        this.$store.state.mailbox.getParm = false;
        this.$forceUpdate();
      },
      immediate: true,
      deep: true,
    },
    loginState: {
      handler(newVal, oldVal) {
        if (this.$store.state.mailbox.loginSucc) {
          this.loginLoading = false;//关闭登录中

          this.regist = false;
          this.logIn = false;
          this.afterLogin = true;
          this.$forceUpdate();
          this.$store.state.mailbox.logInSucc = false;
        }
      },
      immediate: true,
      deep: true,
    },
    getInbox: {
      handler(newVal, oldVal) {
        this.curInbox = this.$store.state.mailbox.curInbox;
        // this.curMailboxId = this.$store.state.mailbox.curMailboxId
        this.$forceUpdate();
      },
      immediate: true,
      deep: true,
    },
    //监听数据的变化
    headersData: {
      handler(nval, oval) {
        // 加载动画
        this.loading = false;
      },
      deep: true, // 深度监听
      immediate: true, //立即执行
    },
    // basestrs:{
    //   handler(nval, oval) {
    //
    //
    //     this.$store.state.blobstrs = this.dataURLtoBlob(nval)
    //     this.$store.state.blobstrs =URL.createObjectURL(this.$store.state.blobstrs)
    //
    //     this.$store.state.urls = this.$store.state.blobstrs

    //   },
    //   deep: true, // 深度监听
    //   immediate: true, //立即执行

    // },
    mhtmldata: {
      handler(nval, oval) {
        // 加载动画
        this.loading = false;
      },
      deep: true, // 深度监听
      immediate: true, //立即执行
    },
    fileDataAll: {
      handler(nval, oval) { },
      deep: true, // 深度监听
      immediate: true, //立即执行
    },
    passwd: {
      handler(newVal) {
        if (newVal == '') {
          //说明登录失败
          this.ruleForm.pass = '';//清空输入的密码，重新输入
          this.loginLoading = false;//关闭登录中
        }
      }
    }

  },

  methods: {
    moveDirSelect(data) {


      if (data.id == this.curMailboxId) {
        this.$message.error("选择的是当前目录！");
        return;
      }

      this.$confirm("要将邮件移动到[ " + data.label + " ], 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '移动成功'
          // });
          let tmpArg = {};
          tmpArg.src = this.curMailboxId;
          tmpArg.dest = data.id;
          tmpArg.row = this.selectMailId;

          this.$store.commit("mailbox/moveMessage", tmpArg);
          this.moveDialogVisible = false;
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消移动",
          });
          // this.moveDialogVisible = false
        });
    },

    moveMail(row) {

      this.selectMailId = row.rowId;
      this.moveDialogVisible = true;
    },

    dirDialogCancle() {
      this.dirDialogVisible = false;
      this.newDirInput = "";
    },

    dirDialogConfirm() {



      if (!this.newDirInput) {
        this.$message.error("目录名不可为空！");
        return;
      } else {
        if (this.newDirInput.indexOf(".") != -1) {
          this.$message.error("目录名不可包含 . ");
          return;
        }
        this.$store.commit(
          "mailbox/createMailbox",
          this.addMailboxId + "." + this.newDirInput
        );
        this.dirDialogVisible = false;
      }
    },

    inboxListClick(data) {
      this.curMailboxId = data.id;
      this.tabActive = "INBOX";
      this.$store.commit("mailbox/detailMailbox", data.id);
    },

    mouseEnterEven(data) {
      this.treeButtonShow[data.id] = true;
      //
      this.treeId++;
      this.$forceUpdate();
    },
    mouseLeaveEven(data) {
      //
      this.treeButtonShow[data.id] = false;
      this.treeId++;
      this.$forceUpdate();
    },

    MailListAdd(data) {

      // const newChild = { 'id': id++, 'label': 'testtest', 'children': [] };
      //
      // if (!data.children) {
      //   this.$set(data, 'children', []);
      // }
      // data.children.push(newChild);
      this.addMailboxId = data.id;
      this.dirDialogVisible = true;
    },

    MailListRemove(node, data) {


      if (data.children.length) {
        this.$message.error("该目录包含子目录，不可删除！");
        return;
      }

      this.$confirm("此操作将永久删除该目录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("mailbox/delMailbox", data.id);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },
    dataURLtoBlob(dataurl, filename) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    getBase64(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.readAsDataURL(blob);

        reader.onload = () => resolve(reader.result);

        reader.onerror = (error) => reject(error);
      });
    },
    //   test(file){
    //      const fr = new FileReader();
    //      fr.readAsDataURL(file);
    //       fr.onload = function(){
    //
    //       this.urlss = fr.result

    //      };

    //  },
    //  blobToDataURI(blob, callback) {
    //         var reader = new FileReader();
    //         reader.onload = function (e) {
    //             // callback(e.target.result);
    //             let result = e.target.result;
    //             callback(result)
    //         }
    //         reader.readAsDataURL(blob);
    //     },
    uploadFile(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function () {
          resolve(this.result);
        };
      });
    },
    //上传图片
    onChange(file) {

      this.content_text = file.children;
      // this.editor

    },
    changePasswd() {

      this.resetForm("ruleForm");
      this.showChangePasswd = true;
    },

    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错

    },

    async sendBtnClick() {
      if (!this.sendMailTo && this.checkList.length == 0) {
        alert("收件人为空！");
        return;
      }

      if (this.sendMailTo) {
        if (
          this.sendMailTo.indexOf("@") == -1 ||
          this.sendMailTo.indexOf(".com") == -1
        ) {
          alert("收件人格式错误!");
          return;
        }
      }

      let tmpAccount = [];
      for (let i = 0; i < this.checkList.length; i++) {
        let tmpPos1 = this.checkList[i].indexOf("[ ");
        let tmpPos2 = this.checkList[i].indexOf(" ]");
        tmpAccount.push(this.checkList[i].slice(tmpPos1 + 2, tmpPos2));
      }



      this.sendMail.to = [];
      if (this.sendMailTo) {
        if (tmpAccount.indexOf(this.sendMailTo) == -1) {
          this.sendMail.to.push(this.sendMailTo);
          this.sendMail.to = this.sendMail.to.concat(tmpAccount);
        } else {
          this.sendMail.to.concat(tmpAccount);
        }
      } else {
        this.sendMail.to = this.sendMail.to.concat(tmpAccount);
      }



      // this.sendMail.text = this.editor.getHtml()
      //
      // if(this.hash){
      //   this.sendMail.text = this.sendMail.text+(`<P><img src='/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${this.hash}?session_id=${window.main.$store.state.userInfo.session_id}'/></P>`)
      //   // this.sendMail.text = this.sendMail.text+(`<P><img src='/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${this.hash}?session_id=1234546'/></P>`)
      // }

      //  for (let i in this.attachments) {
      //   if (this.attachments[i].size > 1 * 1024 * 1024) {
      //     let formData = new FormData();
      //     formData.append('put_big_file', this.attachments[i].raw)
      //     formData.append('session_id', window.main.$store.state.userInfo.session_id)
      //     formData.append('file_type', 'content_file')
      //     this.$axios.post('/filesystem/api/rest/v1/big_file/put_sha512_file', formData, {
      //       headers: {
      //         'Content-Type': 'multipart/form-data'
      //       },

      //     }).then((res) => {
      //
      //       res.data = res.data.substring(10);
      //       let aa = '/filesystem/api/rest/v1/big_file/get_sha512_file/content_file/' + res.data + '?session_id=' + window.main.$store.state.userInfo.session_id
      //       this.bigattachments.push({ url: aa, name: this.attachments[i].name })

      //     })

      //   } else {

      //     var aBlob = new Blob([this.attachments[i].raw], { type: this.attachments[i].raw.type });
      //     let urlsss = URL.createObjectURL(aBlob)
      //     var reader = new FileReader();
      //     reader.readAsDataURL(aBlob);
      //     let _this = this;
      //     reader.onload = function (e) {

      //       _this.urlss = e.target.result;
      //
      //       _this.smallattachments.push({ url: e.target.result, name: _this.attachments[i].name })

      //     }
      //     //

      //     // this.smallattachments.push({url:this.urlss,name:this.attachments[i].name})

      //   }
      // }

      for (let i in this.content_text) {
        for (let j in this.content_text[i].children) {
          //
          if (this.content_text[i].children[j].type == "image") {
            var s = this.content_text[i].children[j].src;

            var name = this.content_text[i].children[j].alt;
            var ss = this.dataURLtoFile(s, name);

            // ss = URL.createObjectURL(ss)
            //
            if (ss.size > 1 * 1024 * 1024) {
              let formData = new FormData();
              formData.append("put_small_file", ss);
              formData.append(
                "session_id",
                window.main.$store.state.userInfo.session_id
              );
              formData.append("file_type", "icon");
              await this.$axios
                .post(
                  "/filesystem/api/rest/v2/node-0/small_file/put_sha512_file",
                  formData,
                  {
                    headers: {
                      "Content-Type": "multipart/form-data",
                    },
                  }
                )
                .then((res) => {

                  let a =
                    "/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/" +
                    res.data +
                    "?session_id=" +
                    window.main.$store.state.userInfo.session_id;
                  this.bigp.push(a);
                  // file.children[i].children[j].src = '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + res.data +'?session_id=' + window.main.$store.state.userInfo.session_id
                  // file.children[i].children[j].src = "sdgshr"
                  //

                  // let start = this.htmltext.indexOf('data:image/jpeg;base64')
                  // let end = this.htmltext.indexOf(file.children[i].children[j].alt)
                  //
                  // let sessionStr = this.htmltext.substring(start,end)
                  // this.htmltext = this.htmltext.split(sessionStr).join('/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + res.data +'?session_id=' + window.main.$store.state.userInfo.session_id +'" alt="');
                  //
                  //
                });
            } else {

              // var bb =this.dataURLtoBlob(s)
              // bb = URL.createObjectURL(bb)
              var bb = s;
              this.smallp.push(bb);
              //
              //
              // // this.htmltext = this.editor.getHtml()
              //
              // let starts = this.htmltext.indexOf('data:image/jpeg;base64')
              // let ends = this.htmltext.indexOf(file.children[i].children[j].alt)
              //
              // let sessionStr = this.htmltext.substring(starts,ends)
              // this.htmltext = this.htmltext.split(sessionStr).join(bb+'" alt="');
              //
            }
          } else {
            this.texth.push(this.content_text[i]?.children[0]?.text);
          }
        }
      }
      for (let i in this.texth) {
        this.sendMail.text += `<P>${this.texth[i]}</P><br>`;
      }

      for (let j in this.bigp) {
        this.sendMail.text += `<P><img src="${this.bigp[j]}"/></P><br>`;
      }
      for (let i in this.smallp) {
        this.sendMail.text += `<P><img src="${this.smallp[i]}"/></P><br>`;
      }
      for (let i in this.bigattachments) {
        this.sendMail.text += `<a  download="${this.bigattachments[i].name}" href="${this.bigattachments[i].url}"/>${this.bigattachments[i].name}<a><br>`;
      }
      for (let i in this.smallattachments) {
        this.sendMail.text += `<a download="${this.smallattachments[i].name}" href="${this.smallattachments[i].url}"/>${this.smallattachments[i].name}<a><br>`;
      }
      this.$store.commit("mailbox/sendMail", this.sendMail);
      this.sendMail = {
        to: "",
        subject: "",
        text: "",
        picture: [],
        attachment: [],
      };
      (this.texth = []),
        (this.smallp = []),
        (this.bigp = []),
        (this.bigattachments = []),
        (this.smallattachments = []);
      this.tabActive = "Sent";
      this.$refs.uploadFile.clearFiles();
      // this.$refs.uploadPicture.clearFiles()
      this.editor.clear();
      this.$forceUpdate();
    },
    handlePictureChange(file, fileList) {
      if (file.raw.type.slice(0, 5) != "image") {
        alert("只能添加图片！");
        for (let i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == file.uid) {
            fileList.splice(i, 1);
          }
        }
      } else if (file.size > 1024 * 1024 * 1) {
        let formData = new FormData();
        formData.append("put_small_file", file.raw);
        formData.append(
          "session_id",
          window.main.$store.state.userInfo.session_id
        );
        formData.append("file_type", "icon");
        this.$axios
          .post(
            "/filesystem/api/rest/v2/node-0/small_file/put_sha512_file",
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          )
          .then((res) => {
            this.hash = res.data;
            let ha = "hash";
            file[ha] = res.data;
          });
        // for (let i = 0; i < fileList.length; i++){
        //   if (fileList[i].uid == file.uid){
        //     fileList.splice (i, 1)
        //   }
        // }
      } else {
        //  this.sendMail.picture = fileList
      }
    },
    beforePictureRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handlePictureRemove(file, fileList) {
      this.sendMail.picture = fileList;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handlePictureExceed(files, fileList) {
      alert("图片数超过上限！");
    },

    handleChange(file, fileList) {
      if (file.size > 1 * 1024 * 1024) {
        let formData = new FormData();
        formData.append("put_big_file", file.raw);
        formData.append(
          "session_id",
          window.main.$store.state.userInfo.session_id
        );
        formData.append("file_type", "content_file");
        this.$axios
          .post("/filesystem/api/rest/v1/big_file/put_sha512_file", formData, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          })
          .then((res) => {
            res.data = res.data.substring(10);
            let aa =
              "/filesystem/api/rest/v1/big_file/get_sha512_file/content_file/" +
              res.data +
              "?session_id=" +
              window.main.$store.state.userInfo.session_id;
            this.bigattachments.push({ url: aa, name: file.name });
          });
      } else {
        var aBlob = new Blob([file.raw], { type: file.raw.type });
        let urlsss = URL.createObjectURL(aBlob);
        var reader = new FileReader();
        reader.readAsDataURL(aBlob);
        let _this = this;
        reader.onload = function (e) {
          _this.urlss = e.target.result;
          _this.smallattachments.push({
            url: e.target.result,
            name: file.name,
          });
        };
      }
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemove(file, fileList) {
      for (let i = 0; i < this.bigattachments.length; i++) {
        if (this.bigattachments[i].name == file.name) {
          this.bigattachments.splice(i, 1);
        }
      }
      for (let i = 0; i < this.smallattachments.length; i++) {
        if (this.smallattachments[i].name == file.name) {
          this.smallattachments.splice(i, 1);
        }
      }
    },
    handleExceed(files, fileList) {
      alert("附件数超过上限！");
    },
    handleDlgClose() {
      this.loading = true;
      this.showDetail = false;
    },
    handleRowClick(row) {
      this.$store.commit("mailbox/clearFileData");
      this.showDetail = true;
      this.$store.dispatch("mailbox/detailMessage", row.rowId);
    },
    registAccount(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.pass === this.ruleForm.checkPass) {
            this.$store.commit("mailbox/setUser", this.ruleForm.pass);
          } else {
            this.$message.error("两次输入密码不一致！");
          }
        }
      });
    },
    logInBtn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loginLoading = true;
          this.$store.commit("mailbox/listMailbox", this.ruleForm.pass);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleTabClick(tab, event) {
      switch (tab.name) {
        case "INBOX":
          this.$store.commit("mailbox/detailMailbox", "INBOX");
          // this.$store.commit("mailbox/createMailbox")
          // this.$store.commit("mailbox/delMailbox")

          break;
        case "Sent":
          this.curMailboxId = "";
          this.curInbox = [];
          break;
        default:
          break;
      }
    },
    // 删除邮件
    delMail(row) {
      this.$confirm("此操作将永久删除该邮件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("mailbox/delMail", row.rowId);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    changePasswdFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.pass === this.ruleForm.checkPass) {
            this.$store.commit("mailbox/changePasswd", this.ruleForm.checkPass);
          } else {
            this.$message.error("两次输入密码不一致！");
          }
        }
      });
    },
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>

<style lang="scss" scoped>
.custom-tree-node {
  width: 150px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  padding-right: 8px;
}

.model_lay {
  height: 85vh;
  overflow: auto;
}

.detailRow {
  margin: 0px;
  padding: 20px;
  background: #eff5fb;
  height: 80px;
}

.detailCol {
  position: relative;
  top: 10px;
  left: 10px;
}

.emailbody {
  padding-top: 20px;
  padding-left: 20px;
  border: 1px solid #eff5fb;

  .mm {
    font-size: 15px;
    color: rgb(30, 56, 161);
  }
}

.file {
  display: flex;
  align-items: center;
  padding-left: 20px;

  p {
    padding: 0 5px;
  }

  a {
    padding: 5px;
    border: 1px solid #bbb;
    border-radius: 10px;
    margin-right: 8px;
  }

  a:hover {
    border-color: #c7e0f9;
    background-color: #d4e7fa;
  }
}

.mailForm {
  // padding: 20px 20px 40px 20px;
  // position: relative;
  // top: 200px;
  // left: 600px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 300px;
  border: 1px solid #ccc;

  .title {
    width: 100%;
    margin: 20px 0px 20px 0px;
    // padding: 20px 20px 40px 20px;
    // margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
  }
}

.mailRow {
  margin: 0px;
  padding: 20px;
}

.mailCol {
  position: relative;
  top: 10px;
  left: 10px;
}

.newBox {
  .boxRow {
    display: flex;
    margin-top: 20px;
    line-height: 30px;

    .lab {
      margin-left: 10px;
    }

    .boxRowL {
      width: 80px;
      text-align: right;
    }

    .boxRowR {
      flex: 1 1;

      .texInp {
        width: 100%;
        margin-left: 10px;
        margin-right: 40px;
        border: 1px solid #ccc;
        height: 30px;
        line-height: 30px;
        text-indent: 10px;
      }
    }
  }
}

.taskRow {
  display: flex;
  min-height: 42px;
  line-height: 42px;

  .taskRowL {
    font-weight: bold;
    text-indent: 10px;
  }

  .taskRowR {
    flex: 1 1;
    display: flex;
    flex-flow: wrap;

    .rowItem {
      margin-right: 30px;
      display: flex;

      .rowItemT {
        font-weight: bold;
        color: #999;
      }

      .rowItemB {
        margin-left: 10px;
      }
    }
  }
}
</style>
