<template>
  <div class="OrigDialog">
    <el-dialog 
      :visible.sync="data.flag" 
      width="80%" append-to-body top="10px" 
      :close-on-click-modal="false" 
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
      <template #title>
        <span>{{ data.title }}</span>
        <el-button 
          type="primary" 
          size="small" 
          style="margin-left: 16px;"
          @click="showSecondaryPersonDialog"
        >添加关系人</el-button>
      </template>
      <div class="container-dialog">
        <div :class="{'left': true, 'left-with-panel': personFlag}">
          <el-tabs v-model="activeName" tab-position="top" @tab-click="handleClickTabs" :before-leave="beforeClick" @tab-remove="handleTabRemove">
            <el-tab-pane label="个人信息" name="info">
              <el-form class="personForm" ref="basicForm" :model="basicForm" :rules="basicRules" label-width="100px" size="small">
                <el-form-item label="目标人类型" title="目标人类型">
                  <el-radio-group v-model="basicForm.type">
                    <el-radio value="key_person" label="key_person">目标人</el-radio>
                    <el-radio value="secondary_key_person" label="secondary_key_person">次要目标人</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="头像" title="头像">
                  <div class="avatar-uploader">
                    <el-upload class="avatar-uploader" action=""
                      :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                      <el-avatar v-if="basicForm.avatar" :size="80" :src="getAvatarSrc(basicForm.avatar)" class="avatar" />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item label="姓名" prop="name" title="姓名" required>
                  <el-input 
                    v-model="basicForm.name"
                    placeholder="请输入姓名"
                    maxlength="128"
                    show-word-limit  
                  ></el-input>
                </el-form-item>
                <el-form-item label="备注" title="备注">
                  <el-input type="textarea" v-model="basicForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="出生日期" prop="dateBirth" title="出生日期">
                  <el-date-picker v-model="basicForm.dateBirth" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期" @change="calculateAge(0)"
                    :picker-options="birthDatePickerOptions">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="年龄" prop="age" title="年龄">
                  <el-input v-model="basicForm.age"  :placeholder="basicForm.age === null ? '出生日期不能为未来时间' : ''"></el-input>
                </el-form-item>
                <el-form-item label="性别" title="性别">
                  <el-radio-group v-model="basicForm.sex">
                    <el-radio value="男" label="男"></el-radio>
                    <el-radio value="女" label="女"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="身份证号" prop="identity" title="身份证号">
                  <el-input v-model="basicForm.identity"></el-input>
                </el-form-item>
                <el-form-item label="邮箱:" title="邮箱:">
                  <div class="social-input-container">
                    <el-input v-model="newEmail" placeholder="请输入邮箱" class="social-input" @keydown.enter.native="addSocialAccount('emails')" @blur="addSocialAccount('emails')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('emails')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(email, index) in basicForm.emails"
                      :key="'email-' + index"
                      closable
                      @close="removeSocialAccount('emails', index)"
                    >
                      {{ email }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="手机号:" title="手机号:">
                  <div class="social-input-container">
                    <el-select v-model="newPhoneAreaCode" placeholder="区号" class="social-input-area-code" filterable>
                      <el-option
                        v-for="item in areaCodes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-input 
                      v-model="newPhoneNumber" 
                      placeholder="请输入电话号" 
                      class="social-input-phone-number"
                      maxlength="30"
                      @input="newPhoneNumber = newPhoneNumber.replace(/[^\d]/g, '')"
                      @keydown.enter.native="addSocialAccount('phoneNumbers')"
                    ></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('phoneNumbers')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(phone, index) in basicForm.phoneNumbers"
                      :key="'phone-' + index"
                      closable
                      @close="removeSocialAccount('phoneNumbers', index)"
                    >
                      {{ phone }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Twitter ID:" title="Twitter ID:">
                  <div class="social-input-container">
                    <el-input v-model="newTwitterId" placeholder="请输入Twitter ID" class="social-input" @keydown.enter.native="addSocialAccount('twitterIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('twitterIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, index) in basicForm.twitterIds"
                      :key="'twitter-' + index"
                      closable
                      @close="removeSocialAccount('twitterIds', index)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Facebook ID:" title="Facebook ID:">
                  <div class="social-input-container">
                    <el-input v-model="newFacebookId" placeholder="请输入Facebook ID" class="social-input" @keydown.enter.native="addSocialAccount('facebookIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('facebookIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, index) in basicForm.facebookIds"
                      :key="'facebook-' + index"
                      closable
                      @close="removeSocialAccount('facebookIds', index)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="LinkedIn ID:" title="LinkedIn ID:">
                  <div class="social-input-container">
                    <el-input v-model="newLinkedInId" placeholder="请输入LinkedIn ID" class="social-input" @keydown.enter.native="addSocialAccount('linkedInIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('linkedInIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, index) in basicForm.linkedInIds"
                      :key="'linkedin-' + index"
                      closable
                      @close="removeSocialAccount('linkedInIds', index)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Telegram ID:" title="Telegram ID:">
                  <div class="social-input-container">
                    <el-input v-model="newTelegramId" placeholder="请输入Telegram ID" class="social-input" @keydown.enter.native="addSocialAccount('telegramIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSocialAccount('telegramIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, index) in basicForm.telegramIds"
                      :key="'telegram-' + index"
                      closable
                      @close="removeSocialAccount('telegramIds', index)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item v-for="(value, fieldName) in basicForm.customFields" :key="fieldName" :label="fieldName + ':'" :title="fieldName + ':'" class="custom-field-item">
                  <div class="social-input-container">
                    <el-input v-model="basicForm.customFields[fieldName]" :placeholder="`请输入内容`" class="social-input"></el-input>
                    <el-button type="danger" icon="el-icon-delete" @click="removeCustomField(fieldName)"></el-button>
                  </div>
                </el-form-item>
                <el-form-item label="其他信息" title="其他信息">
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="showAddFieldDialog"></el-button>
                </el-form-item>
                <el-form-item label="关系" title="关系">
                  <div class="relation-box-container">
                    <el-button type="primary" size="mini" icon="el-icon-plus" style="width: 50px;" @click.prevent="openRelationPanel()"></el-button>
                    <div v-for="(item, index) in basicForm.relation" :key="index" class="relation-item">
                      <div class="relation-content">
                        <div class="relation-avatar">
                          <el-tag type="primary" v-if="item.intellValue && item.intellValue[0] === origanList[1].label">
                            个人关系
                          </el-tag>
                          <el-tag type="success" v-else-if="item.intellValue && item.intellValue[0] === origanList[0].label">
                            组织关系
                          </el-tag>
                          <el-tag type="warning" v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label">
                            次要目标人
                          </el-tag>
                          <img class="relation-avatar-img" src="@/assets/images/user.png" />
                          <span class="relation-name">{{ getRelationName(item) }}</span>
                          <div class="relation-input">
                            <span>关系：</span>
                            <el-input 
                              v-if="item.intellValue && item.intellValue[0] === origanList[0].label" 
                              v-model="item.organizationRelation" 
                              placeholder="请输入组织关系"
                              maxlength="20"
                              show-word-limit>
                            </el-input>
                            <el-input 
                              v-else-if="item.intellValue && item.intellValue[0] === origanList[1].label" 
                              v-model="item.personRelation" 
                              placeholder="请输入个人关系"
                              maxlength="20"
                              show-word-limit>
                            </el-input>
                            <el-input 
                              v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label" 
                              v-model="item.personRelation" 
                              placeholder="请输入个人关系"
                              maxlength="20"
                              show-word-limit>
                            </el-input>
                          </div>
                          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click.prevent="removeRelation(item)"></el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane 
              v-for="(secondaryPerson, index) in secondaryPersons" 
              :key="'secondary-' + index"
              :label="secondaryPerson.name" 
              :name="'secondary-' + index"
              closable
            >
              <el-form class="personForm" :ref="'secondaryForm-' + index" :model="secondaryPerson" :rules="secondaryPersonRules" label-width="100px" size="small">
                <el-form-item label="目标人类型" title="目标人类型">
                  <el-radio-group v-model="secondaryPerson.type">
                    <el-radio value="key_person" label="key_person">目标人</el-radio>
                    <el-radio value="secondary_key_person" label="secondary_key_person">次要目标人</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="关系" title="关系">
                  <el-input v-model="secondaryPerson.relationDesc" placeholder="请输入关系"></el-input>
                </el-form-item>
                <el-form-item label="头像" title="头像">
                  <div class="avatar-uploader">
                    <el-upload class="avatar-uploader" action=""
                      :show-file-list="false" :on-success="(res, file) => handleSecondaryAvatarSuccess(res, file, index)" :before-upload="(file) => beforeAvatarUpload(file, index)">
                      <el-avatar v-if="secondaryPerson.avatar" :size="80" :src="getAvatarSrc(secondaryPerson.avatar)" class="avatar" />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item label="姓名" prop="name" title="姓名">
                  <el-input v-model="secondaryPerson.name"></el-input>
                </el-form-item>
                <el-form-item label="备注" title="备注">
                  <el-input v-model="secondaryPerson.remark"></el-input>
                </el-form-item>
                <el-form-item label="出生日期" prop="dateBirth" title="出生日期">
                  <el-date-picker v-model="secondaryPerson.dateBirth" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期" @change="(date) => calculateSecondaryAge(index, date)"
                    :picker-options="birthDatePickerOptions">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="年龄" prop="age" title="年龄">
                  <el-input v-model="secondaryPerson.age" readonly :placeholder="secondaryPerson.age === null ? '出生日期不能为未来时间' : ''"></el-input>
                </el-form-item>
                <el-form-item label="性别" title="性别">
                  <el-radio-group v-model="secondaryPerson.sex">
                    <el-radio value="男" label="男"></el-radio>
                    <el-radio value="女" label="女"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="身份证号" prop="identity" title="身份证号">
                  <el-input v-model="secondaryPerson.identity"></el-input>
                </el-form-item>
                <el-form-item label="邮箱:" title="邮箱:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].email" placeholder="请输入邮箱" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'emails')" @blur="addSecondarySocialAccount(index, 'emails')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'emails')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(email, emailIndex) in secondaryPerson.emails"
                      :key="'secondary-email-' + index + '-' + emailIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'emails', emailIndex)"
                    >
                      {{ email }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="手机号:" title="手机号:">
                  <div class="social-input-container">
                    <el-select v-model="secondaryNewInputs[index].phoneAreaCode" placeholder="区号" class="social-input-area-code" filterable>
                      <el-option
                        v-for="item in areaCodes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-input 
                      v-model="secondaryNewInputs[index].phoneNumber" 
                      placeholder="请输入电话号" 
                      class="social-input-phone-number"
                      maxlength="30"
                      @input="secondaryNewInputs[index].phoneNumber = secondaryNewInputs[index].phoneNumber.replace(/[^\d]/g, '')"
                      @keydown.enter.native="addSecondarySocialAccount(index, 'phoneNumbers')"
                    ></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'phoneNumbers')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(phone, phoneIndex) in secondaryPerson.phoneNumbers"
                      :key="'secondary-phone-' + index + '-' + phoneIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'phoneNumbers', phoneIndex)"
                    >
                      {{ phone }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Twitter ID:" title="Twitter ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].twitterId" placeholder="请输入Twitter ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'twitterIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'twitterIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, idIndex) in secondaryPerson.twitterIds"
                      :key="'secondary-twitter-' + index + '-' + idIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'twitterIds', idIndex)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Facebook ID:" title="Facebook ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].facebookId" placeholder="请输入Facebook ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'facebookIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'facebookIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, idIndex) in secondaryPerson.facebookIds"
                      :key="'secondary-facebook-' + index + '-' + idIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'facebookIds', idIndex)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="LinkedIn ID:" title="LinkedIn ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].linkedInId" placeholder="请输入LinkedIn ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'linkedInIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'linkedInIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, idIndex) in secondaryPerson.linkedInIds"
                      :key="'secondary-linkedin-' + index + '-' + idIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'linkedInIds', idIndex)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="Telegram ID:" title="Telegram ID:">
                  <div class="social-input-container">
                    <el-input v-model="secondaryNewInputs[index].telegramId" placeholder="请输入Telegram ID" class="social-input" @keydown.enter.native="addSecondarySocialAccount(index, 'telegramIds')"></el-input>
                    <el-button type="primary" icon="el-icon-plus" @click="addSecondarySocialAccount(index, 'telegramIds')"></el-button>
                  </div>
                  <div class="social-tags">
                    <el-tag
                      v-for="(id, idIndex) in secondaryPerson.telegramIds"
                      :key="'secondary-telegram-' + index + '-' + idIndex"
                      closable
                      @close="removeSecondarySocialAccount(index, 'telegramIds', idIndex)"
                    >
                      {{ id }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item v-for="(value, fieldName) in secondaryPerson.customFields" :key="fieldName" :label="fieldName + ':'" :title="fieldName + ':'" class="custom-field-item">
                  <div class="social-input-container">
                    <el-input v-model="secondaryPerson.customFields[fieldName]" :placeholder="`请输入${fieldName}`" class="social-input"></el-input>
                    <el-button type="danger" icon="el-icon-delete" @click="removeSecondaryCustomField(index, fieldName)"></el-button>
                  </div>
                </el-form-item>
                <el-form-item label="其他信息" title="其他信息">
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="showSecondaryAddFieldDialog(index)"></el-button>
                </el-form-item>
                <el-form-item label="关系" title="关系">
                  <div class="relation-box-container">
                    <el-button type="primary" size="mini" icon="el-icon-plus" style="width: 50px;" @click.prevent="openRelationPanel(index)"></el-button>
                    <div v-for="(item, itemIndex) in secondaryPerson.relation" :key="'secondary-relation-' + index + '-' + itemIndex" class="relation-item">
                      <div class="relation-content">
                        <div class="relation-avatar">
                          <el-tag type="primary" v-if="item.intellValue && item.intellValue[0] === origanList[1].label">
                            个人关系
                          </el-tag>
                          <el-tag type="success" v-else-if="item.intellValue && item.intellValue[0] === origanList[0].label">
                            组织关系
                          </el-tag>
                          <el-tag type="warning" v-else-if="item.intellValue && item.intellValue[0] === origanList[2].label">
                            次要目标人
                          </el-tag>
                          <img class="relation-avatar-img" src="@/assets/images/user.png" />
                          <span class="relation-name">{{ getRelationName(item) }}</span>
                          <div class="relation-input">
                            <span>关系：</span>
                            <el-input 
                              v-if="item.intellValue && item.intellValue[0] === origanList[0].label" 
                              v-model="item.organizationRelation" 
                              placeholder="请输入组织关系"
                              maxlength="20"
                              show-word-limit>
                            </el-input>
                            <el-input 
                              v-else-if="item.intellValue && (item.intellValue[0] === origanList[1].label || item.intellValue[0] === origanList[2].label)" 
                              v-model="item.personRelation" 
                              placeholder="请输入个人关系"
                              maxlength="20"
                              show-word-limit>
                            </el-input>
                          </div>
                          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click.prevent="removeSecondaryRelation(index, item)"></el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-if="personFlag" class="right">
          <organiRelationVue 
            ref="organiRelationRef"
            :showFlag="personFlag" 
            :initialSelectedOrganizations="selectedOrganizations"
            :initialSelectedPersons="selectedPersons"
            :initialSelectedSecondaryPersons="selectedSecondaryPersons"
            @selectOrganizations="handleSelectOrganizations"
            @selectPersons="handleSelectPersons"
            @selectSecondaryPersons="handleSelectSecondaryPersons"
          ></organiRelationVue>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" v-if="data.title == '添加目标人'" @click="submitForm">提交</el-button>
        <el-button type="primary" v-else @click="editForm">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加关系人"
      :visible.sync="secondaryPersonDialogVisible"
      width="30%"
      append-to-body
    >
      <el-form :model="secondaryPersonForm" :rules="secondaryPersonRules" ref="secondaryPersonForm" label-width="100px">
        <el-form-item label="姓名" prop="name" title="姓名">
          <el-input v-model="secondaryPersonForm.name" placeholder="请输入关系人姓名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="secondaryPersonDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSecondaryPersonFn">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加字段"
      :visible.sync="addFieldDialogVisible"
      width="30%"
      append-to-body
    >
      <el-form :model="newFieldForm" :rules="newFieldRules" ref="newFieldForm" label-width="100px">
        <el-form-item label="字段名" prop="name" title="字段名">
          <el-input v-model="newFieldForm.name" placeholder="请输入字段名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addFieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCustomField">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加字段"
      :visible.sync="secondaryAddFieldDialogVisible"
      width="30%"
      append-to-body
    >
      <el-form :model="secondaryNewFieldForm" :rules="newFieldRules" ref="secondaryNewFieldForm" label-width="100px">
        <el-form-item label="字段名" prop="name" title="字段名">
          <el-input v-model="secondaryNewFieldForm.name" placeholder="请输入字段名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="secondaryAddFieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSecondaryCustomField">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import organiRelationVue from "./organiRelation.vue";

export default {
  name: "OrganizationPersonnel",
  data() {
    // 自定义验证器
    const validateIdentity = (rule, value, callback) => {
      if (value && value.trim()) {
        if (value.length > 18) {
          callback(new Error('身份证号长度不能超过18位'));
          return
        } else {
          const idCardRegex = /^\d{1,17}(\d|X|x)?$/;
          if (!idCardRegex.test(value)) {
            callback(new Error('身份证号格式不正确，允许最后一位是字母'));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    };
    
    const validateBirthDate = (rule, value, callback) => {
      if (value) {
        const birthDate = new Date(value);
        const currentDate = new Date();
        if (birthDate > currentDate) {
          callback(new Error('出生日期不能选择未来时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    
    return {
      activeName: "info",
      // 基础信息
      basicForm: {
        avatar: "",
        name: "", // 姓名
        remark: "",
        emails: [], // 邮箱
        dateBirth: "",
        age: "", // 年龄
        sex: "", // 性别
        identity: "", // 身份
        relation: [], // 关系
        phoneNumbers: [],
        twitterIds: [],
        facebookIds: [],
        linkedInIds: [],
        telegramIds: [],
        customFields: {},
        type: 'key_person', // 目标人类型默认为目标人
      },
      // 基础信息验证规则
      basicRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { max: 128, message: '姓名长度不能超过128个字符', trigger: 'blur' }
        ],
        dateBirth: [
          { validator: validateBirthDate, trigger: 'change' }
        ],
        age: [
          { type: 'number', min: 0, message: '年龄不能为负数', trigger: 'blur', transform: value => Number(value) }
        ],
        identity: [
          { validator: validateIdentity, trigger: ['blur', 'change'] }
        ],
      },
      
      // 次要目标人相关数据
      secondaryPersons: [], // 次要目标人数组
      secondaryPersonDialogVisible: false, // 次要目标人弹窗显示状态
      secondaryPersonForm: {
        name: '' // 次要目标人名称
      },
      secondaryPersonRules: {
        name: [
          { required: true, message: '请输入次要目标人姓名', trigger: 'blur' },
          { max: 128, message: '姓名长度不能超过128个字符', trigger: 'blur' }
        ],
        dateBirth: [
          { validator: validateBirthDate, trigger: 'change' }
        ],
        age: [
          { type: 'number', min: 0, message: '年龄不能为负数', trigger: 'blur', transform: value => Number(value) }
        ],
        identity: [
          { validator: validateIdentity, trigger: ['blur', 'change'] }
        ],
      },
      secondaryNewInputs: [], // 次要目标人的新输入值
      
      // 社交媒体账号新输入值
      newEmail: '',
      newPhoneAreaCode: '+86',
      newPhoneNumber: '',
      newTwitterId: '',
      newFacebookId: '',
      newLinkedInId: '',
      newTelegramId: '',
      
      personFlag: false, // 是否展示右侧组织人员选择面板
      circleUrl: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
      selectRelation: "ori",
      selectedOrganizations: [],
      selectedPersons: [],
      selectedSecondaryPersons: [],
      birthDatePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      addFieldDialogVisible: false,
      newFieldForm: {
        name: '',
      },
      newFieldRules: {
        name: [
          { required: true, message: '请输入字段名', trigger: 'blur' }
        ]
      },
      secondaryAddFieldDialogVisible: false,
      secondaryNewFieldForm: {
        name: '',
      },
      currentSecondaryIndex: -1,
      areaCodes: [
        { label: '+86 (中国)', value: '+86' },
        { label: '+1 (美国/加拿大)', value: '+1' },
        { label: '+44 (英国)', value: '+44' },
        { label: '+81 (日本)', value: '+81' },
        { label: '+852 (香港)', value: '+852' },
        { label: '+853 (澳门)', value: '+853' },
        { label: '+886 (台湾)', value: '+886' },
      ],
    };
  },
  components: {
    organiRelationVue,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    info: {
      type: Object,
    },
  },
  created() {
    console.log("created", this.info, !this.info);
    if (!this.info) {
      this.basicForm.relation = [];
      this.basicForm.customFields = {};
    } else {
      const paramsCopy = JSON.parse(JSON.stringify(this.info._source.params));

      this.basicForm = paramsCopy.basic || {};

      this.$set(this.basicForm, 'relation', []);
      this.basicForm.type = this.info._source.type
      this.basicForm.emails = paramsCopy.emails || [];
      this.basicForm.phoneNumbers = paramsCopy.phoneNumbers || [];
      this.basicForm.twitterIds = paramsCopy.twitterIds || [];
      this.basicForm.facebookIds = paramsCopy.facebookIds || [];
      this.basicForm.linkedInIds = paramsCopy.linkedInIds || [];
      this.basicForm.telegramIds = paramsCopy.telegramIds || [];
      this.basicForm.customFields = paramsCopy.customFields || {};

      // if (paramsCopy.secondaryPersons) {
      //   this.secondaryPersons =paramsCopy.secondaryPersons.map((person) => {
      //     if (!person.relation) {
      //       this.$set(person, 'relation', [])
      //     }
      //     if (person.relationDesc === undefined) {
      //       this.$set(person, 'relationDesc', '');
      //     }
      //     if (!person.customFields) {
      //       this.$set(person, 'customFields', {});
      //     }
      //     if (!person.type) {
      //       this.$set(person, 'type', 'secondary_key_person');
      //     }
      //     this.secondaryNewInputs.push({
      //       email: '', phoneAreaCode: '+86', phoneNumber: '', twitterId: '', facebookId: '', linkedInId: '', telegramId: '',
      //     });
      //     return person;
      //   });
      // }

      // if (this.personRelationSearchList.length) {
      //   this.personRelationSearchList.forEach(item => {
      //     this.basicForm.relation.push({
      //       intellValue: ['目标人'],
      //       reversePersonRelation: '',
      //       personId: item.basic.id,
      //       personData: item
      //     });
      //   });
      // }

      // if (this.organizationRelationSearchList.length) {
      //   this.organizationRelationSearchList.forEach(item => {
      //     this.basicForm.relation.push({
      //       intellValue: ['目标组织'],
      //       reverseOrganizationRelation: '',
      //       organizationId: item.basic.id,
      //       organizationData: item
      //     });
      //   });
      // }
    }
    console.log('basicForm', this.basicForm);
  },

  computed: {
    ...mapState({
      origanList: (state) => state.intellManageTree.origanList,
      selectPerson: (state) => state.person.selectPerson,
      personList: (state) => state.person.personList,
      organiList: (state) => state.organization.organiList,
      personRelationSearchList: (state) => state.relationsshipTopologyDiagram.personRelationSearchList,
      organizationRelationSearchList: (state) => state.relationsshipTopologyDiagram.organizationRelationSearchList,
    }),
  },
  watch: {
    selectPerson: {
      handler(val, oldVal) {
        if (Array.isArray(val) && val.length) {
          if (this.data.title === "添加目标人") {
            console.log("selectPerson", val[val.length - 1].person);
            this.selectRelation = "per";
            this.basicForm.relation[
              this.basicForm.relation.length - 1
            ].personData = val[val.length - 1].person;
            window.main.$store.commit("person/clearSelect");
          } else {
            this.selectRelation = "per";
            if (
              this.info._source.params.basic.relation &&
              this.info._source.params.basic.relation.length
            ) {
              this.info._source.params.basic.relation[
                this.info._source.params.basic.relation.length - 1
              ].personData = val[val.length - 1].person;
            }
            window.main.$store.commit("person/clearSelect");
          }
        }
      },
      deep: true,
      immediate: true,
    },
    data: {
      handler(newVal, oldVal) {
        if (newVal.title !== "添加目标人" && this.info) {
          if (this.info._source.params.media.telegram[0]?.nickName) {
            this.telFlag = true;
          }
          if (this.info._source.params.media.facebook[0]?.idNum) {
            this.faceFlag = true;
          }
          if (this.info._source.params.media.twitter[0]?.idNum) {
            this.twiFlag = true;
          }
          if (this.info._source.params.media.linkedin[0]?.idNum) {
            this.linFlag = true;
          }
        }
      },
    },
  },
  methods: {
    // 处理头像
    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require('@/assets/images/user.png');
    },

    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = ["a","b","c","d","e","f","g","h","i","j","k","l","m","n"];
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },
    handleClose() {
      this.$confirm('您有未保存的更改，确定要关闭吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.closeDialog();
      }).catch(() => {});
    },
    closeDialog() {
      this.data.flag = false;
      this.basicForm = {
        type: "key_person",
        avatar: "",
        name: "",
        remark: "",
        emails: [],
        dateBirth: "",
        age: "",
        sex: "男",
        identity: "",
        relation: [],
        phoneNumbers: [],
        twitterIds: [],
        facebookIds: [],
        linkedInIds: [],
        telegramIds: [],
        customFields: {},
      }
      
      this.secondaryPersons = [];
      this.secondaryNewInputs = [];
      this.newEmail = '';
      this.newPhoneAreaCode = '+86';
      this.newPhoneNumber = '';
      this.newTwitterId = '';
      this.newFacebookId = '';
      this.newLinkedInId = '';
      this.newTelegramId = '';
      this.activeName = "info";
      this.selectedOrganizations = [];
      this.selectedPersons = [];
      this.personFlag = false;
    },
    beforeClick(activeName, oldActiveName) {
      return true;
    },
    handleClickTabs(tab) {
      console.log(tab, this.activeName);
      this.personFlag = false;
      if (tab.name === "info") {
        this.currentSecondaryIndex = -1;
      } else if (tab.name.startsWith('secondary-')) {
        this.currentSecondaryIndex = parseInt(tab.name.split('-')[1]);
      }
    },
    handleTabRemove(targetName) {
      const index = parseInt(targetName.split('-')[1]);
      this.removeSecondaryPerson(index);
    },
    handleCascaderChange(item) {
      console.log("Cascader value changed:", item.intellValue);
      if (item.intellValue && item.intellValue.length > 0) {
        const selectedType = item.intellValue[0];
        const selectedName = item.intellValue[1];

        if (selectedType === this.origanList[0].value) {
          this.selectRelation = "ori";
          const selectedOrg = this.origanList[0].children.find(org => org.value === selectedName);
          if (selectedOrg) {
            item.organizationData = selectedOrg.info;
          }
        } else if (selectedType === this.origanList[1].value) {
          this.selectRelation = "per";
          const selectedPerson = this.origanList[1].children.find(person => person.value === selectedName);
          if (selectedPerson) {
            item.personData = selectedPerson.info;
          }
        }
      }
    },
    removeRelation(item) {
      this.$confirm('确定要删除该关系吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const currentRelations = this.basicForm.relation;
        const index = currentRelations.findIndex(rel => rel === item);
        if (index !== -1) {
          currentRelations.splice(index, 1);
        }
      }).catch(() => {});
    },
    showInput(index) {
      if (this.data.title == "添加目标人") {
        this.basicForm.other[index].showInput = true;
        this.basicForm.other.push({ key: "", value: "", showInput: false });
      } else {
        this.info._source.params.basic.other[index].showInput = true;
        this.info._source.params.basic.other.push({ key: "", value: "", showInput: false });
      }
    },
    removeItem(index) {
      if (this.data.title == "添加目标人") {
        this.basicForm.other.splice(index, 1);
      } else {
        this.info._source.params.basic.other.splice(index, 1);
      }
    },
    handleAvatarSuccess(res, file) {
      console.log("handleAvatarSuccess", res);
      if (this.data.title == "添加目标人") {
        this.basicForm.avatar = res
      } else {
        this.info._source.params.basic.avatar = res;
      }
    },
    beforeAvatarUpload(file, personIndex = null) {
      console.log("beforeAvatarUpload", file, "personIndex:", personIndex);
      const isJPGorPNG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPGorPNG) {
        this.$message.error("头像图片只能是 JPG/PNG 格式!");
        return false;
      }
      if (!isLt5M) {
        this.$message.error("头像图片大小不能超过 5MB!");
        return false;
      }

      // 根据是否传入 personIndex 来决定使用哪个回调
      if (personIndex !== null) {
        // 次要目标人上传
        this.uploadFile(file, (res, file) => this.handleSecondaryAvatarSuccess(res, file, personIndex));
      } else {
        // 主要目标人上传
        this.uploadFile(file, (res, file) => this.handleAvatarSuccess(res, file));
      }

      return false;
    },

    // 通用文件上传方法
    uploadFile(file, successCallback) {
      let formData = new FormData();
      formData.append("put_small_file", file);
      formData.append('file_type', 'icon');

      this.$axios.post('/filesystem/api/rest/v2/node-0/small_file/put_sha512_file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((response) => {
        console.log("response", response);
        if (response.status == 200) {
          // 调用传入的成功回调函数
          successCallback(response.data, file);
        }
      }).catch((error) => {
        console.error("上传失败", error);
        this.$message.error("头像上传失败");
      });
    },

    submitForm() {
      const promises = [this.$refs.basicForm.validate()];
      let relationMap = [];
      this.secondaryPersons.forEach((_, index) => {
        const formRef = this.$refs[`secondaryForm-${index}`];
        if (Array.isArray(formRef)) {
          if (formRef[0] && typeof formRef[0].validate === 'function') {
            promises.push(formRef[0].validate());
          }
        } else if (formRef && typeof formRef.validate === 'function') {
          promises.push(formRef.validate());
        }
      });
      Promise.all(promises).then(() => {
        // 为次要目标人分配ID
        if (this.secondaryPersons.length > 0) {
          this.secondaryPersons.forEach(person => {
            if (!person.id) {
              person.id = this.reduceNumber();
            }
            this.$store.commit("person/addPerson", person);
            relationMap.push({
              id: person.id,
              relationDesc: person.relationDesc,
            });
          });
        }
        this.basicForm['id'] = this.reduceNumber();
        console.log("基本目标人信息:", this.basicForm);
        this.$store.commit("person/addPerson", this.basicForm);
        if (relationMap.length > 0) {
          relationMap.forEach(item => {
            this.$store.commit("person/addPersonRelation", {id: this.basicForm['id'], table: 'key_person', targetID: item.id, relation: 'key_person', desc: item.relationDesc});
          });
        }
        this.closeDialog();
      }).catch(error => {
        console.error("表单验证失败", error);
        this.$message.error("表单验证失败，请检查输入");
      });
    },

    editForm() {
      // 表单验证逻辑保持不变
      const promises = [this.$refs.basicForm.validate()];
      this.secondaryPersons.forEach((_, index) => {
        const formRef = this.$refs[`secondaryForm-${index}`];
        if (Array.isArray(formRef)) {
          if (formRef[0] && typeof formRef[0].validate === 'function') {
            promises.push(formRef[0].validate());
          }
        } else if (formRef && typeof formRef.validate === 'function') {
          promises.push(formRef.validate());
        }
      });
      
      Promise.all(promises).then(() => {
        // --- 逻辑补全开始 ---
        // 1. 获取原始关系ID集合，用于后续对比，判断哪些是新关系
        const originalRelations = this.info._source.params.relation || [];
        const originalRelationIds = new Set(originalRelations.map(r => r.personData?._id || r.organizationData?._id).filter(Boolean));

        // 2. 将更新后的数据赋值回 'info' 对象，准备保存
        this.info._source.params.basic = this.basicForm;
        // 注意：根据你的数据结构，这些社交账号和关系数组似乎是和 basic 平级的
        this.info._source.params.emails = this.basicForm.emails;
        this.info._source.params.phoneNumbers = this.basicForm.phoneNumbers;
        this.info._source.params.twitterIds = this.basicForm.twitterIds;
        this.info._source.params.facebookIds = this.basicForm.facebookIds;
        this.info._source.params.linkedInIds = this.basicForm.linkedInIds;
        this.info._source.params.telegramIds = this.basicForm.telegramIds;
        this.info._source.params.customFields = this.basicForm.customFields;
        this.info._source.params.relation = this.basicForm.relation;
        
        this.secondaryPersons.forEach(person => { if (!person.id) person.id = this.reduceNumber(); });
        this.info._source.params.secondaryPersons = this.secondaryPersons;

        // 3. 提交主要的编辑请求 (这部分只更新人员文档)
        window.main.$store.commit("person/editPerson", this.info);

        // 4. 遍历当前的关系列表，为【新添加】的关系手动创建链接
        const currentRelations = this.basicForm.relation || [];
        currentRelations.forEach(item => {
          let targetId = null;
          let relationType = '';
          let relationDesc = '';
          
          const personDataId = item.personData?._id || item.personData?.id;
          const orgDataId = item.organizationData?._id || item.organizationData?.id;

          if ((item.intellValue[0] === '目标人' || item.intellValue[0] === '次要目标人') && personDataId) {
            targetId = personDataId;
            relationType = 'key_person';
            relationDesc = item.personRelation;
          } else if (item.intellValue[0] === '目标组织' && orgDataId) {
            targetId = orgDataId;
            relationType = 'key_organization';
            relationDesc = item.organizationRelation;
          }

          // 如果这个关系是新的（它的ID不在原始关系ID集合中），就调用addForwardRelation创建它
          if (targetId && !originalRelationIds.has(targetId)) {
            console.log("正在为新关系创建正向链接:", targetId);
            window.main.$store.commit("person/addForwardRelation", {
              id: this.info._id, // 关键：被编辑人员的ID
              table: 'key_person',
              targetID: targetId,
              relation: relationType,
              desc: relationDesc
            });
          }
        });
        // --- 逻辑补全结束 ---

        this.closeDialog();
      }).catch(error => {
        console.error("表单验证失败", error);
        this.$message.error("表单验证失败，请检查输入");
      });
    },

    calculateAge(type, index) {
      const calculateAgeFromDate = (dateString) => {
        if (!dateString) return null;
        const birthDate = new Date(dateString);
        const currentDate = new Date();
        if (birthDate > currentDate) {
          this.$message.warning("出生日期不能选择未来时间");
          return null;
        }
        let age = currentDate.getFullYear() - birthDate.getFullYear();
        const monthDiff = currentDate.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
          age--;
        }
        return age;
      };

      if (type === 0) {
        if (this.data.title === "添加目标人") {
          this.basicForm.age = calculateAgeFromDate(this.basicForm.dateBirth);
        } else {
          this.info._source.params.basic.age = calculateAgeFromDate(this.info._source.params.basic.dateBirth);
        }
      } else if (type === 1) {
        const relation = this.data.title === "添加目标人" ? this.socializeForm.relation[index] : this.info._source.params.socialize.relation[index];
        relation.relationInfo.age = calculateAgeFromDate(relation.relationInfo.dateBirth);
      }
    },
    getRelationName(item) {
      if (item.intellValue && item.intellValue[0] === this.origanList[0].label && item.organizationData) {
        return item.organizationData?._source?.params?.basic?.name || item.organizationData?.basic?.name || '未知组织';
      } else if (item.intellValue && item.intellValue[0] === this.origanList[1].label && item.personData) {
        return item.personData?._source?.params?.basic?.name || item.personData?.basic?.name || '未知人员';
      } else if (item.intellValue && item.intellValue[0] === this.origanList[2].label && item.personData) {
        return item.personData?._source?.params?.basic?.name || item.personData?.basic?.name || '未知次要目标人';
      }
      return '未选择';
    },

    handleSelectOrganizations(organizations) {
      let currentRelations;
      if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[0].label) {
          currentRelations.splice(i, 1);
        }
      }
      organizations.forEach(org => {
        currentRelations.push({
          intellValue: [this.origanList[0].label, org._source.params.basic.name],
          organizationData: JSON.parse(JSON.stringify(org)),
          organizationRelation: "",
        });
      });
    },

    handleSelectPersons(persons) {
      let currentRelations;
      if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[1].label) {
          currentRelations.splice(i, 1);
        }
      }
      persons.forEach(person => {
        currentRelations.push({
          intellValue: [this.origanList[1].label, person._source.params.basic.name],
          personData: JSON.parse(JSON.stringify(person)),
          personRelation: "",
        });
      });
    },

    handleSelectSecondaryPersons(persons) {
      let currentRelations;
      if (this.currentSecondaryIndex >= 0) {
        currentRelations = this.secondaryPersons[this.currentSecondaryIndex].relation;
      } else {
        currentRelations = this.basicForm.relation;
      }
      for (let i = currentRelations.length - 1; i >= 0; i--) {
        if (currentRelations[i].intellValue && currentRelations[i].intellValue[0] === this.origanList[2].label) {
          currentRelations.splice(i, 1);
        }
      }
      persons.forEach(person => {
        currentRelations.push({
          intellValue: [this.origanList[2].label, person._source.params.basic.name],
          personData: JSON.parse(JSON.stringify(person)),
          personRelation: "",
        });
      });
    },

    // handleSelectOrganizations(organizations) {
    //   let currentRelations = this.currentSecondaryIndex >= 0 ? (this.secondaryPersons[this.currentSecondaryIndex].relation || []) : this.basicForm.relation;
    //   if (!Array.isArray(currentRelations)) currentRelations = [];

    //   if (Array.isArray(organizations)) {
    //     const existingOrgMap = new Map(currentRelations.map((item, index) => [item.organizationData?._source?.params?.id, index]).filter(([id]) => id));
    //     const newOrgIds = organizations.map(org => org?._source?.params?.id).filter(id => id);
        
    //     currentRelations = currentRelations.filter(item => !item.organizationData || newOrgIds.includes(item.organizationData?._source?.params?.id));
        
    //     organizations.forEach(org => {
    //       if (org?._source?.params?.id && !existingOrgMap.has(org._source.params.id)) {
    //         currentRelations.push({
    //           intellValue: [this.origanList[0].label, org._source.params.basic.name],
    //           organizationData: org,
    //           organizationRelation: "",
    //           reverseOrganizationRelation: "",
    //           personData: {},
    //         });
    //       }
    //     });
    //   }
    //   if (this.currentSecondaryIndex >= 0) this.secondaryPersons[this.currentSecondaryIndex].relation = currentRelations;
    //   else this.basicForm.relation = currentRelations;
    // },

    // handleSelectPersons(persons) {
    //   let currentRelations = this.currentSecondaryIndex >= 0 ? (this.secondaryPersons[this.currentSecondaryIndex].relation || []) : this.basicForm.relation;
    //   if (!Array.isArray(currentRelations)) currentRelations = [];

    //   if (Array.isArray(persons)) {
    //     const existingPersonMap = new Map(currentRelations.map((item, index) => [item.personData?._source?.params?.id, index]).filter(([id]) => id));
    //     const newPersonIds = persons.map(p => p?._source?.params?.id).filter(id => id);

    //     currentRelations = currentRelations.filter(item => !item.personData || newPersonIds.includes(item.personData?._source?.params?.id));

    //     persons.forEach(person => {
    //       if (person?._source?.params?.id && !existingPersonMap.has(person._source.params.id)) {
    //         currentRelations.push({
    //           intellValue: [this.origanList[1].label, person._source.params.basic.name],
    //           personData: person,
    //           personRelation: "",
    //           reversePersonRelation: "",
    //           organizationData: {},
    //         });
    //       }
    //     });
    //   }
    //   if (this.currentSecondaryIndex >= 0) this.secondaryPersons[this.currentSecondaryIndex].relation = currentRelations;
    //   else this.basicForm.relation = currentRelations;
    // },

    openRelationPanel(secondaryPersonIndex) {
      this.currentSecondaryIndex = secondaryPersonIndex !== undefined ? secondaryPersonIndex : -1;
      this.prepareSelectedRelations(secondaryPersonIndex);
      this.personFlag = true;
    },
    prepareSelectedRelations(secondaryPersonIndex) {
      let relations = [];
      if (secondaryPersonIndex !== undefined) {
        relations = this.secondaryPersons[secondaryPersonIndex]?.relation || [];
      } else {
        relations = this.basicForm.relation || [];
      }
      
      this.selectedOrganizations = relations.filter(item => item.intellValue?.[0] === this.origanList[0].label && item.organizationData).map(item => item.organizationData);
      this.selectedPersons = relations.filter(item => item.intellValue?.[0] === this.origanList[1].label && item.personData).map(item => item.personData);
      this.selectedSecondaryPersons = relations.filter(item => item.intellValue?.[0] === this.origanList[2].label && item.personData).map(item => item.personData);
    },
    showSecondaryPersonDialog() {
      this.secondaryPersonForm.name = '';
      this.secondaryPersonDialogVisible = true;
      this.$nextTick(() => { this.$refs.secondaryPersonForm.clearValidate(); });
    },
    addSecondaryPersonFn() {
      this.$refs.secondaryPersonForm.validate(valid => {
        if (valid) {
          const newSecondaryPerson = {
            name: String(this.secondaryPersonForm.name || ''),
            relationDesc: String(this.secondaryPersonForm.relationDesc || ''),
            avatar: "", remark: "", emails: [], dateBirth: "", age: "", sex: "", identity: "",
            phoneNumbers: [], twitterIds: [], facebookIds: [], linkedInIds: [], telegramIds: [],
            customFields: {}, relation: [], id: this.reduceNumber(),
            type: 'key_person', // 目标人类型默认为目标人
          };
          this.secondaryPersons.push(newSecondaryPerson);
          this.secondaryNewInputs.push({
            email: '', phoneAreaCode: '+86', phoneNumber: '', twitterId: '', facebookId: '', linkedInId: '', telegramId: '',
          });
          this.secondaryPersonDialogVisible = false;
        }
      });
    },
    handleSecondaryAvatarSuccess(res, _file, personIndex) {
      console.log("handleSecondaryAvatarSuccess", res, "personIndex:", personIndex);
      this.secondaryPersons[personIndex].avatar = res;
    },
    calculateSecondaryAge(personIndex, dateString) {
      if (!dateString) {
        this.secondaryPersons[personIndex].age = null;
        return;
      }
      const birthDate = new Date(dateString);
      const currentDate = new Date();
      if (birthDate > currentDate) {
        this.$message.warning("出生日期不能选择未来时间");
        this.secondaryPersons[personIndex].age = null;
        return;
      }
      let age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDiff = currentDate.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
        age--;
      }
      this.secondaryPersons[personIndex].age = age;
    },
    addSecondarySocialAccount(personIndex, type) {
      const person = this.secondaryPersons[personIndex];
      const inputs = this.secondaryNewInputs[personIndex];
      const actionMap = {
        'emails': { value: inputs.email, key: 'email', validate: this.validateEmail },
        'phoneNumbers': { value: `${inputs.phoneAreaCode}-${inputs.phoneNumber}`, key: 'phoneNumber', areaCodeKey: 'phoneAreaCode' },
        'twitterIds': { value: inputs.twitterId, key: 'twitterId' },
        'facebookIds': { value: inputs.facebookId, key: 'facebookId' },
        'linkedInIds': { value: inputs.linkedInId, key: 'linkedInId' },
        'telegramIds': { value: inputs.telegramId, key: 'telegramId' },
      };
      const action = actionMap[type];
      const valueToAdd = (type === 'phoneNumbers') ? (inputs.phoneNumber.trim() ? action.value : '') : action.value.trim();

      if (valueToAdd) {
        if (action.validate && !action.validate(valueToAdd)) {
          this.$message.error('格式不正确，请检查输入');
          return;
        }
        if (!person[type].includes(valueToAdd)) {
          person[type].push(valueToAdd);
          inputs[action.key] = '';
          if (action.areaCodeKey) inputs[action.areaCodeKey] = '+86';
        } else {
          this.$message.warning('该内容已存在');
        }
      }
    },
    removeSecondarySocialAccount(personIndex, type, index) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        this.secondaryPersons[personIndex][type].splice(index, 1);
      }).catch(() => {});
    },
    removeSecondaryPerson(index) {
      this.$confirm('确定要删除该关系人吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.secondaryPersons.splice(index, 1);
        this.secondaryNewInputs.splice(index, 1);
        if (this.activeName === 'secondary-' + index) {
          if (this.secondaryPersons.length === 0) {
            this.activeName = 'info';
          } else {
            const newIndex = Math.max(0, index - 1);
            this.activeName = 'secondary-' + newIndex;
          }
        } else {
          const activeIndex = parseInt(this.activeName.split('-')[1]);
          if (index < activeIndex) {
            this.activeName = 'secondary-' + (activeIndex - 1);
          }
        }
      }).catch(() => {});
    },
    validateEmail(email) {
      if (email && email.trim()) {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailRegex.test(email);
      }
      return false;
    },
    addSocialAccount(type) {
      const actionMap = {
        'emails': { value: this.newEmail, key: 'newEmail', validate: this.validateEmail },
        'phoneNumbers': { value: `${this.newPhoneAreaCode}-${this.newPhoneNumber}`, key: 'newPhoneNumber', areaCodeKey: 'newPhoneAreaCode' },
        'twitterIds': { value: this.newTwitterId, key: 'newTwitterId' },
        'facebookIds': { value: this.newFacebookId, key: 'newFacebookId' },
        'linkedInIds': { value: this.newLinkedInId, key: 'newLinkedInId' },
        'telegramIds': { value: this.newTelegramId, key: 'newTelegramId' },
      };
      const action = actionMap[type];
      const valueToAdd = (type === 'phoneNumbers') ? (this.newPhoneNumber.trim() ? action.value : '') : action.value.trim();

      if (valueToAdd) {
        if (action.validate && !action.validate(valueToAdd)) {
          this.$message.error('邮箱格式不正确');
          return;
        }
        if (!this.basicForm[type].includes(valueToAdd)) {
          this.basicForm[type].push(valueToAdd);
          this[action.key] = '';
          if (action.areaCodeKey) this[action.areaCodeKey] = '+86';
        } else {
          this.$message.warning('该内容已存在');
        }
      }
    },
    removeSocialAccount(type, index) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        this.basicForm[type].splice(index, 1);
      }).catch(() => {});
    },
    showAddFieldDialog() {
      this.addFieldDialogVisible = true;
      this.$nextTick(() => { this.$refs.newFieldForm.clearValidate(); });
    },
    addCustomField() {
      this.$refs.newFieldForm.validate(valid => {
        if (valid) {
          if (!this.basicForm.customFields) this.$set(this.basicForm, 'customFields', {});
          const fieldName = this.newFieldForm.name;
          if (!this.basicForm.customFields.hasOwnProperty(fieldName)) {
            this.$set(this.basicForm.customFields, fieldName, '');
            this.addFieldDialogVisible = false;
            this.newFieldForm.name = '';
          } else {
            this.$message.warning('该字段名已存在');
          }
        }
      });
    },
    removeCustomField(fieldName) {
      this.$delete(this.basicForm.customFields, fieldName);
    },
    showSecondaryAddFieldDialog(index) {
      this.currentSecondaryIndex = index;
      this.secondaryAddFieldDialogVisible = true;
      this.$nextTick(() => { this.$refs.secondaryNewFieldForm.clearValidate(); });
    },
    addSecondaryCustomField() {
      this.$refs.secondaryNewFieldForm.validate(valid => {
        if (valid) {
          const personIndex = this.currentSecondaryIndex;
          if (!this.secondaryPersons[personIndex].customFields) this.$set(this.secondaryPersons[personIndex], 'customFields', {});
          const fieldName = this.secondaryNewFieldForm.name;
          if (!this.secondaryPersons[personIndex].customFields.hasOwnProperty(fieldName)) {
            this.$set(this.secondaryPersons[personIndex].customFields, fieldName, '');
            this.secondaryAddFieldDialogVisible = false;
            this.secondaryNewFieldForm.name = '';
          } else {
            this.$message.warning('该字段名已存在');
          }
        }
      });
    },
    removeSecondaryCustomField(personIndex, fieldName) {
      this.$delete(this.secondaryPersons[personIndex].customFields, fieldName);
    },
    removeSecondaryRelation(personIndex, item) {
      this.$confirm('确定要删除该关系吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const relations = this.secondaryPersons[personIndex].relation;
        if (!relations) return;
        const index = relations.findIndex(rel => rel === item);
        if (index !== -1) relations.splice(index, 1);
      }).catch(() => {});
    },

    // handleSelectSecondaryPersons(persons) {
    //   let currentRelations = this.currentSecondaryIndex >= 0 ? (this.secondaryPersons[this.currentSecondaryIndex].relation || []) : this.basicForm.relation;
    //   if (!Array.isArray(currentRelations)) currentRelations = [];

    //   if (Array.isArray(persons)) {
    //     const existingPersonMap = new Map(currentRelations.map((item, index) => [item.personData?._source?.params?.id, index]).filter(([id]) => id && item.intellValue?.[0] === this.origanList[2].label));
    //     const newPersonIds = persons.map(p => p?._source?.params?.id).filter(id => id);

    //     currentRelations = currentRelations.filter(item => item.intellValue?.[0] !== this.origanList[2].label || !item.personData || newPersonIds.includes(item.personData?._source?.params?.id));

    //     persons.forEach(person => {
    //       if (person?._source?.params?.id && !existingPersonMap.has(person._source.params.id)) {
    //         currentRelations.push({
    //           intellValue: [this.origanList[2].label, person._source.params.basic.name],
    //           personData: person,
    //           personRelation: "",
    //           reversePersonRelation: "",
    //           organizationData: {},
    //         });
    //       }
    //     });
    //   }
    //   if (this.currentSecondaryIndex >= 0) this.secondaryPersons[this.currentSecondaryIndex].relation = currentRelations;
    //   else this.basicForm.relation = currentRelations;
    // },
  },
};
</script>
<style lang="scss" scoped>
.OrigDialog {
  height: 100%;
  width: 100%;
}

.container-dialog {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
}

.left {
  width: 100%;
  padding: 10px;
  overflow-y: auto;
  max-height: 80vh;
}

.left-with-panel {
  width: calc(100% - 300px);
}

.right {
  width: 300px;
  padding: 10px;
}

.personForm {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 600px;
  margin: 0 auto 0 0;
}

.avatar-uploader {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
      border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }

  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }

.social-input-container {
  display: flex;
  align-items: center;
}

.social-input-area-code {
  width: 180px;
  margin-right: 5px;
}

.social-input-phone-number {
  flex: 1;
}

.social-input {
  flex: 1;
}

.social-tags {
  margin-top: 8px;
}

.social-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.relation-box-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.relation-item {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.relation-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.relation-avatar {
  display: flex;
  align-items: center;
  width: 100%;
}

.relation-avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 10px;
}

.relation-name {
  margin-left: 10px;
  font-weight: bold;
}

.relation-input {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 10px;
}

.relation-input span {
  margin-right: 5px;
}

.relation-input .el-input {
  width: 150px;
}

.custom-field-item .el-input {
  flex: 1;
}
</style>
