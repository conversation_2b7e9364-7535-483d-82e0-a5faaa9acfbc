export default {
  namespaced: true,
  state: {
    origanList: [
      {
        value: "zhinan",
        label: "目标组织",
        children: [],
      },
      {
        value: "zhinan",
        label: "目标人",
        children: [],
      },
      {
        value: "zhinan",
        label: "次要目标人",
        children: [],
      },
    ],

    selectedItemId: "",
    selectedItemType: null,

    // 情报列表
    from: 1,
    size: 10,
    total: 0,
    case_id: null,
    bulletinList: [],
    /**临时变量 */
    currentCaseInfo: {},
    currentView: "caseDir",

    /**案件目录 */
    case_dir_father_path: "",
    case_dir_father_list: [],
    case_dir_father_list_obj: [],
    caseRootDirList: [],
    rootNode: {},
    currentRoot: "",
    componentKey: 0,
    nowRowKey: [],

    /**案件列表 */
    caseList: [],
    case_dir_id: "",
    caseDirDetail: {},
    req: false,
    resData: {},

    // 案件目录分享
    treeData: [],
    tmpData: [],
    tmpFather: 0,
    treeDataChange: false,
    tmpNodeSum: 0,
    tmpNodeNum: 0,

    // 案件操作----->
    // 案件目录详情信息
    caseDirDetail: {},
    caseDetail: {},
    caseinfoMsg: {
      case_dir_father_path: "",
      case_dir_name: "",
    },

    nowChooseCase: "dir",
  },
  mutations: {
    setSelectedItem(state, payload) {
      state.selectedItemId = payload.id;
      state.selectedItemType = payload.type;
    },

    // 清除选中项
    clearSelectedItem(state) {
      state.selectedItemId = null;
      state.selectedItemType = null;
    },

    // 保存情报
    sendSaveIntelligence(state, params) {
      console.log("params:", params);
      let ID = null;
      if (params.id != "") {
        ID = params.id;
      } else {
        ID = window.main.$tools.reduceNumber;
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "case",
              index: "key_intelligence",
              id: ID,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              content_article: params.content,
              title: params.title,
              params: [
                {
                  k: "type",
                  v: ["generate_public_opinion_report_from_collection"],
                },
                { k: "id", v: [ID] },
              ],
            },
          },
        ],
        (res) => {
          console.log("res:", res);

          if (res.status == "ok") {
            // 调用组件的成功回调方法
            const currentRoute = window.main.$router.currentRoute;
            if (currentRoute.name === "intellManage") {
              // 通过事件总线或直接调用组件方法
              window.main.$root.$emit("saveIntelligenceSuccess");
            }
          }
        }
      );
    },

    // 分享情报
    shareIntelligence(state, item) {
      console.log("item:", item);
      let relation = {
        id: item.po._id, // 组织或人的id
        table: "key_intelligence", // 要存入的hb表
        name: item.intel._source.title, // 情报的标题
        targetID: item.intel._id, // 情报的id
        relation: "key_intelligence", // 两者之间的关系
        caseID: item.intel._source.case_id, // 情报所属的 案件ID
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {},
            msg: {
              type: "public",
              table: relation.table,
              prefix: relation.targetID,
              relation: relation.id + ";" + relation.relation,
              data: {
                data: {
                  caseID: relation.caseID,
                  intelID: relation.targetID,
                },
              },
            },
          },
        ],
        (res) => {
          console.log("添加正向关系1", res);
          if (res?.status == "ok") {
            window.main.$message.success("分享成功");
          }
        }
      );
    },

    // 翻页清除缓存,并设置页码
    clearBulletinAndSetFrom(state, v) {
      state.bulletinList = [];
      state.from = v;
    },

    setIntellList(state, data) {
      let perli = [];
      data.data.forEach((e) => {
        let orili = {
          label: e._source.name[0],
          value: e._source.name[0],
          info: e,
        };
        perli.push(orili);
      });
      state.origanList[data.index].children = perli;
    },
    //删除情报
    sendDelFileData(state, data) {
      console.log("nowCollect", data);

      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DelData",
        [
          {
            head: {
              row_key: [data.row],
            },
            msg: {
              table: "key_intelligence",
              type: "case",
              case_id: state.caseDetail.row,
            },
          },
        ],
        "intellManageTree/isDelFileData"
      );
    },
    // 是否删除收藏成功
    isDelFileData(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除成功");
        window.main.$store.commit("intellManageTree/clearSessionList");
        window.main.$store.commit(
          "intellManageTree/sendCaseSessionList",
          state.caseDetail.row
        );
      } else {
        window.main.$message.warning("删除失败！");
      }
    },
    //初始化case_id
    setClearcaseId(state, v) {
      state.case_id = null;
    },
    // 查看案件详情信息
    sendCaseDetail(state, v) {
      state.case_id = v;
      state.caseDetail = {};
      window.main.$case_socket.sendData(
        "Api.Case.Detail",
        [
          {
            head: {
              row_key: [v],
            },
          },
        ],
        "intellManageTree/setCaseDetail"
      );
    },
    setCaseDetail(state, res) {
      console.log("案件详情", res);
      state.caseDetail = res;
    },
    // 删除案件
    sendDelCase(state, v) {
      console.log("删除案件", v);

      window.main.$case_socket.sendData(
        "Api.Case.Del",
        [
          {
            head: {
              row_key: [v.row],
            },
          },
        ],
        "intellManageTree/setDelCase"
      );
    },
    setDelCase(state, data) {
      if (data.status === "ok") {
        window.main.$message({
          message: "删除成功",
          type: "success",
        });
        window.main.$store.commit("intellManageTree/sendCaseDirFatherArr");
        state.caseDetail = {};
      } else {
        window.main.$message.error("删除失败：可能有子权限");
      }
    },
    nowChooseCase(state, data) {
      state.nowChooseCase = data;
      console.log("showCaseDir", state.nowChooseCase);
    },
    // 修改案件目录信息
    sendEditDirectory(state, v) {
      state.caseinfoMsg.case_dir_father_path =
        v.caseDirDetail.columnValues.i.case_dir_father_path;
      state.caseinfoMsg.case_dir_name =
        v.caseDirDetail.columnValues.i.case_dir_name;
      window.main.$case_socket.sendData(
        "Api.CaseDir.Set",
        [
          {
            head: {
              row_key: [v.caseDirDetail.row],
            },
            msg: {
              case_dir_father_path:
                v.caseDirDetail.columnValues.i.case_dir_father_path,
              case_dir_name: v.caseDirDetail.columnValues.i.case_dir_name,
              map: {
                summary: v.summary,
              },
            },
          },
        ],
        "intellManageTree/setEditDirectory"
      );
    },
    setEditDirectory(state, res) {
      console.log("setEditDirectory", res);

      if (res.status === "ok") {
        window.main.$message.success("修改目录信息成功！");
        window.main.$store.commit(
          "intellManageTree/sendCaseDirDetail",
          state.caseinfoMsg
        );
      } else {
        window.main.$message.warning("修改目录信息失败！");
      }
    },
    // 添加案件
    sendAddCase(state, v) {
      window.main.$case_socket.sendData(
        "Api.Case.Add",
        [
          {
            msg: v,
          },
        ],
        "intellManageTree/setAddCase"
      );
    },
    setAddCase(state, res) {
      if (res.status === "ok") {
        window.main.$message({
          message: "添加案件成功",
          type: "success",
        });
        window.main.$case_socket.sendData(
          "Api.Case.List",
          [
            {
              head: {
                row_key: [],
                from: 0,
                size: 100,
              },
              msg: {
                case_dir_father_path: state.case_dir_father_path,
              },
            },
          ],
          "intellManageTree/setCaseTreeData"
        );
      } else {
        window.main.$message.error("修改失败：可能重名");
      }
    },
    // 添加案件目录
    sendAddCaseDir(state, v) {
      window.main.$case_socket.sendData(
        "Api.CaseDir.Add",
        [
          {
            msg: {
              case_dir_father_path: v.case_dir_father_path,
              case_dir_name: v.case_dir_name,
              summary: v.case_dir_summary,
            },
          },
        ],
        "intellManageTree/setAddCaseDir"
      );
    },
    setAddCaseDir(state, res) {
      if (res.status === "ok") {
        window.main.$message({
          message: "添加案件目录成功",
          type: "success",
        });
        window.main.$store.commit("intellManageTree/sendCaseDirList");
      } else {
        window.main.$message.error("修改失败：可能重名");
      }
    },
    // 删除案件目录
    sendDelCaseDir(state, v) {
      window.main.$case_socket.sendData(
        "Api.CaseDir.Del",
        [
          {
            msg: {
              case_dir_father_path: v.columnValues.i.case_dir_father_path,
              case_dir_name: v.columnValues.i.case_dir_name,
            },
          },
        ],
        "intellManageTree/setDelCaseDir"
      );
    },

    setDelCaseDir(state, data) {
      if (data.status === "ok") {
        window.main.$message({
          message: "删除成功",
          type: "success",
        });
        state.startCount = 0;
        window.main.$store.commit("intellManageTree/sendCaseDirFatherArr");
        state.caseDirDetail = {};
      } else {
        window.main.$message.error("删除失败：可能有子权限");
      }
    },
    sendSharedDirectory(state, node) {
      state.tmpNodeSum = node.length;
      let sub_authority_father = "/63617365";
      let sub_authority;
      let sub_authority_detail;
      if (!state.caseDirDetail.hasOwnProperty("columnValues")) {
        sub_authority = "/";
        sub_authority_detail = "";
      } else {
        if (state.caseDirDetail.columnValues.i.case_dir_father_path == "/") {
          sub_authority =
            state.caseDirDetail.columnValues.i.case_dir_father_path +
            state.caseDirDetail.columnValues.i.case_dir_name;
        } else {
          sub_authority =
            state.caseDirDetail.columnValues.i.case_dir_father_path +
            "/" +
            state.caseDirDetail.columnValues.i.case_dir_name;
        }
        sub_authority_detail = state.caseDirDetail.columnValues.i.case_dir_name;
      }

      for (let i = 0; i < node.length; i++) {
        state.tmpNodeNum = i;
        window.main.$pki_socket.sendData(
          "Api.SubAuthority.Add",
          [
            {
              msg: {
                authority: node[i].authority,
                sub_authority: "case",
                sub_authority_father: "/",
                sub_authority_detail: "案件权限",
              },
            },
          ],
          (res) => {
            console.log("");
          }
        );
      }

      for (let i = 0; i < node.length; i++) {
        state.tmpNodeNum = i;
        window.main.$pki_socket.sendData(
          "Api.SubAuthority.Add",
          [
            {
              msg: {
                authority: node[i].authority,
                sub_authority: sub_authority,
                sub_authority_father: sub_authority_father,
                sub_authority_detail: sub_authority_detail,
              },
            },
          ],
          "intellManageTree/setAddOneAuthority"
        );
      }
    },
    setAddOneAuthority(state, data) {
      if (state.tmpNodeNum == state.tmpNodeSum - 1) {
        window.main.$message({
          message: "案件目录分享成功!",
          type: "success",
        });
      }
    },
    setTreeData(state, data) {
      state.treeData = data;
    },
    getDepartmentList(state, authority) {
      let Authority = authority
        ? authority
        : window.main.$store.state.userInfo.userinfo.authority;
      window.main.$pki_socket.sendData(
        "Api.Authority.List",
        [
          {
            head: {
              from: 0,
              size: 10000,
            },
            msg: {
              authority_father: Authority,
            },
          },
        ],
        "intellManageTree/setDepartmentList"
      );
    },
    setDepartmentList(state, data) {
      console.log("setDepartmentList", data);
      if (data) {
        if (data.length) {
          state.tmpFather = data[0].authority_father;
          if (!state.treeData.length) {
            state.treeData = data;
          } else {
            state.tmpData = data;
            window.main.$store.commit(
              "intellManageTree/findFather",
              state.treeData
            );
          }
          for (let i = 0; i < data.length; i++) {
            window.main.$store.commit(
              "intellManageTree/getDepartmentList",
              data[i].authority
            );
          }
        } else {
          state.treeDataChange = true;
        }
      }
    },
    findFather(state, tree) {
      for (let i = 0; i < tree.length; i++) {
        console.log("111111", tree[i].authority_id, state.tmpFather);

        if (tree[i].authority_id == state.tmpFather) {
          tree[i].children = state.tmpData;
          state.treeDataChange = false;
          return;
        }
        if (tree[i].children) {
          window.main.$store.commit(
            "intellManageTree/findFather",
            tree[i].children
          );
        }
      }
    },
    /**初始化 */
    setCaseDirFatherListObj(state, value) {
      state.case_dir_father_list_obj = value;
    },
    setCaseDirFatherList(state, value) {
      state.case_dir_father_list = value;
    },

    /**点击删除子节点 */
    setReqNode(state, v) {
      state.req = v;
    },
    clearCaseTree(state, v) {
      v.subMap = new Object();
      window.main.$nextTick(() => {
        state.componentKey++;
      });
    },
    sendCaseList(state, v) {
      state.currentRoot = v.columnValues.i.current_root;
      if (state.req) {
        (function findClear(searchName, tmpSubNode) {
          for (let str in tmpSubNode) {
            if (str === searchName) {
              tmpSubNode[str].subMap = {};
            } else {
              findClear(searchName, tmpSubNode[str].subMap);
            }
          }
        })(v.columnValues.i.case_dir_name, state.rootNode.subMap);
        window.main.$nextTick(() => {
          state.componentKey++;
        });
      } else {
        let case_dir_father_path;
        if (v.columnValues.i.case_dir_father_path === "") {
          case_dir_father_path = "/" + v.columnValues.i.case_dir_name;
        } else if (v.columnValues.i.case_dir_father_path === "/") {
          case_dir_father_path = "/" + v.columnValues.i.case_dir_name;
        } else {
          case_dir_father_path =
            v.columnValues.i.case_dir_father_path +
            "/" +
            v.columnValues.i.case_dir_name;
        }
        window.main.$case_socket.sendData(
          "Api.Case.List",
          [
            {
              head: {
                row_key: [],
                from: 0,
                size: 100,
              },
              msg: {
                case_dir_father_path: case_dir_father_path,
              },
            },
          ],
          "intellManageTree/setCaseTreeData"
        );
      }
    },

    /**案件目录 */
    setCaseDirFather(state, v) {
      state.case_dir_father_path = v;
    },

    sendCaseDirList(state, v) {
      if (v) {
        state.currentRoot = v.columnValues.i.current_root;
        if (state.req) {
          (function findClear(searchName, tmpSubNode) {
            for (let str in tmpSubNode) {
              if (searchName.substring(0, 2) === "//") {
                searchName = searchName.substring(1, searchName.length);
              }
              if (str === searchName) {
                tmpSubNode[str].subMap = {};
              } else {
                findClear(searchName, tmpSubNode[str].subMap);
              }
            }
          })(
            v.columnValues.i.case_dir_father_path +
              "/" +
              v.columnValues.i.case_dir_name,
            state.rootNode.subMap
          );
          state.req = false;
          window.main.$nextTick(() => {
            state.componentKey++;
          });
        } else {
          let case_dir_father_path;
          if (v.columnValues.i.case_dir_father_path === "") {
            case_dir_father_path = "/" + v.columnValues.i.case_dir_name;
          } else if (v.columnValues.i.case_dir_father_path === "/") {
            case_dir_father_path = "/" + v.columnValues.i.case_dir_name;
          } else {
            case_dir_father_path =
              v.columnValues.i.case_dir_father_path +
              "/" +
              v.columnValues.i.case_dir_name;
          }
          window.main.$case_socket.sendData(
            "Api.CaseDir.List",
            [
              {
                head: {
                  row_key: state.nowRowKey,
                  //from: 0,
                  size: 10,
                },
                msg: {
                  case_dir_father_path: case_dir_father_path,
                },
              },
            ],
            "intellManageTree/setCaseTreeData"
          );
        }
      } else {
        window.main.$case_socket.sendData(
          "Api.CaseDir.List",
          [
            {
              head: {
                row_key: state.nowRowKey,
                //from: 0,
                size: 10,
              },
              msg: {
                case_dir_father_path: state.case_dir_father_path,
              },
            },
          ],
          "intellManageTree/setCaseTreeData"
        );
      }
    },

    sendCaseDirDetail(state, v) {
      console.log("sendCaseDirDetail", v.columnValues.i);
      state.caseDirDetail = {};
      window.main.$case_socket.sendData(
        "Api.CaseDir.Detail",
        [
          {
            head: { row_key: [v.row] },
            msg: {
              case_dir_father_path: v.columnValues.i.case_dir_father_path,
              case_dir_name: v.columnValues.i.case_dir_name,
            },
          },
        ],
        (res) => {
          if (res[0]) {
            state.caseDirDetail = res[0];
          } else {
            state.caseDirDetail = res;
          }
        }
      );
    },

    /**重置树 */
    resetCaseDirTree(state) {
      state.componentKey = 0;
      state.rootNode = new Object();
      state.rootNode["subMap"] = {};
    },
    /**请求最高权限案件目录 */
    sendCaseDirFatherArr(state, v) {
      let authority = window.main.$cookies.get("authority")
        ? window.main.$cookies.get("authority")
        : window.main.$store.state.userInfo.userinfo.authority;
      if (v == null) {
        window.main.$pki_socket.sendData(
          "Api.SubAuthority.List",
          [
            {
              head: {
                from: 0,
                size: 1000,
              },
              msg: {
                authority,
                sub_authority_father: "/",
              },
            },
          ],
          "intellManageTree/setCaseDirFatherArr"
        );
      } else {
        window.main.$pki_socket.sendData(
          "Api.SubAuthority.List",
          [
            {
              head: {
                from: 0,
                size: 1000,
              },
              msg: {
                authority,
                sub_authority_father: "/",
              },
            },
          ],
          "intellManageTree/setCaseDirFatherArr"
        );
      }
      let senMsg = {
        head: {
          from: 0,
          size: 1000,
        },
        msg: {
          authority: authority,
          sub_authority_father: "/",
        },
      };
      window.main.$pki_socket.sendData(
        "Api.SubAuthority.List",
        [senMsg],
        "intellManageTree/setCaseDirFatherArr"
      );
    },
    setCaseDirFatherArr(state, data) {
      let authority = window.main.$cookies.get("authority")
        ? window.main.$cookies.get("authority")
        : window.main.$store.state.userInfo.userinfo.authority;
      console.log("setCaseDirFatherArr", data);

      if (data) {
        let res = data.some((item, index) => {
          return item["sub_authority"] === "case";
        });
        if (res) {
          for (let i = 0; i < data.length; i++) {
            if (data[i]["sub_authority"] === "case") {
              window.main.$pki_socket.sendData(
                "Api.SubAuthority.List",
                [
                  {
                    head: {
                      from: 0,
                      size: 1000,
                    },
                    msg: {
                      authority,
                      sub_authority_father: "/63617365",
                    },
                  },
                ],
                "intellManageTree/setCaseDirFatherArrData"
              );
            }
          }
          if (data.length === 1000) {
            window.main.$pki_socket.sendData(
              "Api.SubAuthority.List",
              [
                {
                  head: {
                    from: 0,
                    size: 1000,
                  },
                  msg: {
                    authority,
                    sub_authority_father: "/63617365",
                  },
                },
              ],
              "intellManageTree/setCaseDirFatherArr"
            );
          }
        } else {
          if (data.length === 1000) {
            window.main.$pki_socket.sendData(
              "Api.SubAuthority.List",
              [
                {
                  head: {
                    from: 0,
                    size: 1000,
                  },
                  msg: {
                    authority,
                    sub_authority_father: "/63617365",
                  },
                },
              ],
              "intellManageTree/setCaseDirFatherArr"
            );
          }
        }
      }
    },
    setCaseDirFatherArrData(state, data) {
      console.log("setCaseDirFatherArrData", data);

      let authority = window.main.$cookies.get("authority")
        ? window.main.$cookies.get("authority")
        : window.main.$store.state.userInfo.userinfo.authority;
      if (data) {
        for (let i = 0; i < data.length; i++) {
          state.case_dir_father_list.push(Number(data[i]["sub_authority"]));
          state.case_dir_father_list_obj.push(data[i]);
        }

        if (data.length === 1000) {
          window.main.$pki_socket.sendData(
            "Api.SubAuthority.List",
            [
              {
                head: {
                  from: 0,
                  size: 1000,
                },
                msg: {
                  authority,
                  sub_authority_father: "/63617365",
                },
              },
            ],
            "intellManageTree/setCaseDirFatherArrData"
          );
        } else {
        }
        window.main.$store.commit(
          "intellManageTree/setCaseTreeData",
          state.case_dir_father_list_obj
        );
      }
    },

    setCaseTreeData(state, data) {
      console.log("setCaseTreeData", data);

      let getFlag = function searchTreeAndSetData(searchName, tmpSubNode) {
        let getNode = null;
        if (tmpSubNode.columnValues.i.hasOwnProperty("current_root")) {
          if (searchName == tmpSubNode.columnValues.i.current_root) {
            getNode = tmpSubNode;
          }
        }

        if (!getNode) {
          getNode = tmpSubNode.subMap[searchName];
        }
        if (getNode) {
          if (!getNode.hasOwnProperty("subMap")) {
            getNode.subMap = new Object();
          }
          data.forEach((item, index) => {
            let tmpObj = new Object();
            tmpObj.columnValues = new Object();
            tmpObj.columnValues.i = new Object();
            tmpObj.subMap = new Object();
            if (item.case_name === "case_dir") {
              tmpObj["iconStyle"] = "triangle";
              tmpObj["row"] = item.row;
              tmpObj.columnValues.i["current_root"] = state.currentRoot;
              tmpObj.columnValues.i["case_dir_father_path"] =
                item.columnValues.i.case_dir_father_path;
              tmpObj.columnValues.i["case_dir_name"] =
                item.columnValues.i.case_dir_name;
              tmpObj.columnValues.i["create_timestamp"] =
                item.columnValues.i.create_timestamp;
              tmpObj["case_name"] = item.case_name;
              let tmpKey;
              if (item.columnValues.i.case_dir_father_path == "/") {
                tmpKey = "case_dir;/" + item.columnValues.i.case_dir_name;
              } else {
                tmpKey =
                  "case_dir;" +
                  item.columnValues.i.case_dir_father_path +
                  "/" +
                  item.columnValues.i.case_dir_name;
              }
              getNode.subMap[tmpKey] = tmpObj;
            } else {
              tmpObj["iconStyle"] = "el-icon-collection";
              tmpObj["row"] = item.row;
              tmpObj.columnValues.i["current_root"] = state.currentRoot;
              tmpObj.columnValues.i["create_username"] =
                item.columnValues.i.create_username;
              tmpObj.columnValues.i["create_authority"] =
                item.columnValues.i.create_authority;
              tmpObj.columnValues.i["create_timestamp"] =
                item.columnValues.i.create_timestamp;
              tmpObj.columnValues.i["case_id"] = item.columnValues.i.case_id;
              tmpObj.columnValues.i["case_dir"] = item.columnValues.i.case_dir;
              tmpObj.columnValues.i["case_name"] =
                item.columnValues.i.case_name;
              tmpObj["case_name"] = item.case_name;
              getNode.subMap["case;" + item.row] = tmpObj;
            }
          });
          if (searchName == tmpSubNode.columnValues.i.current_root) {
            state.rootNode.subMap[searchName] = getNode;
          } else {
            tmpSubNode.subMap[searchName] = getNode;
          }
          window.main.$nextTick(() => {
            state.componentKey++;
          });
          return true;
        } else {
          //如果没有找到子节点，继续找
          for (const key in tmpSubNode.subMap) {
            if (searchTreeAndSetData(searchName, tmpSubNode.subMap[key])) {
              return true;
            }
          }
          return false;
        }
      };
      window.main.$nextTick(() => {
        state.componentKey++;
      });
      if (!data || data.length == undefined || data.length <= 0) {
        window.main.$message({
          message: "没有数据了，已是最后一条数据！",
          type: "success",
        });
        state.noneData = true;
        return;
      }
      let list = [];
      if (data[0].hasOwnProperty("sub_authority")) {
        state.rootNode = {};
        state.rootNode.subMap = new Object();
        data.forEach((item, index) => {
          let newItem = {};
          if (item.sub_authority == "/") {
            newItem = {
              iconStyle: "triangle",
              columnValues: {
                i: {
                  case_dir_father_path: "",
                  case_dir_name: item.sub_authority,
                  current_root: "case_dir;" + item.sub_authority,
                  create_username: "",
                  create_authority: "",
                  create_timestamp: item.create_timestamp,
                },
              },
            };
          } else {
            newItem = {
              iconStyle: "triangle",
              columnValues: {
                i: {
                  case_dir_father_path: item.sub_authority.substr(
                    0,
                    item.sub_authority.lastIndexOf("/")
                  ),
                  case_dir_name: item.sub_authority.substr(
                    item.sub_authority.lastIndexOf("/") + 1
                  ),
                  current_root: "case_dir;" + item.sub_authority,
                },
              },
            };
          }
          let tmpKey;
          if (item.sub_authority == "/") {
            tmpKey = "case_dir;/";
          } else {
            tmpKey = "case_dir;" + item.sub_authority;
          }
          state.rootNode.subMap[tmpKey] = newItem;

          //加载目录自动请求第一级
          state.caseDirDetail = Object.values(state.rootNode.subMap)[0];
          window.main.$store.commit(
            "intellManageTree/sendCaseDirList",
            state.caseDirDetail
          );
          setTimeout(() => {
            window.main.$store.commit(
              "intellManageTree/sendCaseList",
              state.caseDirDetail
            );

            // state.sendCaseList(state.caseDirDetail)
          }, 500);
          // state.showDuanFnData.row = state.caseDirDetail.row
          // state.showDuanFnData.name = state.caseDirDetail.columnValues.i.case_dir_name
        });
        return;
      }
      if (
        data[0].columnValues.i.hasOwnProperty("case_name") ||
        data[0].columnValues.i.hasOwnProperty("case_dir_name")
      ) {
        if (data[0].columnValues.i.hasOwnProperty("case_name")) {
          data = data.map((item, index) => {
            let tmp = {
              columnValues: {
                i: {
                  create_username: item.columnValues.i.create_username,
                  create_authority: item.columnValues.i.create_authority,
                  case_dir: item.columnValues.i.case_dir,
                  case_name: item.columnValues.i.case_name,
                  create_timestamp: item.columnValues.i.create_timestamp,
                },
              },
              row: item.row,
              case_name: "case",
            };
            return tmp;
          });
          let tmpKey;
          if (data[0].columnValues.i.case_dir == "/") {
            tmpKey = "case_dir;/";
          } else {
            tmpKey = "case_dir;" + data[0].columnValues.i.case_dir;
          }
          getFlag(tmpKey, state.rootNode.subMap[state.currentRoot]);
        }
        if (data[0].columnValues.i.hasOwnProperty("case_dir_name")) {
          data = data.map((item, index) => {
            let tmp = {
              columnValues: {
                i: {
                  create_username: item.columnValues.i.create_username,
                  create_authority: item.columnValues.i.create_authority,
                  case_dir_father_path:
                    item.columnValues.i.case_dir_father_path,
                  case_dir_name: item.columnValues.i.case_dir_name,
                  create_timestamp: item.columnValues.i.create_timestamp,
                },
              },
              row: item.row,
              case_name: "case_dir",
            };
            return tmp;
          });
          let tmpKey;
          if (data[0].columnValues.i.case_dir_father_path == "/") {
            tmpKey = "case_dir;/";
          } else {
            tmpKey = "case_dir;" + data[0].columnValues.i.case_dir_father_path;
          }
          getFlag(tmpKey, state.rootNode.subMap[state.currentRoot]);
        }
      }
    },

    // 获取案件下的session列表
    sendCaseSessionList(state, v) {
      if (v) {
        state.case_id = v;
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: (state.from - 1) * state.size,
              size: state.size,
            },
            control: {
              query_string: "",
              query_type: "case",
              case_id: [state.case_id],
              condition: {
                query_mode: "match",
                time_range: "无",
                time_range_begin: 0,
                time_range_end: 0,
                collection_time_range: "无",
                collection_time_range_begin: 0,
                collection_time_range_end: 0,
              },
            },
            msg: {
              data_range_index_name: "key_intelligence",
            },
          },
        ],
        "intellManageTree/setSessionList"
      );
    },
    setSessionList(state, res) {
      console.log("setSessionList:", res);

      if (res?.hits?.hits.length) {
        state.bulletinList = res.hits.hits;
        state.total = res.hits.total.value;
      } else {
        if (!window.main.$route.path.includes("/taskDetail")) {
          window.main.$message({
            message: "暂无情报",
            type: "warning",
          });
        }

        state.bulletinList = [];
      }
    },
    // 清除列表
    clearSessionList(state, v) {
      state.bulletinList = [];
      state.nowFileId = "";
      state.from = 1;
      state.total = 0;
    },
  },
  actions: {},
  modules: {},
};
