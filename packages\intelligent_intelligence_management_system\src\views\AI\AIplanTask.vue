<template>
  <div class="ai-plan-task-container">
    <div class="header-actions">
      <el-button type="primary" size="small" @click="showAddTaskDialog"
        >添加计划任务</el-button
      >
    </div>

    <div
      class="task-cards-container"
      ref="taskCardsContainer"
      @scroll="handleScroll"
    >
      <div
        v-if="localPlanTaskList.length === 0 && !loading"
        class="empty-container"
      >
        <el-empty description="暂无计划任务"></el-empty>
      </div>

      <!-- 任务卡片和加载提示容器 -->
      <div class="task-cards-wrapper">
        <task-card
          v-for="task in localPlanTaskList"
          :key="task.id"
          :task-id="task.id"
          :frequency="task.frequency"
          :time="task.time"
          :cronExpression="task.cronExpression"
          :templateName="task.templateName"
          :describe="task.describe"
          :environment="task.environment"
          :taskName="task.taskName"
          @edit="handleEditTask"
          @delete="handleDeleteTask"
        ></task-card>

        <!-- 加载更多提示 -->
        <div v-if="loading" class="loading-container">
          <el-loading-spinner></el-loading-spinner>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 没有更多数据提示 - 独自占一行 -->
    <div v-if="showNoMore" class="no-more-container">
      <span>没有更多数据了</span>
    </div>

    <!-- 添加/编辑任务对话框 -->
    <el-dialog
      :title="dialogStatus === 'add' ? '添加计划任务' : '编辑计划任务'"
      :visible.sync="dialogVisible"
      width="40%"
      append-to-body
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        :model="taskForm"
        :rules="rules"
        ref="taskForm"
        label-width="100px"
      >
        <el-form-item label="任务名" prop="taskName">
          <el-input
            v-model="taskForm.taskName"
            placeholder="请输入任务名称"
            style="width: 100%"
          ></el-input>
        </el-form-item>
        <el-form-item label="频次" prop="frequency">
          <el-radio-group
            v-model="taskForm.frequency"
            @change="handleFrequencyChange"
          >
            <el-radio label="每天">每天</el-radio>
            <el-radio label="每周">每周</el-radio>
            <el-radio label="一次" @click.native.prevent="handleOnceClick"
              >一次</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item
          label="日期范围"
          prop="dateRange"
          v-if="taskForm.frequency !== '一次'"
        >
          <el-date-picker
            v-model="taskForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="日期"
          prop="singleDate"
          v-if="taskForm.frequency === '一次'"
        >
          <el-date-picker
            v-model="taskForm.singleDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="时间" prop="timeValue">
          <el-time-picker
            v-model="taskForm.timeValue"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            @change="calculateCronExpression"
          >
          </el-time-picker>
        </el-form-item>
        <el-form-item label="Cron表达式">
          <el-input
            v-model="cronExpression"
            readonly
            placeholder="Cron表达式将根据频次和时间自动生成"
          >
            <template slot="append">
              <el-tooltip content="Cron表达式用于定时任务调度" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="任务模板" prop="templateNumber">
          <el-select
            v-model="taskForm.templateNumber"
            placeholder="请选择任务模板"
            style="width: 100%"
            :disabled="dialogStatus === 'edit'"
          >
            <el-option
              v-for="item in templateList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述信息" prop="describe">
          <el-input
            type="textarea"
            v-model="taskForm.describe"
            :rows="3"
            placeholder="请输入任务描述信息"
          ></el-input>
        </el-form-item>
        <el-form-item label="环境变量" prop="environment">
          <el-select
            v-model="environmentSelectValue"
            placeholder="请选择环境变量"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
            @change="handleEnvironmentChange"
          >
            <el-option
              v-for="item in taskQueueList"
              :key="item.name"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          v-if="dialogStatus === 'add'"
          @click="submitTaskForm"
          >确认</el-button
        >
        <el-button
          type="primary"
          v-if="dialogStatus === 'edit'"
          @click="updateTaskForm"
          >确认</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import TaskCard from "@/components/TaskCard.vue";

export default {
  name: "AIplanTask",
  components: {
    TaskCard,
  },
  data() {
    // 动态生成日期验证规则
    const validateDateInput = (rule, value, callback) => {
      if (this.taskForm.frequency === "一次") {
        if (!this.taskForm.singleDate) {
          return callback(new Error("请选择日期"));
        }
      } else {
        if (!this.taskForm.dateRange || this.taskForm.dateRange.length !== 2) {
          return callback(new Error("请选择日期范围"));
        }
      }
      callback();
    };

    return {
      dialogVisible: false,
      dialogStatus: "add",
      currentTaskId: "",
      taskForm: {
        frequency: "每天",
        dateRange: [],
        singleDate: "",
        timeValue: "",
        templateNumber: "",
        describe: "",
        environment: [],
        cronExpression: "",
        taskName: "",
      },
      rules: {
        frequency: [
          { required: true, message: "请选择任务频次", trigger: "change" },
        ],
        // dateRange: [{ validator: validateDateInput, trigger: "change" }],
        singleDate: [{ validator: validateDateInput, trigger: "change" }],
        timeValue: [
          { required: true, message: "请选择执行时间", trigger: "change" },
        ],
        templateNumber: [
          { required: true, message: "请选择任务模板", trigger: "change" },
        ],
        describe: [
          { required: true, message: "请输入任务描述", trigger: "blur" },
        ],
        taskName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
      },
      cronExpression: "",
      localPlanTaskList: [],
      loading: false,
      hasMore: true,
      showNoMore: false,
      taskQueueList: [],
    };
  },

  created() {
    this.resetPlanTaskData();
    this.resetTaskState();
    this.getPlanTaskTemplateList();
    this.getPlanTaskList();
    this.sendListAnalysisList();
  },

  computed: {
    ...mapState({
      templateList: (state) => state.aiPlanTask.templateList,
      planTaskList: (state) => state.aiPlanTask.planTaskList,
      size: (state) => state.aiPlanTask.size,
      allTaskQueueList: (state) => state.aiTaskQueue.allTaskList,
    }),

    // 环境变量选择器的值
    environmentSelectValue: {
      get() {
        // 从 taskForm.environment 数组中获取第一个元素的 value
        return this.taskForm.environment && this.taskForm.environment.length > 0
          ? this.taskForm.environment[0].value
          : "";
      },
      set(value) {
        // 这个 setter 不会被直接调用，因为我们使用 @change 事件
        console.log("environmentSelectValue setter:", value);
      },
    },
  },

  watch: {
    planTaskList: {
      handler(newVal, oldVal) {
        if (this.loading) {
          // 如果是加载更多，则累加数据
          if (newVal && newVal.length > 0) {
            const newTasks = newVal.map((item) => this.adaptTask(item));
            this.localPlanTaskList = [...this.localPlanTaskList, ...newTasks];

            // 如果返回的数据少于20条，说明没有更多数据了
            if (newVal.length < this.size) {
              this.hasMore = false;
              this.$nextTick(() => {
                this.updateShowNoMore();
              });
            }
          } else {
            // 如果没有返回数据，说明没有更多数据了
            this.hasMore = false;
            this.$nextTick(() => {
              this.updateShowNoMore();
            });
          }
          this.loading = false;
        } else {
          // 如果是首次加载或刷新，直接替换数据
          this.localPlanTaskList = newVal.map((item) => this.adaptTask(item));
          this.hasMore = newVal.length === this.size; // 如果返回20条数据，可能还有更多
          this.$nextTick(() => {
            this.updateShowNoMore();
          });
        }
      },
    },
    allTaskQueueList: {
      handler(newVal, oldVal) {
        newVal.forEach((item) => {
          this.taskQueueList.push({
            name: item.columnValues.info.title,
            value: item.row,
            type: item.columnValues.info.task_type,
          });
        });
      },
    },
  },

  methods: {
    ...mapMutations({
      resetPlanTaskData: "aiPlanTask/resetPlanTaskData",
      getPlanTaskTemplateList: "aiPlanTask/getPlanTaskTemplateList",
      getPlanTaskList: "aiPlanTask/getPlanTaskList",
      createPlanTask: "aiPlanTask/createPlanTask",
      deletePlanTask: "aiPlanTask/deletePlanTask",
      updatePlanTask: "aiPlanTask/updatePlanTask",
      getPlanTaskDetail: "aiPlanTask/getPlanTaskDetail",
      sendListAnalysisList: "aiTaskQueue/sendListAnalysisList",
      resetTaskState: "aiTaskQueue/resetTaskState",
    }),

    // 适配后端原始任务为前端卡片格式
    adaptTask(raw) {
      console.log("raw", raw);
      const task_name =
        JSON.parse(raw.metadata?.annotations?.parameter).task_name || "";
      // 描述信息
      const describe = raw.metadata?.annotations?.describe || "";
      // 任务模板id
      let templateName = "";
      this.templateList.forEach((item) => {
        if (item.key === raw.metadata?.annotations?.cronjobTemplateNumber) {
          templateName = item.value;
        }
      });
      // 环境变量
      const env =
        raw.spec?.jobTemplate?.spec?.template?.spec?.containers?.[0]?.env || [];
      // Cron表达式
      const cron =
        raw.metadata?.annotations?.schedule || raw.spec?.schedule || "";
      // 逆向解析时间
      let time = "";
      if (cron) {
        const parts = cron.split(" ");
        if (parts.length >= 2) {
          time = `${parts[1].padStart(2, "0")}:${parts[0].padStart(2, "0")}`;
        }
      }
      // 频次推断
      let frequency = "每天";
      if (/^\d+ \d+ \* \* \*$/.test(cron)) frequency = "每天";
      else if (/^\d+ \d+ \* \* \d+$/.test(cron)) frequency = "每周";
      else frequency = "一次";
      return {
        id: raw.metadata?.name || "",
        frequency,
        time,
        cronExpression: cron,
        templateName,
        describe,
        environment: env.map((e) => ({
          name: e.name,
          value: e.value,
          type: e.type,
        })),
        taskName: task_name,
      };
    },

    // 反向适配为后端结构
    buildRawTask(task) {
      return {
        metadata: {
          name: task.id,
          annotations: {
            describe: task.describe,
            timestamp: task.startDate,
            // ...其他注解
          },
          labels: {
            app: task.scope,
          },
        },
        spec: {
          schedule: task.cronExpression,
          jobTemplate: {
            spec: {
              template: {
                spec: {
                  containers: [
                    {
                      name: "hello",
                      env: task.environment.map((e) => ({
                        name: e.name,
                        value: e.value,
                      })),
                      // ...其他容器配置
                    },
                  ],
                },
              },
            },
          },
        },
      };
    },

    // 显示添加任务对话框
    showAddTaskDialog() {
      this.dialogStatus = "add";
      this.resetTaskForm();
      this.dialogVisible = true;
    },

    // 处理编辑任务
    handleEditTask(taskId) {
      this.dialogStatus = "edit";
      this.currentTaskId = taskId;
      const task = this.localPlanTaskList.find((item) => item.id === taskId);
      console.log("task", task);
      let templateNum = "";
      this.templateList.forEach((item) => {
        if (item.value === task.templateName) {
          templateNum = item.key;
        }
      });
      if (task) {
        // 确保 environment 数据格式正确
        const environmentData = Array.isArray(task.environment)
          ? task.environment
          : [];

        // 设置基础表单数据
        this.taskForm = {
          id: task.id,
          frequency: task.frequency,
          timeValue: task.time,
          cronExpression: task.cronExpression,
          templateNumber: templateNum,
          describe: task.describe,
          environment: environmentData,
          taskName: task.taskName || "",
        };
        this.cronExpression = task.cronExpression;
        // 根据频次类型设置日期字段
        if (task.frequency === "一次") {
          this.taskForm.singleDate = task.startDate;
          this.taskForm.dateRange = [];
        } else {
          this.taskForm.dateRange = [task.startDate, task.endDate];
          this.taskForm.singleDate = "";
        }

        this.dialogVisible = true;
      }
    },

    // 处理删除任务
    handleDeleteTask(taskId) {
      this.$confirm("确认删除该任务吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deletePlanTask(taskId);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    updateTaskForm() {
      this.updatePlanTask(this.taskForm);
      this.dialogVisible = false;
    },

    // 计算Cron表达式
    calculateCronExpression() {
      if (!this.taskForm.timeValue) {
        this.cronExpression = "";
        return;
      }

      const [hours, minutes] = this.taskForm.timeValue.split(":").map(Number);

      switch (this.taskForm.frequency) {
        case "每天":
          this.cronExpression = `${minutes} ${hours} * * *`;
          break;
        case "每周":
          this.cronExpression = `${minutes} ${hours} * * 1`;
          break;
        default:
          this.cronExpression = "";
      }
    },

    // 处理频次变化
    handleFrequencyChange(value) {
      // 切换频次时重置日期相关字段
      if (value === "一次") {
        this.taskForm.dateRange = [];
        this.taskForm.singleDate = "";
      } else {
        this.taskForm.singleDate = "";
        this.taskForm.dateRange = [];
      }
      // 重新计算cron表达式
      this.calculateCronExpression();
    },

    // 处理"一次"选项点击
    handleOnceClick() {
      this.$message({
        message: "功能正在开发中...",
        type: "info",
      });
    },

    // 提交任务表单
    submitTaskForm() {
      this.$refs.taskForm.validate((valid) => {
        if (valid) {
          // 构建任务数据对象
          const taskData = {
            cronjob_template_number: this.taskForm.templateNumber,
            schedule: this.cronExpression,
            describe: this.taskForm.describe,
            environment: this.taskForm.environment,
            task_name: this.taskForm.taskName,
          };

          // 创建任务
          this.createPlanTask(taskData);
          this.dialogVisible = false;
        } else {
          return false;
        }
      });
    },

    // 重置任务表单
    resetTaskForm() {
      this.taskForm = {
        frequency: "每天",
        dateRange: [],
        singleDate: "",
        timeValue: "",
        templateNumber: "",
        describe: "",
        environment: [],
        cronExpression: "",
        taskName: "",
      };
      this.cronExpression = "";
      if (this.$refs.taskForm) {
        this.$refs.taskForm.resetFields();
      }
    },

    // 处理滚动加载
    handleScroll() {
      if (this.$refs.taskCardsContainer) {
        const container = this.$refs.taskCardsContainer;
        const scrollTop = container.scrollTop;
        const clientHeight = container.clientHeight;
        const scrollHeight = container.scrollHeight;

        // 当滚动到距离底部100px时触发加载
        if (scrollTop + clientHeight >= scrollHeight - 100) {
          if (!this.loading && this.hasMore) {
            this.loadMoreData();
          }
        }
      }
    },

    // 加载更多数据
    loadMoreData() {
      this.loading = true;
      this.getPlanTaskList();
    },

    // 检测是否有滚动条
    hasScrollbar() {
      if (!this.$refs.taskCardsContainer) {
        return false;
      }
      const container = this.$refs.taskCardsContainer;
      return container.scrollHeight > container.clientHeight;
    },

    // 更新是否显示没有更多数据的提示
    updateShowNoMore() {
      this.showNoMore =
        !this.hasMore &&
        this.localPlanTaskList.length > 0 &&
        this.hasScrollbar();
    },

    // 处理环境变量变化
    handleEnvironmentChange(value) {
      // 根据选中的 value 找到对应的 item
      const selectedItem = this.taskQueueList.find(
        (item) => item.value === value
      );

      if (selectedItem) {
        // 设置为正确的格式：[{name: item.name, value: item.value}]
        this.taskForm.environment = [
          {
            name: selectedItem.name,
            value: selectedItem.value,
            type: selectedItem.type,
          },
        ];
      } else {
        // 如果没有找到对应的 item，可能是用户手动输入的
        this.taskForm.environment = [
          {
            name: value,
            value: value,
            type: "ai_workflow_task",
          },
        ];
      }

      console.log(
        "环境变量变化:",
        value,
        "设置结果:",
        this.taskForm.environment
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.ai-plan-task-container {
  padding: 20px;

  .header-actions {
    margin-bottom: 20px;
    text-align: right;
  }

  .task-cards-container {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding-right: 10px;
  }

  .task-cards-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .empty-container {
    width: 100%;
    padding: 40px 0;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #909399;
    font-size: 14px;

    .el-loading-spinner {
      margin-right: 8px;
    }
  }

  .no-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #909399;
    font-size: 14px;
    width: 100%;
    margin-top: 20px;
    border-top: 1px solid #ebeef5;
  }
}

::v-deep {
  // 解决遮罩层在对话框下方的问题
  .el-dialog__wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    // z-index: 2050 !important;
  }

  .el-dialog {
    position: relative;
    margin: 15vh auto 50px !important;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5) !important;
    box-sizing: border-box;
    // z-index: 2051 !important;
  }

  // 提高遮罩层的z-index
  .v-modal {
    /* position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: #000; */
    // z-index: 2040 !important;
  }

  // 确保日期选择器弹出层在对话框之上
  .el-picker-panel {
    z-index: 2060 !important;
  }

  .el-date-picker {
    z-index: 2060 !important;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }

  .el-dialog__body {
    padding: 20px 20px 0;
  }

  .el-form-item {
    margin-bottom: 18px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-form-item__content {
      line-height: 32px;
    }
  }

  .el-input__inner {
    height: 36px;
    line-height: 36px;
  }

  .el-radio-group {
    width: 100%;

    .el-radio {
      margin-right: 20px;
      margin-bottom: 0;
    }
  }

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .el-checkbox {
      margin-right: 15px;
      margin-bottom: 5px;
    }
  }

  .el-date-editor,
  .el-time-picker {
    width: 100%;
  }

  .el-dialog__footer {
    padding: 15px 20px 20px;
    text-align: center;

    .el-button {
      min-width: 100px;
    }
  }
}
</style>
