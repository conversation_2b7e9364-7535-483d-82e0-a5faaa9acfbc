export default {
  namespaced: true,
  state: {
    numberOfChannels: {
      intelligence: 0,
      relevantPerson: 0,
      relevantOriganization: 0,
      publicOpinion: 0,
      Telegram: 0,
      Twitter: 0,
      Facebook: 0,
      LinkedIn: 0,
    },
  },
  mutations: {
    // 频道数量统计，需要把所有频道搜一次ES
    sendChannelData(state) {
      let person_origanization = JSON.parse(window.main.$route.query.data) 
      console.log("person_origanization", person_origanization);
      
      let twitterIds = person_origanization._source.params.twitterIds;
      let telegramIds = person_origanization._source.params.telegramIds;
      let facebookIds = person_origanization._source.params.facebookIds;
      let linkedinIds = person_origanization._source.params.linkedInIds;

      // 关系人和组织数量
      if (person_origanization._source.params.relation.length > 0) {
        state.numberOfChannels.relevantPerson = JSON.parse(person_origanization._source.params?.relation[0]).person.length;
        state.numberOfChannels.relevantOriganization = JSON.parse(person_origanization._source.params?.relation[1]).organization.length;
        window.main.$store.commit("channelNum/getRelatedInformation", person_origanization._id);
        window.main.$store.commit("channelNum/getPublicOpinion", person_origanization);
      }

      // 搜索调用
      if (twitterIds && twitterIds.length > 0) {
        window.main.$store.commit("channelNum/getTwitterCount", twitterIds);
      }
      if (linkedinIds && linkedinIds.length > 0) {
        setTimeout(() => {
          window.main.$store.commit("channelNum/getLinkedInCount", linkedinIds);
        }, 3000);
      }
      // if (facebookIds && facebookIds.length > 0) {
      //   setTimeout(() => {
      //     window.main.$store.commit("channelNum/getFacebookCount", facebookIds);
      //   }, 2500);
      // }
    },

    /*** 获取情报数量 */
    getRelatedInformation(state, personId) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              row_key: [],
              size: 200,
            },
            msg: {
              table: "key_intelligence",
              type: "public",
              relation: personId + ";key_intelligence",
            },
          },
        ],
        (res) => {
          console.log("获取关联情报数据", res.length);
          if (res?.length > 0) {
            let add_es_query_conditions_list = {
              bool: {
                should: [],
              },
            };
            let case_id_list = [];
            res.forEach((item) => {
              if (!case_id_list.includes(item.columnValues.d.caseID)) {
                case_id_list.push(item.columnValues.d.caseID);
              }
              add_es_query_conditions_list.bool.should.push({
                bool: {
                  must: [
                    {
                      term: {
                        case_id: item.columnValues.d.caseID,
                      },
                    },
                    {
                      term: {
                        _id: item.columnValues.d.intelID,
                      },
                    },
                  ],
                },
              });
            });
            window.main.$main_socket.sendData(
              "Api.Search.SearchList.Query",
              [
                {
                  head: {
                    from: 0,
                    size: 200,
                  },
                  control: {
                    query_type: "case",
                    query_string: "",
                    add_es_query_conditions: add_es_query_conditions_list,
                    case_id: case_id_list,
                  },
                  msg: {
                    data_range_index_name: "key_intelligence",
                  },
                },
              ],
              (data) => {
                state.numberOfChannels.intelligence = data.hits.total.value;
              }
            );
          }
        }
      );
    },

    /*** 获取舆情数量 */
    getPublicOpinion(state, person) {
      let add_es_query_conditions = {
        bool: {
          should: [
            {
              match: {
                content: person._source.params.basic.name,
              },
            },
            {
              match: {
                title: person._source.params.basic.name,
              },
            }
          ],
        },
      };
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", add_es_query_conditions);
      window.main.$store.commit("newsSearchList/clearDataRangeTree")
      window.main.$store.commit("newsSearchList/setDataRangeGetter", [
        { data_range_path: "/public_sentiment", data_range_type: true },
      ]);
    },
    setPublicOpinionTotal(state, data) {
      state.numberOfChannels.publicOpinion = data;
    },

    /** 获取Twitter数量 */
    getTwitterCount(state, twitterID) {
      let obj = {
        data_range_father_path: "/social_platform/twitter/timeline",
        data_range_index_prefix: "social_platform_timeline_prefix_twitter",
      };
      let add_es_query_conditions = {
        bool: {
          should: twitterID.map(account => ({
            term: { user_id: account }
          }))
        }
      };
      window.main.$store.commit("search/twLinFacSearch/setNumberOfType", "twitter")
      window.main.$store.commit("search/twLinFacSearch/setAddEsQueryConditions", add_es_query_conditions);
      window.main.$store.commit("search/conditions/setConditionsData")
      window.main.$store.commit("search/twLinFacSearch/setSearchType", obj);
      window.main.$store.dispatch("search/twLinFacSearch/getListTrue", obj);
    },
    setTwitterCount(state, data) {
      state.numberOfChannels.Twitter = data;
    },

    /** 获取LinkedIn数量 */
    getLinkedInCount(state, linkedInID) {
      let obj = {
        data_range_father_path: "/social_platform/linkedin/timeline",
        data_range_index_prefix: "social_platform_timeline_prefix_linkedin",
      };
      let add_es_query_conditions = {
        bool: {
          should: linkedInID.map(account => ({
            term: { user_id: account }
          }))
        }
      };
      window.main.$store.commit("search/twLinFacSearch/setNumberOfType", "linkedin")
      window.main.$store.commit("search/twLinFacSearch/setAddEsQueryConditions", add_es_query_conditions);
      window.main.$store.commit("search/conditions/setConditionsData")
      window.main.$store.commit("search/twLinFacSearch/setSearchType", obj);
      window.main.$store.dispatch("search/twLinFacSearch/getListTrue", obj);
    },
    setLinkedInCount(state, data) {
      state.numberOfChannels.LinkedIn = data;
    },

    /** 获取Facebook数量 */
    getFacebookCount(state, facebookID) {
      let obj = {
        data_range_father_path: "/social_platform/facebook/timeline",
        data_range_index_prefix: "social_platform_timeline_prefix_facebook",
      };
      let add_es_query_conditions = {
        bool: {
          should: facebookID.map(account => ({
            term: { user_id: account }
          }))
        }
      };
      window.main.$store.commit("search/twLinFacSearch/setNumberOfType", "facebook")
      window.main.$store.commit("search/twLinFacSearch/setAddEsQueryConditions", add_es_query_conditions);
      window.main.$store.commit("search/conditions/setConditionsData")
      window.main.$store.commit("search/twLinFacSearch/setSearchType", obj);
      window.main.$store.dispatch("search/twLinFacSearch/getListTrue", obj);
    },
    setFacebookCount(state, data) {
      state.numberOfChannels.Facebook = data;
    },
  },
  actions: {},
};
