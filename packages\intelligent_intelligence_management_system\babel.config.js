const path = require("path");

module.exports = {
  presets: [
    [
      "@vue/cli-plugin-babel/preset",
      {
        useBuiltIns: "entry",
        corejs: 3,
        // 完全移除非标准的 include 配置
      },
    ],
  ],
  env: {
    development: {
      plugins: [
        "dynamic-import-node",
        [
          "@babel/plugin-proposal-nullish-coalescing-operator",
          {},
          "root-nullish",
        ],
        ["@babel/plugin-proposal-optional-chaining", {}, "root-optional"],
        ["@babel/plugin-proposal-numeric-separator", {}, "root-numeric"],
      ],
    },
  },
};
