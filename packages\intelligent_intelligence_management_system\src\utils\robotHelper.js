/**
 * 机器人助手插件
 * 提供方便的API来控制全局机器人组件
 */

import { formatEmailToHtml } from "./emailParser";

// 预警HTML内容处理工具函数
function parseWarningHtml(htmlContent) {
  try {
    // 创建临时DOM解析HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, "text/html");

    // 提取关键信息
    const title =
      doc.querySelector(".warningArticle .title")?.textContent || "未知标题";
    const source =
      doc.querySelector(".warningArticle .info div:nth-child(2) span")
        ?.textContent || "未知来源";
    const time =
      doc.querySelector(".warningArticle .info div:nth-child(3) span")
        ?.textContent || "";

    // 提取内容并截取摘要
    const contentElement = doc.querySelector(".warningArticle .content p");
    const fullContent = contentElement?.textContent || "";
    const summary =
      fullContent.length > 100
        ? fullContent.substring(0, 100) + "..."
        : fullContent;

    return {
      title,
      source,
      time,
      summary,
      fullContent,
    };
  } catch (error) {
    console.error("解析预警HTML失败:", error);
    return {
      title: "预警消息",
      source: "未知来源",
      time: "",
      summary: "无法解析预警内容",
      fullContent: "",
    };
  }
}

// 生成预警消息缩略内容
function generateWarningPreview(warningInfo) {
  return `
    <div class="warning-preview" data-id="${Date.now()}">
      <div class="warning-title">${warningInfo.title}</div>
      <div class="warning-meta">
        <span class="warning-source">${warningInfo.source}</span>
        <span class="warning-time">${warningInfo.time}</span>
      </div>
      <div class="warning-summary">${warningInfo.summary}</div>
      <div class="warning-action">点击查看详情</div>
    </div>
    <style>
      .warning-preview {
        background-color: #f9f9f9;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        cursor: pointer;
      }
      .warning-preview:hover {
        background-color: #f0f0f0;
      }
      .warning-title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 8px;
        color: #333;
      }
      .warning-meta {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #777;
        margin-bottom: 8px;
      }
      .warning-summary {
        font-size: 14px;
        color: #555;
        margin-bottom: 8px;
        line-height: 1.4;
      }
      .warning-action {
        text-align: right;
        font-size: 12px;
        color: #1890ff;
      }
    </style>
  `;
}

// 处理预警HTML消息
function processWarningHtml(htmlContent) {
  // 解析预警信息
  const warningInfo = parseWarningHtml(htmlContent);

  // 生成预览内容
  const previewHtml = generateWarningPreview(warningInfo);

  // 存储完整内容到全局对象，用于后续点击查看
  if (!window._warningContents) {
    window._warningContents = {};

    // 添加全局点击事件处理
    document.addEventListener("click", (event) => {
      const previewElement = event.target.closest(".warning-preview");
      if (!previewElement) return;

      const id = previewElement.dataset.id;
      if (!id || !window._warningContents[id]) return;

      showWarningDialog(window._warningContents[id]);
    });
  }

  // 存储原始HTML内容
  const id = Date.now().toString();
  window._warningContents[id] = htmlContent;

  return previewHtml;
}

// 显示预警详情对话框
function showWarningDialog(htmlContent) {
  // 如果已有预警对话框，则移除
  const existingDialog = document.getElementById("warning-dialog");
  if (existingDialog) {
    document.body.removeChild(existingDialog);
  }

  // 创建对话框
  const dialog = document.createElement("div");
  dialog.id = "warning-dialog";
  dialog.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  `;

  dialog.innerHTML = `
    <div style="
      background-color: #fff;
      border-radius: 8px;
      width: 80%;
      max-width: 800px;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    ">
      <div style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e0e0e0;
      ">
        <span style="font-size: 18px; font-weight: bold;">预警详情</span>
        <span style="font-size: 24px; cursor: pointer; color: #999;" id="close-warning-dialog">&times;</span>
      </div>
      <div style="padding: 16px; overflow-y: auto;">
        ${htmlContent}
      </div>
    </div>
  `;

  // 添加到文档
  document.body.appendChild(dialog);

  // 绑定关闭事件
  document
    .getElementById("close-warning-dialog")
    .addEventListener("click", () => {
      document.body.removeChild(dialog);
    });

  // 点击空白处关闭
  dialog.addEventListener("click", (e) => {
    if (e.target === dialog) {
      document.body.removeChild(dialog);
    }
  });
}

export default {
  install(Vue) {
    // 添加全局方法
    Vue.prototype.$robot = {
      /**
       * 显示或隐藏机器人
       * @param {Boolean} status - 可选，true显示，false隐藏，不传则切换状态
       */
      toggle(status) {
        Vue.prototype.$bus.$emit("toggleRobot", status);
      },

      /**
       * 显示机器人
       */
      show() {
        Vue.prototype.$bus.$emit("toggleRobot", true);
      },

      /**
       * 隐藏机器人
       */
      hide() {
        Vue.prototype.$bus.$emit("toggleRobot", false);
      },

      /**
       * 显示自定义提示
       * @param {String} tip - 要显示的提示内容
       */
      showTip(tip) {
        Vue.prototype.$bus.$emit("showRobotTip", tip);
      },

      /**
       * 显示通知提示
       * @param {String} message - 通知内容
       */
      notify(message) {
        const prefix = "📢 通知：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示提醒提示
       * @param {String} message - 提醒内容
       */
      remind(message) {
        const prefix = "⏰ 提醒：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示成功提示
       * @param {String} message - 成功内容
       */
      success(message) {
        const prefix = "✅ 成功：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示警告提示
       * @param {String} message - 警告内容
       */
      warning(message) {
        const prefix = "⚠️ 警告：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示错误提示
       * @param {String} message - 错误内容
       */
      error(message) {
        const prefix = "❌ 错误：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示系统消息
       * @param {String} message - 系统消息内容
       */
      systemMessage(message) {
        console.log("systemMessage:message", message);

        // 检查是否是HTML内容
        if (message.includes("<!DOCTYPE html>") || message.includes("<html")) {
          this.warningHtmlMessage(message);
          return;
        }

        const prefix = "🖥️ 系统：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示部门消息
       * @param {String} message - 部门消息内容
       */
      departmentMessage(message) {
        const prefix = "🏢 部门：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示广播消息
       * @param {String} message - 广播消息内容
       */
      broadcastMessage(message) {
        const prefix = "📡 广播：";
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 显示用户消息
       * @param {String} username - 用户名
       * @param {String} message - 消息内容
       */
      userMessage(username, message) {
        const prefix = `👤 ${username}：`;
        Vue.prototype.$bus.$emit("showRobotTip", `${prefix}${message}`);
      },

      /**
       * 以HTML格式显示消息
       * @param {String} message - HTML格式的消息内容
       * @param {String} type - 消息类型前缀（可选）
       * @param {Object} sender - 消息发送者信息（可选）
       */
      htmlMessage(message, type = "", sender = null) {
        let prefix = "";
        switch (type) {
          case "system":
            prefix = "🖥️ 系统：";
            break;
          case "broadcast":
            prefix = "📡 广播：";
            break;
          case "department":
            prefix = "🏢 部门：";
            break;
          case "user":
            if (sender) {
              prefix = `👤 ${sender.from || sender.username || "用户"}(${
                sender.account || ""
              })：`;
            } else {
              prefix = "👤 用户：";
            }
            break;
          case "email":
            prefix = "📧 邮件：";
            break;
          case "warning":
            prefix = "⚠️ 预警：";
            break;
          default:
            prefix = "";
        }

        Vue.prototype.$bus.$emit("showHtmlTip", { prefix, content: message });
      },

      /**
       * 显示邮件消息，自动解析MIME格式
       * @param {String} emailContent - MIME格式的邮件内容
       * @param {Object} sender - 消息发送者信息（可选）
       */
      emailMessage(emailContent, sender = null) {
        try {
          // 使用邮件解析工具解析并格式化邮件内容
          const formattedHtml = formatEmailToHtml(emailContent);

          // 显示邮件内容，包含发送者信息
          this.htmlMessage(formattedHtml, "email", sender);
        } catch (error) {
          console.error("处理邮件消息失败:", error);
          // 发生错误时降级为纯文本显示
          this.showTip("📧 邮件内容解析失败，请查看原始邮件");
        }
      },

      /**
       * 处理HTML格式的预警消息，显示为缩略内容
       * @param {String} htmlContent - HTML格式的预警消息内容
       */
      warningHtmlMessage(htmlContent) {
        try {
          // 处理预警消息，生成缩略内容
          const processedHtml = processWarningHtml(htmlContent);

          // 显示处理后的预警内容
          this.htmlMessage(processedHtml, "warning");
        } catch (error) {
          console.error("处理预警HTML消息失败:", error);
          // 错误时直接显示原始HTML
          this.htmlMessage(htmlContent, "warning");
        }
      },
    };
  },
};
