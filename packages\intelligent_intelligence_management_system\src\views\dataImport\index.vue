<template>
  <div class="card-list">
    <a
      :href="item.url"
      class="card-item"
      target="_blank"
      v-for="(item, index) in dataList"
      :key="index"
    >
      <el-card shadow="hover">
        <div class="card-content">
          <img :src="item.src" />
          <p>{{ item.description }}</p>
        </div>
      </el-card>
    </a>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dataList: [],
    };
  },

  created() {
    this.importData();
  },
  methods: {
    importData() {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs":
                "",
            },
          },
        ],
        (res) => {
          let arr = [];
          let data =
            res[
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
            ].tabs;
          for (let i = 0; i < data.length; i++) {
            switch (data[i].value) {
              case "public_opinion":
                arr.push({
                  src: require("@/assets/images/dataImport/yuqing.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8915",
                });
                break;
              case "telegram":
                arr.push({
                  src: require("@/assets/images/dataImport/telegram.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8911",
                });
                break;
              case "twitter":
                arr.push({
                  src: require("@/assets/images/dataImport/twitter.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8904",
                });
                break;
              case "facebook":
                arr.push({
                  src: require("@/assets/images/dataImport/facebook.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8902",
                });
                break;
              case "linkedin":
                arr.push({
                  src: require("@/assets/images/dataImport/Linkedin.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8930",
                });
                break;
              case "social_work_library":
                arr.push({
                  src: require("@/assets/images/dataImport/社工库.png"),
                  description: data[i].label,
                  url:
                    "https://" +
                    (window.location.host.split(":")[0] || "localhost") +
                    ":8930",
                });
                break;
              default:
                console.log("子系统", res);
            }
          }
          this.dataList = arr;
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.icon {
  display: block;
}
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 36px;
  padding: 16px;
}
.card-item {
  flex: 0 1 calc(20% - 32px);
  box-sizing: border-box;
}

.card-content {
  text-align: center;
  padding: 16px;
}
@media (max-width: 1200px) {
  .card-item {
    flex: 1 1 calc(25% - 16px);
  }
}
@media (max-width: 768px) {
  .card-item {
    flex: 1 1 calc(50% - 16px);
  }
}

@media (max-width: 480px) {
  .card-item {
    flex: 1 1 100%;
  }
}
</style>
