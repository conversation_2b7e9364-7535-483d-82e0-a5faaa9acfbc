export default {
  namespaced: true,
  state: {},
  mutations: {
    async sendGetDataDetailBaseData(state) {
      const sha512 = require("sha512");
      let tmpObj =
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["d"] ||
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["elasticsearch_data"];
      const hash =
        sha512(tmpObj["_index"] + "/_doc/" + tmpObj["_id"]).toString("hex") +
        ";" +
        tmpObj["_index"] +
        "/_doc/" +
        tmpObj["_id"];
      await window.main.$main_socket.sendData(
        "Api.Search.DataDetail.BaseData",
        [
          {
            head: {
              session_id: this.state.userInfo.session_id,
              row_key: [hash],
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramBaseData/setDataDetailBaseData"
      );
    },
    setDataDetailBaseData(state, data) {
      this.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "base_data",
        value: data,
      });
      let tmpObj =
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["d"] ||
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["elasticsearch_data"];
      if (tmpObj._source) {
        if (tmpObj._source.type == "twitter") {
          if (tmpObj._source.user_id != null) {
            if (tmpObj._source.article_count != null) {
              if (tmpObj._source.article_count != 0) {
              }
            }
            if (tmpObj._source.likes_count != null) {
              if (tmpObj._source.likes_count != 0) {
              }
            }
          }
        }
      }

      this.commit("telegramSearch/telegramSearchDataDetail/setDialogVisible", true);

      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data.columnValues.nlp ==
        null
      ) {
        return;
      }
      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data.columnValues.nlp
          .hanlp_server == null
      ) {
        return;
      }

      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data.columnValues.nlp
          .hanlp_server.analysis_doc != null &&
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data.columnValues.nlp
          .hanlp_server.analysis_doc.length == 2
      ) {
        window.main.$nextTick(() => {
          // 基于准备好的dom，初始化echarts实例
          let myChart = window.main.$echarts.init(
            document.getElementById("myChart")
          );
          let option = {
            xAxis: {
              type: "category",
              data: this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data
                .columnValues.nlp.hanlp_server.analysis_doc[0],
              axisLabel: {
                rotate: 60,
              },
            },
            yAxis: {
              type: "value",
            },
            series: [
              {
                data: this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_data
                  .columnValues.nlp.hanlp_server.analysis_doc[1],
                type: "bar",
                barWidth: 20,
              },
            ],
          };
          myChart.setOption(option);
        });
      }
    },
    async sendGetDataDetailBaseDataB(state) {
      let tmpObj =
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["elasticsearch_dataB"];
      const sha512 = require("sha512");
      const hash =
        sha512(tmpObj["_index"] + "/_doc/" + tmpObj["_id"]).toString("hex") +
        ";" +
        tmpObj["_index"] +
        "/_doc/" +
        tmpObj["_id"];
      await window.main.$main_socket.sendData(
        "Api.Search.DataDetail.BaseData",
        [
          {
            head: {
              session_id: this.state.userInfo.session_id,
              row_key: [hash],
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramBaseData/setDataDetailBaseDataB"
      );
    },
    setDataDetailBaseDataB(state, data) {
      this.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "base_dataB",
        value: data,
      });
      this.commit("telegramSearch/telegramSearchDataDetail/setDialogVisibleB", true);

      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB.columnValues
          .nlp == null
      ) {
        return;
      }
      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB.columnValues.nlp
          .hanlp_server == null
      ) {
        return;
      }

      if (
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB.columnValues.nlp
          .hanlp_server.analysis_doc != null &&
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB.columnValues.nlp
          .hanlp_server.analysis_doc.length == 2
      ) {
        window.main.$nextTick(() => {
          // 基于准备好的dom，初始化echarts实例
          let myChartB = window.main.$echarts.init(
            document.getElementById("myChartB")
          );
          let optionB = {
            xAxis: {
              type: "category",
              data: this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                .columnValues.nlp.hanlp_server.analysis_doc[0],
              axisLabel: {
                rotate: 60,
              },
            },
            yAxis: {
              type: "value",
            },
            series: [
              {
                data: this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail.base_dataB
                  .columnValues.nlp.hanlp_server.analysis_doc[1],
                type: "bar",
                barWidth: 20,
              },
            ],
          };
          myChartB.setOption(optionB);
        });
      }
    },
  },
};
