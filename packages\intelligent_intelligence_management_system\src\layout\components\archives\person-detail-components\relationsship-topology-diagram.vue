<template>
  <div class="relationship-topology">
    <div class="container">
      <!-- 主标题 -->
      <div class="header-card">
        <div class="card-header">
          <h1 class="card-title">
            <span class="icon">👥</span>
            关系拓扑图 - {{ centerPerson.name }}
          </h1>
        </div>
        
        <div class="main-content">
          <!-- ECharts拓扑图区域 -->
          <div class="topology-section">
            <div class="chart-container">
              <!-- 遮罩层 - 只在图表容器内显示 -->
              <div v-if="loading" class="loading-overlay">
                <div class="loading-content">
                  <div class="loading-spinner"></div>
                  <p class="loading-text">正在加载关系拓扑图...</p>
                  <div class="loading-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
                    </div>
                    <span class="progress-text">{{ loadingProgress }}%</span>
                  </div>
                  <p class="loading-step">{{ currentLoadingStep }}</p>
                  <button v-if="loadingError" @click="retryLoading" class="retry-button">
                    重新加载
                  </button>
                </div>
              </div>
              <div ref="chartContainer" class="echarts-container"></div>
            </div>
          </div>
          
          <!-- 信息面板 -->
          <div class="info-panel">
            <!-- 选中节点详情 -->
            <div class="info-card" v-if="selectedNode">
              <div class="card-header">
                <h3 class="node-title">
                  <span :class="getNodeIconClass(selectedNode.category)"></span>
                  {{ selectedNode.name }}
                </h3>
              </div>
              <div class="card-content">
                <div class="node-badge">{{ getNodeTypeLabel(selectedNode.category) }}</div>
                <div class="node-details" v-if="selectedNode.data">
                  <div v-if="selectedNode.data.age" class="detail-item">
                    <strong>年龄:</strong> {{ selectedNode.data.age }}
                  </div>
                  <div v-if="selectedNode.data.phone" class="detail-item">
                    <strong>电话:</strong> {{ selectedNode.data.phone }}
                  </div>
                  <div v-if="selectedNode.data.followers_count" class="detail-item">
                    <strong>粉丝数:</strong> {{ selectedNode.data.followers_count }}
                  </div>
                  <div v-if="selectedNode.data.following_count" class="detail-item">
                    <strong>关注数:</strong> {{ selectedNode.data.following_count }}
                  </div>
                  <div v-if="selectedNode.data.nickname" class="detail-item">
                    <strong>昵称:</strong> {{ selectedNode.data.nickname }}
                  </div>
                  <div v-if="selectedNode.data.remark" class="detail-item">
                    <strong>备注:</strong> {{ selectedNode.data.remark }}
                  </div>
                  <div v-if="selectedNode.data.belong" class="detail-item">
                    <strong>所属:</strong> {{ selectedNode.data.belong }}
                  </div>
                  <div v-if="selectedNode.data.relationship" class="detail-item">
                    <strong>关系:</strong> {{ selectedNode.data.relationship }}
                  </div>
                  <div v-if="selectedNode.data.desi" class="detail-item">
                    <strong>简介:</strong> {{ selectedNode.data.desi }}
                  </div>
                  <div v-if="selectedNode.data.createTime" class="detail-item">
                    <strong>创建时间:</strong> {{ selectedNode.data.createTime }}
                  </div>
                  <div v-if="selectedNode.data.dateBirth" class="detail-item">
                    <strong>生日:</strong> {{ selectedNode.data.dateBirth }}
                  </div>

                </div>
              </div>
            </div>
            
            <!-- 默认提示 -->
            <div class="info-card" v-else>
              <div class="card-content">
                <p class="empty-hint">点击节点查看详细信息</p>
                <p class="empty-hint-sub">拖拽节点可以调整位置</p>
              </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="stats-card">
              <div class="card-header">
                <h3>关系统计</h3>
              </div>
              <div class="card-content">

                <div class="stat-item">
                  <span>个人关系</span>
                  <span class="stat-badge personal">{{ personalRelations.length }}</span>
                </div>
                <div class="stat-item">
                  <span>组织关系</span>
                  <span class="stat-badge organizational">{{ organizationalRelations.length }}</span>
                </div>
                <div class="stat-item">
                  <span>Twitter用户</span>
                  <span class="stat-badge twitter-user">{{ twitterUser.length }}</span>
                </div>
                <div class="stat-item">
                  <span>Telegram用户</span>
                  <span class="stat-badge telegram">{{ telegramUser.length }}</span>
                </div>
                <div class="stat-item">
                  <span>LinkedIn用户</span>
                  <span class="stat-badge linkedin-user">{{ linkedinUser.length }}</span>
                </div>
                <div class="stat-item">
                  <span>Facebook用户</span>
                  <span class="stat-badge facebook-user">{{ facebookUser.length }}</span>
                </div>
                <div class="stat-item">
                  <span>收信邮箱</span>
                  <span class="stat-badge email-recipient">{{ Array.isArray(recipientEmail) ? recipientEmail.length : 0 }}</span>
                </div>
                <div class="stat-item">
                  <span>发信邮箱</span>
                  <span class="stat-badge email-sender">{{ Array.isArray(senderEmail) ? senderEmail.length : 0 }}</span>
                </div>
                <div class="stat-item">
                  <span>总节点数</span>
                  <span class="stat-badge total">{{ totalNodes }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 详细数据标签页 -->
      <div class="tabs-card">
        <div class="tabs-header">
          <div class="tab-list">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              :class="['tab-button', { active: activeTab === tab.key }]"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
        
        <div class="tab-content">
          <!-- 概览标签页 -->
          <div v-if="activeTab === 'overview'" class="overview-content">
            <div class="overview-grid">

              <div class="overview-card personal">
                <div class="overview-icon">👤</div>
                <div class="overview-number">{{ personalRelations.length }}</div>
                <p class="overview-label">个人关系</p>
              </div>
              <div class="overview-card organizational">
                <div class="overview-icon">🏢</div>
                <div class="overview-number">{{ organizationalRelations.length }}</div>
                <p class="overview-label">组织关系</p>
              </div>
              <div class="overview-card telegram">
                <div class="overview-icon">💬</div>
                <div class="overview-number">{{ telegramUser.length }}</div>
                <p class="overview-label">Telegram用户</p>
              </div>
              <div class="overview-card email-recipient">
                <div class="overview-icon">📥</div>
                <div class="overview-number">{{ Array.isArray(recipientEmail) ? recipientEmail.length : 0 }}</div>
                <p class="overview-label">收信邮箱</p>
              </div>
              <div class="overview-card email-sender">
                <div class="overview-icon">📤</div>
                <div class="overview-number">{{ Array.isArray(senderEmail) ? senderEmail.length : 0 }}</div>
                <p class="overview-label">发信邮箱</p>
              </div>
              <div class="overview-card twitter-user">
                <div class="overview-icon">🐦</div>
                <div class="overview-number">{{ twitterUser.length }}</div>
                <p class="overview-label">Twitter用户</p>
              </div>
              <div class="overview-card linkedin-user">
                <div class="overview-icon">💼</div>
                <div class="overview-number">{{ linkedinUser.length }}</div>
                <p class="overview-label">LinkedIn用户</p>
              </div>
              <div class="overview-card facebook-user">
                <div class="overview-icon">📘</div>
                <div class="overview-number">{{ facebookUser.length }}</div>
                <p class="overview-label">Facebook用户</p>
              </div>
            </div>
            
            <!-- Facebook用户展示 -->
            <div class="relation-group" v-if="facebookUser.length > 0">
              <h4 class="group-title">
                <span class="icon">📘</span>
                Facebook用户 ({{ facebookUser.length }})
              </h4>
              <div class="relation-grid">
                <div 
                  v-for="user in facebookUser" 
                  :key="user.id" 
                  class="relation-item facebook-user-card"
                >
                  <div class="relation-avatar">📘</div>
                  <div class="relation-info">
                    <div class="relation-name">{{ user.name }}</div>
                    <div class="relation-meta">{{ user.username }}</div>
                    <div class="relation-stats">
                      <span>朋友: {{ user.friends_count || 0 }}</span>
                      <span>关注者: {{ user.followers_count || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          

          
          <!-- 个人&组织关系标签页 -->
          <div v-if="activeTab === 'relations'" class="relations-section">
            <div class="relation-group">
              <h3 class="group-title">
                <span class="icon">👤</span>
                个人关系
              </h3>
              <div class="relations-grid" :class="{ 'single-item': personalRelations.length === 1 }">
                <div v-for="(person, index) in personalRelations" :key="index" class="relation-card personal-card">
                  <div class="relation-header">
                    <div class="relation-avatar">👤</div>
                    <div class="relation-info">
                      <h4 class="relation-name">{{ person.name }}</h4>
                      <p class="relation-type">个人关系</p>
                    </div>
                  </div>
                  <div class="relation-details">
                    <div class="detail-row">
                      <span class="detail-label">关系:</span>
                      <span class="detail-value">{{ person.relationship || '未指定' }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">年龄:</span>
                      <span class="detail-value">{{ person.age }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="relation-group">
              <h3 class="group-title">
                <span class="icon">🏢</span>
                组织关系
              </h3>
              <div class="relations-grid" :class="{ 'single-item': organizationalRelations.length === 1 }">
                <div v-for="(org, index) in organizationalRelations" :key="index" class="relation-card org-card">
                  <div class="relation-header">
                    <div class="relation-avatar">🏢</div>
                    <div class="relation-info">
                      <h4 class="relation-name">{{ org.name }}</h4>
                      <p class="relation-type">组织关系</p>
                    </div>
                  </div>
                  <div class="relation-details">
                    <div class="detail-row">
                      <span class="detail-label">关系:</span>
                      <span class="detail-value">{{ org.relationship }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">所属:</span>
                      <span class="detail-value">{{ org.belong }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Twitter用户标签页 -->
          <div v-if="activeTab === 'twitter-user'" class="relations-grid" :class="{ 'single-item': twitterUser.length === 1 }">
            <div v-for="(user, index) in twitterUser" :key="index" class="relation-card twitter-user-card">
              <div class="relation-header">
                <div class="relation-avatar">🐦</div>
                <div class="relation-info">
                  <h4 class="relation-name">{{ user.name }}</h4>
                  <p class="relation-nickname">{{ user.nickname }}</p>
                </div>
              </div>
              <div class="relation-stats">
                <div class="stat-item-small">
                  <span class="stat-label-small">粉丝数</span>
                  <span class="stat-value-small">{{ user.followers_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.following_count">
                  <span class="stat-label-small">关注数</span>
                  <span class="stat-value-small">{{ user.following_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.likes_count">
                  <span class="stat-label-small">点赞数</span>
                  <span class="stat-value-small">{{ user.likes_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.article_count">
                  <span class="stat-label-small">推文数</span>
                  <span class="stat-value-small">{{ user.article_count }}</span>
                </div>
              </div>
              <div class="relation-details">
                <div class="detail-row" v-if="user.username">
                  <span class="detail-label">用户名:</span>
                  <span class="detail-value">{{ user.username }}</span>
                </div>
                <div class="detail-row" v-if="user.id">
                  <span class="detail-label">用户ID:</span>
                  <span class="detail-value">{{ user.id }}</span>
                </div>
              </div>
              <div class="relation-summary" v-if="user.summary">
                {{ user.summary.substring(0, 100) }}{{ user.summary.length > 100 ? '...' : '' }}
              </div>
            </div>
          </div>
          
          <!-- Telegram用户标签页 -->
          <div v-if="activeTab === 'telegram'" class="relations-grid" :class="{ 'single-item': telegramUser.length === 1 }">
            <div v-for="(user, index) in telegramUser" :key="index" class="relation-card telegram-card">
              <div class="relation-header">
                <div class="relation-avatar">💬</div>
                <div class="relation-info">
                  <h4 class="relation-name">{{ user.name }}</h4>
                  <p class="relation-nickname">ID: {{ user.id }}</p>
                </div>
              </div>
              <div class="relation-details">
                <div class="detail-row" v-if="user.nickname">
                  <span class="detail-label">昵称:</span>
                  <span class="detail-value">{{ user.nickname }}</span>
                </div>
                <div class="detail-row" v-if="user.phone">
                  <span class="detail-label">电话:</span>
                  <span class="detail-value">{{ user.phone }}</span>
                </div>
                <div class="detail-row" v-if="user.username">
                  <span class="detail-label">用户名:</span>
                  <span class="detail-value">{{ user.username }}</span>
                </div>
                <div class="detail-row" v-if="user.status">
                  <span class="detail-label">状态:</span>
                  <span class="detail-value">{{ user.status }}</span>
                </div>
                <div class="detail-row" v-if="user.verified">
                  <span class="detail-label">认证:</span>
                  <span class="detail-value">{{ user.verified ? '是' : '否' }}</span>
                </div>
                <div class="detail-row" v-if="user.premium">
                  <span class="detail-label">高级用户:</span>
                  <span class="detail-value">{{ user.premium ? '是' : '否' }}</span>
                </div>
                <div class="detail-row" v-if="user.isBot">
                  <span class="detail-label">机器人:</span>
                  <span class="detail-value">{{ user.isBot ? '是' : '否' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- LinkedIn用户标签页 -->
          <div v-if="activeTab === 'linkedin-user'" class="relations-grid" :class="{ 'single-item': linkedinUser.length === 1 }">
            <div v-for="(user, index) in linkedinUser" :key="index" class="relation-card linkedin-user-card">
              <div class="relation-header">
                <div class="relation-avatar">💼</div>
                <div class="relation-info">
                  <h4 class="relation-name">{{ user.name }}</h4>
                  <p class="relation-nickname">ID: {{ user.id }}</p>
                </div>
              </div>
              <div class="relation-details">
                <div class="detail-row" v-if="user.nickname">
                  <span class="detail-label">昵称:</span>
                  <span class="detail-value">{{ user.nickname }}</span>
                </div>
                <div class="detail-row" v-if="user.icon">
                  <span class="detail-label">头像:</span>
                  <span class="detail-value"><img :src="user.icon" alt="icon" style="width:24px;height:24px;border-radius:50%;" /></span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Facebook用户标签页 -->
          <div v-if="activeTab === 'facebook-user'" class="relations-grid" :class="{ 'single-item': facebookUser.length === 1 }">
            <div v-for="(user, index) in facebookUser" :key="index" class="relation-card facebook-user-card">
              <div class="relation-header">
                <div class="relation-avatar">📘</div>
                <div class="relation-info">
                  <h4 class="relation-name">{{ user.name }}</h4>
                  <p class="relation-nickname">ID: {{ user.id }}</p>
                </div>
              </div>
              <div class="relation-stats" v-if="user.friends_count || user.followers_count || user.likes_count">
                <div class="stat-item-small" v-if="user.friends_count">
                  <span class="stat-label-small">好友数</span>
                  <span class="stat-value-small">{{ user.friends_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.followers_count">
                  <span class="stat-label-small">关注者</span>
                  <span class="stat-value-small">{{ user.followers_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.likes_count">
                  <span class="stat-label-small">点赞数</span>
                  <span class="stat-value-small">{{ user.likes_count }}</span>
                </div>
                <div class="stat-item-small" v-if="user.posts_count">
                  <span class="stat-label-small">帖子数</span>
                  <span class="stat-value-small">{{ user.posts_count }}</span>
                </div>
              </div>
              <div class="relation-details">
                <div class="detail-row" v-if="user.nickname">
                  <span class="detail-label">昵称:</span>
                  <span class="detail-value">{{ user.nickname }}</span>
                </div>
                <div class="detail-row" v-if="user.username">
                  <span class="detail-label">用户名:</span>
                  <span class="detail-value">{{ user.username }}</span>
                </div>
                <div class="detail-row" v-if="user.email">
                  <span class="detail-label">邮箱:</span>
                  <span class="detail-value">{{ user.email }}</span>
                </div>
                <div class="detail-row" v-if="user.location">
                  <span class="detail-label">位置:</span>
                  <span class="detail-value">{{ user.location }}</span>
                </div>
                <div class="detail-row" v-if="user.verified">
                  <span class="detail-label">认证:</span>
                  <span class="detail-value">{{ user.verified ? '是' : '否' }}</span>
                </div>
              </div>
              <div class="relation-summary" v-if="user.bio">
                {{ user.bio.substring(0, 100) }}{{ user.bio.length > 100 ? '...' : '' }}
              </div>
            </div>
          </div>
          
          <!-- 邮箱标签页 -->
          <div v-if="activeTab === 'email'" class="relations-section">
            <div class="relation-group">
              <h3 class="group-title">
                <span class="icon">📥</span>
                收信邮箱
              </h3>
              <div class="relations-grid" :class="{ 'single-item': (Array.isArray(recipientEmail) ? recipientEmail.length : 0) === 1 }">
                <div v-if="Array.isArray(recipientEmail) && recipientEmail.length > 0">
                  <div v-for="(mail, index) in recipientEmail" :key="index" class="relation-card email-recipient-card">
                    <div class="relation-header">
                      <div class="relation-avatar">📥</div>
                      <div class="relation-info">
                        <h4 class="relation-name">{{ mail }}</h4>
                        <p class="relation-type">收信邮箱</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="relation-card email-recipient-card">
                  <div class="card-content" style="text-align:center;color:#a0aec0;">暂无收信邮箱数据</div>
                </div>
              </div>
            </div>
            
            <div class="relation-group">
              <h3 class="group-title">
                <span class="icon">📤</span>
                发信邮箱
              </h3>
              <div class="relations-grid" :class="{ 'single-item': (Array.isArray(senderEmail) ? senderEmail.length : 0) === 1 }">
                <div v-if="Array.isArray(senderEmail) && senderEmail.length > 0">
                  <div v-for="(mail, index) in senderEmail" :key="index" class="relation-card email-sender-card">
                    <div class="relation-header">
                      <div class="relation-avatar">📤</div>
                      <div class="relation-info">
                        <h4 class="relation-name">{{ mail }}</h4>
                        <p class="relation-type">发信邮箱</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="relation-card email-sender-card">
                  <div class="card-content" style="text-align:center;color:#a0aec0;">暂无发信邮箱数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'EChartsRelationshipTopology',
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
      selectedNode: null,
      activeTab: 'overview',
      physicsEnabled: true,
      loading: true,
      loadingProgress: 0,
      currentLoadingStep: '初始化数据...',
      loadingError: false,
      resizeObserver: null,
      resizeTimer: null,
      // 标签页配置
      tabs: [
        { key: 'overview', label: '概览' },
        { key: 'twitter-user', label: 'Twitter用户' },
        { key: 'linkedin-user', label: 'LinkedIn用户' },
        { key: 'facebook-user', label: 'Facebook用户' },
        { key: 'relations', label: '个人&组织关系' },
        { key: 'telegram', label: 'Telegram用户' },
        { key: 'email', label: '邮箱' }
      ]
    }
  },
  
  computed: {
    // 中心人物数据
    centerPerson() {
      return this.$store.state.relationsshipTopologyDiagram.selectPerson?._source.params.basic;
    },

    // 个人关系数据
    personalRelations() {
      let person = []
      this.$store.state.relationsshipTopologyDiagram.personRelationSearchList.map(item => {
        person.push({
          name: item.basic.name,
          relationship: item.relation,
          age: item.basic.age,
          phone: item.basic.phone,
          identity: item.basic.identity,
          dateBirth: item.basic.dateBirth,
          remark: item.basic.remark,
          sex: item.basic.sex,
        })
      });
      return person;
    },

    // 组织关系数据
    organizationalRelations() {
      // 兼容直接为数组或从selectPerson中提取
      const orgs = this.$store.state.relationsshipTopologyDiagram.organizationRelationSearchList;
      return orgs.map(item => ({
        name: item.basic.name,
        relationship: item.relation,
        belong: item.basic.belong,
        createTime: item.basic.createTime,
        desi: item.basic.desi,
        remark: item.basic.remark
      }));
    },

    // Twitter用户数据
    twitterUser() {
      let twitter = []
      this.$store.state.relationsshipTopologyDiagram.twitterSearchList.map(item => {
        twitter.push({  
            id: item.user_id || '',
            name: item.nickname.nickname || item.user_id || '',
            nickname: item.nickname.nickname || '',
            username: item.username.username || '',
            icon: item.icon?.[0]?.icon.file_url || '',
            followers_count: item.followers_count || '0',
            following_count: item.following_count || '0',
            followers: item?.followers || [],
            following: item?.following || [],
            likes_count: item.likes_count || '0',
            article_count: item.article_count || '0',
            summary: item.summary || '',
            type: 'twitter',
            category: 'twitter-user',
        })
      });
      return twitter;
    },

    // Telegram用户（单个对象，非数组）
    telegramUser() {
      let telegram = []
      this.$store.state.relationsshipTopologyDiagram.telegramSearchList.map(item => {
        telegram.push({
          id: item.user_id || item.user_id.user_id || '',
          name: item.nickname.nickname || '',
          nickname: item.nickname.nickname || '',
          icon: item.icon.icon.file_url || '',
          phone: item.telephone.telephone || '',
          username: item.username.username || '',
          type: item.type.type || 'telegram',
          status: item.parms.parms.status,
          verified: item.parms.parms.verified || false,
          premium: item.parms.parms.premium || false,
          isBot: item.parms.parms.bot || false,
          category: 'telegram-user',
        })
      });
      return telegram;
    },

    // Linkedin用户数据
    linkedinUser() {
      let linkedin = []
      this.$store.state.relationsshipTopologyDiagram.linkedinSearchList.map(item => {
        linkedin.push({
          id: item._source.user_id || '',
          name: item._source.nickname || '',
          nickname: item._source.nickname || '',
          icon: item._source.icon?.[0]?.file_url || '',
        })
      });
      return linkedin;
    },

    // Facebook用户数据
    facebookUser() {
      let facebook = []
      this.$store.state.relationsshipTopologyDiagram.facebookSearchList.map(item => {
        facebook.push({
          id: item._source.user_id || item.user_id || '',
          name: item._source.nickname || item.nickname || '',
          nickname: item._source.nickname || item.nickname || '',
          username: item._source.username || item.username || '',
          icon: item._source.icon?.[0]?.file_url || item.icon || '',
          email: item._source.email || item.email || '',
          location: item._source.location || item.location || '',
          bio: item._source.bio || item.bio || '',
          friends_count: item._source.friends_count || item.friends_count || '0',
          followers_count: item._source.followers_count || item.followers_count || '0',
          likes_count: item._source.likes_count || item.likes_count || '0',
          posts_count: item._source.posts_count || item.posts_count || '0',
          verified: item._source.verified || item.verified || false,
          type: 'facebook',
          category: 'facebook-user',
        })
      });
      return facebook;
    },

    // 收信邮箱数据
    recipientEmail() {
      return this.$store.state.relationsshipTopologyDiagram.mailRecipientSearchList;
    },

    // 发信邮箱数据
    senderEmail() {
      return this.$store.state.relationsshipTopologyDiagram.mailSenderSearchList;
    },

    totalNodes() {
      return 1
        + this.twitterUser.length
        + this.linkedinUser.length
        + this.facebookUser.length
        + this.personalRelations.length
        + this.organizationalRelations.length
        + this.telegramUser.length
        + (Array.isArray(this.recipientEmail) ? this.recipientEmail.length : 0)
        + (Array.isArray(this.senderEmail) ? this.senderEmail.length : 0);
    },
    
    networkDensity() {
      const totalPossibleConnections = (this.totalNodes * (this.totalNodes - 1)) / 2
      const actualConnections = this.totalNodes - 1 // 星形网络
      return Math.round((actualConnections / totalPossibleConnections) * 100)
    },
    
    averageConnections() {
      return Math.round(((this.totalNodes - 1) * 2) / this.totalNodes * 10) / 10
    },
    
    maxConnections() {
      return this.totalNodes - 1 // 中心节点的连接数
    },
  },

  created() {
    window.main.$store.commit("relationsshipTopologyDiagram/resetAllData");
    const queryData = this.$route.query.data;
    let info = JSON.parse(queryData);
    window.main.$store.commit("relationsshipTopologyDiagram/setSelectPerson", info);
  },
  
  mounted() {
    // 确保DOM已经渲染完成后再开始加载
    this.$nextTick(() => {
      // 启动进度条动画
      this.startProgressAnimation();
      
      // 设置ResizeObserver监听容器大小变化
      this.setupResizeObserver();
      
      // 延迟4秒后加载ECharts
      setTimeout(() => {
        this.tryInitECharts();
      }, 6000);
    });
  },
  
  beforeDestroy() {
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = null;
    }
    
    // 清理ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
    
    // 清理图表实例
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  
  methods: {
    // 获取节点样式
    getNodeStyle(nodeName) {
      const targetNodes = {
        '关注': { color: '#409EFF', textColor: '#FFFFFF', fontSize: 10, symbolSize: 50, lineHeight: 13 },
        '粉丝': { color: '#67C23A', textColor: '#FFFFFF', fontSize: 10, symbolSize: 50, lineHeight: 13 },
        '个人关系': { color: '#E6A23C', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
        '组织关系': { color: '#F56C6C', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
        '收信箱': { color: '#909399', textColor: '#FFFFFF', fontSize: 10, symbolSize: 50, lineHeight: 13 },
        '邮箱': { color: '#7e57c2', textColor: '#FFFFFF', fontSize: 10, symbolSize: 50, lineHeight: 13 },
        '发信箱': { color: '#606266', textColor: '#FFFFFF', fontSize: 10, symbolSize: 50, lineHeight: 13 },
        'Telegram': { color: '#0088CC', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
        'LinkedIn': { color: '#0077B5', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
        'Facebook': { color: '#1877F2', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
        'Twitter': { color: '#1DA1F2', textColor: '#FFFFFF', fontSize: 10, symbolSize: 60, lineHeight: 13 },
      };

      // 查找匹配的节点类型
      for (const [key, style] of Object.entries(targetNodes)) {
        if (nodeName.includes(key)) {
          return {
            symbolSize: style.symbolSize,
            label: {
              show: true,
              position: 'inside',
              color: style.textColor,
              fontSize: style.fontSize,
              fontWeight: 'bold',
              lineHeight: style.lineHeight,
              formatter: function(params) {
                // 如果有 displayName 则使用，否则使用 name
                return params.data.displayName || params.data.name;
              }
            },
            itemStyle: {
              color: style.color,
              borderColor: '#fff',
              borderWidth: 2
            }
          };
        }
      }

      // 默认样式
      return {
        label: {
          show: true,
          color: '#333333',
          fontSize: 10
        }
      };
    },

    // 获取节点数量的辅助函数
    getNodeCount(nodeType, data) {
      switch(nodeType) {
        case '关注':
          return data && data.parentUser ? data.parentUser.following_count || data.parentUser.following?.length || 0 : 0;
        case '粉丝':
          return data && data.parentUser ? data.parentUser.followers_count || data.parentUser.followers?.length || 0 : 0;
        case '个人关系':
          return this.personalRelations.length;
        case '组织关系':
          return this.organizationalRelations.length;
        case '收信箱':
          return Array.isArray(this.recipientEmail) ? this.recipientEmail.length : 0;
        case '发信箱':
          return Array.isArray(this.senderEmail) ? this.senderEmail.length : 0;
        case 'Telegram':
          return this.telegramUser.length;
        case 'LinkedIn':
          return this.linkedinUser.length;
        case 'Facebook':
          return this.facebookUser.length;
        case 'Twitter':
          return this.twitterUser.length;
        default:
          return 0;
      }
    },


    setupResizeObserver() {
      // 检查浏览器是否支持ResizeObserver
      if (typeof ResizeObserver === 'undefined') {
        console.warn('ResizeObserver not supported, falling back to window resize event');
        // 降级到window resize事件
        window.addEventListener('resize', this.handleResize);
        return;
      }
      
      // 创建ResizeObserver监听容器大小变化
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // 检查是否是图表容器
          if (entry.target === this.$refs.chartContainer) {
            this.handleResize();
            break;
          }
        }
      });
      
      // 延迟监听，确保DOM已经渲染
      this.$nextTick(() => {
        if (this.$refs.chartContainer) {
          this.resizeObserver.observe(this.$refs.chartContainer);
          console.log('ResizeObserver started observing chart container');
        }
      });
    },
    
    handleResize() {
      // 防抖处理，避免频繁调用
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      
      this.resizeTimer = setTimeout(() => {
        if (this.chart) {
          console.log('Resizing ECharts chart...');
          this.chart.resize();
        }
      }, 100);
    },
    
    async tryInitECharts(retryCount = 0) {
      const MAX_RETRIES = 15; // 最多重试15次
      const RETRY_DELAY = 200; // 每次重试间隔200ms
      
      try {
        // 检查DOM元素是否存在且有尺寸
        const container = this.$refs.chartContainer;
        
        if (!container) {
          console.log(`Chart container not found, retry ${retryCount + 1}/${MAX_RETRIES}`);
          this.currentLoadingStep = `等待DOM渲染... (${retryCount + 1}/${MAX_RETRIES})`;
          
          if (retryCount < MAX_RETRIES) {
            setTimeout(() => this.tryInitECharts(retryCount + 1), RETRY_DELAY);
            return;
          } else {
            throw new Error('Chart container not found after maximum retries');
          }
        }
        
        // 检查容器是否有有效尺寸
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          console.log(`Chart container has zero dimensions, retry ${retryCount + 1}/${MAX_RETRIES}`);
          this.currentLoadingStep = `等待容器尺寸... (${retryCount + 1}/${MAX_RETRIES})`;
          
          if (retryCount < MAX_RETRIES) {
            setTimeout(() => this.tryInitECharts(retryCount + 1), RETRY_DELAY);
            return;
          } else {
            throw new Error('Chart container has no valid dimensions after maximum retries');
          }
        }
        
        // 检查ECharts是否可用
        if (!this.$echarts) {
          console.error('ECharts not available');
          this.currentLoadingStep = 'ECharts库未加载...';
          throw new Error('ECharts library not available');
        }
        
        this.currentLoadingStep = '初始化图表...';
        this.loadingProgress = Math.max(this.loadingProgress, 95);
        
        // 使用全局引入的 ECharts
        const echarts = this.$echarts;
        this.initChart(echarts);
        
        this.currentLoadingStep = '图表初始化完成';
        this.loadingProgress = 100;
        
      } catch (error) {
        console.error('Failed to load ECharts:', error);
        this.currentLoadingStep = '加载失败，使用备用方案...';
        this.loadingError = true;
        this.renderFallbackChart();
      } finally {
        // 无论成功还是失败，都隐藏遮罩层
        setTimeout(() => {
          this.loading = false;
        }, 500); // 延迟500ms隐藏，让用户看到完成状态
      }
    },
    
    initChart(echarts) {
      try {
        // 如果已经存在图表实例，先销毁
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }
        
        // 容器已经在tryInitECharts中验证过了
        const container = this.$refs.chartContainer;
        this.chart = echarts.init(container);
        
        const chartData = this.getChartData();
        
        const option = {
          title: {
            text: '关系网络拓扑图',
            top: 'top',
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              if (params.dataType === 'node') {
                const data = params.data.data || {};
                let content = `<strong>${params.data.name}</strong><br/>`;
                content += `类型: ${this.getNodeTypeLabel(params.data.category)}<br/>`;
                
                if (data.age) content += `年龄: ${data.age}<br/>`;
                if (data.phone) content += `电话: ${data.phone}<br/>`;
                if (data.followers_count) content += `粉丝数: ${data.followers_count}<br/>`;
                if (data.nickname) content += `昵称: ${data.nickname}<br/>`;
                if (data.belong) content += `所属: ${data.belong}<br/>`;
                if (data.relationship) content += `关系: ${data.relationship}<br/>`;
                
                return content;
              } else if (params.dataType === 'edge') {
                return `关系: ${params.data.name}`;
              }
            }
          },
          legend: {
            show: false
          },
          animationDuration: 1500,
          animationEasingUpdate: 'quinticInOut',
          series: [
            {
              type: 'graph',
              layout: 'force',
              data: chartData.nodes,
              links: chartData.links,
              categories: [
                { name: 'center' },
                { name: 'personal-group' },
                { name: 'personal' },
                { name: 'org-group' },
                { name: 'organization' },
                { name: 'twitter-user-group' },
                { name: 'twitter-user' },
                { name: 'twitter-followers' },
                { name: 'twitter-follower' },
                { name: 'twitter-following' },
                { name: 'twitter-following-data' },
                { name: 'linkedin-user-group' },
                { name: 'linkedin-user' },
                { name: 'facebook-user-group' },
                { name: 'facebook-user' },
                { name: 'telegram-group' },
                { name: 'telegram-user' },
                { name: 'email-group' },
                { name: 'email-recipient-group' },
                { name: 'email-recipient' },
                { name: 'email-sender-group' },
                { name: 'email-sender' }
              ],
              roam: true,
              focusNodeAdjacency: true,
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
              },
              label: {
                show: true,
                position: 'bottom',
                formatter: function(params) {
                  // 如果节点有自定义的 label 配置，则使用节点的 formatter
                  if (params.data.label && params.data.label.formatter) {
                    return params.data.label.formatter(params);
                  }
                  return params.name;
                },
                fontSize: 10,
                color: '#333333'
              },
              lineStyle: {
                opacity: 0.8,
                width: 2,
                curveness: 0.1
              },
              emphasis: {
                focus: 'adjacency',
                lineStyle: {
                  width: 4
                }
              },
              force: {
                repulsion: 600,
                gravity: 0.2,
                edgeLength: [100, 150],
                layoutAnimation: true
              }
            }
          ]
        };
        
        this.chart.setOption(option);
        
        // 添加点击事件
        this.chart.on('click', (params) => {
          if (params.dataType === 'node') {
            this.selectedNode = params.data;
          }
        });
        
        // 响应式处理 - 使用ResizeObserver替代window resize事件
        if (this.resizeObserver && this.$refs.chartContainer) {
          this.resizeObserver.observe(this.$refs.chartContainer);
        }
        
        // 图表加载完成后自动缩小展示
        setTimeout(() => {
          if (this.chart) {
            // 通过调整force布局参数来控制图表大小
            this.chart.setOption({
              series: [{
                force: {
                  repulsion: 600, // 减小斥力，让节点更紧凑
                  gravity: 0.2,   // 增加重力，让节点更集中
                  edgeLength: [50, 100], // 减小边长度
                  layoutAnimation: false // 关闭动画以便立即生效
                }
              }]
            });
            
            // 重新开启动画
            setTimeout(() => {
              if (this.chart) {
                this.chart.setOption({
                  series: [{
                    force: {
                      layoutAnimation: true
                    }
                  }]
                });
              }
            }, 100);
          }
        }, 1500); // 延迟1.5秒执行，确保图表完全渲染
        
        console.log('ECharts初始化成功');
      } catch (error) {
        console.error('ECharts初始化失败:', error);
        throw error;
      }
    },
    
    renderFallbackChart() {
      try {
        // 检查DOM元素是否存在
        if (!this.$refs.chartContainer) {
          console.error('Chart container not found for fallback chart');
          return;
        }
        
        // 如果ECharts加载失败，显示简单的统计信息
        const container = this.$refs.chartContainer
        container.innerHTML = `
          <div style="padding: 20px; text-align: center; color: #666;">
            <h3>关系网络拓扑图</h3>
            <p>ECharts加载失败，显示简化版本</p>
            <div style="margin: 20px 0;">
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>Twitter用户:</strong> ${this.twitterUser.length}
              </div>
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>个人关系:</strong> ${this.personalRelations.length}
              </div>
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>组织关系:</strong> ${this.organizationalRelations.length}
              </div>
            </div>
            <p>请点击下方标签页查看详细数据</p>
          </div>
        `
        console.log('备用图表渲染成功');
      } catch (error) {
        console.error('备用图表渲染失败:', error);
      }
    },
    
    getChartData() {
      // 强制用局部变量，防止污染
      let nodes = [];
      let links = [];

      // 中心节点
      console.log('中心人物数据:', this.centerPerson);
      nodes.push({
        id: 'center',
        name: this.centerPerson?.name || '未知用户',
        category: 'center',
        symbolSize: 100,
        data: this.centerPerson,
        itemStyle: {
          color: '#ff6b6b',
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold',
          position: 'inside',
          color: '#FFFFFF',
          lineHeight: 20,
          align: 'center',
          formatter: function(params) {
            console.log('中心节点 formatter 被调用:', params);
            return params.data.name;
          }
        }
      })


      // Twitter用户分组节点
      const twitterUserArr = Array.isArray(this.twitterUser)
        ? this.twitterUser
        : (this.twitterUser ? Object.values(this.twitterUser) : []);
      if (twitterUserArr.length > 0) {
        nodes.push({
          id: 'twitter-user-group',
          name: 'Twitter',
          displayName: `Twitter\n${twitterUserArr.length}`,
          count: twitterUserArr.length,
          category: 'twitter-user-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#1da1f2' }
        });
        links.push({
          source: 'center',
          target: 'twitter-user-group',
          name: 'Twitter用户',
          lineStyle: { color: '#1da1f2', width: 2 }
        });
        
        for (const [index, user] of twitterUserArr.entries()) {
          const nodeId = `twitter-user-${user.id || index}`;
          nodes.push({
            id: nodeId,
            name: user.name,
            category: 'twitter-user',
            symbolSize: 35,
            data: user,
            itemStyle: { color: '#1da1f2' }
          });
          links.push({
            source: 'twitter-user-group',
            target: nodeId,
            name: 'Twitter用户',
            lineStyle: { color: '#1da1f2', width: 2 }
          });
          
          // 为每个Twitter用户创建粉丝节点（如果有followers数据）
          if (user.followers && Array.isArray(user.followers) && user.followers.length > 0) {
            const followersNodeId = `twitter-followers-${user.id || index}`;
            nodes.push({
              id: followersNodeId,
              name: '粉丝',
              displayName: `粉丝\n${user.followers.length}`,
              count: user.followers.length,
              category: 'twitter-followers',
              symbolSize: 24,
              data: { type: 'followers', parentUser: user },
              itemStyle: { color: '#ff6b9d' }
            });
            links.push({
              source: nodeId,
              target: followersNodeId,
              name: '粉丝',
              lineStyle: { color: '#ff6b9d', width: 2 }
            });
            
            // 连接粉丝数据节点
            user.followers.forEach((follower, followerIndex) => {
              const followerData = follower.columnValues?.d || follower;
              const followerNodeId = `twitter-follower-${user.id || index}-${followerIndex}`;
              nodes.push({
                id: followerData.user_id || followerNodeId,
                name: (followerData.nickname && followerData.nickname[0]) || followerData.user_id || `粉丝${followerIndex + 1}`,
                category: 'twitter-follower',
                symbolSize: 20,
                data: followerData,
                itemStyle: { color: '#ff6b9d' }
              });
              links.push({
                source: followersNodeId,
                target: followerData.user_id || followerNodeId,
                name: '粉丝',
                lineStyle: { color: '#ff6b9d', width: 2 }
              });
            });
          }
          
          // 为每个Twitter用户创建关注节点（如果有following数据）
          if (user.following && Array.isArray(user.following) && user.following.length > 0) {
            const followingNodeId = `twitter-following-${user.id || index}`;
            nodes.push({
              id: followingNodeId,
              name: '关注',
              displayName: `关注\n${user.following.length}`,
              count: user.following.length,
              category: 'twitter-following',
              symbolSize: 24,
              data: { type: 'following', parentUser: user },
              itemStyle: { color: '#4ecdc4' }
            });
            links.push({
              source: nodeId,
              target: followingNodeId,
              name: '关注',
              lineStyle: { color: '#4ecdc4', width: 2 }
            });
            
            // 连接关注数据节点
            user.following.forEach((following, followingIndex) => {
              const followingData = following.columnValues?.d || following;
              const followingDataNodeId = `twitter-following-data-${user.id || index}-${followingIndex}`;
              nodes.push({
                id: followingData.user_id || followingDataNodeId,
                name: (followingData.nickname && followingData.nickname[0]) || followingData.user_id || `关注${followingIndex + 1}`,
                category: 'twitter-following-data',
                symbolSize: 20,
                data: followingData,
                itemStyle: { color: '#4ecdc4' }
              });
              links.push({
                source: followingNodeId,
                target: followingData.user_id || followingDataNodeId,
                name: '关注',
                lineStyle: { color: '#4ecdc4', width: 1 }
              });
            });
          }
        }
      }

      // Telegram分组节点
      const telegramArr = Array.isArray(this.telegramUser)
        ? this.telegramUser
        : (this.telegramUser ? Object.values(this.telegramUser) : []);
      if (telegramArr.length > 0) {
        nodes.push({
          id: 'telegram-group',
          name: 'Telegram',
          displayName: `Telegram\n${telegramArr.length}`,
          count: telegramArr.length,
          category: 'telegram-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#0088cc' }
        });
        links.push({
          source: 'center',
          target: 'telegram-group',
          name: 'Telegram',
          lineStyle: { color: '#0088cc', width: 2 }
        });
        for (const [index, user] of telegramArr.entries()) {
          const nodeId = `telegram-user-${user.id || index}`;
          nodes.push({
            id: nodeId,
            name: user.name,
            category: 'telegram-user',
            symbolSize: 35,
            data: user,
            itemStyle: { color: '#0088cc' }
          });
          links.push({
            source: 'telegram-group',
            target: nodeId,
            name: 'Telegram用户',
            lineStyle: { color: '#0088cc', width: 2 }
          });
        }
      }

      // 组织关系分组节点
      const orgArr = Array.isArray(this.organizationalRelations)
        ? this.organizationalRelations
        : (this.organizationalRelations ? Object.values(this.organizationalRelations) : []);
      if (orgArr.length > 0) {
        nodes.push({
          id: 'org-group',
          name: '组织关系',
          displayName: `组织关系\n${orgArr.length}`,
          count: orgArr.length,
          category: 'org-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#45b7d1' }
        });
        links.push({
          source: 'center',
          target: 'org-group',
          name: '组织关系',
          lineStyle: { color: '#45b7d1', width: 2 }
        });
        for (const [index, org] of orgArr.entries()) {
          const nodeId = `org-${index}`;
          nodes.push({
            id: nodeId,
            name: org.name,
            category: 'organization',
            symbolSize: 35,
            data: org,
            itemStyle: { color: '#45b7d1' }
          });
          links.push({
            source: 'org-group',
            target: nodeId,
            name: org.relationship,
            lineStyle: { color: '#45b7d1', width: 2 }
          });
        }
      }

      // 个人关系分组节点
      const personalArr = Array.isArray(this.personalRelations)
        ? this.personalRelations
        : (this.personalRelations ? Object.values(this.personalRelations) : []);
      if (personalArr.length > 0) {
        nodes.push({
          id: 'personal-group',
          name: '个人关系',
          displayName: `个人关系\n${personalArr.length}`,
          count: personalArr.length,
          category: 'personal-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#4ecdc4' }
        });
        links.push({
          source: 'center',
          target: 'personal-group',
          name: '个人关系',
          lineStyle: { color: '#4ecdc4', width: 2 }
        });
        for (const [index, person] of personalArr.entries()) {
          const nodeId = `personal-${index}`;
          nodes.push({
            id: nodeId,
            name: person.name,
            category: 'personal',
            symbolSize: 35,
            data: person,
            itemStyle: { color: '#4ecdc4' }
          });
          links.push({
            source: 'personal-group',
            target: nodeId,
            name: person.relationship || '个人关系',
            lineStyle: { color: '#4ecdc4', width: 2 }
          });
        }
      }

      // 邮箱分组节点 - 分为收信邮箱和发信邮箱两个子节点
      const recipientEmailArr = Array.isArray(this.recipientEmail)
        ? this.recipientEmail
        : (this.recipientEmail ? Object.values(this.recipientEmail) : []);
      const senderEmailArr = Array.isArray(this.senderEmail)
        ? this.senderEmail
        : (this.senderEmail ? Object.values(this.senderEmail) : []);
      if (recipientEmailArr.length > 0 || senderEmailArr.length > 0) {
        // 邮箱主分组节点
        nodes.push({
          id: 'email-group',
          name: '邮箱',
          category: 'email-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#7e57c2' }
        });
        links.push({
          source: 'center',
          target: 'email-group',
          name: '邮箱',
          lineStyle: { color: '#7e57c2', width: 2 }
        });
        
        // 收信邮箱子节点
        if (recipientEmailArr.length > 0) {
          nodes.push({
            id: 'email-recipient-group',
            name: '收信箱',
            displayName: `收信箱\n${recipientEmailArr.length}`,
            count: recipientEmailArr.length,
            category: 'email-recipient-group',
            symbolSize: 35,
            data: {},
            itemStyle: { color: '#9c27b0' }
          });
          links.push({
            source: 'email-group',
            target: 'email-recipient-group',
            name: '收信箱',
            lineStyle: { color: '#9c27b0', width: 2 }
          });
          
          // 收信邮箱地址节点
          for (const [index, email] of recipientEmailArr.entries()) {
            const nodeId = `recipient-email-${index}`;
            nodes.push({
              id: nodeId,
              name: email,
              category: 'email-recipient',
              symbolSize: 20,
              data: { email, type: 'recipient' },
              itemStyle: { color: '#9c27b0' }
            });
            links.push({
              source: 'email-recipient-group',
              target: nodeId,
              name: '收信地址',
              lineStyle: { color: '#9c27b0', width: 2 }
            });
          }
        }
        
        // 发信邮箱子节点
        if (senderEmailArr.length > 0) {
          nodes.push({
            id: 'email-sender-group',
            name: '发信箱',
            displayName: `发信箱\n${senderEmailArr.length}`,
            count: senderEmailArr.length,
            category: 'email-sender-group',
            symbolSize: 35,
            data: {},
            itemStyle: { color: '#673ab7' }
          });
          links.push({
            source: 'email-group',
            target: 'email-sender-group',
            name: '发信箱',
            lineStyle: { color: '#673ab7', width: 2 }
          });
          
          // 发信邮箱地址节点
          for (const [index, email] of senderEmailArr.entries()) {
            const nodeId = `sender-email-${index}`;
            nodes.push({
              id: nodeId,
              name: email,
              category: 'email-sender',
              symbolSize: 20,
              data: { email, type: 'sender' },
              itemStyle: { color: '#673ab7' }
            });
            links.push({
              source: 'email-sender-group',
              target: nodeId,
              name: '发信地址',
              lineStyle: { color: '#673ab7', width: 2 }
            });
          }
        }
      }

      // LinkedIn用户分组节点
      const linkedinUserArr = Array.isArray(this.linkedinUser) ? this.linkedinUser : (this.linkedinUser ? Object.values(this.linkedinUser) : []);
      if (linkedinUserArr.length > 0) {
        nodes.push({
          id: 'linkedin-user-group',
          name: 'LinkedIn',
          displayName: `LinkedIn\n${linkedinUserArr.length}`,
          count: linkedinUserArr.length,
          category: 'linkedin-user-group',
          symbolSize: 60,
          data: {},
          itemStyle: { color: '#0077b5' }
        });
        links.push({ 
          source: 'center', 
          target: 'linkedin-user-group', 
          name: 'LinkedIn用户', 
          lineStyle: { color: '#0077b5', width: 2 } 
        });
        for (const [index, user] of linkedinUserArr.entries()) {
          const nodeId = `linkedin-user-${user.id || index}`;
          nodes.push({ 
            id: nodeId, 
            name: user.name, 
            category: 'linkedin-user', 
            symbolSize: 35, 
            data: user, 
            itemStyle: { color: '#0077b5' } 
          });
          links.push({ 
            source: 'linkedin-user-group', 
            target: nodeId, 
            name: 'LinkedIn用户', 
            lineStyle: { color: '#0077b5', width: 2 } 
          });
        }
      }

      // Facebook用户分组节点
      const facebookUserArr = Array.isArray(this.facebookUser) ? this.facebookUser : (this.facebookUser ? Object.values(this.facebookUser) : []);
      // 在getChartData方法中添加Facebook数据处理
      // Facebook用户分组节点
      if (this.facebookUser.length > 0) {
        nodes.push({
          id: 'facebook-group',
          name: 'Facebook',
          displayName: `Facebook\n${this.facebookUser.length}`,
          count: this.facebookUser.length,
          category: 'facebook-group',
          symbolSize: 60,
          itemStyle: {
            color: '#1877F2'
          },
          label: {
            show: true,
            fontSize: 10,
            fontWeight: 'normal'
          }
        });

        links.push({
          source: 'center',
          target: 'facebook-group',
          name: 'Facebook关系',
          lineStyle: {
            color: '#1877F2',
            width: 2
          }
        });

        // Facebook用户节点
        this.facebookUser.forEach((user, index) => {
          const userId = `facebook-user-${user.id || index}`;
          
          nodes.push({
            id: userId,
            name: user.name,
            category: 'facebook-user',
            symbolSize: 35,
            data: user,
            itemStyle: {
              color: '#1877F2'
            }
          });

          links.push({
            source: 'facebook-group',
            target: userId,
            name: 'Facebook用户',
            lineStyle: {
              color: '#1877F2',
              width: 1
            }
          });

          // Facebook朋友节点（Facebook特有）
          if (user.friendsCount > 0) {
            const friendsId = `${userId}-friends`;
            nodes.push({
              id: friendsId,
              name: `朋友 (${user.friendsCount})`,
              category: 'facebook-friends',
              symbolSize: Math.max(15, Math.min(30, user.friendsCount / 10)),
              itemStyle: {
                color: '#42B883'
              }
            });

            links.push({
              source: userId,
              target: friendsId,
              name: '朋友关系',
              lineStyle: {
                color: '#42B883',
                width: 1
              }
            });
          }

          // Facebook关注者节点
          if (user.followersCount > 0) {
            const followersId = `${userId}-followers`;
            nodes.push({
              id: followersId,
              name: `关注者 (${user.followersCount})`,
              category: 'facebook-followers',
              symbolSize: Math.max(15, Math.min(30, user.followersCount / 100)),
              itemStyle: {
                color: '#E1306C'
              }
            });

            links.push({
              source: userId,
              target: followersId,
              name: '关注者',
              lineStyle: {
                color: '#E1306C',
                width: 1
              }
            });
          }
        });
      }
      // 应用样式到节点
      const styledNodes = nodes.map(node => {
        // 跳过中心节点，保持其原有样式
        if (node.id === 'center') {
          return node;
        }

        const nodeStyle = this.getNodeStyle(node.name);
        return {
          ...node,
          ...nodeStyle
        };
      });
      return { nodes: styledNodes, links }
    },
    
    resetLayout() {
      if (this.chart) {
        this.chart.setOption({
          series: [{
            force: {
              repulsion: 1000,
              gravity: 0.1,
              edgeLength: [100, 200],
              layoutAnimation: true
            }
          }]
        })
      }
    },
    
    togglePhysics() {
      this.physicsEnabled = !this.physicsEnabled
      if (this.chart) {
        this.chart.setOption({
          series: [{
            force: {
              layoutAnimation: this.physicsEnabled
            }
          }]
        })
      }
    },
    
    getNodeIconClass(category) {
      const iconMap = {
        'center': 'fas fa-user-circle',
        'personal': 'fas fa-user',
        'organization': 'fas fa-building',
        'twitter-group': 'fab fa-twitter',
        'twitter-user': 'fab fa-twitter',
        'twitter-followers': 'fas fa-users',
        'twitter-following': 'fas fa-user-plus',
        'telegram-group': 'fab fa-telegram-plane',
        'telegram-user': 'fab fa-telegram-plane',
        'linkedin-group': 'fab fa-linkedin',
        'linkedin-user': 'fab fa-linkedin',
        'facebook-group': 'fab fa-facebook-f',
        'facebook-user': 'fab fa-facebook-f',
        'facebook-friends': 'fas fa-user-friends',
        'facebook-followers': 'fas fa-users',
        'email-recipient': 'fas fa-envelope',
        'email-sender': 'fas fa-paper-plane',
        'twitter-user-group': 'fab fa-twitter',
        'email-group': 'fas fa-envelope',
        'email-recipient-group': 'fas fa-inbox',
        'email-sender-group': 'fas fa-paper-plane',
        'linkedin-user-group': 'fab fa-linkedin',
        'facebook-user-group': 'fab fa-facebook-f'
      };
      return iconMap[category] || 'fas fa-circle';
    },
    
    getNodeTypeLabel(category) {
      const labelMap = {
        'center': '中心人物',
        'personal': '个人关系',
        'organization': '组织关系',
        'twitter-group': 'Twitter用户组',
        'twitter-user': 'Twitter用户',
        'twitter-followers': 'Twitter粉丝',
        'twitter-following': 'Twitter关注',
        'telegram-group': 'Telegram用户组',
        'telegram-user': 'Telegram用户',
        'linkedin-group': 'LinkedIn用户组',
        'linkedin-user': 'LinkedIn用户',
        'facebook-group': 'Facebook用户组',
        'facebook-user': 'Facebook用户',
        'facebook-friends': 'Facebook朋友',
        'facebook-followers': 'Facebook关注者',
        'email-recipient': '邮件收件人',
        'email-sender': '邮件发件人',
        'twitter-user-group': 'Twitter用户',
        'email-group': '邮箱',
        'email-recipient-group': '收信邮箱',
        'email-sender-group': '发信邮箱',
        'linkedin-user-group': 'LinkedIn用户',
        'facebook-user-group': 'Facebook用户'
      };
      return labelMap[category] || '未知类型';
    },

    updateChart() {
      if (this.chart) {
        const chartData = this.getChartData();
        this.chart.setOption({
          series: [{
            data: chartData.nodes,
            links: chartData.links
          }]
        });
      }
    },

    startProgressAnimation() {
      const duration = 2500; // 2.5秒，与延迟加载时间匹配
      const interval = 50; // 每50ms更新一次
      const steps = duration / interval;
      const increment = 100 / steps;
      
      const loadingSteps = [
        { progress: 20, text: '初始化数据...' },
        { progress: 40, text: '加载Twitter数据...' },
        { progress: 60, text: '加载Telegram数据...' },
        { progress: 80, text: '准备图表组件...' },
        { progress: 100, text: '完成加载...' }
      ];
      
      const timer = setInterval(() => {
        this.loadingProgress += increment;
        
        // 更新加载步骤
        const currentStep = loadingSteps.find(step => this.loadingProgress <= step.progress);
        if (currentStep) {
          this.currentLoadingStep = currentStep.text;
        }
        
        if (this.loadingProgress >= 100) {
          this.loadingProgress = 100;
          this.currentLoadingStep = '完成加载...';
          clearInterval(timer);
        }
      }, interval);
    },
    
    retryLoading() {
      this.loading = true;
      this.loadingError = false;
      this.loadingProgress = 0;
      this.currentLoadingStep = '重新加载...';
      
      // 重新启动进度条动画
      this.startProgressAnimation();
      
      // 使用新的重试机制
      setTimeout(() => {
        this.tryInitECharts();
      }, 2000);
    },
  }
}
</script>

<style scoped>
.relationship-topology {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  overflow-y: auto;
}

/* 卡片样式 */
.header-card,
.tabs-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.card-header {
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.card-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  font-size: 24px;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  padding: 10px;
}

/* 拓扑图区域 */
.topology-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.chart-container {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  min-height: 0;
}

.echarts-container {
  width: 100%;
  height: 100%;
}

/* 控制面板 */
.control-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-section h4,
.layout-controls h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4a5568;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.layout-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  padding: 8px 16px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background: #3182ce;
}

/* 信息面板 */
.info-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.stats-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-content {
  padding: 20px;
}

.node-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-badge {
  display: inline-block;
  padding: 4px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 16px;
}

.node-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.detail-item strong {
  color: #2d3748;
}

.empty-hint {
  text-align: center;
  color: #718096;
  margin: 0 0 8px 0;
  font-size: 16px;
}

.empty-hint-sub {
  text-align: center;
  color: #a0aec0;
  margin: 0;
  font-size: 14px;
}

/* 统计项 */
.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f7fafc;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}



.stat-badge.personal {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.stat-badge.organizational {
  background: linear-gradient(135deg, #45b7d1, #3498db);
}

.stat-badge.telegram {
  background: linear-gradient(135deg, #0088cc, #4fc3f7);
}

.stat-badge.twitter-user {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.stat-badge.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-badge.email-recipient {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.stat-badge.email-sender {
  background: linear-gradient(135deg, #673ab7, #9575cd);
}

.stat-badge.linkedin-user {
  background: linear-gradient(135deg, #0077b5, #00a0dc);
}

.stat-badge.facebook-user {
  background: linear-gradient(135deg, #1877f2, #42a5f5);
}

/* 标签页 */
.tabs-header {
  border-bottom: 1px solid #e2e8f0;
}

.tab-list {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  overflow: hidden;
}

.tab-button {
  flex: 1 1 0;
  min-width: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #718096;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #4a5568;
  background: #f7fafc;
}

.tab-button.active {
  color: #4299e1;
  border-bottom-color: #4299e1;
  background: #ebf8ff;
}

.tab-content {
  padding: 32px;
  max-height: 60vh;
  overflow: auto;
}

/* 概览内容 */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}



.overview-card.personal {
  border-left: 4px solid #4ecdc4;
}

.overview-card.organizational {
  border-left: 4px solid #45b7d1;
}

.overview-card.telegram {
  border-left: 4px solid #0088cc;
}

.overview-card.email-recipient {
  border-left: 4px solid #9c27b0;
}

.overview-card.email-sender {
  border-left: 4px solid #673ab7;
}

.overview-card.twitter-user {
  border-left: 4px solid #1da1f2;
}

.overview-card.linkedin-user {
  border-left: 4px solid #0077b5;
}

.overview-card.facebook-user {
  border-left: 4px solid #1877f2;
}

.overview-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.overview-number {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.overview-label {
  font-size: 14px;
  color: #718096;
  margin: 0;
  font-weight: 500;
}

/* 网络统计 */
.network-stats h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 关系网格 */
.relations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.relation-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.relation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}



.personal-card {
  border-left: 4px solid #4ecdc4;
}

.org-card {
  border-left: 4px solid #45b7d1;
}

.relation-header {
  padding: 20px 20px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.relation-avatar {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
  border-radius: 50%;
}

.relation-info {
  flex: 1;
}

.relation-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.relation-nickname,
.relation-type {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.relation-stats {
  padding: 0 20px 16px;
  display: flex;
  gap: 16px;
}

.stat-item-small {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label-small {
  font-size: 12px;
  color: #a0aec0;
}

.stat-value-small {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
}

.relation-summary {
  padding: 0 20px 20px;
  font-size: 14px;
  color: #718096;
  line-height: 1.5;
}

.relation-details {
  padding: 0 20px 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f7fafc;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #a0aec0;
}

.detail-value {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

/* 关系分组 */
.relations-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.relation-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.group-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .control-panel {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .relationship-topology {
    padding: 16px;
  }
  
  .tab-list {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .relations-grid {
    grid-template-columns: 1fr;
  }
  
  .echarts-container {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-list {
    grid-template-columns: 1fr;
  }
}

.telegram-card {
  border-left: 4px solid #0088cc;
}

.twitter-user-card {
  border-left: 4px solid #1da1f2;
}

.linkedin-user-card {
  border-left: 4px solid #0077b5;
}

.facebook-user {
  border-left: 4px solid #1877F2;
}

.facebook-user .user-avatar img {
  border: 2px solid #1877F2;
}

.facebook-user .stat-value {
  color: #1877F2;
  font-weight: bold;
}

.facebook-friends {
  color: #42B883;
}

.facebook-followers {
  color: #E1306C;
}

.verified-badge {
  color: #1877F2;
  font-size: 12px;
}

.verified-badge i {
  margin-right: 4px;
}

.facebook-user-card {
  border-left: 4px solid #1877f2;
}

/* 新增邮箱标签页样式 */
.email-recipient-card {
  border-left: 4px solid #9c27b0;
}

.email-sender-card {
  border-left: 4px solid #673ab7;
}

/* 遮罩层样式 - 只在图表容器内显示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(4px);
  border-radius: 8px;
}

.loading-content {
  background: white;
  padding: 32px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  min-width: 200px;
}

.loading-spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #4299e1;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #4a5568;
  font-weight: 500;
}

.loading-progress {
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  width: 200px;
  height: 20px;
  background-color: #e2e8f0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #667eea);
  transition: width 0.1s ease-out;
  border-radius: 10px;
}

.progress-text {
  margin-left: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  min-width: 40px;
  text-align: left;
}

.loading-step {
  margin-top: 12px;
  font-size: 13px;
  color: #718096;
  font-style: italic;
}

.retry-button {
  padding: 8px 16px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #3182ce;
}

/* 单条数据时卡片宽度1/3 */
.relations-grid.single-item {
  grid-template-columns: 33.33% !important;
  justify-content: flex-start;
}
.relations-grid.single-item > .relation-card {
  /* max-width: 33.33%; */
  flex: 0 0 33.33%;
}
</style>