export default {
  namespaced: true,
  state: {
    showArr: [],
    statisticsData: null,
    listDataa: [],
    logs: [],
    resolveoof: false,
    qualifierid: "",
    logdialogVisible: false,
    pramasId: 0,
    publicTemplate: {},
    UserTemplate: {},
  },
  mutations: {
    sendPublicTemplate(state, v) {
      window.main.$main_socket.sendData(
        "Api.Overview.PublicTemplate",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getPublicTemplate"
      );
    },
    getPublicTemplate(state, data) {
      state.publicTemplate = data;
    },
    sendUserTemplate(state, v) {
      window.main.$main_socket.sendData(
        "Api.Overview.UserTemplate",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getUserTemplate"
      );
    },
    getUserTemplate(state, data) {
      state.UserTemplate = data;
    },
    setShowArr(state, v) {
      state.showArr = v;
    },
    setResolveoof(state, v) {
      state.resolveoof = v;
    },
    setlogdialogVisible(state, v) {
      state.logdialogVisible = v;
    },
    getListData(state, data) {
      if (data) {
        if (data.length == 0) {
          window.main.$store.commit("telegramSearch/telegramSearchStatistics/setResolveoof", true);
          window.main.$message.success("没有数据了");
          return;
        }
        state.listDataa = [];
        let resdataList = data[0].columnValues.data_list;

        for (let k in resdataList) {
          state.qualifierid = k;
          if (resdataList[k]._source) {
            resdataList[k]._source["_id"] = resdataList[k]._id;
            state.listDataa.push(resdataList[k]._source);
          } else {
            state.listDataa.push(resdataList[k]);
          }
        }
      }

      window.main.$store.commit("telegramSearch/telegramSearchStatistics/setResolveoof", true);
    },
    getLogsData(state, data) {
      state.logs = [];
      let logs = data[0].columnValues.logs;

      for (let k in logs) {
        let obj = new Object();
        obj["info"] = logs[k];
        obj["id"] = k;
        state.logs.push(obj);
      }

      window.main.$store.commit("telegramSearch/telegramSearchStatistics/setlogdialogVisible", true);
    },
    setSendLogsData(state, data) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [window.main.$route.params.id],
            },
            msg: {
              task_authority: "username",
              familys: ["logs"],
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getLogsData"
      );
    },
    setSendListData(state, data) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [window.main.$route.params.id],
              qualifier: "",
              size: 0,
            },
            msg: {
              task_authority: "username",
              familys: ["data_list"],
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getListData"
      );
    },
    setSendListDataMore(state, data) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [window.main.$route.params.id],
              qualifier: state.qualifierid,
              size: 0,
            },
            msg: {
              task_authority: "username",
              familys: ["data_list"],
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getListData"
      );
    },
    getType(state, data) {
      if (!data[0].columnValues.chart) {
        window.main.$message.error("暂时无法查看，请重新分析");
        return;
      }
      let arr = Object.keys(data[0].columnValues.chart);
      let obj = null;
      var a = arr.indexOf("collision_key_parm");
      if (a > 0) {
        data[0].columnValues.chart = Object.assign(
          { collision_key_parm: null },
          data[0].columnValues.chart
        );
      }
      var b = arr.indexOf("search_task_overview");
      if (b > 0) {
        data[0].columnValues.chart = Object.assign(
          { search_task_overview: null },
          data[0].columnValues.chart
        );
      }
      state.statisticsData = data;
      window.main.$router.push(`/analysis/searchTasklist`);
    },
    senddataType(state, v) {
      state.pramasId = v.row;
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [v.row],
            },
            msg: {
              task_authority: "username",
              familys: ["parm", "chart", "info"],
              task_type: v.columnValues.info.task_type,
            },
          },
        ],
        "telegramSearch/telegramSearchStatistics/getType"
      );
    },
  },
};
