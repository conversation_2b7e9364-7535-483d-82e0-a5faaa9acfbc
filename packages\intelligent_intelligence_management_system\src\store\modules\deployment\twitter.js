export default {
  namespaced: true,
  state: {
    twitterLastRowkey: [],
    twitterList: [],
    loading: true,
    userId: "",
    row: "",
    twitterRow: "",
    twitterDetail: null,
  },
  mutations: {
    setUserId(state, v) {
      state.userId = v;
    },
    setRow(state, v) {
      state.row = v;
    },
    //得到从插件获取的推特目标列表
    setTwitterList(state, res) {
      let newRes = [];
      console.log("sendTwitterList", res);
      if (res?.length > 0) {
        for (let i = 0; i < res.length; i++) {
          for (let str in res[i].columnValues.d) {
            let newStr;
            if (str.indexOf(";") != -1) {
              let ind = str.indexOf(";");
              newStr = str.slice(0, ind);
            }

            Object.assign(res[i].columnValues.d, {
              [newStr]: res[i].columnValues.d[str][newStr],
            });
            if (typeof res[i].columnValues.d[str] == "string") {
              res[i].columnValues.d[str] = res[i].columnValues.d[str].replace(
                /"|\\/g,
                ""
              );
            }
          }
        }
        state.twitterList = state.twitterList.concat(res);
        state.twitterLastRowkey = [
          state.twitterList[state.twitterList.length - 1].row,
        ];
        window.main.$store.dispatch("deployment/twitter/getTwitterList");
        state.loading = false;
      } else {
        state.loading = false;
        window.main.$message.warning("已加载全部!");
      }
    },
    //得到详情
    setTwitterDetail(state, res) {
      for (let str in res[0].columnValues.d) {
        let newStr;
        if (str.indexOf(";") != -1) {
          let ind = str.indexOf(";");
          newStr = str.slice(0, ind);
        }

        Object.assign(res[0].columnValues.d, {
          [newStr]: res[0].columnValues.d[str][newStr],
        });
      }
      state.row = res[0].row;
      state.userId = res[0].columnValues.d.user_id;
      state.twitterDetail = res[0].columnValues.d;
      window.main.$router.push({
        path: `/deployment/TwitterDetail/${state.row}`,
      });

      //   //搜索目标人相关回复开始
      /*  window.main.$store.commit('search/searchReplyList/setusePublic', 'public')//为啦查看数据先暂时为public
      window.main.$store.commit('search/dataRangeReply/setMaxInit')
      window.main.$store.commit('search/selectRangeReply/getSearchType', '/social_platform/twitter/informationID')
      window.main.$store.commit('search/selectRangeReply/setIDString', '')
      window.main.$store.commit('search/searchReplyList/setQueryString', '')
      window.main.$store.commit('search/dataRangeReply/setChartOn', false)
      window.main.$store.commit('search/dataRangeReply/clearDataRangeTree')//清除之前的查询数据范围
      window.main.$store.commit('search/searchReplyList/clearSearchList')//清除之前的查询结果数据
      window.main.$store.commit('search/selectRangeReply/setIDString', state.userId)//'Butch_Gen'代替state.userId
      window.main.$store.commit('search/dataRangeReply/setDataRangeGetter', [{ data_range_index_prefix: "social_platform_timeline_prefix_twitter", data_range_path: '/social_platform/twitter/timeline', data_range_type: true, name: "user_id" }]) */
      // //搜索目标人相关回复结束
    },
  },
  actions: {
    //获取从插件获取的推特目标列表
    getTwitterList({ state, commit }, v) {
      state.loading = true;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              row_key: state.twitterLastRowkey,
              size: 1,
            },
            msg: {
              type: "username",
              table: "monitor_task",
              prefix: "",
              relation: "twitter",
            },
          },
        ],
        "deployment/twitter/setTwitterList"
      );
    },
    //获取详情
    getTwitterDetail({ state, commit }, v) {
      if (window.main.$route.params.id) {
        state.twitterRow = window.main.$route.params.id;
        window.main.$store.commit(
          "search/twLinFacSearch/setAddEsQueryConditions",
          {
            bool: {
              must: [
                {
                  term: {
                    type: "twitter",
                  },
                },
                {
                  term: {
                    relation: "reply",
                  },
                },
                {
                  term: {
                    user_id:
                      state.twitterRow.split(";")[
                        state.twitterRow.split(";").length - 1
                      ],
                  },
                },
              ],
            },
          }
        );
      } else {
        state.twitterRow = v;
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DetailMulti",
        [
          {
            head: {
              row_key: [state.twitterRow],
            },
            msg: {
              type: "username",
              table: "monitor_task",
              relation: "twitter",
              prefix: "",
            },
          },
        ],
        "deployment/twitter/setTwitterDetail"
      );
    },
  },
  modules: {},
};
