<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #fefce8;
          line-height: 42px;
          font-weight: bold;
          color: #854d0e;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-s-flag"></i
          ><span style="margin-left: 10px">国家</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div>
        <div
          ref="data_overview_diagram"
          id="data_overview_diagram"
          style="height: 400px"
        ></div>
        <div
          v-if="myDataObj"
          style="padding: 10px; color: #7d7878; background-color: #eee"
        >
          {{ myDataObj }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import html2canvas from "html2canvas";
import nameMap from "@/plugins/name";

export default {
  data() {
    return {
      myDataObj: null,
    };
  },
  mounted() {},
  computed: {
    ...mapState({
      dataObj: (state) => state.aiTaskQueue.taskDetail.public_opinion,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "country_map_data") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
          this.dataDiagram(newVal.country_map_data);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 动态地球
    dataDiagram(v) {
      // 转换函数
      function convertToEChartsFormat(dataObj) {
        return Object.keys(dataObj).map((key) => ({
          value: dataObj[key],
          name: key,
        }));
      }

      // 转换并输出
      const geoCoordMap = convertToEChartsFormat(v);
      /* let statisticsDatas = JSON.parse(
        sessionStorage.getItem("statisticsData")
      );
      let geoCoordMap =
        statisticsDatas[0].columnValues.chart.countryNewsList;
      geoCoordMap.forEach((e) => {
        e.name = e.keyWord;
        e.value = e.number;
      }); */
      this.$nextTick(() => {
        const dataEcharts = window.main.$echarts.init(
          document.getElementById("data_overview_diagram")
        );

        window.addEventListener("resize", () => {
          dataEcharts.resize();
        });

        dataEcharts.setOption({
          tooltip: {
            show: true,
            trigger: "item",
          },
          visualMap: [
            {
              //映射-颜色值
              orient: "vertical", //分段方向:horizontal水平
              type: "piecewise", //分段
              show: false,
              splitNumber: 5, //对于连续型数据，自动平均切分成几段。默认为5段
              pieces: [
                //配置颜色区间
                {
                  value: 0,
                  color: "#FFFFFF",
                },
                {
                  min: 1,
                  max: 1000,
                  color: "#FDFDCF",
                },
                {
                  min: 1000,
                  max: 10000,
                  color: "#FE9E83",
                },
                {
                  min: 10000,
                  max: 100000,
                  color: "#E55A4E",
                },
                {
                  min: 100000,
                  color: "#4F070D",
                },
              ],
            },
          ],
          series: [
            {
              label: {
                show: true,
                formatter: function (params) {
                  if (isNaN(params.value)) {
                    //为0时不显示
                    return "";
                  } else {
                    return params.name + "\n" + params.value;
                  }
                },
              },
              name: "世界地图",
              type: "map",
              map: "world",
              roam: true,
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              nameMap: nameMap,
              itemStyle: {
                normal: {
                  borderColor: "#000",
                  areaColor: "#2455ad",
                  borderWidth: 0.2,
                },
                emphasis: {
                  areaColor: "#409EFF",
                  borderWidth: 0,
                  label: {
                    show: false, // 是否显示标签
                    // color:'#000',
                  },
                },
              },
              data: geoCoordMap,
            },
          ],
        });
        this.$bus.$dataEcharts = dataEcharts;
        //鼠标离开隐藏
      });
    },
    saveDivAsImage() {
      const captureElement = this.$refs.data_overview_diagram;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "国家统计.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
