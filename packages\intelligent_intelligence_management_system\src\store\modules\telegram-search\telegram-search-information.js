import parse from "emailjs-mime-parser";
export default {
  namespaced: true,
  state: {
    case_id: "", //案件id
    case_name: "", //案件名称
    usersList: [], //监控的用户列表
    allIdList: "", //全部联系人id
    getAllIdListQueueCount: 3,
    allList: "", //全部联系人列表
    groupMsgList: "", //群消息列表
    groupMemberList: "", //群成员列表
    equipment: "telegram", //设备
    equipmentOptions: [], //设备列表
    selectUserListIndex: -1,
    selectESGroupListIndex: -1,
    selectMemberIndex: -1,
    loadGroupId: "",
    lastMsgRow: "",
    msgLoading: false,
    lastMemberRow: "",
    loadUserId: "",
    lastUserRow: "",
    userLoading: false,
    AppointRelationId: "",
    relationUser: {},
    relationIdList: [],
    lastRelationRow: "",
    groupLoading: false,
    msgIconList: [],
    addRemark: true,
    addTagCount: 0,
    addMsgTagCount: 0,
    delMsgTagCount: 0,
    userTagList: {}, //用户标签列表
    addDelUserId: "",
    delUserTagCount: 0,
    userIcon: {},
    friendIcon: {},
    allGroupRow: {}, //全部群组滚动加载
    groupType: ["channel", "chat", "contact"],
    rollRowId: [],
    memberMsg: 0,
    addDelMsgId: "",
    msgTagList: {},
    friendCountList: {},
    addDelGroupId: "",
    addGroupTagCount: 0,
    addDelGroupId: "",
    groupTagList: {},
    delGroupTagCount: 0,
    //导出功能
    exportAllList: "", //导出全部联系人列表
    exportGroupMsgList: [], //导出消息列表
    exportGroupMemberList: [], //导出群成员列表
    exporpAllGroupMember: [], //导出Hbase群成员
    exporpAllESGroupMember: [], //导出ES群成员
    exportLastMsgRow: "",
    lastExportMemberRow: "",
    exportRelationIdList: [],
    exportLastRelationRow: [],
    exportAllGroupMsg: [],
    exportAllGroupMsgs: [],
    lastExportMsgRow: [],
    userNoReadMsg: {}, //当前用户未读消息
    userIsCreator: {}, //当前用户是否群主
    userIsLeft: {}, //当前用户是否退群
    caseList: new Map(),
    userAllTagList: [],
    groupAllTagList: [],
    msgAllTagList: [],
    oneExportMember: null,
    Shang: null,
    Yu: null,
    GroupYu: 1,
    Input: null,

    // 解决判断请求过于频繁
    userAnalysisState: {}, //目标人分析状态
    groupAnalysiState: {}, //群分析状态
    groupMemberIsHave: {}, //是否拥有群成员
    nowUserTelephone: "",
    groupProgress: {}, //群主的爬取进度缓存表
  },
  mutations: {
    // 当前查看用户的手机号
    setNowUserTelephone(state, v) {
      state.nowUserTelephone = v;
    },
    //导出的是哪个群的群成员
    setChangeExport(state, v) {
      state.oneExportMember = v;
    },
    // 存储案件
    setCaseList(state, v) {
      state.caseList.set(v.row, v.case_name);
    },
    // 改变设备
    changeEquipment(state, v) {
      state.equipment = v;
    },
    // 获取设备列表
    sendEquipmentList(state, v) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              "/etc/web/instant_msg_analysis_platform/is_whatsapp": "",
            },
          },
        ],
        (res) => {
          let isWhatsApp =
            res["/etc/web/instant_msg_analysis_platform/is_whatsapp"]
              .isWhatsApp;
          if (isWhatsApp) {
            state.equipmentOptions = [
              {
                value: "whatsapp",
              },
            ];
            state.equipment = "whatsapp";
          } else {
            window.main.$constant_socket.sendData(
              "Api.Node.NodeData",
              [
                {
                  head: {
                    session_id: window.main.$store.state.userInfo.session_id,
                  },
                  msg: {
                    "/etc/web/instant_msg_analysis_platform/equipment_list": "",
                  },
                },
              ],
              "telegramSearch/telegramSearchInformation/setEquipmentList"
            );
          }
        }
      );
    },
    setEquipmentList(state, res) {
      state.equipmentOptions =
        res[
          "/etc/web/instant_msg_analysis_platform/equipment_list"
        ].equipment_list;
    },
    // 获取任务时的案件id
    setCaseID(state, v) {
      state.case_name = v.columnValues.i.case_dir_name;
      state.case_id = v.row;
    },
    // 设置案件信息
    setSearchCaseID(state, v) {
      state.case_name = v.case_name;
      state.case_id = v.case_id;
    },
    // 清除列表
    clearUserList(state) {
      state.case_id = "";
      state.usersList = [];
      state.allIdList = "";
      state.allList = "";
      state.groupMsgList = "";
      state.msgIconList = "";
      state.groupMemberList = "";
      state.selectUserListIndex = -1;
      state.selectGroupListIndex = "";
      state.selectMemberIndex = -1;
      state.lastUserRow = "";
      state.userTagList = {};
      state.msgTagList = {};
      state.groupTagList = {};
      state.exportGroupMemberList = [];
      state.exportGroupMsgList = [];
    },
    // 返回列表清空
    backUserClear(state) {
      state.usersList = [];
      state.lastUserRow = "";
    },
    // 获取监控用户列表
    sendUsers(state, v) {
      state.userLoading = true;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastUserRow ? [state.lastUserRow] : [],
            },
            msg: {
              type: "case",
              path: "/instant_msg/" + state.equipment + "/user_id",
              case_id: state.case_id,
              relation: "",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setUsers"
      );
    },
    setUsers(state, res) {
      state.userLoading = false;
      if (res?.length < 20) {
        state.usersList.push(...res);
        window.main.eventBus.$emit("equivalenceUserList");
      } else {
        state.usersList.push(...res);
        state.lastUserRow = res[res.length - 1].row;
        this.commit("telegramSearch/telegramSearchInformation/sendUsers");
      }
    },
    // 切换群组清空
    resetGroupinfo(state, res) {
      state.groupMsgList = "";
      state.lastMsgRow = "";
      state.loadGroupId = "";
      state.lastMemberRow = "";
      state.groupMemberList = "";
      state.msgIconList = "";
      state.memberMsg = 0;
      state.msgTagList = {};
      state.exportGroupMsgList = [];
      state.exportLastMsgRow = "";
      state.lastExportMemberRow = "";
      state.exportGroupMemberList = [];
    },
    // 存储群组频道id
    setdallListId(state, data) {
      data?.res?.forEach((item) => {
        const info = item.columnValues.d;
        // 未读消息数 是不是群主 是否退群
        let unread_count = 0;
        let is_creator = "no_have";
        let is_left = "no_have";
        for (const key in info) {
          if (key.startsWith("unread_count")) {
            unread_count = info[key].unread_count;
          }
          if (key.startsWith("is_creator")) {
            is_creator = info[key].is_creator;
          }
          if (key.startsWith("is_left")) {
            is_left = info[key].is_left;
          }
        }
        window.main.$set(
          state.userNoReadMsg,
          item.columnValues.d._._,
          unread_count
        );
        window.main.$set(
          state.userIsCreator,
          item.columnValues.d._._,
          is_creator
        );
        window.main.$set(state.userIsLeft, item.columnValues.d._._, is_left);
      });
      state.getAllIdListQueueCount--;
      data?.res?.forEach((item) => {
        state.rollRowId.push(item.columnValues.d._._);
      });
      if (data?.res?.length < 20) {
        const index = state.groupType.indexOf(data.type);
        state.groupType.splice(index, 1);
        delete state.allGroupRow[data.type];
      } else {
        state.allGroupRow[data?.type] = data?.res[data?.res?.length - 1]?.row;
      }
      if (state.getAllIdListQueueCount <= 0) {
        state.getAllIdListQueueCount = state.groupType.length;
        state.allIdList = state.allIdList.concat(state.rollRowId);
        state.rollRowId = [];
        if (JSON.stringify(state.allGroupRow) == "{}") {
          this.dispatch("telegramSearch/telegramSearchInformation/sendContacts", state.allIdList);
          this.dispatch("telegramSearch/telegramSearchInformation/sendGetGroupProgress", state.allIdList);
        } else {
          this.dispatch("telegramSearch/telegramSearchInformation/sendallListId");
        }
      }
    },
    clearGroupMsg(state, v) {
      state.lastMsgRow = "";
      state.groupMsgList = [];
    },
    setMsg(state, res) {
      let userIdArr = [];
      res?.forEach((item) => {
        for (const key in item.columnValues.d) {
          if (key.startsWith("user_id") && item.columnValues.d[key].user_id) {
            userIdArr.push(item.columnValues.d[key].user_id);
          }
        }
      });
      this.dispatch("telegramSearch/telegramSearchInformation/sendMsgIcon", userIdArr);
      state.msgLoading = false;
      if (!res?.length && state.groupMsgList.length) {
        window.main.$message.warning("没有消息了！");
        return;
      }
      state.lastMsgRow = res[res.length - 1]?.row;
      if (state.groupMsgList) {
        state.groupMsgList.push(...res);
      } else {
        state.groupMsgList = res;
      }
    },
    resetExportMsg(state) {
      state.exportGroupMsgList = [];
      state.lastExportMsgRow = [];
    },
    setExportMsg(state, res) {
      if (res.length < 200) {
        state.exportGroupMsgList = state.exportGroupMsgList.concat(res);
        state.exportAllGroupMsg = state.exportGroupMsgList;
      } else {
        state.exportGroupMsgList = state.exportGroupMsgList.concat(res);
        let lastExportMsgRow = [res[res.length - 1].row];
        let i = state.Input - 200;
        this.dispatch("telegramSearch/telegramSearchInformation/sendExportMsg", {
          input: i,
          value: lastExportMsgRow,
        });
      }
    },

    setMember(state, res) {
      state.msgLoading = false;
      if (res?.length === 0) {
        if (state.memberMsg >= 1) {
          state.memberMsg = 0;
          window.main.$message.warning("没有群成员了！");
        }
        return;
      }
      state.memberMsg++;
      state.lastMemberRow = res[res.length - 1].row;
      if (state.groupMemberList) {
        state.groupMemberList.push(...res);
      } else {
        state.groupMemberList = res;
      }
    },
    setSeachMember(state, res) {
      if (res.length === 0) {
        window.main.$message.warning("查询不到该群成员！");
      } else {
        if(res[0].columnValues?.d){
          state.groupMemberList = res;
        }
      }
    },
    resetExportMember(state) {
      state.exportGroupMemberList = [];
      state.lastExportMemberRow = [];
    },
    setExportMember(state, res) {
      if (res?.length) {
        if (res.length < 20) {
          state.exportGroupMemberList = state.exportGroupMemberList.concat(res);
          if (state.oneExportMember) {
            state.exporpAllGroupMember = state.exportGroupMemberList;
          } else {
            state.exporpAllESGroupMember = state.exportGroupMemberList;
          }
        } else {
          state.exportGroupMemberList = state.exportGroupMemberList.concat(res);
          state.lastExportMemberRow = [res[res.length - 1].row];
          this.dispatch("telegramSearch/telegramSearchInformation/exportMember");
        }
      }
    },
    // 刷新群组列表
    resetGroupList(state, res) {
      state.allIdList = [];
      state.getAllIdListQueueCount = 3;
      state.allList = "";
      state.lastRelationRow = "";
      state.relationIdList = [];
      state.allGroupRow = {};
      state.groupType = ["channel", "chat", "contact"];
      state.allGroupRow = {};
    },
    resetExportAllIdList(state, res) {
      state.exportGroupMsgList = [];
    },
    // 清除id列表
    resetAllIdList(state, res) {
      window.main.eventBus.$emit("clearGroupList");
      state.userNoReadMsg = {};
      state.userIsCreator = {};
      state.userIsLeft = {};
      state.allIdList = [];
      state.getAllIdListQueueCount = 3;
      state.allList = "";
      state.groupMsgList = "";
      state.groupMemberList = "";
      state.msgIconList = "";
      state.lastRelationRow = "";
      state.relationIdList = [];
      state.relationUser = {};
      state.allGroupRow = {};
      state.groupType = ["channel", "chat", "contact"];
      state.groupTagList = {};
      state.groupProgress = {};
    },
    resetUserlist(state, res) {
      state.usersList = [];
      state.lastUserRow = "";
    },
    //存储群组列表
    setContacts(state, res) {
      state.groupLoading = false;
      state.allList = res;
      state.allList?.sort((a, b) => {
        return (
          b?.columnValues?.d?.last_msg_timestamp?.last_msg_timestamp -
          a?.columnValues?.d?.last_msg_timestamp?.last_msg_timestamp
        );
      });
      window.main.eventBus.$emit("equivalenceGroupList");
    },
    setExportContacts(state, res) {
      state.exportAllList = res;
    },
    // 存储指定的联系人关系id
    setAppointId(state, res) {
      res?.forEach((item) => {
        const info = item.columnValues.d;
        // 未读消息数 是不是群主 是否退群
        let unread_count = 0;
        let is_creator = "no_have";
        let is_left = "no_have";
        for (const key in info) {
          if (key.startsWith("unread_count")) {
            unread_count = info[key].unread_count;
          }
          if (key.startsWith("is_creator")) {
            is_creator = info[key].is_creator;
          }
          if (key.startsWith("is_left")) {
            is_left = info[key].is_left;
          }
        }
        window.main.$set(
          state.userNoReadMsg,
          item.columnValues.d._._,
          unread_count
        );
        window.main.$set(
          state.userIsCreator,
          item.columnValues.d._._,
          is_creator
        );
        window.main.$set(state.userIsLeft, item.columnValues.d._._, is_left);
        state.relationIdList.push(item.columnValues.d._._);
      });
      if (res?.length >= 20) {
        state.lastRelationRow = res[res?.length - 1]?.row;
        this.dispatch("telegramSearch/telegramSearchInformation/sendAppointId");
      } else {
        this.dispatch("telegramSearch/telegramSearchInformation/sendContacts", state.relationIdList);
        this.dispatch("telegramSearch/telegramSearchInformation/sendGetGroupProgress", state.relationIdList);
        state.relationIdList = [];
      }
    },
    exportAppointId(state, res) {
      res.forEach((item) => {
        state.exportRelationIdList.push(item.columnValues.d._._);
      });
      if (res.length >= 20) {
        state.exportLastRelationRow = [res[res.length - 1].row];
        this.dispatch("telegramSearch/telegramSearchInformation/sendExportAppointId");
      } else {
        this.dispatch(
          "telegramSearch/telegramSearchInformation/sendExportContacts",
          state.exportRelationIdList
        );
        state.exportRelationIdList = [];
      }
    },
    // 存储获取到的头像
    setMsgIcon(state, res) {
      if (state.msgIconList) {
        state.msgIconList.push(...res);
      } else {
        state.msgIconList = res;
      }
    },
    // 是否修改备注成功
    isAddDataRemark(state, res) {
      if (res.status === "ok") {
        setTimeout(() => {
          this.commit("telegramSearch/telegramSearchInformation/sendUsers");
        }, 1500);
        window.main.$message.success("修改备注成功！");
      } else {
        window.main.$message.error("修改备注失败！");
      }
    },
    // 是否添加目标人标签成功
    isAddDataTag(state, res) {
      if (res?.status === "ok") {
        state.addTagCount += 1;
        if (state.addTagCount === 3) {
          window.main.$message.success("添加标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendUserTag", state.addDelUserId);
          state.addDelUserId = "";
          state.addTagCount = 0;
        }
      } else {
        window.main.$message.error("添加标签失败！");
      }
    },
    // 获取到的目标人标签
    setUserTag(state, res) {
      window.main.$set(state.userTagList, res.userId, res.res);
    },
    // 是否删除目标人标签成功
    isDelUserTag(state, res) {
      if (res?.status === "ok") {
        state.delUserTagCount += 1;
        if (state.delUserTagCount === 2) {
          window.main.$message.success("删除标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendUserTag", state.addDelUserId);
          state.addDelUserId = "";
          state.delUserTagCount = 0;
        }
      } else {
        window.main.$message.error("删除标签失败！");
      }
    },
    // 获取到的目标人标签下的id
    setSeachUserTag(state, res) {
      if (res.length === 0) {
        window.main.$message.warning("此标签下没有目标人！");
      } else {
        this.dispatch("telegramSearch/telegramSearchInformation/sendTagUsers", res);
      }
    },
    //获取到的目标人标签下的用户
    setTagUsers(state, res) {
      state.usersList = res;
      window.main.eventBus.$emit("equivalenceTagUserList");
    },
    //是否删除消息成功
    isDelGroupMsg(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除消息成功！");

        window.main.$store.dispatch("telegramSearch/telegramSearchInformation/sendMsg", false);
      } else {
        window.main.$message.error("删除消息失败！");
      }
    },
    //是否远程消息成功
    isDelTelegrameGroupMsg(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除中，请稍等...");
      } else {
        window.main.$message.error("删除消息失败！");
      }
    },
    // 存储用户和用户好友的头像
    saveUserIcon(state, data) {
      state.userIcon = data;
    },
    saveFriendIcon(state, data) {
      state.friendIcon = data;
    },
    // 是否添加消息标签成功
    isAddMsgTag(state, res) {
      if (res?.status === "ok") {
        state.addMsgTagCount += 1;
        if (state.addMsgTagCount === 3) {
          window.main.$message.success("添加标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendMsgTag", state.addDelMsgId);
          state.addDelMsgId = "";
          state.addMsgTagCount = 0;
        }
      } else {
        window.main.$message.error("添加标签失败！");
      }
    },
    //存储消息标签
    setMsgTag(state, res) {
      window.main.$set(state.msgTagList, res.userId, res.res);
    },
    //是否删除消息标签成功
    isDelMsgTag(state, res) {
      if (res.status === "ok") {
        state.delMsgTagCount += 1;
        if (state.delMsgTagCount === 2) {
          window.main.$message.success("删除标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendMsgTag", state.addDelMsgId);
          state.addDelMsgId = "";
          state.delMsgTagCount = 0;
        }
      } else {
        window.main.$message.error("删除标签失败！");
      }
    },
    //存储标签下的消息rowkey
    setMsgTagRow(state, res) {
      if (res?.length === 0) {
        window.main.$message.warning("此标签下没有消息！");
      } else {
        this.dispatch("telegramSearch/telegramSearchInformation/sendTagMessage", res);
      }
    },
    // 存储搜索到的消息
    setTagMessages(state, res) {
      state.groupMsgList = res;
    },
    // 存储获取到的好友数量
    setFriendCount(state, res) {
      let user_id = res?.type?.user_id;
      let type = res?.type?.type;
      let group_count = res?.res < 1000 ? res?.res : "999+";
      if (!Object.hasOwnProperty.call(state.friendCountList, user_id)) {
        window.main.$set(state.friendCountList, user_id, {});
      }
      window.main.$set(state.friendCountList[user_id], type, group_count);
    },
    // 是否添加群组标签成功
    isAddGroupTag(state, res) {
      if (res.status === "ok") {
        state.addGroupTagCount += 1;
        if (state.addGroupTagCount === 3) {
          window.main.$message.success("添加标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendGroupTag", state.addDelGroupId);
          state.addDelGroupId = "";
          state.addGroupTagCount = 0;
        }
      } else {
        window.main.$message.error("添加标签失败！");
      }
    },
    // 获取到的群组标签
    setGroupTag(state, res) {
      window.main.$set(state.groupTagList, res.groupId, res.res);
    },
    //是否删除消息标签成功
    isDelGroupTag(state, res) {
      if (res.status === "ok") {
        state.delGroupTagCount += 1;
        if (state.delGroupTagCount === 2) {
          window.main.$message.success("删除标签成功！");
          this.dispatch("telegramSearch/telegramSearchInformation/sendGroupTag", state.addDelGroupId);
          state.addDelGroupId = "";
          state.delGroupTagCount = 0;
        }
      } else {
        window.main.$message.error("删除标签失败！");
      }
    },
    //存储标签下的消息rowkey
    setGroupTagRow(state, res) {
      if (res.length === 0) {
        window.main.$message.warning("此标签下没有群组！");
      } else {
        this.dispatch("telegramSearch/telegramSearchInformation/sendTagGroup", res);
      }
    },
    // 存储搜索到的消息
    setTagGroup(state, res) {
      state.allList = res;
      window.main.eventBus.$emit("equivalenceGroupList");
    },
    //是否成功添加刷新用户信息任务
    isAddRefreshUserInfoTask(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("刷新中，请等待...");
      } else {
        window.main.$message.warning("刷新失败！");
      }
    },
    // 是否成功添加群组链接
    isAddChartsLink(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("已添加爬虫任务，请等待...");
      } else {
        window.main.$message.warning("添加失败！");
      }
    },
    //是否成功添加刷新群组消息任务
    isAddRefreshGroupMsgTask(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("刷新中，请等待...");
      } else {
        window.main.$message.warning("刷新失败！");
      }
    },
    // 查看群组消息回推消息
    showGroupMessage(state, v) {
      const data = parse(v.result);
      let textString = JSON.parse(
        data.childNodes[0].raw.slice(data.childNodes[0].raw.indexOf("\n\n") + 2)
      );
      window.main.$notify({
        title: "成功",
        message: `群组(${textString.group_id})已刷新完成,请重新点击加载该群。`,
        type: "success",
        duration: 0,
      });
    },
    // 查看群组信息回推消息
    showUserMessage(state, v) {
      const data = parse(v.result);
      let textString = JSON.parse(
        data.childNodes[0].raw.slice(data.childNodes[0].raw.indexOf("\n\n") + 2)
      );
      window.main.$notify({
        title: "成功",
        message: `目标人(${textString.telephone})已刷新完成,请重新点击加载该目标人。`,
        type: "success",
        duration: 0,
      });
    },
    // 是否添加临时分组成功
    setAddTemporaryMsg(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("添加分组成功！");
      } else {
        window.main.$message.warning("添加分组失败！");
      }
    },
    // 存储获取到的标签列表
    setUserTagAllList(state, res) {
      state.userAllTagList = res;
    },
    // 是否删除单个标签成功
    isDelUserOnceTag(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除标签成功！");
        window.main.$store.dispatch("telegramSearch/telegramSearchInformation/sendUserTagAllList");
      } else {
        window.main.$message.warning("删除标签失败！");
      }
    },
    // 获取全部群组标签
    setGroupTagAllList(state, res) {
      state.groupAllTagList = res;
    },
    // 是否删除单个群组标签成功
    isDelGroupOnceTag(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除标签成功！");
        window.main.$store.dispatch("telegramSearch/telegramSearchInformation/sendGroupTagAllList");
      } else {
        window.main.$message.warning("删除标签失败！");
      }
    },
    // 获取当前群下所有的消息标签
    setMsgTagAllList(state, res) {
      state.msgAllTagList = res;
    },
    // 是否删除单个消息标签
    isDelMsgOnceTag(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除标签成功！");
        window.main.$store.dispatch("telegramSearch/telegramSearchInformation/sendMsgTagAllList");
      } else {
        window.main.$message.warning("删除标签失败！");
      }
    },
    // 是否删除目标人成功
    isDeleteUserInfo(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除目标人成功！");
        window.main.$store.commit("telegramSearch/telegramSearchInformation/resetUserlist");
        window.main.$store.commit("telegramSearch/telegramSearchInformation/sendUsers");
        window.main.$store.commit("telegramSearch/telegramSearchInformation_tree/sendTaskCount", {
          row: state.case_id,
        });
      } else {
        window.main.$message.warning("删除目标人失败！");
      }
    },
  },
  actions: {
    //获取一个用户下群，频道和联系人的所有ID
    sendallListId({ state, dispatch, commit }, v) {
      state.groupLoading = true;
      if (v) {
        state.loadUserId = v;
      }
      state.groupType.forEach((t_type) => {
        window.main.$data_socket.sendData(
          "Api.Search.SearchPrefix.Query",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                size: 20,
                row_key: state.allGroupRow[t_type]
                  ? [state.allGroupRow[t_type]]
                  : [],
              },
              msg: {
                type: "case",
                path: "/instant_msg/" + state.equipment + "/user_id",
                case_id: state.case_id,
                relation: state.loadUserId + ";" + t_type,
                prefix: "",
              },
            },
          ],
          function aaa(res) {
            window.main.$store.commit("telegramSearch/telegramSearchInformation/setdallListId", {
              res: res,
              type: this,
            });
          }.bind(t_type)
        );
      });
    },
    // 获取一个用户下的群组、频道、联系人数量
    sendFriendCount({ state, dispatch, commit }, user_id) {
      let friendType = ["chat", "channel", "contact"];
      friendType.forEach((type) => {
        window.main.$data_socket.sendData(
          "Api.Search.SearchPrefix.Count",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                row_key: [],
              },
              msg: {
                type: "case",
                path: "/instant_msg/" + state.equipment + "/user_id",
                case_id: state.case_id,
                relation: user_id + ";" + type,
                prefix: "",
              },
            },
          ],
          function ccc(res) {
            window.main.$store.commit("telegramSearch/telegramSearchInformation/setFriendCount", {
              res: res !== undefined ? res : "0",
              type: this,
            });
          }.bind({
            type,
            user_id,
          })
        );
      });
    },
    // 获取所有联系人
    sendContacts({ state, dispatch, commit }, v) {
      if (v) {
        let row_arr = [];
        const sha512 = require("sha512");
        const hash =
          sha512("p;/instant_msg/" + state.equipment + "/group_id").toString(
            "hex"
          ) +
          ";p;/instant_msg/" +
          state.equipment +
          "/group_id;";
        v.forEach((item) => {
          row_arr.push(hash + item);
        });
        let rowArr = Array.from(new Set(row_arr));
        window.main.$data_socket.sendData(
          "Api.Search.SearchPrefix.DetailMulti",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                row_key: rowArr,
              },
              msg: {
                type: "public",
                path: "/instant_msg/" + state.equipment + "/group_id",
                relation: "",
                prefix: "",
              },
            },
          ],
          "telegramSearch/telegramSearchInformation/setContacts"
        );
      }
    },
    // 获取群组的爬取进度
    sendGetGroupProgress({ state, dispatch, commit }, v) {
      let groupHave = {};
      v?.forEach((item) => {
        let path =
          "/tmp/vps/telegram_spider_manager_system/group_data_spider_cache/" +
          item.replace(";", "/");
        groupHave[path] = "";
      });
      if (Object.keys(groupHave).length) {
        window.main.$constant_socket.sendData(
          "Api.Node.NodeData",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
              },
              msg: groupHave,
            },
          ],
          (res) => {
            for (const key in res) {
              let group_id = key
                .substring(key.indexOf("cache/") + 6, key.length)
                .replace("/", ";");
              state.groupProgress[group_id] = res[key];
            }
          }
        );
      }
    },
    //获取群成员
    sendMember({ state, dispatch, commit }, v) {
      state.msgLoading = true;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastMemberRow ? [state.lastMemberRow] : [],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";member",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMember"
      );
    },
    //获取群消息
    sendMsg({ state, dispatch, commit }, v) {
      state.msgLoading = true;
      if (v) {
        state.loadGroupId = v;
      }
      if (v === false) {
        state.lastMsgRow = "";
        state.groupMsgList = [];
        state.msgIconList = [];
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastMsgRow ? [state.lastMsgRow] : [],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMsg"
      );
    },

    // 获取指定范围的消息
    sendTimeMsg({ state, dispatch, commit }, v) {
      state.groupMsgList = "";
      const sha512 = require("sha512");
      const hash =
        sha512(
          "p;/instant_msg/" +
            state.equipment +
            "/group_id;" +
            state.loadGroupId +
            ";msg"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.equipment +
        "/group_id;" +
        state.loadGroupId +
        ";msg;" +
        v;

      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [hash],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMsg"
      );
    },
    //获取指定的群组联系id
    sendAppointId({ state, dispatch, commit }, v) {
      state.groupLoading = true;
      if (v) {
        state.relationUser = v;
        state.loadGroupId = v;
        state.loadUserId = v.userId;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastRelationRow ? [state.lastRelationRow] : [],
            },
            msg: {
              type: "case",
              path: "/instant_msg/" + state.equipment + "/user_id",
              case_id: state.case_id,
              relation:
                state.relationUser.userId + ";" + state.relationUser.type,
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setAppointId"
      );
    },
    //获取群消息的头像和用户名
    sendMsgIcon({ state, dispatch, commit }, v) {
      let row_arr = [];
      const sha512 = require("sha512");
      const hash =
        sha512("p;/instant_msg/" + state.equipment + "/user_id").toString(
          "hex"
        ) +
        ";p;/instant_msg/" +
        state.equipment +
        "/user_id;";
      v.forEach((item) => {
        row_arr.push(hash + item);
      });
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: row_arr,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/user_id",
              relation: "",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMsgIcon"
      );
    },
    //修改备注
    addDataRemark({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              prefix: v.userId + "",
              data: {
                data: {
                  user_remark: {
                    user_remark: v.remarkValue,
                  },
                  _: {
                    _: v.userId + "",
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddDataRemark"
      );
    },
    // 添加目标人标签
    addDataTag({ state, dispatch, commit }, v) {
      state.addDelUserId = v.user_id;
      if (v.addTag === "") {
        return;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              prefix: v.addTag,
              relation: v.user_id + ";tag",
              data: {
                data: {
                  _: {
                    _: v.addTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddDataTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.addTag,
              relation: state.case_id + ";userTag",
              data: {
                data: {
                  _: {
                    _: v.addTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddDataTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.user_id + "",
              relation: state.case_id + ";userTag;" + v.addTag + ";user",
              data: {
                data: {
                  _: {
                    _: v.user_id + "",
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddDataTag"
      );
    },
    // 获取目标人标签
    sendUserTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              prefix: "",
              relation: v + ";tag",
            },
          },
        ],
        function bbb(res) {
          window.main.$store.commit("telegramSearch/telegramSearchInformation/setUserTag", {
            res: res,
            userId: this,
          });
        }.bind(v)
      );
    },
    // 删除目标人标签
    delUserTag({ state, dispatch, commit }, v) {
      state.addDelUserId = v.userId;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v.tag.row],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              relation: v.userId + ";tag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelUserTag"
      );
      const sha512 = require("sha512");
      const hashR =
        sha512(
          "c;" +
            state.case_id +
            ";/instant_msg/" +
            state.equipment +
            "/tag;" +
            state.case_id +
            ";userTag;" +
            v.tag.columnValues.d._._ +
            ";user"
        ).toString("hex") +
        ";c;" +
        state.case_id +
        ";/instant_msg/" +
        state.equipment +
        "/tag;" +
        state.case_id +
        ";userTag;" +
        v.tag.columnValues.d._._ +
        ";user;" +
        v.userId;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [hashR],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation:
                state.case_id +
                ";userTag;" +
                v.tag.columnValues.d._._ +
                ";user",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelUserTag"
      );
    },
    // 目标人自定义标签搜索id
    sendSeachUserTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.case_id + ";userTag;" + v + ";user",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setSeachUserTag"
      );
    },
    // 自动义标签下的用户
    sendTagUsers({ state, dispatch, commit }, v) {
      let row_arr = [];
      const sha512 = require("sha512");
      const hash =
        sha512(
          "c;" + state.case_id + ";/instant_msg/" + state.equipment + "/user_id"
        ).toString("hex") +
        ";c;" +
        state.case_id +
        ";/instant_msg/" +
        state.equipment +
        "/user_id;";
      v.forEach((item) => {
        row_arr.push(hash + item.columnValues.d._._);
      });
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: row_arr,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              prefix: "",
              relation: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setTagUsers"
      );
    },
    // 删除本地群消息
    sendDelGroupMsg({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelGroupMsg"
      );
    },
    // 删除远程消息
    sendDelTelegrameMsg({ state, dispatch, commit }, v) {
      window.main.$constant_socket.sendData(
        "Api.Node.IsNodeExist",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              nodePath:
                "/tmp/vps/telegram_spider_manager_system/telegram_need_delete_message/" +
                state.nowUserTelephone +
                "/" +
                v.group_id,
            },
          },
        ],
        (res) => {
          if (res?.status === "ok") {
            window.main.$constant_socket.sendData(
              "Api.Node.NodeData",
              [
                {
                  head: {
                    session_id: window.main.$store.state.userInfo.session_id,
                  },
                  msg: {
                    ["/tmp/vps/telegram_spider_manager_system/telegram_need_delete_message/" +
                    state.nowUserTelephone +
                    "/" +
                    v.group_id]: "",
                  },
                },
              ],
              (res) => {
                let msgIdsArr =
                  res[
                    "/tmp/vps/telegram_spider_manager_system/telegram_need_delete_message/" +
                      state.nowUserTelephone +
                      "/" +
                      v.group_id
                  ].ids;
                if (msgIdsArr.includes(v.msg_id)) {
                  window.main.$message.success("删除中，请稍等...");
                } else {
                  msgIdsArr.push(v.msg_id);
                  window.main.$constant_socket.sendData(
                    "Api.Node.UpdateNodeData",
                    [
                      {
                        head: {
                          session_id:
                            window.main.$store.state.userInfo.session_id,
                        },
                        msg: {
                          nodePath:
                            "/tmp/vps/telegram_spider_manager_system/telegram_need_delete_message/" +
                            state.nowUserTelephone +
                            "/" +
                            v.group_id,
                          data: {
                            group_id: v.group_id,
                            ids: msgIdsArr,
                          },
                        },
                      },
                    ],
                    "telegramSearch/telegramSearchInformation/isDelTelegrameGroupMsg"
                  );
                }
              }
            );
          } else if (res?.status === "no") {
            window.main.$constant_socket.sendData(
              "Api.Node.CreateNode",
              [
                {
                  head: {
                    session_id: window.main.$store.state.userInfo.session_id,
                  },
                  msg: {
                    nodePath:
                      "/tmp/vps/telegram_spider_manager_system/telegram_need_delete_message/" +
                      state.nowUserTelephone +
                      "/" +
                      v.group_id,
                    data: {
                      group_id: v.group_id,
                      ids: [v.msg_id],
                    },
                  },
                },
              ],
              "telegramSearch/telegramSearchInformation/isDelTelegrameGroupMsg"
            );
          }
        }
      );
    },
    //使用id搜索群成员
    seachIdMember({ state, dispatch, commit }, v) {
      const sha512 = require("sha512");
      const row_arr =
        sha512(
          "p;/instant_msg/" +
            state.equipment +
            "/group_id;" +
            state.loadGroupId +
            ";member"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.equipment +
        "/group_id;" +
        state.loadGroupId +
        ";member;"+v;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [row_arr],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";member",
              prefix:"",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setSeachMember"
      );
    },
    // 刷新群成员
    getGroupMember(){
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              size: 20,
              row_key: [],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";member",
              prefix:"",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setSeachMember"
      );
    },
    // 添加消息标签
    addMsgTag({ state, dispatch, commit }, v) {
      state.addDelMsgId = v.msgId;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.msgTag,
              relation: v.msgId + ";tag",
              data: {
                data: {
                  _: {
                    _: v.msgTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddMsgTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.msgTag,
              relation: v.groupId + ";msgTag",
              data: {
                data: {
                  _: {
                    _: v.msgTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddMsgTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.msgId + "",
              relation: v.groupId + ";msgTag;" + v.msgTag + ";msg",
              data: {
                data: {
                  msg_row: {
                    msg_row: v.msgRowKey,
                  },
                  _: {
                    _: v.msgId,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddMsgTag"
      );
    },
    // 获取消息标签
    sendMsgTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: "",
              relation: v + ";tag",
            },
          },
        ],
        function bbb(res) {
          window.main.$store.commit("telegramSearch/telegramSearchInformation/setMsgTag", {
            res: res,
            userId: this,
          });
        }.bind(v)
      );
    },
    // 删除消息标签
    delMsgTag({ state, dispatch, commit }, v) {
      state.addDelMsgId = v.msgId;
      const sha512 = require("sha512");
      const hash =
        sha512(
          "p;/instant_msg/" + state.equipment + "/tag;" + v.msgId + ";tag"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.equipment +
        "/tag;" +
        v.msgId +
        ";tag;" +
        v.msgTag;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [hash],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: v.msgId + ";tag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelMsgTag"
      );
      const hash2 =
        sha512(
          "p;/instant_msg/" +
            state.equipment +
            "/tag;" +
            v.groupId +
            ";msgTag;" +
            v.msgTag +
            ";msg"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.equipment +
        "/tag;" +
        v.groupId +
        ";msgTag;" +
        v.msgTag +
        ";msg;" +
        v.msgId;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [hash2],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: v.groupId + ";msgTag;" + v.msgTag + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelMsgTag"
      );
    },
    // 自定义标签搜索消息rowKey
    seachMsgTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadGroupId + ";msgTag;" + v + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMsgTagRow"
      );
    },
    // 自定义标签下的消息
    sendTagMessage({ state, dispatch, commit }, v) {
      let row_arr = [];
      v?.forEach((item) => {
        row_arr.push(item.columnValues.d.msg_row.msg_row);
      });
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: row_arr,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              prefix: "",
              relation: state.loadGroupId + ";msg",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setTagMessages"
      );
    },
    // 添加群组标签
    addGroupTag({ state, dispatch, commit }, v) {
      state.addDelGroupId = v.group_id;
      if (v.groupTag === "") {
        return;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              prefix: v.groupTag + "",
              relation: v.group_id + ";tag",
              data: {
                data: {
                  _: {
                    _: v.groupTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddGroupTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.groupTag + "",
              relation: state.loadUserId + ";groupTag",
              data: {
                data: {
                  _: {
                    _: v.groupTag,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddGroupTag"
      );
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              prefix: v.group_id + "",
              relation: state.loadUserId + ";groupTag;" + v.groupTag + ";group",
              data: {
                data: {
                  group_row: {
                    group_row: v.groupRowKey,
                  },
                  _: {
                    _: v.group_id,
                  },
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddGroupTag"
      );
    },
    // 获取群标签
    sendGroupTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              prefix: "",
              relation: v + ";tag",
            },
          },
        ],
        function eee(res) {
          window.main.$store.commit("telegramSearch/telegramSearchInformation/setGroupTag", {
            res: res,
            groupId: this,
          });
        }.bind(v)
      );
    },
    // 删除群标签
    delGroupTag({ state, dispatch, commit }, v) {
      state.addDelGroupId = v.group_id;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [v.groupTag.row],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: v.group_id + ";tag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelGroupTag"
      );

      const sha512 = require("sha512");
      const hash =
        sha512(
          "c;" +
            state.case_id +
            ";/instant_msg/" +
            state.equipment +
            "/tag;" +
            state.loadUserId +
            ";groupTag;" +
            v.groupTag.columnValues.d._._ +
            ";group"
        ).toString("hex") +
        ";c;" +
        state.case_id +
        ";/instant_msg/" +
        state.equipment +
        "/tag;" +
        state.loadUserId +
        ";groupTag;" +
        v.groupTag.columnValues.d._._ +
        ";group;" +
        v.group_id;
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [hash],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation:
                state.loadUserId +
                ";groupTag;" +
                v.groupTag.columnValues.d._._ +
                ";group",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelGroupTag"
      );
    },
    // 自定义标签搜索群组rowKey
    seachGroupTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadUserId + ";groupTag;" + v + ";group",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setGroupTagRow"
      );
    },
    // 自定义标签下的群组
    sendTagGroup({ state, dispatch, commit }, v) {
      let row_arr = [];
      v.forEach((item) => {
        row_arr.push(item.columnValues.d.group_row.group_row);
      });
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: row_arr,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setTagGroup"
      );
    },
    //导出所有联系人
    sendExportContacts({ state, dispatch, commit }, v) {
      if (v) {
        let row_arr = [];
        const sha512 = require("sha512");
        const hash =
          sha512("p;/instant_msg/" + state.equipment + "/group_id").toString(
            "hex"
          ) +
          ";p;/instant_msg/" +
          state.equipment +
          "/group_id;";
        v.forEach((item) => {
          row_arr.push(hash + item);
        });
        window.main.$data_socket.sendData(
          "Api.Search.SearchPrefix.DetailMulti",
          [
            {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                row_key: row_arr,
              },
              msg: {
                type: "public",
                path: "/instant_msg/" + state.equipment + "/group_id",
                relation: "",
                prefix: "",
              },
            },
          ],
          "telegramSearch/telegramSearchInformation/setExportContacts"
        );
      }
    },
    //导出群成员
    exportMember({ state, dispatch, commit }, v) {
      if (v) {
        state.exportGroupId = v;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.lastExportMemberRow,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.exportGroupId + ";member",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setExportMember"
      );
    },
    //导出消息
    sendExportMsg({ state, dispatch, commit }, v) {
      state.Input = v.input;
      let rowKey;
      if (v.value.length > 0) {
        if (v.value[0].includes("p;/instant_msg/")) {
          rowKey = v.value;
        } else {
          const sha512 = require("sha512");
          const hash =
            sha512(
              "p;/instant_msg/" +
                state.equipment +
                "/group_id;" +
                state.loadGroupId +
                ";msg"
            ).toString("hex") +
            ";p;/instant_msg/" +
            state.equipment +
            "/group_id;" +
            state.loadGroupId +
            ";msg;" +
            v.value;
          rowKey = [hash];
        }
      } else {
        rowKey = [];
      }

      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: state.Input > 200 ? 200 : state.Input,
              row_key: rowKey,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: state.loadGroupId + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setExportMsg"
      );
    },

    //导出
    sendExportAppointId({ state, dispatch, commit }, v) {
      if (v) {
        state.relationUser = v;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: state.exportLastRelationRow,
            },
            msg: {
              type: "case",
              path: "/instant_msg/" + state.equipment + "/user_id",
              case_id: state.case_id,
              relation:
                state.relationUser.userId + ";" + state.relationUser.type,
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/exportAppointId"
      );
    },
    // 添加目标人刷新信息
    addRefreshUserInfoTask({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.DataAnalysisTask.SendAsyncTask",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              key: "save",
              topic: "HbaseTask.SpiderTask.Telegram.QueueJumpingTask",
              value: {
                row: v + ";user",
                data: {
                  telephone: v,
                  type: "user",
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddRefreshUserInfoTask"
      );
    },
    // 添加群组链接
    addChartsLink({ state, dispatch, commit }, v) {
      const { task_id, chartLink } = v;
      window.main.$data_socket.sendData(
        "Api.DataAnalysisTask.SendAsyncTask",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              key: "save",
              topic: "HbaseTask.SpiderTask.Telegram.QueueJumpingTask",
              value: {
                row: task_id + ";join_group",
                data: {
                  task_id: task_id,
                  chartLink: chartLink,
                  type: "join_group",
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddChartsLink"
      );
    },
    //添加群组刷新消息
    addRefreshGroupMsgTask({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.DataAnalysisTask.SendAsyncTask",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              key: "save",
              topic: "HbaseTask.SpiderTask.Telegram.QueueJumpingTask",
              value: {
                row: v.targetTelephone + ";" + v.groupID + ";group",
                data: {
                  telephone: v.targetTelephone,
                  group_id: v.groupID,
                  max_id: v.groupMaxID + "",
                  type: "group",
                },
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isAddRefreshGroupMsgTask"
      );
    },
    // 添加临时消息
    sendAddTemporaryMsg({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/group_id",
              relation: "0;msg",
              prefix: v._._,
              data: {
                data: v,
              },
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setAddTemporaryMsg"
      );
    },
    // 获取目标人全部标签
    sendUserTagAllList({ state, dispatch, commit }, v) {
      if (!state.usersList.length) {
        return;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.case_id + ";userTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setUserTagAllList"
      );
    },
    // 删除目标人搜索标签
    sendDelUserOnceTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.case_id + ";userTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelUserOnceTag"
      );
    },
    // 获取群组所有标签
    sendGroupTagAllList({ state, dispatch, commit }, v) {
      if (!state.allList.length) {
        return;
      }
      if (!state.case_id) {
        return;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadUserId + ";groupTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setGroupTagAllList"
      );
    },
    // 删除群组搜索标签
    sendDelGroupOnceTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadUserId + ";groupTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelGroupOnceTag"
      );
    },
    // 获取消息所有标签
    sendMsgTagAllList({ state, dispatch, commit }, v) {
      if (!state.groupMsgList.length) {
        return;
      }
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadGroupId + ";msgTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/setMsgTagAllList"
      );
    },
    // 删除消息搜索标签
    sendDelMsgOnceTag({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.equipment + "/tag",
              relation: state.loadGroupId + ";msgTag",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDelMsgOnceTag"
      );
    },
    //删除目标人
    sendDeleteUserInfo({ state, dispatch, commit }, v) {
      window.main.$data_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [v],
            },
            msg: {
              type: "case",
              case_id: state.case_id,
              path: "/instant_msg/" + state.equipment + "/user_id",
              relation: "",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchInformation/isDeleteUserInfo"
      );
    },
  },
};
