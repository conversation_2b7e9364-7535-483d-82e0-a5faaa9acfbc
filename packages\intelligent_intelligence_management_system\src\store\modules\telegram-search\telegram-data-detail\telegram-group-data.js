export default {
  namespaced: true,
  state: {},
  mutations: {
    clearGroupData(state) {
      state.from = 0;
      this.commit("telegramSearch/telegramSearchDataDetail/setTmpDataDetail", {
        key: "group_data",
        value: [],
      });
    },
    async sendGetDataDetailGroupData(state) {
      let tmpObj =
        this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["elasticsearch_data"];
      if (tmpObj["_source"]) {
        tmpObj["_source"] = tmpObj["_source"];
      }
      await window.main.$main_socket.sendData(
        "Api.Search.DataDetail.GroupData",
        [
          {
            head: {
              session_id: this.state.userInfo.session_id,
            },
            msg: tmpObj,
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramGroupData/setDataDetailGroupData"
      );
    },
    setDataDetailGroupData(state, data) {
      this.state.telegramSearch.telegramSearchDataDetail.tmpDataDetail["group_data"] = data;
    },
  },
};
