export default {
  namespaced: true,
  state: {
    selectPerson: null,
    identity: null,
    email: null,
    /** id 数组 */
    phoneIds: [],
    telegramIds: [],
    twitterIds: [],
    facebookIds: [],
    linkedinIds: [],
    personRelationIds: [],
    organizationRelationIds: [],
    /** id 数组的搜索结果 */
    phoneSearchList: [],
    telegramSearchList: [],
    twitterSearchList: [],
    facebookSearchList: [],
    linkedinSearchList: [],
    personRelationSearchList: [],
    organizationRelationSearchList: [],


    socailPath: "/socail_data",
    mailRecipientPath: "/mail_info/recipient/list",
    mailSenderPath: "/mail_info/sender/list",
    telegramPath: "/instant_msg/telegram/user_id",
    twitterPath: "/social_platform/twitter/relation",
    facebookPath: "/social_platform/facebook",
    linkedinPath: "/social_platform/linkedin",
    twitterPrefixSearchList: [], // twitter前缀集合
    twitterFollowingList: [], // twitter关注集合
    twitterFollowersList: [], // twitter粉丝集合
    mailRecipientSearchList: [], // 邮箱接收者搜索结果列表
    mailSenderSearchList: [], // 邮箱发送者搜索结果列表
    telegramPrefixSearchList: [], // telegram前缀集合
  },
  mutations: {
    // 重置所有数据
    resetAllData(state) {
      state.selectPerson = null;
      state.identity = null;
      state.email = null;
      state.phoneIds = [];
      state.twitterIds = [];
      state.telegramIds = [];
      state.facebookIds = [];
      state.linkedinIds = [];
      state.twitterSearchList = [];
      state.telegramSearchList = [];
      state.facebookSearchList = [];
      state.linkedinSearchList = [];
      state.personRelationIds = [];
      state.organizationRelationIds = [];
      state.personRelationSearchList = [];
      state.organizationRelationSearchList = [];
      state.mailRecipientSearchList = [];
      state.mailSenderSearchList = [];
      console.log("resetAllData");
      
    },

    // 设置选中的目标人以及相关信息
    setSelectPerson(state, person) {
      state.selectPerson = person;
      // 处理基础信息
      state.identity = person._source.params.basic.identity;
      state.email = person._source.params.basic.email;
      state.phoneIds = person._source.params.phoneNumbers;
      state.twitterIds = person._source.params.twitterIds;
      state.telegramIds = person._source.params.telegramIds;
      state.facebookIds = person._source.params.facebookIds;
      state.linkedinIds = person._source.params.linkedInIds;

      // 处理关系数据
      let relations = person._source.params?.relation;
      let personR = Array.isArray(relations) && relations.length > 0 ? relations[0] : null;
      let organizationR = Array.isArray(relations) && relations.length > 1 ? relations[1] : null;
      state.personRelationIds = personR ? JSON.parse(personR).person : [];
      state.organizationRelationIds = organizationR ? JSON.parse(organizationR).organization : [];      

      // 遍历人员关系数据
      if (state.personRelationIds && state.personRelationIds.length > 0) {
        window.main.$store.commit("relationsshipTopologyDiagram/searchPersonRelation", state.personRelationIds);
      }

      // 遍历组织关系数据
      if (state.organizationRelationIds && state.organizationRelationIds.length > 0) {
        window.main.$store.commit("relationsshipTopologyDiagram/searchOrganizationRelation", state.organizationRelationIds);
      }


      // 遍历twitter号码然后发送请求
      if (state.twitterIds && state.twitterIds.length > 0) {
        state.twitterIds.forEach(item => {
          window.main.$store.commit("relationsshipTopologyDiagram/searchTwitter", item);
        });
      }
      // 遍历telegram号码然后发送请求
      if (state.telegramIds && state.telegramIds.length > 0) {
        state.telegramIds.forEach(item => {
          window.main.$store.commit("relationsshipTopologyDiagram/searchTelegram", item);
        });
      }
      // 遍历facebook号码然后发送请求
      // if (state.facebook && state.facebook.length > 0) {
      //   state.facebook.forEach(item => {
      //     window.main.$store.commit("relationsshipTopologyDiagram/searchFacebook", item);
      //   });
      // }
      // 遍历linkedin号码然后发送请求
      if (state.linkedinIds && state.linkedinIds.length > 0) {
        state.linkedinIds.forEach(item => {
          window.main.$store.commit("relationsshipTopologyDiagram/searchLinkedin", item);
        });
      }
      // 遍历邮箱然后发送请求
      if (state.email) {
        window.main.$store.commit("relationsshipTopologyDiagram/searchRecipientMail", state.email);
        window.main.$store.commit("relationsshipTopologyDiagram/searchSenderMail", state.email);
      }
      // 遍历电话号码然后发送请求
      // if (state.phone) {
      //   window.main.$store.commit("relationsshipTopologyDiagram/searchPhone", state.phone);
      // }
      // 遍历身份证号码然后发送请求
      // if (state.identity) {
      //   window.main.$store.commit("relationsshipTopologyDiagram/searchIdentity", state.identity);
      // }
    },
 
    // 通过人员关系id进行搜索ES
    searchPersonRelation(state, personRelationId) {
      console.log("personRelationId", personRelationId);
      let add_es_query_conditions = {
        bool: {
          should: personRelationId.map(account => ({
            term: { _id: account}
          }))
        },
      }
      window.main.$main_socket.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              from: 0,
              size: 200
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        (res) => {
          console.log("res-person-relation", res);
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach(item => {
            let personData = buildPersonData(item._source.params);
            state.personRelationSearchList.push(personData);
          });
        }
      );
      // 目标人数据构造
      function buildPersonData(params) {
        console.log("buildPersonData", params);
        
        // 如果params已经是对象格式，直接返回
        if (typeof params === 'object' && !Array.isArray(params)) {
          return params;
        }
        
        // 处理params为数组的情况
        if (Array.isArray(params)) {
          const paramsObj = {};
          params.forEach((param) => {
            paramsObj[param.k] = param.v;
          });
          
          // 标准字段列表
          const standardFields = [
            "type","avatar", "name", "remark", "sex", "age", "phoneNumbers", "identity", "email", "dateBirth",
            "relation", "twitterIds", "facebookIds", "linkedInIds", "telegramIds", "basic", "socialize", "media", "customFields",
          ];
          
          // 组装basic对象
          const basicObj = {
            type: Array.isArray(paramsObj.type) ? paramsObj.type[0] : (paramsObj.type || 'key_person'),
            id: paramsObj.id ? (Array.isArray(paramsObj.id) ? paramsObj.id[0] : paramsObj.id) : '',
            name: Array.isArray(paramsObj.name) ? paramsObj.name[0] : (paramsObj.name || ''),
            remark: Array.isArray(paramsObj.remark) ? paramsObj.remark[0] : (paramsObj.remark || ''),
            sex: Array.isArray(paramsObj.sex) ? paramsObj.sex[0] : (paramsObj.sex || ''),
            age: paramsObj.age ? parseInt(Array.isArray(paramsObj.age) ? paramsObj.age[0] : paramsObj.age) : 0,
            phone: Array.isArray(paramsObj.phoneNumbers) ? paramsObj.phoneNumbers[0] : (paramsObj.phoneNumbers || ''),
            identity: Array.isArray(paramsObj.identity) ? paramsObj.identity[0] : (paramsObj.identity || ''),
            email: Array.isArray(paramsObj.email) ? paramsObj.email[0] : (paramsObj.email || ''),
            avatar: Array.isArray(paramsObj.avatar) ? paramsObj.avatar[0] : (paramsObj.avatar || ''),
            dateBirth: Array.isArray(paramsObj.dateBirth) ? paramsObj.dateBirth[0] : (paramsObj.dateBirth || '')
          };
          
          // 组装customFields
          const customFields = {};
          Object.keys(paramsObj).forEach(key => {
            if (!standardFields.includes(key)) {
              customFields[key] = Array.isArray(paramsObj[key]) ? paramsObj[key][0] : paramsObj[key];
            }
          });
          
          // 组装media对象
          const buildMedia = (p) => {
            const platforms = [
              { key: 'telegram', idKey: 'telegramIds' },
              { key: 'twitter', idKey: 'twitterIds' },
              { key: 'facebook', idKey: 'facebookIds' },
              { key: 'linkedin', idKey: 'linkedInIds' },
            ];
            const media = {};
            platforms.forEach(({ key, idKey }) => {
              const arr = Array.isArray(p[idKey]) ? p[idKey] : (p[idKey] ? [p[idKey]] : []);
              media[key] = arr.map(id => ({
                idNum: id,
                name: id,
              }));
            });
            return media;
          };
          
          // 处理关系数据（兼容旧数据格式）
          let relationData = { person: [], organization: [] };
          if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
            paramsObj.relation.forEach(relationStr => {
              try {
                const parsed = JSON.parse(relationStr);
                if (parsed.person && Array.isArray(parsed.person)) {
                  relationData.person = [...relationData.person, ...parsed.person];
                }
                if (parsed.organization && Array.isArray(parsed.organization)) {
                  relationData.organization = [...relationData.organization, ...parsed.organization];
                }
              } catch (e) {
                console.warn("解析关系数据失败:", relationStr, e);
              }
            });
          }
          
          // 组装最终params对象
          const result = {
            ...paramsObj,
            basic: basicObj,
            customFields,
            media: buildMedia(paramsObj),
            relation: relationData
          };
          
          console.log("构建后的人员数据:", result);
          return result;
        }
        
        // 如果params格式不识别，返回原始数据
        console.warn("无法识别的params格式:", params);
        return params;
      }
    },

    // 通过组织关系id进行搜索ES
    searchOrganizationRelation(state, organizationRelationId) {
      console.log("organizationRelationId", organizationRelationId);
      let add_es_query_conditions = {
        bool: {
          should: organizationRelationId.map(account => ({
            term: { _id: account}
          }))
        },
      }
      window.main.$main_socket.sendData(
        'Api.Search.SearchList.Query',
        [ 
          {
            head: {
              from: 0,
              size: 200
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: {
              data_range_index_name: "key_organization",
            },
          },
        ],
        (res) => {  
          console.log("res-organization-relation", res);
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach(item => {
            let organizationData = buildOrganizationData(item._source.params);
            state.organizationRelationSearchList.push(organizationData);
          });
        }
      );
      // 目标组织数据构造
      function buildOrganizationData(params) {
        console.log("buildOrganizationData", params);
        
        // 如果params已经是对象格式，直接返回
        if (typeof params === 'object' && !Array.isArray(params)) {
          return params;
        }
        
        // 处理params为数组的情况
        if (Array.isArray(params)) {
          const paramsObj = {};
          params.forEach((param) => {
            paramsObj[param.k] = param.v;
          });
          
          // 定义一个辅助函数来获取数组中的第一个元素或默认值
          const getFirstOrDefault = (arr, defaultVal = '') => {
            return Array.isArray(arr) && arr.length > 0 ? arr[0] : defaultVal;
          };
          
          // 创建basic对象以兼容前端视图
          const basicObj = {
            id: getFirstOrDefault(paramsObj.id),
            name: getFirstOrDefault(paramsObj.name),
            remark: getFirstOrDefault(paramsObj.remark),
            createTime: getFirstOrDefault(paramsObj.createTime),
            belong: getFirstOrDefault(paramsObj.belong),
            desi: getFirstOrDefault(paramsObj.desi),
            avatar: getFirstOrDefault(paramsObj.avatar),
          };
          
          // 组装customFields
          const standardFields = [
            "id", "name", "remark", "createTime", "belong", "desi", "avatar", "relation", "customFields"
          ];
          const customFields = {};
          Object.keys(paramsObj).forEach(key => {
            if (!standardFields.includes(key)) {
              customFields[key] = getFirstOrDefault(paramsObj[key]);
            }
          });
          
          // 处理关系数据
          let relationData = { person: [], organization: [] };
          if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
            paramsObj.relation.forEach(relationStr => {
              try {
                const parsed = JSON.parse(relationStr);
                if (parsed.person && Array.isArray(parsed.person)) {
                  relationData.person = [...relationData.person, ...parsed.person];
                }
                if (parsed.organization && Array.isArray(parsed.organization)) {
                  relationData.organization = [...relationData.organization, ...parsed.organization];
                }
              } catch (e) {
                console.warn("解析组织关系数据失败:", relationStr, e);
              }
            });
          }
          
          // 组装最终params对象
          const result = {
            ...paramsObj,
            basic: basicObj,
            customFields,
            relation: relationData
          };
          
          console.log("构建后的组织数据:", result);
          return result;
        }
        
        // 如果params格式不识别，返回原始数据
        console.warn("无法识别的组织params格式:", params);
        return params;
      }
    },

    // 通过twitter号码进行搜索
    searchTwitter(state, twitter) {
      let row = window.main.$tools.sha512("p;"+state.twitterPath) +";p;"+state.twitterPath+";"+twitter
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.DetailMulti',
        [
          {
            head: {
              row_key: [row],
              size: 200
            },
            msg: {
              type: 'public',
              path: state.twitterPath,
              prefix: twitter,
              relation: "",
            },
          }
        ],
        (data) => {
          console.log("res-twitter", data, state.twitterSearchList);
          if (data?.length && data[0]?.columnValues?.d){
            data.forEach(item => {
              state.twitterSearchList.push(item.columnValues.d)
            });
            window.main.$main_socket.sendData(
              'Api.Search.SearchPrefix.Query',
              [
                {
                  head: {
                    row_key: [],
                    size: 200 
                  },
                  msg: {
                    type: 'public',
                    path: state.twitterPath,
                    prefix: "",
                    relation: twitter+";followers",
                  },
                },
              ],
              (res) => {
                console.log("res-twitter-followers", res);
                if (!res?.length) return;
                window.main.$store.commit("relationsshipTopologyDiagram/updateTwitterFollowers", {userId: twitter, followers: res});
                // state.twitterSearchList.forEach(item => {
                //   if (item.user_id === twitter) {
                //     item['followers'] = res
                //   }
                // });
              }
            );
            window.main.$main_socket.sendData(
              'Api.Search.SearchPrefix.Query',
              [
                {
                  head: {
                    row_key: [],
                    size: 200
                  },
                  msg: {
                    type: 'public',
                    path: state.twitterPath,
                    prefix: "",
                    relation: twitter+";following",
                  },
                },
              ],
              (res) => {
                console.log("res-twitter-following", res);
                if (!res?.length) return;
                window.main.$store.commit("relationsshipTopologyDiagram/updateTwitterFollowing", {userId: twitter, following: res});
                // state.twitterSearchList.forEach(item => {
                //   if (item.user_id === twitter) {
                //     item['following'] = res
                //   }
                // });
              }
            );
          }
        }
      )
    },

    // 通过telegram号码进行搜索
    searchTelegram(state, telegram) {
      console.log("telegram", telegram);
      let row = window.main.$tools.sha512("p;"+state.telegramPath) +";p;"+state.telegramPath+";"+telegram
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.DetailMulti',
        [
          {
            head: {
              row_key: [row],
              size: 200
            },
            msg: {
              type: 'public',
              path: state.telegramPath,
              prefix: telegram,
              relation: "",
            },
          }
        ],
        (data) => {
          console.log("res-telegram", data);
          if (data?.length && data[0]?.columnValues?.d){
            data.forEach(item => {
              state.telegramSearchList.push(item.columnValues.d)
            });
            window.main.$main_socket.sendData(
              'Api.Search.SearchPrefix.Query',
              [
                {
                  head: {
                    row_key: [],
                    size: 20
                  },
                  msg: {
                    type: 'public',
                    path: state.telegramPath,
                    prefix: "",
                    relation: telegram+";followers",
                  },
                },
              ],
              (res) => {
                console.log("res-telegram", res);
                if (!res?.length) return;
                state.telegramSearchList.forEach(item => {
                  if (item.user_id === telegram) {
                    item['followers'] = res
                  }
                });
              }
            );
            window.main.$main_socket.sendData(
              'Api.Search.SearchPrefix.Query',
              [
                {
                  head: {
                    row_key: [],
                    size: 20
                  },
                  msg: {
                    type: 'public',
                    path: state.telegramPath,
                    prefix: "",
                    relation: telegram+";following",
                  },
                },
              ],
              (res) => {
                console.log("res-telegram-following", res);
                if (!res?.length) return;
                state.telegramSearchList.forEach(item => {
                  if (item.user_id === telegram) {
                    item['following'] = res
                  }
                });
              }
            )
          }
        }
      )
    },

    // 通过facebook号码进行搜索
    searchFacebook(state, facebook) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              family: ['r'],
              row_key: [],
              size: 20
            },
            msg: {
              type: 'public',
              path: state.facebookPath,
              prefixe: facebook,
              relation: '',
            },
          },
        ],
        (res) => {
          console.log("res", res);
        }
      );
    },

    // 通过linkedin号码进行搜索
    searchLinkedin(state, linkedin) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              from:0,
              size: 200
            },
            control: {
              query_type: "public",
              query_string: linkedin,
              condition: {
                query_mode: 'match_phrase',
                time_range: '无',
                time_range_begin: 0,
                time_range_end: 0,
              }
            },
            msg: {
              data_range_index_name: "social_platform_information_prefix_linkedin__",
            },
          },
        ],
        (res) => {
          console.log("res", res);
          if (!res?.hits?.hits?.length) return;
          res.hits.hits.forEach(item => {
            state.linkedinSearchList.push(item)
          });
        }
      );
    },

    // 通过邮箱进行收信第一层搜索
    searchRecipientMail(state, mail) {
      console.log("mail", mail);
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              size: 20
            },
            msg: {
              type: 'public',
              path: state.mailRecipientPath,
              prefix: "",
              relation: "",
            },
          },
        ],
        (res) => {
          console.log("res-mail", res);
          if (!res?.length) return;
          window.main.$store.commit("relationsshipTopologyDiagram/searchRecipientMailRelation", {row:res[0]?.row, mail: mail});
        }
      );
    },

    // 通过邮箱进行收信第二层搜索
    searchRecipientMailRelation(state, mailInfo) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              row_key: [],
              size: 20
            },
            msg: {
              type: 'public',
              path: state.mailRecipientPath,
              prefix: "",
              relation: mailInfo.mail,
            },
          },
        ],
        (res) => {
          console.log("res-mail-relation", res);
          if (!res?.length) return;
          res.forEach(item => { 
            state.mailRecipientSearchList.push(item.columnValues?.d?._._);
          });
        }
      );
    },

    // 通过邮箱进行发信第一层搜索
    searchSenderMail(state, mail) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              size: 20
            },
            msg: {
              type: 'public',
              path: state.mailSenderPath,
              prefix: "",
              relation: "",
            },
          },
        ],
        (res) => {
          console.log("res-mail-sender", res);
          if (!res?.length) return;
          window.main.$store.commit("relationsshipTopologyDiagram/searchSenderMailRelation", {row:res[0]?.row, mail: mail});
        }
      );
    },

    // 通过邮箱进行发信第二层搜索
    searchSenderMailRelation(state, mailInfo) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              row_key: [],
              size: 20
            },
            msg: {
              type: 'public',
              path: state.mailSenderPath,
              prefix: "",
              relation: mailInfo.mail,
            },
          },
        ],
        (res) => {
          console.log("res-mail-sender-relation", res);
          if (!res?.length) return;
          res.forEach(item => { 
            state.mailSenderSearchList.push(item.columnValues?.d?._._);
          });
        }
      );
    },

    // 通过电话号码进行搜索
    searchPhone(state, phone) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              family: ['r'],
              row_key: [],
              size: 20
            },
            msg: {
              type: 'public',
              path: state.phoneInfoPath,
              prefixe: phone,
              relation: '',
            },
          },
        ],
        (res) => {
          console.log("res", res);
        }
      );
    },

    // 通过身份证号码进行搜索
    searchIdentity(state, identity) {
      window.main.$main_socket.sendData(
        'Api.Search.SearchPrefix.Query',
        [
          {
            head: {
              family: ['r'],
              row_key: [],
              size: 20
            },
            msg: {
              type: 'public',
              path: state.identityInfoPath,
              prefixe: identity,
              relation: '',
            },
          },
        ],
        (res) => {
          console.log("res", res);
        }
      );
    },

    // 更新Twitter用户的followers数据
    updateTwitterFollowers(state, { userId, followers }) {
      const userIndex = state.twitterSearchList.findIndex(item => item.user_id === userId);
      if (userIndex !== -1) {
        if (window.main && window.main.$set) {
          window.main.$set(state.twitterSearchList[userIndex], 'followers', followers);
        } else {
          // 降级方案：直接赋值并触发更新
          state.twitterSearchList[userIndex].followers = followers;
          // 强制触发响应式更新
          state.twitterSearchList = [...state.twitterSearchList];
        }
        console.log(`Updated followers for user ${userId}:`, followers);
      }
    },

    // 更新Twitter用户的following数据
    updateTwitterFollowing(state, { userId, following }) {
      const userIndex = state.twitterSearchList.findIndex(item => item.user_id === userId);
      if (userIndex !== -1) {
        if (window.main && window.main.$set) {
          window.main.$set(state.twitterSearchList[userIndex], 'following', following);
        } else {
          // 降级方案：直接赋值并触发更新
          state.twitterSearchList[userIndex].following = following;
          // 强制触发响应式更新
          state.twitterSearchList = [...state.twitterSearchList];
        }
        console.log(`Updated following for user ${userId}:`, following);
      }
    },
  }
};