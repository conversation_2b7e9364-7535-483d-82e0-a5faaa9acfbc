<template>
  <div class="personnel-topological-relationship">
    <div class="personnel-topological-relationship-content">
      <div id="topological-chart" style="width: 100%; height: 60vh"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: "PersonnelTopologicalRelationship",
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chart: null,
      nodeData: [], // 节点数据
      linksData: [], // 边数据
      categoriesData: [
        { name: "人物", itemStyle: { color: "#ff7f50" } },
        { name: "组织", itemStyle: { color: "#87cefa" } },
      ], // 节点类别数据
      processNodes: new Set(), // 用于去重
    };
  },
  created() {
    console.log("info", this.info);
    // 添加空值检查，确保info对象存在且有必要的属性
    if (this.info && this.info._source && this.info._source.params) {
      this.parseEntity(this.info, "person");
    } else {
      console.warn("无效的info数据结构，无法解析实体");
    }
  },
  mounted() {
    // 延迟初始化图表，确保DOM已完全渲染
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    // 销毁图表实例，避免内存泄漏
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      try {
        let id = document.getElementById("topological-chart");
        if (!id) {
          console.error("找不到图表容器元素");
          return;
        }
        
        // 确保echarts实例正确创建
        if (this.$echarts) {
          this.chart = this.$echarts.init(id);
        } else {
          console.error("echarts实例不可用");
          return;
        }
        
        this.chart.showLoading();
        
        // 设置图表选项
        const option = {
          title: {
            text: "人物关系网络图",
          },
          tooltip: {
            formatter: function (params) {
              if (params.dataType === "node") {
                const data = params.data.value;
                let tip = `<strong>${params.name}</strong> (${params.seriesName})<br/>`;
                if (data) {
                  for (const key in data) {
                    if (typeof data[key] === "string" && data[key]) {
                      tip += `${key}: ${data[key]}<br/>`;
                    }
                  }
                }
                return tip;
              }
              return `${params.data.source} -> ${params.data.target}<br/>关系: ${params.data.value}`;
            },
          },
          legend: [
            {
              data: this.categoriesData.map((item) => item.name),
            },
          ],
          animationDuration: 1500,
          animationEasingUpdate: "quinticInOut",
          series: [
            {
              name: "关系图",
              type: "graph",
              layout: "force", // 使用力导向布局
              legendHoverLink: false,
              roam: true,
              // 节点上文字的显示
              label: {
                position: "right",
                show: true,
                formatter: "{b}",
              },
              // 边
              lineStyle: {
                color: "source",
                curveness: 0.3,
                opacity: 0.5
              },
              // 修改高亮设置，避免使用可能导致问题的adjacency
              emphasis: {
                scale: true,
                lineStyle: {
                  width: 5,
                },
              },
              // 节点上文字的显示
              labelLayout: {
                hideOverlap: true,
              },
              // 节点缩放限制
              scaleLimit: {
                min: 0.4,
                max: 1,
              },
              // 力导向布局参数
              force: {
                repulsion: 500, // 节点之间的斥力
                gravity: 0.05, // 节点之间的引力
                edgeLength: [100, 300], // 边长
                layoutAnimation: true, // 布局动画
                preventOverlap: true, // 防止节点重叠
              },
              // 边上文字的显示
              edgeLabel: {
                show: true,
                formatter: function (params) {
                  return params.data.value;
                },
              },
              // 节点
              data: this.nodeData,
              // 边
              links: this.linksData,
              // 节点类别
              categories: this.categoriesData,
            },
          ],
        };
        
        // 安全地设置选项
        this.chart.setOption(option);
        this.chart.hideLoading();
      } catch (error) {
        console.error("初始化图表时出错:", error);
      }
    },

    /**
     * 递归解析实体和关系
     * @param entityData - 当前实体的数据对象
     * @param entityType - 实体类型（"person" 或 "organization"）
     */
    parseEntity(entityData, entityType) {
      if (!entityData || !entityData._source?.params?.basic)
        return;
        
      const basicMsg = entityData._source.params.basic;
      const rootName = basicMsg.name;
      const rootDetails = basicMsg;
      let rootCategoryIndex = entityType === "person" ? 0 : 1;

      // 添加根节点
      if (!this.processNodes.has(rootName)) {
        this.nodeData.push({
          id: rootName,
          name: rootName,
          category: rootCategoryIndex,
          symbolSize: 70, // 让根节点大一点
          value: rootDetails,
        });
        this.processNodes.add(rootName);
      }

      // 遍历根节点的第一层关系
      const relations = basicMsg.relation || [];
      if (relations.length > 0) {
        relations.forEach((rel) => {
          // 确保intellValue存在且有效
          if (!rel || !rel.intellValue || !Array.isArray(rel.intellValue) || rel.intellValue.length < 2) {
            console.warn("无效的关系数据:", rel);
            return;
          }
          
          const targetType = rel.intellValue[0];
          const targetName = rel.intellValue[1];
          const linkValue = rel.personRelation || rel.organizationRelation || "关联";

          // 确保目标节点有名字
          if (!targetName) {
            console.warn("关系目标名称为空");
            return;
          }

          // 添加目标节点
          if (!this.processNodes.has(targetName)) {
            const targetData = rel.personData || rel.organizationData;
            const targetCategoryIndex = targetType === "目标人" ? 0 : 1;
            // 从关系数据中提取目标节点的详细信息
            const targetDetails = targetData?.columnValues?.d?.basicMsg
              ?.basicMsg || { name: targetName };

            this.nodeData.push({
              id: targetName,
              name: targetName,
              category: targetCategoryIndex,
              symbolSize: 30,
              value: targetDetails,
            });
            this.processNodes.add(targetName);
          }

          // 添加连接根节点和目标节点的边
          this.linksData.push({
            source: rootName,
            target: targetName,
            value: linkValue,
            name: linkValue // 确保同时设置value和name属性
          });
        });
      }
      console.log("节点数据:", this.nodeData.length, "边数据:", this.linksData.length);
    },
  },
};
</script>
<style lang="scss" scoped>
.personnel-topological-relationship {
  width: 100%;
  height: 100%;

  .personnel-topological-relationship-content {
    width: 100%;
    height: 100%;
  }
}
</style>
