export default {
  namespaced: true,
  state: {
    from: 0,
    size: 200,
    total: 0,
    relevantPersonList: [],
  },
  mutations: {
    resetData(state) {
      state.relevantPersonList = [];
      state.size = 200;
    },

    // 获取目标人数据
    getPerson(state) {
      let person_origanization = JSON.parse(window.main.$route.query.data)
      if (!person_origanization._source.params.hasOwnProperty('relation') || person_origanization._source.params.relation[0] === undefined) {
        return;
      }
      let personRelationIds = JSON.parse(person_origanization._source.params.relation[0]).person
      let add_es_query_conditions = {
        bool: {
          should: personRelationIds.map(account => ({
            term: { _id: account}
          }))
        },
      };
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: state.from,
              size: state.size,
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions,
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        "relevantPerson/setPersonList"
      );
    },

    setPersonList(state, data) {
      console.log("获取目标人数据", data);
      if (data?.hits?.hits?.length) {
        state.relevantPersonList = data.hits.hits.map((item) => {
          const newItem = { ...item };
          // 标准字段
          const standardFields = [
            "type",
            "avatar",
            "name",
            "remark",
            "sex",
            "age",
            "phoneNumbers",
            "identity",
            "email",
            "dateBirth",
            "relation",
            "twitterIds",
            "facebookIds",
            "linkedInIds",
            "telegramIds",
            "basic",
          ];
          // 1. 处理params为对象的情况
          if (
            newItem._source &&
            typeof newItem._source.params === "object" &&
            !Array.isArray(newItem._source.params)
          ) {
            const p = newItem._source.params || {};
            // 组装basic对象
            const basicObj = {};
            standardFields.forEach((field) => {
              if (field === "basic") return; // basic后面单独处理
              if (Array.isArray(p[field])) {
                basicObj[field === "phoneNumbers" ? "phone" : field] =
                  p[field][0] || "";
              } else if (
                typeof p[field] === "string" ||
                typeof p[field] === "number"
              ) {
                basicObj[field === "phoneNumbers" ? "phone" : field] = p[field];
              }
            });
            // 合并basic字段
            if (p.basic && typeof p.basic === "object") {
              Object.assign(basicObj, p.basic);
            }
            // 组装customFields
            const customFields = {};
            Object.keys(p).forEach((key) => {
              if (!standardFields.includes(key)) {
                // 只处理非标准字段
                customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
              }
            });
            // 组装media对象
            const buildMedia = (p) => {
              // 兼容后端可能的字段名
              const platforms = [
                { key: "telegram", idKey: "telegramIds" },
                { key: "twitter", idKey: "twitterIds" },
                { key: "facebook", idKey: "facebookIds" },
                { key: "linkedin", idKey: "linkedInIds" },
              ];
              const media = {};
              platforms.forEach(({ key, idKey }) => {
                const arr = Array.isArray(p[idKey])
                  ? p[idKey]
                  : p[idKey]
                  ? [p[idKey]]
                  : [];
                // 组装为对象数组，结构与PersonDetails.vue一致
                media[key] = arr.map((id) => ({
                  idNum: id,
                  name: id,
                  // 可扩展更多字段
                }));
              });
              return media;
            };
            // 处理关系数据
            let relationData = { person: [], organization: [] };
            if (p.relation && Array.isArray(p.relation)) {
              p.relation.forEach((relationStr) => {
                try {
                  const parsed = JSON.parse(relationStr);
                  if (parsed.person && Array.isArray(parsed.person)) {
                    relationData.person = [
                      ...relationData.person,
                      ...parsed.person,
                    ];
                  }
                  if (
                    parsed.organization &&
                    Array.isArray(parsed.organization)
                  ) {
                    relationData.organization = [
                      ...relationData.organization,
                      ...parsed.organization,
                    ];
                  }
                } catch (e) {
                  console.warn("解析关系数据失败:", relationStr, e);
                }
              });
            }

            // 组装params
            newItem._source.params = {
              ...p,
              basic: basicObj,
              customFields,
              media: buildMedia(p),
              relation: relationData,
            };
          }
          // 2. 处理params为数组的情况（兼容旧数据）
          else if (newItem._source && Array.isArray(newItem._source.params)) {
            // 保持原逻辑不变
            const paramsObj = {};
            newItem._source.params.forEach((param) => {
              paramsObj[param.k] = param.v;
            });
            const basicObj = {
              id: newItem._id,
              name: Array.isArray(newItem._source.name)
                ? newItem._source.name[0]
                : newItem._source.name || "",
              remark: Array.isArray(paramsObj.remark)
                ? paramsObj.remark[0]
                : paramsObj.remark || "",
              sex: Array.isArray(paramsObj.sex)
                ? paramsObj.sex[0]
                : paramsObj.sex || "",
              age: paramsObj.age
                ? parseInt(
                    Array.isArray(paramsObj.age)
                      ? paramsObj.age[0]
                      : paramsObj.age
                  )
                : 0,
              phone: Array.isArray(paramsObj.phoneNumbers)
                ? paramsObj.phoneNumbers[0]
                : paramsObj.phoneNumbers || "",
              identity: Array.isArray(paramsObj.identity)
                ? paramsObj.identity[0]
                : paramsObj.identity || "",
              email: Array.isArray(paramsObj.email)
                ? paramsObj.email[0]
                : paramsObj.email || "",
              avatar: Array.isArray(paramsObj.avatar)
                ? paramsObj.avatar[0]
                : paramsObj.avatar || "",
              dateBirth: Array.isArray(paramsObj.dateBirth)
                ? paramsObj.dateBirth[0]
                : paramsObj.dateBirth || "",
            };
            // 组装customFields
            const customFields = {};
            Object.keys(paramsObj).forEach((key) => {
              if (!standardFields.includes(key)) {
                customFields[key] = Array.isArray(paramsObj[key])
                  ? paramsObj[key][0]
                  : paramsObj[key];
              }
            });
            // 组装media对象
            const buildMedia = (p) => {
              // 兼容后端可能的字段名
              const platforms = [
                { key: "telegram", idKey: "telegramIds" },
                { key: "twitter", idKey: "twitterIds" },
                { key: "facebook", idKey: "facebookIds" },
                { key: "linkedin", idKey: "linkedInIds" },
              ];
              const media = {};
              platforms.forEach(({ key, idKey }) => {
                const arr = Array.isArray(p[idKey])
                  ? p[idKey]
                  : p[idKey]
                  ? [p[idKey]]
                  : [];
                // 组装为对象数组，结构与PersonDetails.vue一致
                media[key] = arr.map((id) => ({
                  idNum: id,
                  name: id,
                  // 可扩展更多字段
                }));
              });
              return media;
            };
            newItem._source.params = {
              ...paramsObj,
              basic: basicObj,
              customFields,
              media: buildMedia(paramsObj),
            };
          }
          return newItem;
        });
        console.log("relevantPersonList:", state.relevantPersonList);
      } else {
        state.relevantPersonList = [];
      }
      state.total = data?.hits?.total?.value;
    },
  },
};
