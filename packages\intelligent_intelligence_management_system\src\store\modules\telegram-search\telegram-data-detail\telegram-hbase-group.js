export default {
  namespaced: true,
  state: {
    groupInfo: null,
    groupMsgList: [],
    groupMsgNextRow: "",
    groupMsgLasttRow: "",
    msgIconList: [],
    groupDetailed: [],
    groupMember: [],
    groupMemberRow: [],
    nowCheckMsgRow: "",
  },
  mutations: {
    // 清除群消息表
    clearGroupMsg(state, v) {
      state.groupMsgList = [];
      state.groupMsgNextRow = "";
      state.msgIconList = [];
    },
    setGroupMsg(state, res) {
      console.log("setGroupMsg:", res, state.nowCheckMsgRow);
      
      let userIdArr = [];
      res?.forEach((item) => {
        for (const key in item.columnValues.d) {
          if (key.startsWith("user_id")) {
            userIdArr.push(item.columnValues.d[key].user_id);
          }
        }
      });
      window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendMsgIcon", userIdArr);
      if (!res?.length && state.groupMsgList.length) {
        window.main.$message.warning("没有消息了！");
        return;
      }
      state.groupMsgNextRow = res[res.length - 1]?.row;
      if (!state.nowCheckMsgRow) {
        state.nowCheckMsgRow = res[0]?.row;
      }
      if (state.groupMsgList) {
        state.groupMsgList.push(...res);
      } else {
        state.groupMsgList = res;
      }
    },
    setGroupTopMsg(state, res) {
      let userIdArr = [];
      res?.forEach((item) => {
        for (const key in item.columnValues.d) {
          if (key.startsWith("user_id")) {
            userIdArr.push(item.columnValues.d[key].user_id);
          }
        }
      });
      window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendMsgIcon", userIdArr);
      if (!res?.length && state.groupMsgList.length) {
        window.main.$message.warning("没有消息了！");
        return;
      }
      state.groupMsgLasttRow = res[res.length - 1]?.row;
      if (state.groupMsgList) {
        state.groupMsgList.unshift(...res.reverse());
      } else {
        state.groupMsgList = res.reverse();
      }
    },
    setTimeMsg(state, v) {
      state.nowCheckMsgRow = "";
      if (v) {
        state.groupInfo = v._source;
      }
      const sha512 = require("sha512");
      const rowNum = BigInt(2 ** 63) - BigInt(state.groupInfo["timestamp"]);
      state.groupMsgNextRow =
        sha512(
          "p;/instant_msg/" +
            state.groupInfo.type +
            "/group_id;" +
            state.groupInfo.group_id +
            ";msg"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.groupInfo.type +
        "/group_id;" +
        state.groupInfo.group_id +
        ";msg;" +
        rowNum.toString();
      state.groupMsgLasttRow = state.groupMsgNextRow;
      window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroupMsg");
      window.main.$store.dispatch("telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/sendGroupTopMsg");
    },
    // 存储获取到的头像
    setMsgIcon(state, res) {
      if (state.msgIconList) {
        state.msgIconList.push(...res);
      } else {
        state.msgIconList = res;
      }
    },
    // 存储群组详细信息
    setGroup(state, res) {
      state.groupDetailed = res;
    },
    // 清除群成员
    clearGroupMember(state) {
      state.groupMember = [];
      state.groupMemberRow = [];
    },
    // 存储群组群成员
    setGroupMember(state, res) {
      state.groupMemberRow = [res[res.length - 1].row];
      if (state.groupMember) {
        state.groupMember.push(...res);
      } else {
        state.groupMember = res;
      }
    },
  },
  actions: {
    // 获取消息
    sendGroupMsg({ state, dispatch, commit }, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [state.groupMsgNextRow],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.groupInfo.type + "/group_id",
              relation: state.groupInfo.group_id + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setGroupMsg"
      );
    },
    // 获取上面的消息
    sendGroupTopMsg({ state }) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: [state.groupMsgLasttRow],
              reversed: true,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.groupInfo.type + "/group_id",
              relation: state.groupInfo.group_id + ";msg",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setGroupTopMsg"
      );
    },
    //获取群消息的头像和用户名
    sendMsgIcon({ state, dispatch, commit }, v) {
      if(!v.length){
        return
      }
      let row_arr = [];
      const sha512 = require("sha512");
      const hash =
        sha512(
          "p;/instant_msg/" +
            state.groupInfo.type +
            "/group_id;" +
            state.groupInfo.group_id +
            ";member"
        ).toString("hex") +
        ";p;/instant_msg/" +
        state.groupInfo.type +
        "/group_id;" +
        state.groupInfo.group_id +
        ";member;";
      v.forEach((item) => {
        row_arr.push(hash + item);
      });
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              size: 20,
              row_key: row_arr,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.groupInfo.type + "/group_id",
              relation: state.groupInfo.group_id + ";member",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setMsgIcon"
      );
    },
    // 获取群信息
    sendGroup({ state, dispatch, commit }, v) {
      state.groupInfo = v._source;
      const sha512 = require("sha512");
      const hash =
        sha512("p;/instant_msg/" + state.groupInfo.type + "/group_id").toString(
          "hex"
        ) +
        ";p;/instant_msg/" +
        state.groupInfo.type +
        "/group_id;" +
        state.groupInfo.group_id;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DetailMulti",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: [hash],
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.groupInfo.type + "/group_id",
              relation: "",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setGroup"
      );
    },
    // 获取群成员
    sendGroupMember({ state, dispatch, commit }, v) {
      if (v) {
        state.groupInfo = v._source;
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              row_key: state.groupMemberRow,
              size: 20,
            },
            msg: {
              type: "public",
              path: "/instant_msg/" + state.groupInfo.type + "/group_id",
              relation: state.groupInfo.group_id + ";member",
              prefix: "",
            },
          },
        ],
        "telegramSearch/telegramSearchDataDetail/telegramHbaseGroup/setGroupMember"
      );
    },
  },
};
