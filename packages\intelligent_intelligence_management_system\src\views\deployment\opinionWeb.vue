<template>
  <div class="intellManage">
    <!-- 网站详情表 -->
    <el-table :data="dataList" style="width: 80%; margin: 0 auto">
      <el-table-column label="国家">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{ scope.row.country }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网站名称">
        <template slot-scope="scope">
          <span>{{ scope.row.type }}</span>
        </template>
      </el-table-column>
      <el-table-column label="舆情地址">
        <template slot-scope="scope">
          <span>{{ scope.row.params.href }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间">
        <template slot-scope="scope">
          <span>{{ scope.row.create_time }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {};
  },
  watch: {},
  computed: {
    ...mapState({
      dataList: (state) => state.deployment.public_opinion.dataList,
    }),
  },
  created() {
    this.loadData();
  },
  mounted() {},
  methods: {
    // 加载数据
    async loadData() {
      try {
        const newData = await this.$store.dispatch(
          "deployment/public_opinion/getNodeList"
        );
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
  },
};
</script>
<style scoped>
.intellManage {
  width: 100%;
  overflow: auto;
  height: 100%;
}
</style>
