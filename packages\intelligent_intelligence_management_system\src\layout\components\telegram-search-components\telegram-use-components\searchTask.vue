<template>
  <div class="model_lay">
    <el-tabs
      type="border-card"
      tab-position="left"
      style="height: 82vh"
      v-model="activeName"
      @tab-click="changeTabClick"
    >
      <el-tab-pane
        v-for="item in analysisState"
        :label="item.label"
        :name="item.value"
        :key="item.label"
      >
        <el-table :data="analysisTaskList">
          <el-table-column
            prop="columnValues.info.title"
            label="任务名称"
            width="200px"
          >
          </el-table-column>
          <el-table-column
            prop="columnValues.info.status"
            label="状态"
            width="200px"
          >
            <template slot-scope="scope">
              {{ $t("analysis." + scope.row.columnValues.info.status) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="columnValues.info.method"
            label="类型"
            width="200px"
          >
          </el-table-column>
          <el-table-column label="进度条" width="500px">
            <template
              slot-scope="scope"
              v-if="
                scope.row.columnValues.info.all_num_received &&
                scope.row.columnValues.info.all_num
              "
            >
              <el-progress
                :text-inside="true"
                :stroke-width="20"
                :percentage="
                  Math.round(
                    (Number(scope.row.columnValues.info.all_num_received) /
                      Number(scope.row.columnValues.info.all_num)) *
                      10000
                  ) / 100
                "
              >
              </el-progress>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="danger"
                style="margin-left: 10px"
                @click="handleDelete(scope.row.row)"
                >删除</el-button
              >
              <el-button
                type="info"
                size="small"
                style="margin-left: 10px"
                @click="handleLog(scope.row.row)"
                >日志</el-button
              >
              <el-button
                type="success"
                size="small"
                style="margin-left: 10px"
                @click="handleSee(scope.row)"
                v-if="scope.row.columnValues.info.status == 'parse_end'"
                >查看</el-button
              >
              <el-dropdown style="margin-left: 10px" @command="handleCommand">
                <el-button type="primary" size="mini">
                  分析选项<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="beforeHandleCommand('parse_pause', scope.row.row)"
                    v-if="
                      scope.row.columnValues.info.status != 'parse_end' &&
                      scope.row.columnValues.info.status != 'error'
                    "
                    >停止分析</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="beforeHandleCommand('parse_ready', scope.row.row)"
                    >重新分析</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="日志" :visible.sync="logdialogVisible" width="80%">
      <el-table
        :data="loglist"
        style="width: 100%"
        v-el-table-infinite-scroll="logsLoad"
      >
        <el-table-column prop="timestamp" label="时间"></el-table-column>
        <el-table-column prop="info" label="日志信息"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeName: "parse_end,error",
      analysisState: [
        {
          value: "parse_end,error",
          label: "已完成",
        },
        {
          value: "parse_ready,parse_pause",
          label: "暂停中",
        },
        {
          value: "initializing,parsing",
          label: "分析中",
        },
      ],
      logdialogVisible: false,
      multipleSelection: [],
      nowCheckLogRow: "",
      timer: null,
    };
  },
  created() {
    this.getTasklist(this.activeName);
  },
  watch: {
    // activeName: {
    //   //深度监听，可监听到对象、数组的变化
    //   handler(newVal, oldVal) {
    //     clearInterval(this.timer);
    //     if (newVal === "parse_ready,parse_pause") {
    //       this.timer = setInterval(() => {
    //         this.$store.commit("searchTask/getUpdateTask");
    //       }, 3000);
    //     }
    //   },
    // },
  },
  computed: {
    loglist() {
      return this.$store.state.searchTask.logList;
    },
    analysisTaskList() {
      return this.$store.state.searchTask.analysisTaskList;
    },
  },
  methods: {
    // tab标签切换事件
    changeTabClick(data) {
      this.getTasklist(data.name);
      clearInterval(this.timer);
      if (data.name !== "parse_end,error") {
        this.timer = setInterval(() => {
          this.getTasklist(data.name);
        }, 3000);
      }
    },
    // 获取任务列表
    getTasklist(name) {
      let active_name = name.split(",");
      this.$store.commit("searchTask/clearAnalysisTaskList");
      this.$store.commit("searchTask/getAnalysisTask", active_name);
    },
    // 查看日志
    handleLog(row) {
      this.nowCheckLogRow = row;
      this.logdialogVisible = true;
      this.$store.commit("searchTask/clearLogList");
      this.$store.commit("searchTask/getTaskLog", row);
    },
    /**日志下拉加载 */
    logsLoad() {
      this.$store.commit("searchTask/getTaskLog", this.nowCheckLogRow);
    },
    /**查看 */
    handleSee(row) {
      clearInterval(this.$store.state.timer);
      this.$store.commit("statistics/senddataType", row);
      this.$store.commit("statistics/sendPublicTemplate");
      this.$store.commit("statistics/sendUserTemplate");
    },
    // 删除任务
    handleDelete(row) {
      this.$confirm("此操作将永久删除该任务, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.commit("searchTask/senddel", row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleCommand(command) {
      switch (command.command) {
        case "parse_ready":
          this.$store.commit("searchTask/sendSetStatus", command);
          break;
        case "parse_pause":
          this.$store.commit("searchTask/sendSetStatus", command);
          break;
      }
    },
    beforeHandleCommand(item, row) {
      return {
        command: item,
        row: row,
      };
    },
  },
};
</script>

<style src="../../../../assets/css/analysis.css" scoped></style>
