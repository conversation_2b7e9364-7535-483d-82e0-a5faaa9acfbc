module.exports = {
  email: "邮箱",
  username: "用户名",
  password: "密码",
  group_name: "群组名",
  nickname: "昵称",
  "@authority": "权限",
  "@username": "用户名",
  all_num: "总分片",
  all_num_received: "已抓取",
  status: "状态",
  task_type: "任务类型",
  title: "任务名称",
  create_timestamp: "创建时间",
  update_timestamp: "更新时间",
  all_num_received_sync: "已同步抓取",
  method: "方法",
  collision_task: "碰撞任务",
  parse_end: "已完成",
  spider_end: "已完成",
  parse_dbfile_task: "数据库分析入库",
  parse_document_file_task: "文档分析任务",
  search_task: "查询任务",
  social_platform_task: "公开社交媒体任务",
  spider_task: "爬虫任务",
  parse_ready: "未分析",
  initializing: "初始化中",
  parsing: "分析中",
  parse_pause: "已停止",
  error: "失败",
  spider_ready: "未爬取",
  spider_running: "爬取中",
  spider_pause: "已停止",
  twitter_person: "推特目标人",
  twitter_article: "推特文章",
  elasticsearch: "elasticsearch",
  hdfs: "hdfs",
  social_platform_twitter_user_information: "推特用户信息",
  social_platform_article_active_chart: "文章发表",
  social_platform_twitter_person_relation_friends_information_score_list:
    "推特好友关系评分",
  query_type: "类型",
  tag: "标签",
  user_id: "用户ID",
  search_task_overview: "查询任务总览",
  search_task_count_chart: "查询数据在库中数量统计",
  search_task_score_chart: "查询数据权重统计",
  username: "私有库",
  authority: "权限库",
  public: "公共库",
  collision_key_score_chart: "碰撞任务评分统计",
  collision_key_parm: "碰撞任务总览",
  collision_key_count_chart: "碰撞任务数量统计",
  collision_twitter_person_relation_chart_friends_information_count_list:
    "相关联推特用户出现次数统计",
  collision_twitter_person_relation_chart_friends_information_score_list:
    "相关联推特用户活跃权重统计",
  social_platform_twitter_content_article_information: "公开媒体社交推文",
  social_platform_target_article_active_chart: "目标推文数据",
  social_platform_twitter_content_article_relation_friends_information_score_list:
    "公开媒体社交好友权重",
  查询任务: "search_task",
  case_info: "案件信息",
  case_id: "案件ID",
  approval: "审批",
};
