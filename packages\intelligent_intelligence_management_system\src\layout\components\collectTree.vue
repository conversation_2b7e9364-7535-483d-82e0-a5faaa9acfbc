<template>
  <div class="collectTree">
    <div class="catalogue">
      <div class="directory"><i class="el-icon-tickets"></i>收藏目录</div>
      <div style="padding-top: 15px; height: 95%">
        <el-tree
          :data="dataTree"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          @node-click="hadleClickNode"
          style="height: 100%; overflow-y: auto"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span v-if="!data.showInput"
              ><i :class="node.data.icon"></i> {{ node.label }}</span
            >
            <el-input
              size="mini"
              ref="inputVal"
              v-if="data.showInput"
              :value="data.label"
              @mousedown.native.prevent.stop
              @click.native.prevent.stop
              @focus.native.prevent.stop
              @focus="(e) => focus(e)"
              @blur="(e) => alters(node, data)"
              @input="(value) => inp(value, data)"
            ></el-input>
            <span>
              <el-button
                v-show="node.data.icon === 'el-icon-folder'"
                size="mini"
                type="text"
                @click.stop="() => addDir(node, data)"
                @keydown.enter.native.prevent="fun2"
                @keyup.space.native.prevent="fun2"
                title="添加目录"
                ><i class="el-icon-folder-add"></i
              ></el-button>
              <el-button
                v-show="node.data.icon === 'el-icon-folder'"
                size="mini"
                type="text"
                @click.stop="() => addfile(data)"
                @keydown.enter.native.prevent="fun2"
                @keyup.space.native.prevent="fun2"
                title="添加文件"
                ><i class="el-icon-document-add"></i
              ></el-button>
              <el-button
                v-show="node.label !== '/'"
                size="mini"
                type="text"
                @click.stop="() => amendName(data)"
                title="修改名字"
                ><i class="el-icon-edit-outline"></i
              ></el-button>
              <el-button
                v-show="node.label !== '/' && !data.children.length"
                size="mini"
                type="text"
                @click.stop="() => remove(node, data)"
                title="删除"
                ><i class="el-icon-delete"></i
              ></el-button>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "collectTree",
  props: {
    listType: {
      type: String,
      required: true,
    },
    tableName: {
      type: String,
      required: true,
    },
    getCollect: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      dataTree: [
        {
          id: 0,
          label: "/",
          icon: "el-icon-folder",
          showInput: false,
          children: [],
        },
      ],
    };
  },
  methods: {
    // 删除某一节点
    remove(node, data) {
      this.$confirm("此操作将永久删除该节点, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let type = data.icon === "el-icon-tickets" ? "file" : "dir";
          this.sendDelNode({
            fatherId: node.parent.data.id,
            row: data.row,
            type: type,
          });
          const parent = node.parent;
          const children = parent.data.children || parent.data;
          const index = children.findIndex((d) => d.id === data.id);
          children.splice(index, 1);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    sendDelNode(data) {
      this.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DelData",
        [
          {
            head: {
              row_key: [data.row],
            },
            msg: {
              type: this.listType,
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              table: this.tableName,
              relation: data.fatherId ? `${data.fatherId};${data.type}` : "",
            },
          },
        ],
        (res) => {
          if (res?.status === "ok") {
            window.main.$message.success("删除成功!");
          } else {
            window.main.$message.warning("删除失败!");
          }
        }
      );
    },
    //失去焦点后
    alters(node, data) {
      data.showInput = !data.showInput;
      // 发送修改请求
      let type = data.icon === "el-icon-tickets" ? "file" : "dir";
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            msg: {
              type: this.listType,
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              table: this.tableName,
              prefix: data.id,
              relation: node.parent.data.id
                ? `${node.parent.data.id};${type}`
                : "",

              data: {
                data: {
                  otherSystem: "intelligent",
                  name: {
                    name: this.nodeName,
                  },
                  _: {
                    _: data.id,
                  },
                },
              },
            },
          },
        ],
        (res) => {
          if (res?.status === "ok") {
            window.main.$message.success("修改成功!");
          } else {
            window.main.$message.warning("修改失败!");
          }
        }
      );
    },
    //修改功能
    inp(value, data) {
      this.nodeName = value;
      data.label = value;
    },
    focus(event) {
      event.currentTarget.select();
    },
    // 点击获取目录表
    hadleClickNode(data) {
      if (data.children.length) {
        return;
      }
      if (data.icon === "el-icon-folder") {
        console.log("否则如果");
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",
          [
            {
              head: {
                size: 200,
              },
              msg: {
                table: this.tableName,
                prefix: "",
                type: this.listType,
                relation: data.id + ";dir",
              },
            },
          ],
          (res) => {
            console.log("收藏目录点击获取文件夹", res);
            res?.forEach((item) => {
              data.children.push({
                id: item.columnValues.d._._,
                label: item.columnValues.d.name.name,
                row: item.row,
                showInput: false,
                icon:
                  item.columnValues.d.type.type === "dir"
                    ? "el-icon-folder"
                    : "el-icon-tickets",
                children: [],
              });
            });
          }
        );
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",
          [
            {
              head: {
                size: 200,
              },
              msg: {
                table: this.tableName,
                prefix: "",
                type: this.listType,
                relation: data.id + ";file",
              },
            },
          ],
          (res) => {
            console.log("收藏目录点击获取文件", res);
            res?.forEach((item) => {
              data.children.push({
                id: item.columnValues.d._._,
                label: item.columnValues.d.name.name,
                row: item.row,
                showInput: false,
                icon:
                  item.columnValues.d.type.type === "dir"
                    ? "el-icon-folder"
                    : "el-icon-tickets",
                children: [],
              });
            });
          }
        );
      } else {
        this.getCollect(data);
      }
    },
    // 获取目录表
    sendDirList(v) {
      this.dataTree = [
        {
          id: 0,
          label: "/",
          icon: "el-icon-folder",
          showInput: false,
          children: [],
        },
      ];
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: 200,
            },
            msg: {
              table: this.tableName,
              prefix: "",
              type: v,
            },
          },
        ],
        (res) => {
          console.log("收藏目录res", res);
          res?.forEach((item) => {
            if (
              item.columnValues.d.otherSystem &&
              item.columnValues.d.otherSystem === "intelligent"
            ) {
              this.dataTree[0].children.push({
                id: item.columnValues.d._._,
                label: item.columnValues.d.name.name,
                row: item.row,
                showInput: false,
                icon:
                  item.columnValues.d.type.type === "dir"
                    ? "el-icon-folder"
                    : "el-icon-tickets",
                children: [],
              });
            }
          });
          console.log("收藏目录", this.dataTree);
        }
      );
    },
    addFilePath(v) {
      console.log("addFilePath", v);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            msg: {
              type: this.listType,
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              table: this.tableName,
              prefix: v.random,

              relation: v.data.id ? `${v.data.id};${v.type}` : "",
              data: {
                data: {
                  otherSystem: "intelligent",
                  name: {
                    name: v.name,
                  },
                  type: {
                    type: v.type,
                  },
                  _: {
                    _: v.random,
                  },
                },
              },
            },
          },
        ],
        (res) => {
          if (res?.status === "ok") {
            window.main.$message.success("添加成功!");
          } else {
            window.main.$message.warning("添加失败!");
          }
        }
      );
    },
    // 添加目录
    addDir(node, data) {
      let newId = this.createRandom();
      this.addFilePath({
        data: data,
        random: newId,
        type: "dir",
        name: "新目录",
      });
      const sha512 = require("sha512");
      let Id = data.id;
      let row = "";
      if (Id === 0) {
        row =
          sha512("p;" + this.tableName).toString("hex") +
          ";p;" +
          this.tableName +
          ";" +
          newId;
      } else {
        row =
          sha512("p;" + this.tableName + ";" + Id + ";dir").toString("hex") +
          ";p;" +
          this.tableName +
          ";" +
          Id +
          ";dir;" +
          newId;
      }
      const newChild = {
        id: newId,
        label: "新目录",
        row: row,
        showInput: false,
        icon: "el-icon-folder",
        children: [],
      };
      if (!data.children) {
        this.$set(data, "children", []);
      }
      data.children.push(newChild);
    },
    // 添加文件
    addfile(data) {
      console.log("addfile", data);

      let newId = this.createRandom();
      this.addFilePath({
        data: data,
        random: newId,
        type: "file",
        name: "新文件",
      });
      const sha512 = require("sha512");
      let Id = data.id;
      let row = "";
      if (Id === 0) {
        row =
          sha512("p;" + this.tableName).toString("hex") +
          ";p;" +
          this.tableName +
          ";" +
          newId;
      } else {
        row =
          sha512("p;" + this.tableName + ";" + Id + ";file").toString("hex") +
          ";p;" +
          this.tableName +
          ";" +
          Id +
          ";file;" +
          newId;
      }
      const newChild = {
        id: newId,
        label: "新文件",
        row: row,
        showInput: false,
        icon: "el-icon-tickets",
        children: [],
      };
      if (!data.children) {
        this.$set(data, "children", []);
      }
      data.children.push(newChild);
    },
    // 生成随机数+当前时间戳
    createRandom() {
      let randomNum = Math.floor(Math.random() * 100000).toString();
      return 2 ** 31 - new Date().getTime() / 1000 + randomNum;
    },
    // 修改名字
    amendName(data) {
      data.showInput = !data.showInput;
      this.$nextTick(() => {
        this.$refs.inputVal.focus(); // 手动触发焦点
      });
    },
  },
  mounted() {
    this.sendDirList(this.listType);
  },
};
</script>
<style lang="scss" scoped>
.collectTree {
  width: 100%;
  height: 100%;
  padding-left: 10px;
}

.collect {
  height: 100%;
  margin: 0 auto;
}

.catalogue {
  width: 100%;
  height: 100%;
  border: 1px solid #ebeef5;
  border-top: 0;
  min-height: 500px;

  .directory {
    height: 4vh;
    padding: 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;

  i {
    font-size: 16px;
  }
}

::v-deep .el-tree-node.is-expanded > .el-tree-node__children {
  padding-top: 5px;
}
</style>
