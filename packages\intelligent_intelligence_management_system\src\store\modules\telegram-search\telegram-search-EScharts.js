export default {
  namespaced: true,
  state: {
    dataRangeGetter: {},
    dataRangeDetail: {},
    dataRangeQueryIndexMaxList: [],
    dataRangeQueryIndexMaxCount: 0,

    searchedTableTotal: 0,
    searchedDataTotal: 0,
    searchedDataTotalSize: 0,

        activeNamePrefix:'',
		// tmpDataListRes: {},
		dataListGetter: {},
		dataListTotal: 0,
		dataList: [],
		from: 0,
		size: 20,
		search: false,
		// 遮罩层定时
		beginBun:0,
		// 遮罩层定时器
		timer: null,
		// 遮罩层
		loading: null,
		// 取消遮罩层的timer
		clearTimer: null,
		// 取消遮罩层定时
		clearBeginBun:0,
		req:false,
		// 是否获取下一个dataRange的数据
		nextFlag: false,
		ESmsgIconList:[],
    },
    mutations: {
        setactiveNamePrefix(state,v){
            state.activeNamePrefix = v
        },
        async setDataRangeTree(state, dataRangeList) {
			// 放到router
			if (window.main.$store.state.userInfo.toPath != '/analysis/searchTasklist') {
				if (state.dataRangeGetter) {
					state.dataRangeGetter.return();
				}
				if (state.dataListGetter) {
					state.dataListGetter.return();
				}
				window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayer')
				// 查询结束了强行关闭遮罩层
				window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayerForceSet')
			}
			if (dataRangeList == null || dataRangeList.length == 0) {
				let tmpObj = state.dataRangeGetter.next([]);
			} else {
				let tmpObj = state.dataRangeGetter.next(dataRangeList)
			}
		},
        async setDataList(state, v) {
			window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayer')
			try {
				if (v == null || v == 'undefined' || !v.hasOwnProperty("hits") || v.hits == null || !v.hits.hasOwnProperty("hits") || v.hits.hits == null) {
					state.req=false
					state.from = 0;
					state.dataListGetter.return();
					state.dataRangeGetter.next("nextDataRangePage")
					return
				}else{state.req=true}
				// 叠加查询总数
				if(v.hits.hits[0]['_index'].indexOf('group_member_data_prefix')>-1){
					state.activeNamePrefix='group_member_data_prefix'
				}
				if(v.hits.hits[0]['_index'].indexOf('group_content_data_prefix')>-1){
					state.activeNamePrefix='group_content_data_prefix'
				}
				state.dataListTotal += v.hits.hits.length;
				// 判断并保存已经取下来的数据
				state.dataList = state.dataList.concat(v.hits.hits);
				// 判断如果还可以取数据，继续取
				if (state.dataList.length < state.size) {
					state.from = 0;
					state.dataListGetter.return();
					state.dataRangeGetter.next("nextDataRangePage")
				}else{
					// 如果是取满size数据了的情况，而且有新数据，强行关闭遮罩层。
					window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayerForceSet')
				}
			} catch (err) {
			}
		},
		clearLoadingLayer(state){
			if (state.clearTimer) {
				state.clearBeginBun = 0;
				return
			}
			state.clearTimer = setInterval(()=>{
				state.clearBeginBun++;
				if (state.clearBeginBun>10) {
					if(state.loading){
						state.loading.close();
						state.loading = null;
						if(document.getElementById('closeMyLoading')!=null){
							document.getElementById('closeMyLoading').remove()
						 }
					}
					if (state.timer) {
						clearInterval(state.timer);
						state.timer = null;
					}
					clearInterval(state.clearTimer);
					state.clearTimer = null;
				}
			}, 300)
		},
		clearLoadingLayerForceSet(state) {
			state.clearBeginBun = 100;
		},
		clearLoadingBun(state){
			state.beginBun = 0
		},
		setLoadingLayer (state) {
			if (state.loading != null || state.timer != null) {
				return
			}
			// 如果重新进入setLoadingLayer了，clearLoadingLayer无论是什么状态都取消掉。
			state.clearBeginBun = 0;
			clearInterval(state.clearTimer);
			state.clearTimer = null;
			state.clearBeginBun = 0;
			state.loading = window.main.$loading({
				target: document.querySelector('.echarts_data_look'),
				lock: true,
				text: '搜索中...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			state.beginBun = 0;
			state.timer=setInterval(()=>{
				state.beginBun++;
				if(state.beginBun>50){
					window.main.$message({
						message: '加载超时',
						type: 'warning'
					});
					state.loading.close()
					if(document.getElementById('closeMyLoading')!=null){
						document.getElementById('closeMyLoading').remove()
					 }
					clearInterval(state.timer)
					state.loading = null
					state.timer = null
				}
			},300)
		},
		clearSearchList(state){
			state.dataListTotal = 0;
			state.dataList = [];
			state.from = 0;
            state.dataRangeQueryIndexMaxList = []
		},
    },
    actions: {
		setDataRangeGetter ({ state, dispatch, commit }, addSeachCondition) {
			let dataRangeList = addSeachCondition.dataRangeList
			function* getter (dataRangeList) {
				if (dataRangeList == null || dataRangeList.length == 0) {
					return
				}
				for(let index=0; index<dataRangeList.length; index++){
					let dataRangeOne
					dataRangeOne = dataRangeList[index]
					let from = 0;
					while (true) {
						if (!dataRangeOne.data_range_type) {
							state.searchedTableTotal += 1;//第一次错误
							state.searchedDataTotal += (Number(dataRangeOne.total)?Number(dataRangeOne.total):0);
							state.searchedDataTotalSize += (Number(dataRangeOne.total_size)?Number(dataRangeOne.total_size):0)
							if((dataRangeOne.data_range_index_name.indexOf(state.activeNamePrefix)>-1)){
									state.dataRangeQueryIndexMaxList.push(dataRangeOne.data_range_index_name)
							}
							state.dataRangeQueryIndexMaxCount += (Number(dataRangeOne.total_size)?Number(dataRangeOne.total_size):0);
							if ((dataRangeList.length < state.size) && (state.dataRangeQueryIndexMaxCount < 50 * 1024 * 1024 * 1024)) {
								if (index >= (dataRangeList.length-1)) {
									let tmpDataRangeDetail = {}
									tmpDataRangeDetail.data_range_index_name = state.dataRangeQueryIndexMaxList.join(',');
									window.main.$store.dispatch('telegramSearch/telegramSearchEScharts/setDataListGetter',
									{tmpDataRangeDetail:tmpDataRangeDetail, addEsQueryConditions:addSeachCondition.addEsQueryConditions})
									yield {res: dataRangeOne};
									state.dataRangeQueryIndexMaxList = [];
									break
								}else{
									break
								}
							}else{
								let tmpDataRangeDetail = {}
									tmpDataRangeDetail.data_range_index_name = state.dataRangeQueryIndexMaxList.join(',');
									window.main.$store.dispatch('telegramSearch/telegramSearchEScharts/setDataListGetter',
									{tmpDataRangeDetail:tmpDataRangeDetail, addEsQueryConditions:addSeachCondition.addEsQueryConditions})
									yield {res: dataRangeOne};
									state.dataRangeQueryIndexMaxList = [];
									break
							}
						}
						let dataRangeDetail
						dataRangeDetail = {
							head: {
								session_id: window.main.$store.state.userInfo.session_id,
								from: from,
								size: state.size,
							},
							control: {
								query_type: 'public',
							},
							msg: {
								data_range_father_path: dataRangeOne.data_range_path,
								data_range_index_prefix: dataRangeOne.data_range_index_prefix,
							},
						};

						state.dataRangeDetail.data_range_father_path=dataRangeOne.data_range_path
						state.dataRangeDetail.data_range_name=dataRangeOne.data_range_name
						window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingBun')
						window.main.$store.commit('telegramSearch/telegramSearchEScharts/setLoadingLayer')
						let nextValue = yield window.main.$main_socket.sendData('Api.Search.DataRange.ListTrue', [dataRangeDetail], 'telegramSearch/telegramSearchEScharts/setDataRangeTree')
						if (nextValue == "nextDataRangePage") {
							from = from + state.size;
							continue
						}
						yield* getter(nextValue)
						if (nextValue == null || nextValue.length < state.size) {
							from = 0;
							break
						} else {
							from = from + state.size;
						}
					}
				}
				return
			};

			state.dataRangeGetter = getter(dataRangeList);
			let nextRes = state.dataRangeGetter.next(dataRangeList)
			if ((nextRes.value === undefined) && nextRes.done) {
				window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayer');
			}
		},
        setDataListGetter ({ state, dispatch, commit }, data) {
			let {tmpDataRangeDetail,addEsQueryConditions} =data
			function* getter (tmpDataRangeDetail) {
					while(true){
					let resData = {
						head: {
							session_id: window.main.$store.state.userInfo.session_id,
							from: state.from,
							size: state.size,
						},
						msg: tmpDataRangeDetail,
						control: addEsQueryConditions
					};
					window.main.$store.commit('telegramSearch/telegramSearchEScharts/setLoadingLayer')
					state.beginBun = 0
					yield window.main.$main_socket.sendData('Api.Search.SearchList.Query', [resData], 'telegramSearch/telegramSearchEScharts/setDataList');
					state.from += state.size
				}
				}
			state.dataListGetter = getter(tmpDataRangeDetail);
			let nextRes = state.dataListGetter.next();
			if ((nextRes.value === undefined) && nextRes.done) {
				window.main.$store.commit('telegramSearch/telegramSearchEScharts/clearLoadingLayer');
			}
		}
    }
}
