@font-face {
  font-family: "iconfont"; /* Project id 4969986 */
  src: url('iconfont.woff2?t=1752111433665') format('woff2'),
       url('iconfont.woff?t=1752111433665') format('woff'),
       url('iconfont.ttf?t=1752111433665') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-baocun:before {
  content: "\e936";
}

.icon-fensi:before {
  content: "\e805";
}

.icon-dianzan_kuai:before {
  content: "\ec8c";
}

