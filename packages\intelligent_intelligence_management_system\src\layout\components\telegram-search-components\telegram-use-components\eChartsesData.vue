<template>
  <div v-if="item && item._source" style="padding-bottom: 10px">
    <div v-if="type === 'telegram群聊天内容'">
      <el-descriptions direction="vertical" :column="6" border>
        <el-descriptions-item label="来自群名称">{{
          item._source.group_name
        }}</el-descriptions-item>
        <el-descriptions-item label="来自群ID">{{
          item._source.group_id
        }}</el-descriptions-item>
        <el-descriptions-item label="发言人员ID">{{
          item._source.user_id
        }}</el-descriptions-item>
        <el-descriptions-item label="发言时间">{{
          $tools.timestampToTime(item._source["timestamp"])
        }}</el-descriptions-item>
        <el-descriptions-item label="搜集时间">{{
          $tools.timestampToTime(item._source["@timestamp"])
        }}</el-descriptions-item>
        <el-descriptions-item label="来自库">{{
          item._source.type
        }}</el-descriptions-item>
        <!-- <el-descriptions-item
          v-if="item.highlight && item.highlight.content.length"
          label="发言内容"
        >
          <p v-for="content_html in item.highlight.content">
            <span v-html="content_html"></span>
          </p>
        </el-descriptions-item> -->
        <el-descriptions-item label="发言内容">
          <span v-html="item._source.content"></span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-else-if="type === 'telegram群'">
      <el-descriptions direction="vertical" :column="4" border>
        <el-descriptions-item label="群名称">{{
          item._source.group_name
        }}</el-descriptions-item>
        <el-descriptions-item label="群ID">{{
          item._source.group_id
        }}</el-descriptions-item>
        <el-descriptions-item label="搜集时间">{{
          $tools.timestampToTime(item._source["@timestamp"])
        }}</el-descriptions-item>
        <el-descriptions-item label="来自库">{{
          item._source.type
        }}</el-descriptions-item>
        <el-descriptions-item
          v-if="item.highlight && item.highlight.content.length"
          label="命中关键字"
        >
          <p v-for="content_html in item.highlight.content">
            <span v-html="content_html"></span>
          </p>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-else>
      <el-descriptions direction="vertical" :column="6" border>
        <el-descriptions-item label="来自群名称">{{
          item._source.group_name
        }}</el-descriptions-item>
        <el-descriptions-item label="来自群ID">{{
          item._source.group_id
        }}</el-descriptions-item>
        <el-descriptions-item label="人员ID">{{
          item._source.user_id
        }}</el-descriptions-item>
        <el-descriptions-item
          label="人员昵称"
          v-if="item._source.nickname.length"
          >{{ item._source.nickname[0] }}</el-descriptions-item
        >
        <el-descriptions-item label="搜集时间">{{
          $tools.timestampToTime(item._source["@timestamp"])
        }}</el-descriptions-item>
        <el-descriptions-item label="来自库">{{
          item._source.type
        }}</el-descriptions-item>
        <el-descriptions-item
          v-if="item.highlight && item.highlight.content.length"
          label="命中关键字"
        >
          <p v-for="content_html in item.highlight.content">
            <span v-html="content_html"></span>
          </p>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
export default {
  name: "echarts-data",
  props: {
    item: {
      type: Object,
      default: {},
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  created() {},
  computed: {},
  methods: {},
};
</script>
<style lang="scss" scoped></style>
