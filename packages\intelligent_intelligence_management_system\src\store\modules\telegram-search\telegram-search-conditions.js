export default {
  namespaced: true,
  state: {
    time_range: "180天",
    time_range_begin: 0,
    time_range_end: 0,
    query_mode: "match",
    collection_time_range: "180天",
    collection_time_range_begin: 0,
    collection_time_range_end: 0,
  },
  mutations: {
    setNowConditions(state, data) {
      state.time_range = data.time_range;
      if (data.time_range === "自定义时间") {
        state.time_range_begin = data.time_range_begin;
        state.time_range_end = data.time_range_end;
      } else {
        state.time_range_begin = 0;
        state.time_range_end = 0;
      }

      state.query_mode = data.query_mode;
      state.collection_time_range = data.collection_time_range;
      if (state.collection_time_range === "自定义时间") {
        state.collection_time_range_begin = data.collection_time_range_begin;
        state.collection_time_range_end = data.collection_time_range_end;
      } else {
        state.collection_time_range_begin = 0;
        state.collection_time_range_end = 0;
      }
    },
    setSearchTask(state, data) {
      if (data.status === "ok") {
        window.main.$message.success("添加分析任务成功");
      }
    },
    sendSearchTask(state, v) {
      let time_range_begin = 0;
      let time_range_end = 0;
      if (v.task.timeRange === "自定义时间") {
        time_range_begin = parseInt(v.task.date1.getTime() / 1000);
        time_range_end = parseInt(v.task.date2.getTime() / 1000);
      }
      let collection_time_range_begin = 0;
      let collection_time_range_end = 0;
      if (v.task.collection_time_range === "自定义时间") {
        collection_time_range_begin = parseInt(
          v.task.collection_time_range_end.getTime() / 1000
        );
        collection_time_range_end = parseInt(
          v.task.collection_time_range_end.getTime() / 1000
        );
      }
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.AddSimpleTask",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              tag: "apocalypse_search",
              task_authority: "username",
              task_type: "search_task",
              method: "elasticsearch",
              title: v.task.name,
              parms: {
                condition: {
                  collection_time_range_begin: collection_time_range_begin,
                  collection_time_range_end: collection_time_range_end,
                  collection_time_range: v.task.collection_time_range,
                  time_range_begin: time_range_begin,
                  time_range_end: time_range_end,
                  time_range: v.task.timeRange,
                  query_mode: v.task.queryMode,
                },
                query_string_list: v.task.queryString,
                query_type_list: v.query_type_list,
              },
            },
          },
        ],
        "telegramSearch/telegramSearchConditions/setSearchTask"
      );
    },
    setConditions(state, v) {
      try {
        state.time_range = v.time_range;
        state.time_range_begin = v.time_range_begin;
        state.time_range_end = v.time_range_end;
        state.query_mode = v.query_mode;
      } catch (err) {
        state.time_range = "无";
        state.time_range_begin = 0;
        state.time_range_end = 0;
        state.query_mode = "match";
      }
    },
    setCollectionTimeRange(state, v) {
      state.collection_time_range = v;
      if (v != "自定义时间") {
        state.collection_time_range_end = 0;
        state.collection_time_range_begin = 0;
      } else {
      }
    },
    setCollectionTimeRangeBegin(state, v) {
      state.collection_time_range_begin = v;
    },
    setCollectionTimeRangeEnd(state, v) {
      state.collection_time_range_end = v;
    },
    setTimeRange(state, v) {
      state.timeRange = v.timeRange;
      state.time_range_begin = v.time_range_begin;
      state.time_range_end = v.time_range_end;
    },
    setTimeRangeBegin(state, v) {
      state.time_range_begin = v;
    },
    setTimeRangeEnd(state, v) {
      state.time_range_end = v;
    },
    setQueryMode(state, v) {
      state.query_mode = v;
    },
    sendSetConditions(state) {
      window.main.$main_socket.sendData(
        "Api.Search.Conditions.Set",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              time_range: state.time_range,
              time_range_begin: state.time_range_end,
              time_range_end: state.time_range_begin,
              query_mode: state.query_mode,
              collection_time_range: state.collection_time_range,
              collection_time_range_begin: state.collection_time_range_end,
              collection_time_range_end: state.collection_time_range_begin,
            },
          },
        ],
        "telegramSearch/telegramSearchConditions/setConditions"
      );
    },
    getNowConditions(state) {
      window.main.$main_socket.sendData(
        "Api.Search.Conditions.Get",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
          },
        ],
        "telegramSearch/telegramSearchConditions/setNowConditions"
      );
    },
  },
  actions: {},
};
