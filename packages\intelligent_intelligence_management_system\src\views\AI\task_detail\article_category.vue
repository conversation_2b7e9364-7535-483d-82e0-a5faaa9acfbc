<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #dcfce7;
          line-height: 42px;
          font-weight: bold;
          color: #166534;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-s-data"></i
          ><span style="margin-left: 10px">文章类型统计</span>
        </div>
      </div>
      <div style="display: flex">
        <el-card
          class="box-card"
          v-for="(item, index) in categoryData"
          :key="index"
          style="width: 200px; margin: 20px 10px; padding: 20px"
        >
          <div
            style="
              color: #166534;
              font-weight: bold;
              font-size: 20px;
              text-align: center;
            "
          >
            {{ item.num }}
          </div>
          <div
            style="
              color: #888;
              font-weight: bold;
              font-size: 16px;
              text-align: center;
            "
          >
            {{ item.category }}
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "article_category",
  data() {
    return {};
  },
  computed: {
    categoryData: {
      get() {
        let a = this.$store.state.aiTaskQueue.taskDetail.article_category;
        console.log("result1", a);
        let mynum = 0;
        const result = [];
        /* for (let i = 0; i < a.length; i += 2) {
          // 确保有足够的数据对
          if (i + 1 < a.length) {
            result.push({
              category: a[i].text,
              num: parseInt(a[i + 1].text), 
            });
          }
         
        } */
        for (let i = 0; i < a.result.length; i++) {
          result.push({
            category: a.result[i][0],
            num: parseInt(a.result[i][1]),
          });
          mynum++;
        }
        if (mynum === a.result.length) {
          console.log("result", result);
          return result;
        }
      },
      set(val) {},
    },
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss"></style>
