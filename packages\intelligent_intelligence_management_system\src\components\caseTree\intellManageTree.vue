<template>
  <div class="childTree">
    <div v-if="dataNode">
      <div class="layout">
        <div class="treeItem">
          <div
            class="info"
            @click="showCaseDir(dataNode)"
            :style="{
              backgroundColor:
                dataNode.row === $store.state.intellManageTree.selectedItemId
                  ? 'rgb(219, 234, 254)'
                  : 'transparent',
            }"
          >
            <i
              class="el-icon-folder"
              v-if="!(dataNode.case_name == 'case')"
            ></i>
            <i class="el-icon-tickets" v-if="dataNode.case_name == 'case'"></i>
            <span class="description">{{
              dataNode.columnValues.i.case_dir_name ||
              dataNode.columnValues.i.case_name
            }}</span>
          </div>
        </div>
        <tree-box
          :data-node="dataNode"
          sub-component="mail-manage-tree"
          class="nextTree"
        ></tree-box>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "mailManageTree",
  props: ["dataNode"],
  data() {
    return {
      formLabelWidth: "120px",
    };
  },
  components: {
    "tree-box": () => import("@/components/caseTree/tree_box.vue"),
  },
  methods: {
    /**下级目录 */
    showCaseDir(val) {
      console.log("showCaseDirsss", val);
      this.$store.commit("intellManageTree/clearCaseFrom");
      // this.$store.commit("intellManageTree/clearDataList");
      this.$store.commit("intellManageTree/nowChooseCase", val.case_name);
      this.$store.commit("intellManageTree/setSelectedItem", {
        id: val.row,
        type: val.case_name === "case" ? "case" : "dir",
      });
      if (val.hasOwnProperty("subMap")) {
        let arr = Object.getOwnPropertyNames(val.subMap);
        if (arr.length == 0) {
          if (val.case_name == "case") {
            console.log("75case");
            this.$store.commit("intellManageTree/nowChooseCase", "case");
            this.$store.commit("intellManageTree/sendCaseDetail", val.row);

            this.$store.commit("intellManageTree/clearSessionList");
            this.$store.commit("intellManageTree/sendCaseSessionList", val.row);
          } else {
            this.$store.commit("intellManageTree/nowChooseCase", "dir");
            this.$store.commit("intellManageTree/sendCaseDirList", val);
            this.$store.commit("intellManageTree/sendCaseList", val);
            if (val.columnValues.i.case_dir_father_path) {
              this.$store.commit("intellManageTree/sendCaseDirDetail", val);
            } else {
              this.$store.commit("intellManageTree/setRoolCaseDirDetail", val);
            }
          }
        } else {
          // 有子节点，执行折叠操作
          this.$store.commit("intellManageTree/clearCaseTree", val);
          // 折叠后不重新展开，只保持选中状态
        }
      } else {
        if (val.case_name == "case") {
          // this.$store.commit("intellManageTree/clearDataList");
          this.$store.commit("intellManageTree/setCaseID", val);
          this.$store.dispatch("intellManageTree/sendCaseFileDataList");
        } else {
          this.$store.commit("intellManageTree/sendCaseDirList", val);
          this.$store.commit("intellManageTree/sendCaseList", val);
          if (val.columnValues.i.case_dir_father_path == "") {
            return;
          }
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.layout {
  width: auto;
  height: auto;

  .treeItem {
    width: auto;
    height: 35px;
    margin: 5px 5px;
    padding: 0;
    font-size: 18px;
    align-items: center;

    &:hover {
      background-color: rgb(232, 240, 239);
      cursor: pointer;
    }

    .info {
      height: 30px;
      line-height: 30px;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .iconfont {
        color: #9cc7f1;
        margin-right: 5px;
        font-size: 18px;
      }
    }

    .arrowUp:hover {
      cursor: pointer;
      color: rgb(238, 172, 128);
    }
  }

  .nextTree {
    padding-left: 20px;
  }
}

.relatedRow {
  margin-bottom: 15px;
  display: flex;
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;

  .rowItem {
    flex: 1 1;
    border-right: 1px solid #ddd;

    .rowItemT {
      padding: 10px;
      text-align: center;
      font-weight: bold;
      border-top: 1px solid #ddd;
    }

    .rowItemB {
      padding: 10px;
      text-align: center;
      border-top: 1px solid #ddd;
    }
  }

  .operation {
    border-right: 1px solid #ddd;
    border-top: 1px solid #ddd;
  }

  .operation {
    padding: 0 10px;
    display: flex;
    align-items: center;
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.dialogTit {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}

.listMoreBOx {
  .listMoreRow {
    margin-top: 10px;
    display: flex;

    .listMoreRowL {
      width: 120px;
      text-align: right;
      font-weight: bold;
      color: #999;
    }

    .listMoreRowR {
      margin-left: 10px;

      .mayBox {
        padding: 10px 10px 0px;

        .mayRow {
          margin-right: 10px;

          .mayTop {
            line-height: 35px;
            font-weight: bold;
          }
        }
      }
    }

    .mayBox {
      display: flex;
    }
  }
}

.tagsSty {
  margin-right: 10px;
  background: #52a04f;
  padding: 2px 3px;
  cursor: pointer;
  color: #fff;
}
</style>
