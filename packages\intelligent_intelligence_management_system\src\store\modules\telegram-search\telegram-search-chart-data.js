export default {
  namespaced: true,
  state: {
    storeMyname: "",
    req: false,
    tmpDataList: {},
    add_es_query_conditions: {},
    // 遮罩层定时
    beginBun: 0,
    // 遮罩层定时器
    timer: null,
    // 遮罩层
    loading: null,
    // 取消遮罩层的timer
    clearTimer: null,
    // 取消遮罩层定时
    clearBeginBun: 0,
    query_string: "",
  },
  mutations: {
    setqueryString(state, v) {
      state.query_string = v;
    },
    chartOncklickClear(state) {
      if (state.loading) {
        if (document.getElementById("closeMyLoading") != null) {
          document.getElementById("closeMyLoading").remove();
        }
        state.loading.close();
        document.getElementById(state.storeMyname).style.display = "none";
        state.loading = null;
        state.timer = null;
        document.getElementById(state.storeMyname).style.display = "block";
      }
    },
    setLoadingLayer(state) {
      if (state.loading != null || state.timer != null) {
        return;
      }

      // 如果重新进入setLoadingLayer了，clearLoadingLayer无论是什么状态都取消掉。
      state.clearBeginBun = 0;
      clearInterval(state.clearTimer);
      state.clearTimer = null;
      state.clearBeginBun = 0;

      let a = document.createElement("div");
      a.id = "closeMyLoading";
      a.style.cssText =
        "position:absolute;top:50px;right:0;z-index:1;cursor:pointer;margin-left:auto;margin-right:auto;background:#409eff;color:#fff;text-align:center;padding:5px 10px;border-radius:3px;width:100px;";
      a.innerHTML = "停止搜索";
      a.onclick = function () {
        if (state.timer) {
          clearInterval(state.timer);
        }

        window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[
          state.storeMyname
        ].dataListGetter.return();
        window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList[
          state.storeMyname
        ].dataRangeGetter.return();
        window.main.$store.commit("telegramSearch/telegramSearchChartData/chartOncklickClear");
        return;
      };
      if (document.querySelector("#close" + state.storeMyname)) {
        document.querySelector("#close" + state.storeMyname).style.position =
          "relative";
        document.querySelector("#close" + state.storeMyname).prepend(a);
      }

      state.loading = window.main.$loading({
        target: document.querySelector("#" + state.storeMyname),
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      state.beginBun = 0;
      state.timer = setInterval(() => {
        state.beginBun++;
        if (state.beginBun > 5) {
          window.main.$message({
            message: "加载超时",
            type: "warning",
          });
          state.loading.close();

          if (document.getElementById("closeMyLoading") != null) {
            document.getElementById("closeMyLoading").remove();
          }
          clearInterval(state.timer);
          state.loading = null;
          state.timer = null;
        }
      }, 300);
    },
    clearLoadingLayer(state) {
      if (state.clearTimer) {
        state.clearBeginBun = 0;
        return;
      }
      state.clearTimer = setInterval(() => {
        state.clearBeginBun++;

        if (state.loading) {
          state.loading.close();
          setTimeout(() => {
            document.querySelector("#" + state.storeMyname)?.append("1");
          }, 100);

          state.loading = null;
          if (document.getElementById("closeMyLoading") != null) {
            document.getElementById("closeMyLoading").remove();
          }
        }
        if (state.timer) {
          clearInterval(state.timer);
          state.timer = null;
        }
        clearInterval(state.clearTimer);
        state.clearTimer = null;
      }, 300);
    },
    clearLoadingBun(state) {
      state.beginBun = 0;
    },
    setAddEsQueryConditions(state, v) {
      state.add_es_query_conditions = v;
    },
    setStoreMyname(state, v) {
      state.storeMyname = v;
    },
    initData(state, v) {
      let basedata =
        window.main.$store.state.telegramSearch.telegramSearchStatistics.statisticsData[0].columnValues;
      let query_string;
      let conditions;
      query_string;
      if (basedata.hasOwnProperty("parm")) {
        conditions = basedata.parm.condition;
      } else {
        query_string = "";
        conditions = {};
      }
      state.tmpDataList = Object.assign({}, state.tmpDataList, {
        [v]: {
          search: false,
          dataList: [],
          from: 0,
          size: 20,
          dataListGetter: {},
          query_string: query_string,
          conditions: conditions,
          use_public: "username",
          dataListTotal: 0,
          direction: true,
        },
      });
    },
    initDatas(state, v) {
      let basedata =
        window.main.$store.state.telegramSearch.telegramSearchStatistics.statisticsData[0].columnValues;
      let conditions;

      if (basedata.hasOwnProperty("parm")) {
        conditions = basedata.parm.condition;
      } else {
        conditions = {};
      }
      state.tmpDataList = Object.assign({}, state.tmpDataList, {
        [v.name]: {
          search: false,
          dataList: [],
          from: 0,
          size: 20,
          dataListGetter: {},
          query_string: v.query_string,
          conditions: conditions,
          use_public: "username",
          dataListTotal: 0,
          direction: true,
        },
      });
    },
    initDataMore(state, v) {
      let query_string = "";
      let conditions = {
        collection_time_range: "无",
        collection_time_range_begin: 0,
        collection_time_range_end: 0,
        query_mode: "match",
        time_range: "无",
        time_range_begin: 0,
        time_range_end: 0,
      };

      state.tmpDataList = Object.assign({}, state.tmpDataList, {
        [v]: {
          search: false,
          dataList: [],
          from: 0,
          size: 20,
          dataListGetter: {},
          query_string: query_string,
          conditions: conditions,
          use_public: "public",
          dataListTotal: 0,
          direction: true,
        },
      });
    },
    setDataListGetter(state, data) {
      let mydata = data.data;
      function* getter(mydata) {
        if (mydata == null) {
          return;
        }
        while (true) {
          let resData;
          if (Object.keys(state.add_es_query_conditions).length === 0) {
            resData = {
              head: {
                session_id: window.main.$store.state.userInfo.session_id,
                from: state.tmpDataList[data.name].from,
                size: state.tmpDataList[data.name].size,
              },
              msg: mydata,
              control: {
                condition: state.tmpDataList[data.name].conditions,
                query_string: state.tmpDataList[data.name].query_string,
                query_type: state.tmpDataList[data.name].use_public,
              },
            };
          } else {
            if (
              data.name === "likes_count" ||
              data.name === "article_count" ||
              data.name === "reply_count"
            ) {
              resData = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: state.tmpDataList[data.name].from,
                  size: state.tmpDataList[data.name].size,
                },
                msg: mydata,
                control: {
                  order: "desc",
                  condition: state.tmpDataList[data.name].conditions,
                  query_string: state.tmpDataList[data.name].query_string,
                  query_type: state.tmpDataList[data.name].use_public,
                  add_es_query_conditions: state.add_es_query_conditions,
                },
              };
            } else if (data.name === "groupContentData") {
              resData = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: state.tmpDataList[data.name].from,
                  size: state.tmpDataList[data.name].size,
                },
                msg: mydata,
                control: {
                  order: "desc",
                  condition: {
                    collection_time_range: "无",
                    collection_time_range_begin: 0,
                    collection_time_range_end: 0,
                    query_mode: "match",
                    time_range: "无",
                    time_range_begin: 0,
                    time_range_end: 0,
                  },
                  query_string: state.tmpDataList[data.name].query_string,
                  query_type: state.tmpDataList[data.name].use_public,
                  add_es_query_conditions: state.add_es_query_conditions,
                },
              };
            } else {
              resData = {
                head: {
                  session_id: window.main.$store.state.userInfo.session_id,
                  from: state.tmpDataList[data.name].from,
                  size: state.tmpDataList[data.name].size,
                },
                msg: mydata,
                control: {
                  condition: {
                    collection_time_range: "无",
                    collection_time_range_begin: 0,
                    collection_time_range_end: 0,
                    query_mode: "match",
                    time_range: "无",
                    time_range_begin: 0,
                    time_range_end: 0,
                  },
                  query_string: state.tmpDataList[data.name].query_string,
                  query_type: state.tmpDataList[data.name].use_public,
                  add_es_query_conditions: state.add_es_query_conditions,
                },
              };
            }
          }

          yield window.main.$main_socket.sendData(
            "Api.Search.SearchList.Query",
            [resData],
            `telegramSearch/telegramSearchChartData/set${data.name}DataList`
          );
          state.tmpDataList[data.name].from +=
            state.tmpDataList[data.name].size;
        }

      }
      state.tmpDataList[data.name].dataListGetter = getter(mydata);
      let nextRes = state.tmpDataList[data.name].dataListGetter.next();
      if (nextRes.value === undefined && nextRes.done) {
        window.main.$store.commit("telegramSearch/telegramSearchChartData/clearLoadingLayer");
      }
    },
    setReq(state, v) {
      state.req = false;
    },
    setSearch(state, v) {
      state.tmpDataList[v.name].search = v.data;
    },
    clearSearchList(state, name) {
      state.tmpDataList[name].dataListTotal = 0;
      state.tmpDataList[name].dataList = [];
      state.tmpDataList[name].queryString = "";
      state.tmpDataList[name].direction = true;
      state.tmpDataList[name].from = 0;
    },
    setusePublic(state, v) {
      state.tmpDataList[v.name].use_public = v.data;
    },
    setQueryString(state, v) {
      state.queryString = v;
    },
    optionsetDataList(state, optiondata) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/clearLoadingLayer");
      state.req = true;
      try {
        if (
          optiondata.v == null ||
          optiondata.v == "undefined" ||
          !optiondata.v.hasOwnProperty("hits") ||
          optiondata.v.hits == null ||
          !optiondata.v.hits.hasOwnProperty("hits") ||
          optiondata.v.hits.hits == null
        ) {
          state.tmpDataList[optiondata.name].from = 0;
          window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[
            optiondata.name
          ].dataListGetter.return();
          if (
            optiondata.name ===
            ("groupContentData" || "groupData" || "groupContentData")
          ) {
            return;
          }
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList[
            optiondata.name
          ].dataRangeGetter.next("nextDataRangePage");
          return;
        }
        // 叠加查询总数
        state.tmpDataList[optiondata.name].dataListTotal +=
          optiondata.v.hits.hits.length;

        // 判断并保存已经取下来的数据
        state.tmpDataList[optiondata.name].dataList = state.tmpDataList[
          optiondata.name
        ].dataList.concat(optiondata.v.hits.hits);
        // 判断如果还可以取数据，继续取
        if (
          state.tmpDataList[optiondata.name].dataList.length <
          state.tmpDataList[optiondata.name].size
        ) {
          state.tmpDataList[optiondata.name].from = 0;
          window.main.$store.state.telegramSearch.telegramSearchChartData.tmpDataList[
            optiondata.name
          ].dataListGetter.return();
          //
          window.main.$store.state.telegramSearch.telegramSearchChartRange.tmpDataList[
            optiondata.name
          ].dataRangeGetter.next("nextDataRangePage");
          return;
        }
      } catch (err) {
        throw err;
      }
    },
    async setcollision_key_count_chartDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "collision_key_count_chart",
        v: v,
      });
    },
    async setcollision_key_score_chartDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "collision_key_score_chart",
        v: v,
      });
    },
    async setsearch_task_count_chartDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "search_task_count_chart",
        v: v,
      });
    },
    async setsearch_task_score_chartDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "search_task_score_chart",
        v: v,
      });
    },
    async setarticle_countDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "article_count",
        v: v,
      });
    },
    async setlikes_countDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "likes_count",
        v: v,
      });
    },
    async setreply_countDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "reply_count",
        v: v,
      });
    },
    async setgroupMemberDataDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "groupMemberData",
        v: v,
      });
    },
    async setgroupDataDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "groupData",
        v: v,
      });
    },
    async setgroupContentDataDataList(state, v) {
      window.main.$store.commit("telegramSearch/telegramSearchChartData/optionsetDataList", {
        name: "groupContentData",
        v: v,
      });
    },
  },
};
