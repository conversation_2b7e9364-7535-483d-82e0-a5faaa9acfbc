<template>
  <div class="collect">
    <div style="width: 15%; height: 100%">
      <collectTree
        :listType="'username'"
        :tableName="'favorites_data'"
        :getCollect="getCollect"
      ></collectTree>
    </div>
    <div class="right">
      <div class="title">
        收藏列表
      </div>
      <div class="collectListBox">
        <div style="width: 100%; height: 5%">
          <el-tabs
            @tab-click="handleClickTabs"
            v-model="collectTabsActiveName"
            style="width: 100%; padding: 0px 10px"
            class="no-underline-tabs"
          >
            <el-tab-pane
              :label="item.label"
              :name="item.value"
              v-for="item in searchTabs"
              :key="item.value"
            ></el-tab-pane>
          </el-tabs>
        </div>
        <div
          v-show="dataList.length"
          style="width: 100%; height: 95%"
          v-loading="collectLoading"
          element-loading-text="加载中..."
          element-loading-background="rgba(0,0,0,0.6)"
        >
          <component
            :is="collectTabsActiveName"
            v-if="showComponent"
            :listType="'username'"
            :tableName="'favorites_data'"
          ></component>
        </div>
        <div
          style="width: 100%; height: 95%"
          v-show="!dataList.length"
          v-loading="collectLoading"
          element-loading-text="加载中..."
        >
          <el-empty :image-size="300" description="暂无收藏"></el-empty>
        </div>
      </div>
    </div>
    <!-- 生成情报 -->
    <el-dialog
      title="生成舆情简报"
      :visible.sync="createIntell"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="intellForm" :rules="intellRules" ref="intellForm" label-width="100px">
        <el-form-item label="情报标题" prop="title">
          <el-input
            v-model="intellForm.title"
            placeholder="请输入情报标题"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="情报模板" prop="template">
          <el-select 
            v-model="intellForm.template" 
            filterable 
            placeholder="请选择情报模板"
            style="width: 100%"
          >
            <el-option
              v-for="(item, index) in aiAnalyzeTemplateList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cleareData">取 消</el-button>
        <el-button type="primary" @click="submitIntellForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { collectTree } from "@/layout/components";
import { mapMutations, mapState } from "vuex";
import public_opinion from "@/layout/components/collect/opinion.vue";
import telegram from "@/layout/components/collect/telegram.vue";
import twitter from "@/layout/components/collect/twitter.vue";
import facebook from "@/layout/components/collect/facebook.vue";
import linkedin from "@/layout/components/collect/linkedin.vue";
import social_work_library from "@/layout/components/collect/socialWorkLibrary.vue";

export default {
  data() {
    return {
      createIntell: false,
      creIntellTit: "情报标题",
      peopleValue: "",
      personList: [],
      intellForm: {
        title: "",
        template: "",
      },
      intellRules: {
        title: [
          { required: true, message: '请输入情报标题', trigger: 'blur' },
          { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
        ],
        template: [
          { required: true, message: '请选择情报模板', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    ...mapState({
      dataList: (state) => state.collect.dataList,
      nowCollect: (state) => state.collect.nowCollect,
      showComponent: (state) => state.collect.showComponent,
      searchTabs: (state) => state.collect.searchTabs,
      collectTabsActiveName: (state) => state.collect.collectTabsActiveName,
      collectLoading: (state) => state.collect.collectLoading,
    }),
    collectTabsActiveName: {
      get() {
        return this.$store.state.collect.collectTabsActiveName;
      },
      set(val) {
        this.$store.commit("collect/setCollectTabsActiveName", val);
      },
    },
    aiAnalyzeTemplateList: {
      get() {
        const templateList = this.$store.state.aiAnalyze.aiAnalyzeTemplateList;
        return templateList.map(item => ({
          label: item._source.ai_template_name,
          value: item._source
        }));
      }
    }
  },
  mounted() {
    if (!this.searchTabs.length) {
      this.$store.dispatch("collect/getTabs");
    }
    this.$store.commit("collect/setNowCollect", null);
  },
  components: {
    collectTree,
    public_opinion,
    telegram,
    twitter,
    facebook,
    linkedin,
    social_work_library,
  },

  created() {
    this.resetAllData();
    this.getAIAnalyzeTemplate();
  },

  methods: {
    ...mapMutations({
      getAIAnalyzeTemplate: "aiAnalyze/getAIAnalyzeTemplate",
      resetAllData: "aiAnalyze/resetAllData"
    }),

    cleareData() {
      this.createIntell = false;
      this.intellForm.title = "";
      this.intellForm.template = "";
    },
    submitIntellForm() {
      this.$refs.intellForm.validate((valid) => {
        if (valid) {
          window.main.$main_socket.sendData(
            "Api.DataAnalysisTask.AddSimpleTask",
            [
              {
                msg: {
                  task_authority: "username",
                  task_type: "ai_workflow_task",
                  method: "elasticsearch",
                  title: this.intellForm.title,
                  parms: {
                    template: this.intellForm.template,
                  },
                },
              },
            ],
            (res) => {
              console.log("res", res);
              if (res?.status == 'ok') {
                this.$message.success('情报创建成功');
              }
            }
          );
          this.createIntell = false;
        } else {
          console.log('表单验证失败');
        }
      });
    },
    toCreateIntell() {
      this.createIntell = true;
    },
    getCollect(data) {
      console.log("getCollect", data);
      this.$store.commit("collect/clearDataList");
      this.$store.commit("collect/setCollectLoading", true);
      this.$store.commit("collect/setNowCollect", data);
      this.$store.commit("collect/getOpinionCollect");
      this.$store.commit(
        "collect/setCollectTabsActiveName",
        this.searchTabs[0].value
      );
    },
    //点击tabs
    handleClickTabs(v) {
      console.log("vvvv", this.collectTabsActiveName);
    },

    docx(title) {
      console.log("nowCollect");

      //生成段落 根据options进行配置
      const generateParagraph = (options) => {
        let {
          text = "",
          size = 26,
          margin = {
            left: 30,
            right: 30,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              font: {
                name: "宋体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      const generateParagraphHead = (options) => {
        let {
          text = "",
          bold = true,
          size = 32,
          margin = {
            left: 50,
            right: 50,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              bold,
              font: {
                name: "黑体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      let test = this.html.replace(/<[^>]*>/g, "<br>").split("<br>");
      let paragraphList = test.map((e) => {
        if (e.slice(0, 1) == "头") {
          let opt = {
            text: e.slice(1),
          };

          return generateParagraphHead(opt);
        } else {
          let opt = {
            text: e,
          };
          return generateParagraph(opt);
        }
      });
      //按照数据填充生成文档 内容放置于sections
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: paragraphList,
          },
        ],
      });

      //保存导出为word
      Packer.toBlob(doc).then((blob) => {
        saveAs(
          blob,
          this.nowCollect.label ? this.nowCollect.label : "舆情情报.docx"
        );
      });
    },
  },
};
</script>
<style scoped lang="scss">
.custom-icon {
  display: inline-block;
  width: 24px;
  height: 14px;
  background-image: url("../../assets/images/AI.png");
  background-size: cover;
}

.right {
  width: 85%;
  height: 91vh;
  background-color: rgba(0, 0, 0, 0);
}

.collect {
  height: 100%;
  width: 100%;
  display: flex;
}

.title {
  height: 4vh;
  background-color: #f5f7fa;
  line-height: 39px;
  padding-left: 5px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
}

.collectListBox {
  height: 100%;
}

::v-deep .el-tabs__header {
  margin-bottom: 0;
  /* 与内容间隔 */
}
</style>
