export default {
  namespaced: true,
  state: {
    currentPage: 1,
    pageSize: 20,
    total: 0,
    intelligenceList: [],
    // 临时关联数据
    tmpRelatedData: [],
    tmpRowKey: [],
  },
  mutations: {
    // 重置所有数据
    resetAllData(state) {
      state.intelligenceList = [];
      state.tmpRowKey = [""];
      state.tmpRelatedData = [];
      state.currentPage = 1;
      state.pageSize = 20;
      state.total = 0;
    },

    // 删除情报之后的重置部分数据
    resetPartData(state) {
      state.intelligenceList = [];
      state.tmpRelatedData = [];
      state.currentPage = 1;
      state.pageSize = 20;
      state.total = 0;
    },

    /***获取关联情报数据 */
    getRelatedInformation(state, id) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              row_key: state.tmpRowKey,
              size: 200,
            },
            msg: {
              table: "key_intelligence",
              type: "public",
              relation: id + ";key_intelligence",
            },
          },
        ],
        (res) => {
          console.log("获取关联情报数据", res.length);
          if (res?.length > 0) {
            state.tmpRelatedData = res;
            window.main.$store.commit("detailIntelligence/getRelatedInformationList");
          }
        }
      );
    },

    // 根据关联的hbase返回值去ES中查询
    getRelatedInformationList(state) {
      let add_es_query_conditions_list = {
        bool: {
          should: [],
        },
      };
      let case_id_list = [];
      state.tmpRelatedData.forEach((item) => {
        if (!case_id_list.includes(item.columnValues.d.caseID)) {
          case_id_list.push(item.columnValues.d.caseID)
        }
        add_es_query_conditions_list.bool.should.push({
          bool: {
            must: [
              { 
                term: {
                  case_id: item.columnValues.d.caseID
                }
              },
              {
                term: {
                  _id: item.columnValues.d.intelID
                },
              }
            ]
          },
        });
      });
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: (state.currentPage - 1) * state.pageSize,
              size: state.pageSize,
            },
            control: {
              query_type: "case",
              query_string: "",
              add_es_query_conditions: add_es_query_conditions_list,
              case_id: case_id_list
            },
            msg: {
              data_range_index_name: "key_intelligence",
            },
          },
        ],
        (data) => {
          if (data.hits.hits.length > 0) {
            state.intelligenceList = data.hits.hits;
          }
          state.total = data.hits.total.value;
        }
      );
    },

    // 设置翻页页码
    setCurrentPage(state, page) {
      state.currentPage = page;
      state.intelligenceList = [];
    },

    // 删除情报
    sendDelFileData(state, id) {
      let row = state.tmpRelatedData.find(item => item.columnValues.d.intelID == id)?.row
      console.log("row:",row);
      const queryData = this.$router.query.data;
      let _id = JSON.parse(queryData)._id;
      let relation = _id + ";key_intelligence";
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DelData",
        [
          {
            head: {
              row_key: [row],
            },
            msg: {
              table: "key_intelligence",
              type: "public",
              relation: relation,
            },
          },
        ],
        (res) => {
          if (res?.status == 'ok') {
            window.main.$message.success("删除成功");
            window.main.$store.commit("detailIntelligence/resetPartData");
            window.main.$store.commit("detailIntelligence/getRelatedInformation", _id);
          } else {
            window.main.$message.error("删除失败");
          }
        }
      );
    },

    // 保存情报
    sendSaveIntelligence(state, params) {
      console.log("params:", params);
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "case",
              index: "key_intelligence",
              id: params.id,
            },
            msg: {
              type: "case",
              case_id: params.case_id,
              content_article: params.content,
              title: params.title,
              params: [
                { k: "type", v: ["generate_public_opinion_report_from_collection"] },
                { k: "id", v: [params.id] },
              ],
            },
          },
        ],
        (res) => {
          console.log("res:", res);
          
          if (res.status == "ok") {
            window.main.$message.success("添加情报成功");
          }
        }
      );
    },
  },
};
