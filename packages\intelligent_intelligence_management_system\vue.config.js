const path = require("path");
const fs = require("fs");
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  lintOnSave: false,
  assetsDir: "./pc",
  indexPath: "index.html",
  productionSourceMap: false,
  devServer: {
    host: "0.0.0.0",
    port: 7777,
    disableHostCheck: true,
    https: {
      ca: fs.readFileSync(resolve("../../ssl/ca.crt")),
      key: fs.readFileSync(resolve("../../ssl/server.key")),
      cert: fs.readFileSync(resolve("../../ssl/server.crt")),
      requestCert: true,
    },
    proxy: {
      "/sw": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      "/plugin": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      "/data_analysis_platform/api/ws-jsonrpc/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      //案件管理API
      "/case/api/ws-jsonrpc/v2": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      //ai的mcp工具API
      "/ai/api/ws-jsonrpc/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      //获取用户信息API
      "/pki/api/ws-jsonrpc/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      "/filesystem/api/ws-jsonrpc/v2": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      //没用上
      "/filesystem/api/rest/v2": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      "/eml_msg/api/ws-jsonrpc/v1?msg": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      // "/eml_msg/api/ws-jsonrpc/v1?msg": {
      //   target: "https://**************:8443",
      //   changeOrigin: false,
      //   ws: true,
      // },
      "/eml_msg/api/ws-jsonrpc/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      // "/eml_msg/api/ws-jsonrpc/v1": {
      //   target: "https://**************:8443",
      //   changeOrigin: false,
      //   ws: true,
      // },
      "/websocket/constant_data/api/ws-jsonrpc/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      // "/websocket/constant_data/api/ws-jsonrpc/v1": {
      //   target: "wss://**************:8443",
      //   changeOrigin: false,
      //   ws: true,
      // },
      // "/eml_msg/api/rest/v1/eml/detail_message": {
      //   target: {
      //     protocol: "https:",
      //     host: "**************",
      //     port: 8443,
      //     pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
      //   },
      //   changeOrigin: false,
      // },
      "/eml_msg/api/rest/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
        ws: true,
      },
      // "/eml_msg/api/rest/v1/eml/detail_message": {
      //   target: "https://**************:8443",
      //   changeOrigin: false,
      // },
      "/offline_map/*": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
      },
      "/websocket/api/ai/v1": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
      },
      "/cronjob/api/ws-jsonrpc/v2": {
        target: {
          protocol: "https:",
          host: "**************",
          hostname: "**************",
          servername: "ii.zhengzhoudacheng.com",
          port: 8443,
          pfx: fs.readFileSync(resolve("../../ssl/client.pfx")),
        },
        changeOrigin: false,
      },
      // "/offline_map/*": {
      //   target: "https://**************:8443",
      //   changeOrigin: false,
      // },
    },
  },
  configureWebpack: {
    name: "智能情报管理系统",
    resolve: {
      alias: {
        "@": resolve("src"),
        devlop: path.resolve(__dirname, "some/path"), // 指向空模块
        "ant-design-x-vue": "ant-design-x-vue/dist/index.umd.js",
      },
    },
    stats: {
      warningsFilter: [
        /#minpath/,
        /#minproc/,
        /#minurl/,
        /devlop/,
        /do-not-use-color/,
      ],
    },
    externals: {
      vfile: "vfile",
      "@mdx-js/mdx": "@mdx-js/mdx",
      "mdast-util-gfm-autolink-literal": "{}",
      "unist-util-visit-parents": "{}",
    },
    module: {
      rules: [
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: "javascript/auto", // 关键：强制 Webpack 解析 .mjs
        },
      ],
    },
  },
  chainWebpack(config) {
    config.plugin("preload").tap(() => [
      {
        rel: "preload",
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: "initial",
      },
    ]);
    config.plugins.delete("prefetch");
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    config.plugin("copy").use(require("copy-webpack-plugin"), [
      {
        patterns: [
          {
            from: "./public",
            to: "./",
          },
          {
            from: "./node_modules/certificate-login/src/service/wasm/dc_websocket_jsonrpc_client_golang.wasm",
            to: "./pc/wasm/",
          },
          {
            from: "./node_modules/certificate-login/src/service/serviceWorker.js",
            to: "./",
          },
        ],
      },
    ]);
    config.when(process.env.NODE_ENV !== "development", (config) => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial",
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk("single");
    });
  },
  //transpileDependencies: [/@kousum\/semi-ui-vue/],
  transpileDependencies: ["ant-design-x-vue", "dc-websocket-jsonrpc"],
  // pwa: {
  //   name: 'New Edition Virtual Machine Public Combat Platform Client',
  //   themeColor: '#4DBA87',
  //   msTileColor: '#000000',
  //   appleMobileWebAppCapable: 'yes',
  //   appleMobileWebAppStatusBarStyle: 'black',
  //   workboxPluginMode: 'InjectManifest',
  //   workboxOptions: {
  //     exclude: [/\.html$/],//html不进行service Worker缓存
  //     // 自定义 Service Worker 文件的位置
  //     swSrc: '/public/serviceWorker.js'
  //     // swSrc: 'serviceWorker.js'
  //   }
  // },
};
