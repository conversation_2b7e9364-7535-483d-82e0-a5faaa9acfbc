<template>
  <div
    class="warning"
    v-loading="warningLoading"
    element-loading-text="加载中..."
    element-loading-background="rgba(0, 0, 0, 0.7)"
    element-loading-spinner="el-icon-loading"
  >

    <div class="warning-header">
      <el-button type="primary" size="mini" @click="openAddDialog" class="add-warning-button">添加预警</el-button>
    </div>

    <div class="warning-list" ref="warningListContainer">
      <div class="table-container">
        <el-table :data="warningList" style="width: 100%" height="100%" ref="warningTable">
          <el-table-column prop="columnValues.d.WarningWord" label="预警词" align="center">
            <template slot-scope="scope">
            <span
              v-for="(word, index) in scope.row.columnValues.d.WarningWord"
              :key="index"
              class="warning-word-span"
              :title="word"
            >
              {{ word.length > 5 ? word.substring(0, 5) + '...' : word }}
            </span>
            </template>
          </el-table-column>
          <el-table-column label="排除预警词" align="center">
            <template slot-scope="scope">
            <span
              v-for="(author, index) in scope.row.columnValues.d.eliminateWords || []"
              :key="index"
              class="eliminate-word-span"
              :title="author"
            >
              {{ author.length > 5 ? author.substring(0, 5) + '...' : author }}
            </span>
            </template>
          </el-table-column>
          <el-table-column label="平台" align="left">
            <template slot-scope="scope">
            <span
              v-for="(platform, index) in scope.row.columnValues.d.platform || []"
              :key="index"
              class="platform-word-span"
              :title="platform"
            >
              {{ platform.length > 5 ? platform.substring(0, 5) + '...' : platform }}
            </span>
            </template>
          </el-table-column>
          <el-table-column label="预警账户" align="left">
            <template slot-scope="scope">
              <div v-for="(account, email) in scope.row.columnValues.d.to || {}" :key="email">
                <span>{{ email }}</span>
                <span v-if="account.username">({{ account.username }})</span>

                <el-tag v-if="account.messageWarning&&account.messageWarning.enable" size="mini" type="success">消息
                </el-tag>
                <el-tag v-if="account.privateMailWarning&&account.privateMailWarning.enable" size="mini" type="info">
                  邮件
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center">
            <template slot-scope="scope">
              {{
                (scope.row.columnValues && scope.row.columnValues.d.warning_creator ? scope.row.columnValues.d.warning_creator.warning_creator : '-')
              }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center">
            <template slot-scope="scope">
              {{
                formatTimestamp(scope.row.columnValues && scope.row.columnValues.d.create_timestamp ? scope.row.columnValues.d.create_timestamp.create_timestamp : '')
              }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button
                type="success"
                size="mini"
                @click="editWarning(scope.row)"
              >编辑
              </el-button
              >
              <el-button
                type="danger"
                size="mini"
                @click="deleteWarning(scope.$index, scope.row)"
              >删除
              </el-button
              >
            </template>
          </el-table-column>

        </el-table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[15]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>


    </div>

    <el-dialog
      title="添加预警"
      :visible.sync="dialogVisible"
      width="800px"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="cancelDialog"
    >
      <div class="warning-dialog-content">
        <!-- 预警词 -->
        <div class="warning-section">
          <div class="section-title">预警词:</div>
          <div class="tag-container">
            <el-tag
              v-for="(tag, index) in warningWords"
              :key="'word-' + index"
              size="small"
              closable
              @close="handleClose(tag, 'warningWords')"
              :title="tag"
            >
              <span class="tag-text">{{ tag }}</span>
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="mini"
              maxlength="50"
              show-word-limit
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            ></el-input>
            <el-button
              v-else
              size="mini"
              type="primary"
              plain
              icon="el-icon-plus"
              @click="addWarningWord"
            >预警词
            </el-button
            >
          </div>
        </div>

        <!-- 排除预警词 -->
        <div class="warning-section">
          <div class="section-title">排除预警词:</div>
          <div class="tag-container">
            <el-tag
              v-for="(tag, index) in eliminateWords"
              :key="'eliminate-' + index"
              size="small"
              closable
              @close="handleClose(tag, 'eliminateWords')"
              type="danger"
              :title="tag"
            >
              <span class="tag-text">{{ tag }}</span>
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputEliminateVisible"
              v-model="inputEliminateValue"
              ref="saveTagInputEliminate"
              size="mini"
              maxlength="50"
              show-word-limit
              @keyup.enter.native="handleEliminateInputConfirm"
              @blur="handleEliminateInputConfirm"
            ></el-input>
            <el-button
              v-else
              size="mini"
              type="danger"
              plain
              icon="el-icon-plus"
              @click="addEliminateWord"
            >排除预警词
            </el-button
            >
          </div>
        </div>

        <!-- 预警账户 -->
        <div class="warning-section">
          <div class="section-header">
            <div class="section-title">预警账户:</div>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="addAccount"
            >
              添加
            </el-button>
          </div>

          <el-table :data="computedMainAccountList"
                    style="width: 100%; margin-top: 10px"
                    height="200"
          >
            <el-table-column
              prop="email"
              label="用户名"
              width="180"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.email"
                  size="mini"
                  maxlength="50"
                  placeholder="请输入邮箱"
                  @focus="tempOldEmail = scope.row.email"
                  @blur="updateMainTableEmail(tempOldEmail, scope.row.email)"
                  @keyup.enter.native="$event.target.blur()"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="推送方式">
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.messageWarning.enable"
                  @change="updateMainTableWarning(scope.row.originalKey, 'messageWarning', scope.row.messageWarning.enable)"
                >
                  消息推送
                </el-checkbox>
                <el-checkbox
                  v-model="scope.row.privateMailWarning.enable"
                  @change="updateMainTableWarning(scope.row.originalKey, 'privateMailWarning', scope.row.privateMailWarning.enable)"
                >
                  邮件推送
                </el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  @click="deleteAccount(scope.row.originalKey)"
                >删除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 预警账户选择对话框 -->
        <el-dialog
          title="编辑预警账户"
          :visible.sync="accountDialogVisible"
          width="800px"
          :modal="true"
          :modal-append-to-body="true"
          :append-to-body="true"
          :close-on-click-modal="false"
          @close="closeAccountDialog"
        >
          <div class="account-select-container">
            <div class="account-select-left">
              <div class="section-title">选择联系人</div>
              <div class="account-list" ref="accountListContainer">
                <div
                  v-for="account in allAccounts"
                  :key="account.row"
                  class="account-item"
                  :class="{ 'selected': selectedAccounts.findIndex(item => item.email === account.columnValues.d.account.account) > -1 }"
                  @click="handleAccountSelect(account)"
                >
                  <div class="account-email">{{ account.columnValues.d.account.account }}</div>
                </div>

                <!-- 账户列表加载状态 -->
                <div class="account-load-more-status"
                     v-if="allAccounts.length > 0 && (accountListIsLoadingMore || !accountListHasMoreData)"
                >
                  <div v-if="accountListIsLoadingMore" class="loading-more">
                    <i class="el-icon-loading"></i>
                    <span>加载中...</span>
                  </div>
                  <div v-else-if="!accountListHasMoreData" class="no-more-data">
                    <span>没有更多数据了</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="account-select-right">
              <div class="section-header">
                <div class="section-title">预警账户</div>
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-plus"
                  @click="addCustomAccount"
                />
              </div>
              <div class="selected-account-table" ref="selectedAccountTableWrapper">
                <el-table :data="selectedAccounts" size="mini" style="width: 100%" height="300">
                  <el-table-column label="预警账户" width="180">
                    <template slot-scope="scope">
                      <el-input
                        v-model="scope.row.email"
                        size="mini"
                        placeholder="请输入邮箱"
                        maxlength="50"
                        :ref="`accountEmailInput${scope.$index}`"
                        :class="{ 'duplicate-email': scope.row.isDuplicate }"
                        @blur="updateSelectedAccountEmail(scope.$index, scope.row.email)"
                        @input="validateEmailOnInput(scope.$index, scope.row.email)"
                        @keyup.enter.native="$event.target.blur()"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="推送方式" width="200">
                    <template slot-scope="scope">
                      <div class="push-options">
                        <el-checkbox
                          v-model="scope.row.messageWarning.enable"
                          size="mini"
                          @change="updateSelectedAccountWarning(scope.$index, 'messageWarning', scope.row.messageWarning.enable)"
                        >消息推送
                        </el-checkbox>
                        <el-checkbox
                          v-model="scope.row.privateMailWarning.enable"
                          size="mini"
                          @change="updateSelectedAccountWarning(scope.$index, 'privateMailWarning', scope.row.privateMailWarning.enable)"
                        >邮件推送
                        </el-checkbox>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template slot-scope="scope">
                      <el-button
                        type="danger"
                        size="mini"
                        @click="removeSelectedAccount(scope.$index)"
                      >删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-if="selectedAccounts.length === 0" class="no-selected">
                  暂无选中账户
                </div>
              </div>
            </div>
          </div>

          <span slot="footer" class="dialog-footer">
        <el-button @click="closeAccountDialog">取消</el-button>
        <el-button type="primary" @click="confirmAddAccounts">确认</el-button>
      </span>
        </el-dialog>

        <!-- 媒体选择对话框 -->
        <el-dialog
          title="编辑舆情预警条件"
          :visible.sync="mediaDialogVisible"
          width="800px"
          :modal="true"
          :modal-append-to-body="true"
          :append-to-body="true"
          :close-on-click-modal="false"
          @close="closeMediaDialog"
        >
          <div class="media-select-container">
            <div class="media-select-left">
              <div class="section-title">选择媒体</div>
              <div class="media-list">
                <div v-if="Object.keys(allMedias).length === 0" class="no-data">
                  正在加载媒体数据...
                </div>
                <div
                  v-for="(media, mediaId) in allMedias"
                  :key="mediaId"
                  class="media-item"
                  :class="{ 'selected': selectedMedias.findIndex(item => item.id === mediaId) > -1 }"
                  @click="handleMediaSelect(media, mediaId)"
                >
                  <div class="media-name">{{ media.type }}</div>
                </div>
              </div>
            </div>

            <div class="media-select-right">

              <div class="author-section">
                <div class="section-subtitle">作者:</div>
                <div class="tag-container">
                  <el-tag
                    v-for="(tag, index) in tempAuthorsInMediaDialog"
                    :key="'media-author-' + index"
                    size="small"
                    closable
                    :title="tag"
                    @close="tempAuthorsInMediaDialog.splice(index, 1)"
                  >
                    <span class="tag-text">{{ tag }}</span>
                  </el-tag>
                  <el-input
                    class="input-new-tag"
                    v-if="inputMediaDialogAuthorVisible"
                    v-model="inputPublicSentimentAuthorValue"
                    ref="mediaAuthorInput"
                    size="mini"
                    maxlength="50"
                    show-word-limit
                    @keyup.enter.native="handlePublicSentimentAuthorInputConfirm"
                    @blur="handlePublicSentimentAuthorInputConfirm"
                  ></el-input>
                  <el-button
                    v-else
                    size="mini"
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    @click="addMediaDialogAuthor"
                  >作者
                  </el-button>
                </div>
              </div>

              <!-- 已选媒体列表 -->
              <div class="selected-media-section">
                <div class="section-subtitle">媒体:</div>
                <div class="selected-media-table">
                  <el-table :data="selectedMedias" size="mini" style="width: 100%" height="300">
                    <el-table-column label="媒体" width="200">
                      <template slot-scope="scope">
                        <span>{{ scope.row.name }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="作者" width="200">
                      <template slot-scope="scope">
                        <div class="tag-container">
                          <el-tag
                            v-for="(author, index) in scope.row.authors || []"
                            :key="'media-author-' + scope.$index + '-' + index"
                            size="small"
                            closable
                            :title="author"
                            @close="removeMediaAuthor(scope.$index, index)"
                          >
                            <span class="tag-text">{{ author }}</span>
                          </el-tag>
                          <el-input
                            class="media-author-input"
                            v-if="scope.row.inputAuthorVisible"
                            v-model="scope.row.inputAuthorValue"
                            :ref="'saveMediaAuthorTagInput' + scope.$index"
                            size="mini"
                            maxlength="50"
                            show-word-limit
                            @keyup.enter.native="handleMediaAuthorInputConfirm(scope.$index)"
                            @blur="handleMediaAuthorInputConfirm(scope.$index)"
                          ></el-input>
                          <el-button
                            v-else
                            size="mini"
                            type="primary"
                            plain
                            icon="el-icon-plus"
                            @click="addMediaAuthor(scope.$index)"
                          >作者
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                      <template slot-scope="scope">
                        <el-button
                          type="danger"
                          size="mini"
                          @click="removeSelectedMedia(scope.$index)"
                        >
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-if="selectedMedias.length === 0" class="no-selected">
                    暂无选中媒体
                  </div>
                </div>
              </div>
            </div>
          </div>

          <span slot="footer" class="dialog-footer">
            <el-button @click="closeMediaDialog">取消</el-button>
            <el-button type="primary" @click="confirmAddMedia">确认添加</el-button>
          </span>
        </el-dialog>

        <!-- 预警范围选择 -->
        <div class="warning-section">
          <div class="section-title">预警类型:</div>
          <div class="warning-range-checkboxes">
            <el-checkbox v-model="warningRanges.publicSentiment" @change="handleRangeChange">舆情预警</el-checkbox>
            <el-checkbox v-model="warningRanges.telegram" @change="handleRangeChange">Telegram预警</el-checkbox>
            <el-checkbox v-model="warningRanges.twitter" @change="handleRangeChange">Twitter预警</el-checkbox>
            <el-checkbox v-model="warningRanges.linkedin" @change="handleRangeChange">LinkedIn预警</el-checkbox>
            <el-checkbox v-model="warningRanges.facebook" @change="handleRangeChange">Facebook预警</el-checkbox>
          </div>
        </div>

        <!-- 舆情预警 -->
        <div class="warning-section" v-if="warningRanges.publicSentiment">
          <div class="section-header">
            <div class="section-title">舆情预警范围:</div>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="addImagePerson"
            >添加
            </el-button>
          </div>

          <div class="section-title" style="margin-top: 15px;">作者:</div>
          <div class="tag-container">
            <el-tag
              v-for="(tag, index) in publicSentimentAuthors"
              :key="'public-sentiment-author-' + index"
              size="small"
              closable
              :title="tag"
              @close="removePublicSentimentAuthor(index)"
            >
              <span class="tag-text">{{ tag }}</span>
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputMainDialogAuthorVisible"
              v-model="inputPublicSentimentAuthorValue"
              ref="mainAuthorInput"
              size="mini"
              maxlength="50"
              show-word-limit
              @keyup.enter.native="handlePublicSentimentAuthorInputConfirm"
              @blur="handlePublicSentimentAuthorInputConfirm"
            ></el-input>
            <el-button
              v-else
              size="mini"
              type="primary"
              plain
              icon="el-icon-plus"
              @click="addMainDialogAuthor"
            >作者
            </el-button>
          </div>

          <el-table
            :data="public_sentiment_prefix"
            style="width: 100%; margin-top: 10px"
            height="200"
          >
            <el-table-column
              prop="type"
              label="媒体"
              width="180"
            ></el-table-column>
            <el-table-column prop="author_id" label="作者" width="180">
              <template slot-scope="scope">
                <div class="tag-container">
                  <el-tag
                    v-for="(tag, index) in scope.row.author_id"
                    :key="index"
                    size="small"
                    closable
                    :title="tag"
                    @close="removeAuthorTag(scope.row, index)"
                  >
                    <span class="tag-text">{{ tag }}</span>
                  </el-tag>
                  <el-input
                    class="input-new-tag"
                    v-if="scope.row.inputSentimentVisible"
                    v-model="scope.row.inputSentimentValue"
                    :ref="'saveSentimentTagInput'+scope.$index"
                    size="mini"
                    maxlength="50"
                    show-word-limit
                    @keyup.enter.native="handleSentimentInputConfirm(scope.row)"
                    @blur="handleSentimentInputConfirm(scope.row)"
                  ></el-input>
                  <el-button
                    v-else
                    size="mini"
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    @click="addSentimentTag(scope.row, scope.$index)"
                  >作者
                  </el-button
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  @click="deleteImagePerson(scope.$index)"
                >删除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- Telegram预警花名册 -->
        <div class="warning-section" v-if="warningRanges.telegram">
          <div class="section-header">
            <div class="section-title">Telegram预警范围:</div>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="addTelegramPerson"
            >添加
            </el-button>
          </div>

          <el-table
            :data="group_content_data_prefix_telegram"
            style="width: 100%; margin-top: 10px"
            height="200"
            ref="telegramTable"
          >
            <el-table-column
              prop="group_id"
              label="群组ID"
              width="180"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.group_id"
                  size="mini"
                  placeholder="请输入群组ID"
                  maxlength="50"
                  show-word-limit
                  @blur="validateTelegramGroupId(scope.row, scope.$index)"
                  @input="handleGroupIdInput($event, scope.$index)"
                  @keyup.enter.native="$event.target.blur()"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="用户ID">
              <template slot-scope="scope">
                <div class="tag-container">
                  <el-tag
                    v-for="(tag, index) in scope.row.user_id"
                    :key="index"
                    size="small"
                    closable
                    @close="handleRemoveUserTag(scope.row, index)"
                  >
                    <span class="tag-text">{{ tag }}</span>
                  </el-tag>
                  <el-input
                    class="input-new-tag"
                    v-if="scope.row.inputTelegramVisible"
                    v-model="scope.row.inputTelegramValue"
                    :ref="'saveTelegramTagInput' + scope.$index"
                    size="mini"
                    maxlength="50"
                    show-word-limit
                    @keyup.enter.native="handleTelegramInputConfirm(scope.row)"
                    @blur="handleTelegramInputConfirm(scope.row)"
                  ></el-input>
                  <el-button
                    v-else
                    size="mini"
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    @click="addTelegramTag(scope.row, scope.$index)"
                  >用户ID
                  </el-button
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  @click="deleteTelegramPerson(scope.$index)"
                >删除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- Twitter预警花名册 -->
        <div class="warning-section" v-if="warningRanges.twitter">
          <h2>Twitter预警范围:</h2>
          <div class="section-header">
            <div class="section-title">Twitter用户ID:</div>
            <div class="tag-container">
              <el-tag
                v-for="(tag, index) in social_platform_timeline_prefix_twitter"
                :key="'twitter-' + index"
                size="small"
                closable
                @close="handleClose(tag, 'social_platform_timeline_prefix_twitter')"
                :title="tag"
              >
                <span class="tag-text">{{ tag }}</span>
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputTwitterVisible"
                v-model="inputTwitterValue"
                ref="saveTwitterTagInput"
                size="mini"
                maxlength="50"
                show-word-limit
                @keyup.enter.native="handleTwitterInputConfirm"
                @blur="handleTwitterInputConfirm"
              ></el-input>
              <el-button
                v-else
                size="mini"
                type="primary"
                plain
                icon="el-icon-plus"
                @click="addTwitterId"
              >用户ID
              </el-button>
            </div>
          </div>
        </div>

        <!-- LinkedIn预警花名册 -->
        <div class="warning-section" v-if="warningRanges.linkedin">
          <h2>Linkedin预警范围:</h2>
          <div class="section-header">
            <div class="section-title">LinkedIn用户ID:</div>
            <div class="tag-container">
              <el-tag
                v-for="(tag, index) in social_platform_timeline_prefix_linkedin"
                :key="'linkedin-' + index"
                size="small"
                closable
                @close="handleClose(tag, 'social_platform_timeline_prefix_linkedin')"
                :title="tag"
              >
                <span class="tag-text">{{ tag }}</span>
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputLinkedinVisible"
                v-model="inputLinkedinValue"
                ref="saveLinkedinTagInput"
                size="mini"
                maxlength="50"
                show-word-limit
                @keyup.enter.native="handleLinkedinInputConfirm"
                @blur="handleLinkedinInputConfirm"
              ></el-input>
              <el-button
                v-else
                size="mini"
                type="primary"
                plain
                icon="el-icon-plus"
                @click="addLinkedinId"
              >用户ID
              </el-button>
            </div>
          </div>
        </div>

        <!-- Facebook预警范围 -->
        <div class="warning-section" v-if="warningRanges.facebook">
          <h2>Facebook预警范围:</h2>
          <div class="section-header">
            <div class="section-title">Facebook用户ID:</div>
            <div class="tag-container">
              <el-tag
                v-for="(tag, index) in social_platform_timeline_prefix_facebook"
                :key="'facebook-' + index"
                size="small"
                closable
                @close="handleClose(tag, 'social_platform_timeline_prefix_facebook')"
                :title="tag"
              >
                <span class="tag-text">{{ tag }}</span>
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputFacebookVisible"
                v-model="inputFacebookValue"
                ref="saveFacebookTagInput"
                size="mini"
                maxlength="50"
                show-word-limit
                @keyup.enter.native="handleFacebookInputConfirm"
                @blur="handleFacebookInputConfirm"
              ></el-input>
              <el-button
                v-else
                size="mini"
                type="primary"
                plain
                icon="el-icon-plus"
                @click="addFacebookId"
              >用户ID
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- ======================= 修正点 1: 绑定新的取消方法 ======================= -->
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="saveWarning">确认</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import { sub } from 'echarts-gl'
import { mapMutations, mapState } from 'vuex'

export default {
  name: 'Warning',
  data() {
    return {
      // 添加预警范围选择状态
      warningRanges: {
        publicSentiment: false,
        telegram: false,
        twitter: false,
        linkedin: false,
        facebook: false
      },


      currentTelegramRow: null,
      dialogVisible: false,
      inputVisible: false,
      inputValue: '',
      inputEliminateVisible: false,
      inputEliminateValue: '',

      inputTelegramVisible: false,
      inputTelegramValue: '',
      inputTwitterVisible: false,
      inputTwitterValue: '',
      inputLinkedinVisible: false,
      inputLinkedinValue: '',
      inputFacebookVisible: false,
      inputFacebookValue: '',
      warningWords: [],
      eliminateWords: [],
      // platform 数组现在动态生成，无需在此初始化
      platform: [],
      public_sentiment_prefix: [],
      group_content_data_prefix_telegram: [],
      social_platform_timeline_prefix_twitter: [],
      social_platform_timeline_prefix_linkedin: [],
      social_platform_timeline_prefix_facebook: [],
      to: {},
      // 当前编辑的预警ID
      currentEditId: null,
      isEditing: false,
      originalTimestamp: '',
      originalCreator: '',
      // 账户选择对话框相关
      accountDialogVisible: false,
      selectedAccounts: [], // 已选中的账户
      // 临时数据，用于编辑时保存原始数据的深拷贝
      tempTo: {},
      tempWarningWords: [],
      tempEliminateWords: [],
      tempPublicSentimentPrefix: [],
      tempGroupContentDataPrefixTelegram: [],
      tempSocialPlatformTimelinePrefixTwitter: [],
      tempSocialPlatformTimelinePrefixLinkedin: [],
      tempSocialPlatformTimelinePrefixFacebook: [],
      tempPublicSentimentAuthors: <AUTHORS>
      tempWarningRanges: {
        publicSentiment: false,
        telegram: false,
        twitter: false,
        linkedin: false,
        facebook: false
      },

      // 媒体选择弹窗相关数据
      mediaDialogVisible: false,
      selectedMedias: [],

      // 舆情预警作者相关数据
      publicSentimentAuthors: <AUTHORS>
      inputPublicSentimentAuthorValue: '',
      tempAuthorsInMediaDialog: [], // 用于媒体选择对话框的临时作者列表

      inputMainDialogAuthorVisible: false,
      inputMediaDialogAuthorVisible: false,

      tempOldEmail: '' // 用于主表格编辑时临时存储旧邮箱
    }
  },
  computed: {
    computedMainAccountList() {
      return Object.entries(this.to).map(([key, data]) => ({
        originalKey: key, // 保存原始 key
        email: key, // v-model 绑定这个
        ...data
      }))
    },
    ...mapState({
      warningList: (state) => state.warningManagement.warningList,
      warningLoading: (state) => state.warningManagement.warningLoading,
      allAccounts: (state) => state.warningManagement.allAccounts,
      allMedias: (state) => state.warningManagement.allMedias,
      // 分页相关状态
      currentPage: (state) => state.warningManagement.currentPage,
      pageSize: (state) => state.warningManagement.pageSize,
      total: (state) => state.warningManagement.total,
      // 账户列表滚动加载状态
      accountListHasMoreData: (state) => state.warningManagement.accountListHasMoreData,
      accountListIsLoadingMore: (state) => state.warningManagement.accountListIsLoadingMore
    })
  },
  created() {
    this.resetAllData()
    this.getWarningList()
  },
  mounted() {
    // 移除滚动事件监听器，因为现在使用分页
  },

  beforeDestroy() {
    // 移除滚动事件监听器，因为现在使用分页
  },

  updated() {
    // 当账户对话框打开时，绑定账户列表的滚动事件
    if (this.accountDialogVisible && this.$refs.accountListContainer) {
      this.$refs.accountListContainer.addEventListener('scroll', this.handleAccountListScroll)
    }
  },
  methods: {
    ...mapMutations({
      getWarningList: 'warningManagement/getWarningList',
      addWarning: 'warningManagement/addWarning',
      resetAllData: 'warningManagement/resetAllData',
      getAllAccounts: 'warningManagement/getAllAccounts',
      getAllWebsites: 'warningManagement/getAllWebsites',
      getAllMedias: 'warningManagement/getAllMedias',
      // 分页相关方法
      setCurrentPage: 'warningManagement/setCurrentPage',
      setPageSize: 'warningManagement/setPageSize'
    }),

    // 处理预警范围选择变化
    handleRangeChange() {
      this.updatePlatformList()
    },

    updatePlatformList() {
      this.platform = []
      if (this.warningRanges.publicSentiment) this.platform.push('public_sentiment_prefix')
      if (this.warningRanges.telegram) this.platform.push('group_content_data_prefix_telegram')
      if (this.warningRanges.twitter) this.platform.push('social_platform_timeline_prefix_twitter')
      if (this.warningRanges.linkedin) this.platform.push('social_platform_timeline_prefix_linkedin')
      if (this.warningRanges.facebook) this.platform.push('social_platform_timeline_prefix_facebook')
    },

    openAddDialog() {
      this.currentEditId = null
      this.isEditing = false
      this.resetForm()
      this.resetTempData()
      this.dialogVisible = true
    },
    resetForm() {
      this.warningWords = []
      this.eliminateWords = []
      this.platform = []
      this.public_sentiment_prefix = []
      this.group_content_data_prefix_telegram = []
      this.social_platform_timeline_prefix_twitter = []
      this.social_platform_timeline_prefix_linkedin = []
      this.social_platform_timeline_prefix_facebook = []
      this.to = {}
      this.warningRanges = { publicSentiment: false, telegram: false, twitter: false, linkedin: false, facebook: false }
      this.publicSentimentAuthors = []
      this.inputPublicSentimentAuthorValue = ''
      this.inputMainDialogAuthorVisible = false
      this.inputMediaDialogAuthorVisible = false
    },
    resetTempData() {
      this.tempTo = {}
      this.tempWarningWords = []
      this.tempEliminateWords = []
      this.tempPublicSentimentPrefix = []
      this.tempGroupContentDataPrefixTelegram = []
      this.tempSocialPlatformTimelinePrefixTwitter = []
      this.tempSocialPlatformTimelinePrefixLinkedin = []
      this.tempSocialPlatformTimelinePrefixFacebook = []
      this.tempPublicSentimentAuthors = []
      this.tempWarningRanges = {
        publicSentiment: false,
        telegram: false,
        twitter: false,
        linkedin: false,
        facebook: false
      }
    },

    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString()
      let random = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n']
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13)
        soleValue += random[index]
      }
      return soleValue
    },

    saveWarning() {
      try {
        const isValid = this.validateForm()
        if (!isValid) {
          // 校验失败时，validateForm内部已经弹出了提示
          console.log('表单校验失败，停止保存')
          return
        }
        const platforms = this.generatePlatforms()
        const warningId = this.currentEditId || this.reduceNumber()
        const warningData = this.prepareWarningData(platforms, warningId)

        // 发送数据到后台
        this.addWarning(warningData)

        // 刷新列表
        this.getWarningList();

        // 关闭对话框并清理数据
        this.dialogVisible = false
        this.resetTempData()
        this.resetForm()
      } catch (error) {
        this.$message.error('操作失败：' + error.message)
      }
    },

    // ======================= 修正点: 新增的取消确认方法 =======================
    handleCancel() {
      this.$confirm('确认关闭？未保存的数据将会丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.cancelDialog(); // 用户确认后，调用原有的取消逻辑
      }).catch(() => {
        // 用户点击了“取消”按钮，不做任何事
      });
    },

    cancelDialog() {
      if (this.isEditing) {
        this.warningWords = [...this.tempWarningWords]
        this.eliminateWords = [...this.tempEliminateWords]
        this.to = { ...this.tempTo }
        this.public_sentiment_prefix = [...this.tempPublicSentimentPrefix]
        this.group_content_data_prefix_telegram = [...this.tempGroupContentDataPrefixTelegram]
        this.social_platform_timeline_prefix_twitter = [...this.tempSocialPlatformTimelinePrefixTwitter]
        this.social_platform_timeline_prefix_linkedin = [...this.tempSocialPlatformTimelinePrefixLinkedin]
        this.social_platform_timeline_prefix_facebook = [...this.tempSocialPlatformTimelinePrefixFacebook]
        this.publicSentimentAuthors = [...this.tempPublicSentimentAuthors]
        this.warningRanges = { ...this.tempWarningRanges }
      } else {
        this.resetForm()
      }
      this.dialogVisible = false
      this.resetTempData()
      this.isEditing = false
    },

    editWarning(row) {
      try {
        const warningData = row.columnValues.d
        this.currentEditId = row.columnValues.d._._
        this.isEditing = true
        this.originalTimestamp = warningData.create_timestamp?.create_timestamp || ''
        this.originalCreator = warningData.warning_creator?.warning_creator || ''

        this.tempWarningWords = JSON.parse(JSON.stringify(warningData.WarningWord || []))
        this.tempEliminateWords = JSON.parse(JSON.stringify(warningData.eliminateWords || []))
        const tempTo = JSON.parse(JSON.stringify(warningData.to || {}))
        Object.keys(tempTo).forEach(email => {
          const account = tempTo[email]
          if (!account.messageWarning) account.messageWarning = { enable: true, account: email }
          if (!account.privateMailWarning) account.privateMailWarning = { enable: false, account: email }
        })
        this.tempTo = tempTo
        this.tempPublicSentimentPrefix = JSON.parse(JSON.stringify(warningData.public_sentiment_prefix || []))
        const tempGroupContentDataPrefixTelegram = JSON.parse(JSON.stringify(warningData.group_content_data_prefix_telegram || []))
        tempGroupContentDataPrefixTelegram.forEach(item => {
          item.inputTelegramVisible = false
          item.inputTelegramValue = ''
        })
        this.tempGroupContentDataPrefixTelegram = tempGroupContentDataPrefixTelegram
        this.tempSocialPlatformTimelinePrefixTwitter = JSON.parse(JSON.stringify(warningData.social_platform_timeline_prefix_twitter || []))
        this.tempSocialPlatformTimelinePrefixLinkedin = JSON.parse(JSON.stringify(warningData.social_platform_timeline_prefix_linkedin || []))
        this.tempSocialPlatformTimelinePrefixFacebook = JSON.parse(JSON.stringify(warningData.social_platform_timeline_prefix_facebook || []))
        this.tempPublicSentimentAuthors = JSON.parse(JSON.stringify(warningData.publicSentimentAuthors || []))

        this.tempWarningRanges.publicSentiment = (warningData.public_sentiment_prefix || []).length > 0
        this.tempWarningRanges.telegram = (warningData.group_content_data_prefix_telegram || []).length > 0
        this.tempWarningRanges.twitter = (warningData.social_platform_timeline_prefix_twitter || []).length > 0
        this.tempWarningRanges.linkedin = (warningData.social_platform_timeline_prefix_linkedin || []).length > 0
        this.tempWarningRanges.facebook = (warningData.social_platform_timeline_prefix_facebook || []).length > 0

        this.warningWords = this.tempWarningWords
        this.eliminateWords = this.tempEliminateWords
        this.to = this.tempTo
        this.public_sentiment_prefix = this.tempPublicSentimentPrefix
        this.group_content_data_prefix_telegram = this.tempGroupContentDataPrefixTelegram
        this.social_platform_timeline_prefix_twitter = this.tempSocialPlatformTimelinePrefixTwitter
        this.social_platform_timeline_prefix_linkedin = this.tempSocialPlatformTimelinePrefixLinkedin
        this.social_platform_timeline_prefix_facebook = this.tempSocialPlatformTimelinePrefixFacebook
        this.publicSentimentAuthors = this.tempPublicSentimentAuthors
        this.warningRanges = { ...this.tempWarningRanges }

        this.dialogVisible = true
      } catch (error) {
        this.$message.error('编辑失败：' + error.message)
      }
    },

    deleteWarning(index, row) {
      this.$confirm('确认删除该预警?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用store中的方法删除数据
        this.$store.commit('warningManagement/delWarning', row.row);
        // ======================= 修正点: 删除成功后刷新列表 =======================
        this.getWarningList();
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    handleClose(tag, name) {
      const listMap = {
        warningWords: this.warningWords,
        eliminateWords: this.eliminateWords,
        social_platform_timeline_prefix_twitter: this.social_platform_timeline_prefix_twitter,
        social_platform_timeline_prefix_linkedin: this.social_platform_timeline_prefix_linkedin,
        social_platform_timeline_prefix_facebook: this.social_platform_timeline_prefix_facebook
      }
      if (listMap[name]) {
        const index = listMap[name].indexOf(tag)
        if (index > -1) {
          listMap[name].splice(index, 1)
        }
      }
    },

    addWarningWord() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      let inputValue = this.inputValue.trim()
      if (inputValue) {
        if (this.warningWords.includes(inputValue)) {
          this.$message.warning('预警词已存在，请勿重复添加')
        } else if (this.eliminateWords.includes(inputValue)) {
          this.$message.warning('预警词不能与排除预警词重复')
        } else {
          this.warningWords.push(inputValue)
        }
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    addEliminateWord() {
      this.inputEliminateVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInputEliminate.$refs.input.focus()
      })
    },
    handleEliminateInputConfirm() {
      let inputValue = this.inputEliminateValue.trim()
      if (inputValue) {
        if (this.eliminateWords.includes(inputValue)) {
          this.$message.warning('排除预警词已存在，请勿重复添加')
        } else if (this.warningWords.includes(inputValue)) {
          this.$message.warning('排除预警词不能与预警词重复')
        } else {
          this.eliminateWords.push(inputValue)
        }
      }
      this.inputEliminateVisible = false
      this.inputEliminateValue = ''
    },

    addAccount() {
      this.getAllAccounts()
      this.accountDialogVisible = true
      this.waitForAccountsAndMatch()
      this.$nextTick(() => {
        if (this.$refs.accountListContainer) {
          this.$refs.accountListContainer.addEventListener('scroll', this.handleAccountListScroll)
        }
      })
    },

    addCustomAccount() {
      this.selectedAccounts.push({
        email: '',
        username: '',
        department: '',
        messageWarning: { enable: true, account: '' },
        privateMailWarning: { enable: false, account: '' },
        isCustom: true,
        isDuplicate: false
      })
      this.$nextTick(() => {
        const newIndex = this.selectedAccounts.length - 1
        const inputComponent = this.$refs[`accountEmailInput${newIndex}`]
        if (inputComponent) inputComponent.focus()
        this.scrollToAccountTableBottom()
      })
    },

    updateSelectedAccountEmail(index, newEmail) {
      const account = this.selectedAccounts[index]
      if (!account) return
      const trimmedEmail = (newEmail || '').trim()
      if (trimmedEmail) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(trimmedEmail)) {
          this.$message.error('请输入有效的邮箱格式')
          return
        }
        const isDuplicate = this.selectedAccounts.some((item, idx) =>
          idx !== index && item.email && item.email.trim() === trimmedEmail
        )
        if (isDuplicate) {
          this.$message.error('该邮箱已存在，请重新输入')
          return
        }
      }
      account.messageWarning.account = trimmedEmail
      account.privateMailWarning.account = trimmedEmail
    },

    updateSelectedAccountWarning(index, warningType, enabled) {
      const account = this.selectedAccounts[index]
      if (!account) return
      if (!enabled) {
        const isMessageWarningEnabled = warningType === 'messageWarning' ? false : account.messageWarning.enable
        const isMailWarningEnabled = warningType === 'privateMailWarning' ? false : account.privateMailWarning.enable
        if (!isMessageWarningEnabled && !isMailWarningEnabled) {
          this.$message.warning('必须至少选择一种推送方式')
          this.$nextTick(() => {
            account[warningType].enable = true
          })
        }
      }
    },

    handleAccountSelect(account) {
      const email = account.columnValues.d.account.account
      const username = account.columnValues.d.username.username
      const authority = account.columnValues.d.authority.authority
      const index = this.selectedAccounts.findIndex(item => item.email === email)
      if (index > -1) {
        this.selectedAccounts.splice(index, 1)
      } else {
        this.selectedAccounts.push({
          email: email,
          username: username || email.split('@')[0],
          department: authority || email.split('@')[1]?.split('.')[0] || '',
          messageWarning: { enable: true, account: email },
          privateMailWarning: { enable: false, account: email },
          isDuplicate: false
        })
        this.$nextTick(() => {
          this.scrollToAccountTableBottom()
        })
      }
    },

    removeSelectedAccount(index) {
      const account = this.selectedAccounts[index]
      if (!account) return
      this.selectedAccounts.splice(index, 1)
      if (account.email) this.$delete(this.to, account.email)
      this.validateAllEmailsAfterDelete()
    },

    waitForAccountsAndMatch() {
      const checkAccounts = () => {
        if (this.allAccounts && this.allAccounts.length > 0) {
          this.matchExistingAccounts()
        } else {
          setTimeout(checkAccounts, 100)
        }
      }
      checkAccounts()
    },

    matchExistingAccounts() {
      this.selectedAccounts = []
      const existingEmails = Object.keys(this.to)
      this.allAccounts.forEach(account => {
        const email = account.columnValues.d.account.account
        const username = account.columnValues.d.username.username
        const authority = account.columnValues.d.authority.authority
        if (existingEmails.includes(email)) {
          const existingAccount = this.to[email]
          this.selectedAccounts.push({
            email: email,
            username: username || email.split('@')[0],
            department: authority || email.split('@')[1]?.split('.')[0] || '',
            messageWarning: { enable: existingAccount.messageWarning?.enable ?? true, account: email },
            privateMailWarning: { enable: existingAccount.privateMailWarning?.enable ?? false, account: email },
            isDuplicate: false
          })
        }
      })
      existingEmails.forEach(email => {
        const existingAccount = this.to[email]
        const isInAllAccounts = this.allAccounts.some(account => account.columnValues.d.account.account === email)
        if (!isInAllAccounts) {
          this.selectedAccounts.push({
            email: email,
            username: existingAccount.username || email.split('@')[0],
            department: existingAccount.department || email.split('@')[1]?.split('.')[0] || '',
            messageWarning: { enable: existingAccount.messageWarning?.enable ?? true, account: email },
            privateMailWarning: { enable: existingAccount.privateMailWarning?.enable ?? false, account: email },
            isCustom: true,
            isDuplicate: false
          })
        }
      })
    },

    closeAccountDialog() {
      this.selectedAccounts = []
      this.accountDialogVisible = false
      if (this.$refs.accountListContainer) {
        this.$refs.accountListContainer.removeEventListener('scroll', this.handleAccountListScroll)
      }
    },

    confirmAddAccounts() {
      for (let i = 0; i < this.selectedAccounts.length; i++) {
        const account = this.selectedAccounts[i]
        if (!account.email || account.email.trim() === '') {
          this.$message.warning('请为所有账户输入有效的邮箱地址')
          return
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(account.email)) {
          this.$message.warning('请为所有账户输入有效的邮箱格式')
          return
        }
      }
      const emails = this.selectedAccounts.map(account => account.email.trim()).filter(email => email)
      const uniqueEmails = new Set(emails)
      if (emails.length !== uniqueEmails.size) {
        this.$message.warning('预警账户中存在重复的邮箱，请检查并修改')
        return
      }
      Object.keys(this.to).forEach(email => {
        this.$delete(this.to, email)
      })
      this.selectedAccounts.forEach(account => {
        this.$set(this.to, account.email, {
          username: account.username,
          department: account.department,
          messageWarning: { enable: account.messageWarning.enable, account: account.email },
          privateMailWarning: { enable: account.privateMailWarning.enable, account: account.email }
        })
      })
      this.selectedAccounts = []
      this.accountDialogVisible = false
      this.$message.success('账户添加成功')
    },

    validateEmailOnInput(index, email) {
      const account = this.selectedAccounts[index]
      if (!account) return
      if (!email || !email.trim()) {
        this.$set(account, 'isDuplicate', false)
        return
      }
      const isDuplicate = this.selectedAccounts.some((item, idx) =>
        idx !== index && item.email && item.email.trim() === email.trim()
      )
      this.$set(account, 'isDuplicate', isDuplicate)
      if (isDuplicate) {
        this.$message.warning('该邮箱已存在，请使用其他邮箱')
      }
    },

    validateAllEmailsAfterDelete() {
      this.$nextTick(() => {
        this.selectedAccounts.forEach((account, index) => {
          if (account.email && account.email.trim()) {
            const isDuplicate = this.selectedAccounts.some((item, idx) =>
              idx !== index && item.email && item.email.trim() === account.email.trim()
            )
            this.$set(account, 'isDuplicate', isDuplicate)
          } else {
            this.$set(account, 'isDuplicate', false)
          }
        })
      })
    },

    updateMainTableWarning(key, warningType, enabled) {
      const account = this.to[key]
      if (!account) return
      const wouldBeMessageEnabled = warningType === 'messageWarning' ? enabled : account.messageWarning.enable
      const wouldBeMailEnabled = warningType === 'privateMailWarning' ? enabled : account.privateMailWarning.enable
      if (!wouldBeMessageEnabled && !wouldBeMailEnabled) {
        this.$message.warning('必须至少选择一种推送方式')
        this.$nextTick(() => {
          account[warningType].enable = !enabled
        })
        return
      }
      account[warningType].enable = enabled
      if (this.accountDialogVisible) {
        const selectedAccount = this.selectedAccounts.find(acc => acc.email === key)
        if (selectedAccount) selectedAccount[warningType].enable = enabled
      }
    },

    updateMainTableEmail(oldEmail, newEmail) {
      const trimmedNewEmail = (newEmail || '').trim()
      if (oldEmail === trimmedNewEmail) return
      if (!trimmedNewEmail) {
        this.$message.error('预警账户不能为空')
        this.$set(this.to[oldEmail], 'email', oldEmail)
        return
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(trimmedNewEmail)) {
        this.$message.error('请输入有效的邮箱格式')
        this.$set(this.to[oldEmail], 'email', oldEmail)
        return
      }
      if (oldEmail !== trimmedNewEmail && this.to[trimmedNewEmail]) {
        this.$message.error('该邮箱已存在')
        this.$set(this.to[oldEmail], 'email', oldEmail)
        return
      }
      const accountData = this.to[oldEmail]
      this.$delete(this.to, oldEmail)
      accountData.messageWarning.account = trimmedNewEmail
      accountData.privateMailWarning.account = trimmedNewEmail
      this.$set(this.to, trimmedNewEmail, accountData)
    },

    deleteAccount(key) {
      this.$delete(this.to, key)
      if (this.accountDialogVisible) {
        const selectedIndex = this.selectedAccounts.findIndex(account => account.email === key)
        if (selectedIndex > -1) this.selectedAccounts.splice(selectedIndex, 1)
      }
    },

    addImagePerson() {
      this.openMediaDialog()
    },
    addSentimentTag(row, index) {
      this.$set(row, 'inputSentimentVisible', true)
      this.$set(row, 'inputSentimentValue', '')
      this.$nextTick(() => {
        const inputRef = this.$refs['saveSentimentTagInput' + index]
        if (inputRef && inputRef.$refs.input) {
          inputRef.$refs.input.focus()
        }
      })
    },
    handleSentimentInputConfirm(row) {
      let inputValue = row.inputSentimentValue.trim()
      if (inputValue) {
        if (!row.author_id) this.$set(row, 'author_id', [])
        if (row.author_id.includes(inputValue)) {
          this.$message.warning('该作者已存在！')
        } else {
          row.author_id.push(inputValue)
        }
      }
      row.inputSentimentVisible = false
      row.inputSentimentValue = ''
    },
    removeAuthorTag(row, index) {
      row.author_id.splice(index, 1)
    },
    deleteImagePerson(index) {
      this.public_sentiment_prefix.splice(index, 1)
    },

    addTelegramPerson() {
      const emptyRowIndex = this.group_content_data_prefix_telegram.findIndex(
        item => !item.group_id || item.group_id.trim() === ''
      )
      if (emptyRowIndex !== -1) {
        this.$message.warning('请先填写完已有的群组ID，再添加新行。')
        this.$nextTick(() => {
          const tableEl = this.$refs.telegramTable.$el
          if (tableEl) {
            const inputWrappers = tableEl.querySelectorAll('.el-table__body-wrapper .el-input')
            if (inputWrappers[emptyRowIndex]) {
              const emptyInputWrapper = inputWrappers[emptyRowIndex]
              const emptyInput = emptyInputWrapper.querySelector('input')
              emptyInputWrapper.classList.add('input-error')
              if (emptyInput) emptyInput.focus()
            }
          }
        })
        return
      }
      this.group_content_data_prefix_telegram.push({
        group_id: '',
        user_id: [],
        inputTelegramVisible: false,
        inputTelegramValue: ''
      })
      this.$message.info('请为新添加的行输入群组ID')
      this.$nextTick(() => {
        this.scrollToTelegramTableBottom()
        const tableEl = this.$refs.telegramTable.$el
        if (tableEl) {
          const inputs = tableEl.querySelectorAll('.el-table__body-wrapper .el-input__inner')
          if (inputs.length > 0) {
            const lastInput = inputs[inputs.length - 1]
            lastInput.focus()
          }
        }
      })
    },
    addTelegramTag(row, index) {
      this.$set(row, 'inputTelegramVisible', true)
      this.$set(row, 'inputTelegramValue', '')
      this.$nextTick(() => {
        this.$refs['saveTelegramTagInput' + index].$refs.input.focus()
      })
    },
    handleTelegramInputConfirm(row) {
      let inputValue = row.inputTelegramValue.trim()
      if (inputValue) {
        if (!row.user_id) this.$set(row, 'user_id', [])
        if (row.user_id.includes(inputValue)) {
          this.$message.warning('该用户ID已存在！')
        } else {
          row.user_id.push(inputValue)
        }
      }
      row.inputTelegramVisible = false
      row.inputTelegramValue = ''
    },
    handleRemoveUserTag(row, index) {
      row.user_id.splice(index, 1)
    },
    deleteTelegramPerson(index) {
      this.group_content_data_prefix_telegram.splice(index, 1)
    },

    validateTelegramGroupId(row, currentIndex) {
      const groupId = row.group_id.trim()
      if (!groupId) return true // 允许暂时为空，由最终的 validateForm 来捕获
      const isDuplicate = this.group_content_data_prefix_telegram.some((item, index) =>
        index !== currentIndex && item.group_id && item.group_id.trim() === groupId
      )
      if (isDuplicate) {
        this.$message.warning('该群组ID已存在，请使用其他群组ID')
        row.group_id = ''
        return false
      }
      return true
    },

    handleGroupIdInput(value, index) {
      if (value && value.trim() !== '') {
        const tableEl = this.$refs.telegramTable.$el
        if (tableEl) {
          const inputWrappers = tableEl.querySelectorAll('.el-table__body-wrapper .el-input')
          if (inputWrappers[index]) inputWrappers[index].classList.remove('input-error')
        }
      }
    },

    scrollToTelegramTableBottom() {
      const tableWrappers = document.querySelectorAll('.el-table__body-wrapper')
      for (let i = 0; i < tableWrappers.length; i++) {
        const wrapper = tableWrappers[i]
        const section = wrapper.closest('.warning-section')
        if (section) {
          const title = section.querySelector('.section-title')
          if (title && title.textContent.includes('Telegram预警范围')) {
            wrapper.scrollTop = wrapper.scrollHeight
            break
          }
        }
      }
    },

    scrollToAccountTableBottom() {
      const wrapper = this.$refs.selectedAccountTableWrapper
      if (wrapper) {
        const bodyWrapper = wrapper.querySelector('.el-table__body-wrapper')
        if (bodyWrapper) bodyWrapper.scrollTop = bodyWrapper.scrollHeight
      }
    },

    scrollToMediaTableBottom() {
      const mediaTableWrappers = document.querySelectorAll('.media-select-right .el-table__body-wrapper')
      if (mediaTableWrappers.length > 0) {
        const lastWrapper = mediaTableWrappers[mediaTableWrappers.length - 1]
        lastWrapper.scrollTop = lastWrapper.scrollHeight
      }
    },

    addTwitterId() {
      this.inputTwitterValue = ''
      this.inputTwitterVisible = true
      this.$nextTick(() => {
        this.$refs.saveTwitterTagInput.$refs.input.focus()
      })
    },
    handleTwitterInputConfirm() {
      let inputValue = this.inputTwitterValue.trim()
      if (inputValue) {
        if (this.social_platform_timeline_prefix_twitter.includes(inputValue)) {
          this.$message.warning('该账号已存在')
        } else {
          this.social_platform_timeline_prefix_twitter.push(inputValue)
        }
      }
      this.inputTwitterVisible = false
      this.inputTwitterValue = ''
    },

    addLinkedinId() {
      this.inputLinkedinValue = ''
      this.inputLinkedinVisible = true
      this.$nextTick(() => {
        this.$refs.saveLinkedinTagInput.$refs.input.focus()
      })
    },
    handleLinkedinInputConfirm() {
      let inputValue = this.inputLinkedinValue.trim()
      if (inputValue) {
        if (this.social_platform_timeline_prefix_linkedin.includes(inputValue)) {
          this.$message.warning('该账号已存在')
        } else {
          this.social_platform_timeline_prefix_linkedin.push(inputValue)
        }
      }
      this.inputLinkedinVisible = false
      this.inputLinkedinValue = ''
    },

    addFacebookId() {
      this.inputFacebookValue = ''
      this.inputFacebookVisible = true
      this.$nextTick(() => {
        this.$refs.saveFacebookTagInput.$refs.input.focus()
      })
    },
    handleFacebookInputConfirm() {
      let inputValue = this.inputFacebookValue.trim()
      if (inputValue) {
        if (this.social_platform_timeline_prefix_facebook.includes(inputValue)) {
          this.$message.warning('该账号已存在')
        } else {
          this.social_platform_timeline_prefix_facebook.push(inputValue)
        }
      }
      this.inputFacebookVisible = false
      this.inputFacebookValue = ''
    },

    // ======================= 修正点: 增强的表单校验方法 =======================
    validateForm() {
      // 1. 预警词校验
      if (!this.warningWords || this.warningWords.length === 0) {
        this.$message.warning('请至少添加一个预警词');
        return false;
      }

      // 2. 预警账户校验
      if (!this.to || Object.keys(this.to).length === 0) {
        this.$message.warning('请至少添加一个预警账户');
        return false;
      }
      for (const email in this.to) {
        const account = this.to[email];
        if (!account.messageWarning?.enable && !account.privateMailWarning?.enable) {
          this.$message.warning(`请为预警账户 ${email} 至少选择一种推送方式。`);
          return false;
        }
      }

      // 3. 预警范围至少选择一个
      const hasPlatform = this.warningRanges.publicSentiment || this.warningRanges.telegram || this.warningRanges.twitter || this.warningRanges.linkedin || this.warningRanges.facebook;
      if (!hasPlatform) {
        this.$message.warning('请至少选择一个预警类型');
        return false;
      }

      // 4. Telegram 群组ID非空校验
      if (this.warningRanges.telegram && this.group_content_data_prefix_telegram.length > 0) {
        const hasEmptyGroupId = this.group_content_data_prefix_telegram.some(item =>
          !item.group_id || !item.group_id.trim()
        );
        if (hasEmptyGroupId) {
          this.$message.warning('Telegram预警范围中存在空的群组ID，请填写完整。');
          return false;
        }
      }

      // 其他校验...
      // (您原有的重复性校验等逻辑可以保留在这里)

      return true
    },

    generatePlatforms() {
      const PLATFORM_CONFIG = {
        publicSentiment: { key: 'public_sentiment_prefix', name: '舆情预警' },
        telegram: { key: 'group_content_data_prefix_telegram', name: 'Telegram预警' },
        twitter: { key: 'social_platform_timeline_prefix_twitter', name: 'Twitter预警' },
        linkedin: { key: 'social_platform_timeline_prefix_linkedin', name: 'LinkedIn预警' },
        facebook: { key: 'social_platform_timeline_prefix_facebook', name: 'Facebook预警' }
      }
      return Object.entries(PLATFORM_CONFIG)
        .filter(([rangeKey, config]) => this.warningRanges[rangeKey] && this[config.key]?.length > 0)
        .map(([_, config]) => config.name)
    },

    prepareWarningData(platforms, warning_id) {
      const timestamp = this.isEditing ? this.originalTimestamp : Math.round(new Date().getTime() / 1000).toString()
      const creator = this.isEditing ? this.originalCreator : window.main.$store.state.userInfo.userinfo.username
      this.group_content_data_prefix_telegram.forEach(entry => {
        if (Array.isArray(entry.userid)) entry.userid = [...new Set(entry.userid)]
      })
      return {
        data: {
          _: { _: warning_id },
          WarningWord: this.warningWords,
          create_timestamp: { create_timestamp: timestamp },
          warning_creator: { warning_creator: creator },
          eliminateWords: this.eliminateWords,
          platform: platforms,
          to: this.to,
          publicSentimentAuthors: <AUTHORS>
          public_sentiment_prefix: this.warningRanges.publicSentiment ? this.public_sentiment_prefix : [],
          group_content_data_prefix_telegram: this.warningRanges.telegram ? this.group_content_data_prefix_telegram : [],
          social_platform_timeline_prefix_twitter: this.warningRanges.twitter ? this.social_platform_timeline_prefix_twitter : [],
          social_platform_timeline_prefix_linkedin: this.warningRanges.linkedin ? this.social_platform_timeline_prefix_linkedin : [],
          social_platform_timeline_prefix_facebook: this.warningRanges.facebook ? this.social_platform_timeline_prefix_facebook : []
        }
      }
    },

    formatTimestamp(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(parseInt(timestamp) * 1000)
      return date.toLocaleString()
    },

    // 媒体选择对话框相关方法
    openMediaDialog() {
      this.tempAuthorsInMediaDialog = [...this.publicSentimentAuthors]
      this.mediaDialogVisible = true
      this.getAllMedias()
      this.waitForMediasAndMatch()
    },
    closeMediaDialog() {
      this.mediaDialogVisible = false
      this.selectedMedias = []
      this.tempAuthorsInMediaDialog = []
      this.inputMediaDialogAuthorVisible = false
      this.inputPublicSentimentAuthorValue = ''
    },
    handleMediaSelect(media, mediaId) {
      const index = this.selectedMedias.findIndex(item => item.id === mediaId)
      if (index === -1) {
        this.selectedMedias.push({
          id: mediaId,
          name: media.type,
          authors: [],
          inputAuthorVisible: false,
          inputAuthorValue: ''
        })
        this.$nextTick(() => {
          this.scrollToMediaTableBottom()
        })
      } else {
        this.selectedMedias.splice(index, 1)
      }
    },
    removeSelectedMedia(index) {
      this.selectedMedias.splice(index, 1)
    },

    addMediaAuthor(mediaIndex) {
      this.selectedMedias[mediaIndex].inputAuthorVisible = true
      this.$nextTick(() => {
        this.$refs['saveMediaAuthorTagInput' + mediaIndex].$refs.input.focus()
      })
    },

    handleMediaAuthorInputConfirm(mediaIndex) {
      const media = this.selectedMedias[mediaIndex]
      const inputValue = (media.inputAuthorValue || '').trim()
      if (inputValue) {
        if (!media.authors) this.$set(media, 'authors', [])
        if (media.authors.includes(inputValue)) {
          this.$message.warning('该作者已存在！')
        } else {
          media.authors.push(inputValue)
        }
      }
      media.inputAuthorVisible = false
      media.inputAuthorValue = ''
    },

    removeMediaAuthor(mediaIndex, authorIndex) {
      this.selectedMedias[mediaIndex].authors.splice(authorIndex, 1)
    },

    addMainDialogAuthor() {
      this.inputMainDialogAuthorVisible = true
      this.$nextTick(() => {
        this.$refs.mainAuthorInput.$refs.input.focus()
      })
    },

    addMediaDialogAuthor() {
      this.inputMediaDialogAuthorVisible = true
      this.$nextTick(() => {
        this.$refs.mediaAuthorInput.$refs.input.focus()
      })
    },

    handlePublicSentimentAuthorInputConfirm() {
      const inputValue = (this.inputPublicSentimentAuthorValue || '').trim()
      if (!inputValue) {
        this.inputMainDialogAuthorVisible = false
        this.inputMediaDialogAuthorVisible = false
        this.inputPublicSentimentAuthorValue = ''
        return
      }

      if (this.mediaDialogVisible) {
        if (this.tempAuthorsInMediaDialog.includes(inputValue)) {
          this.$message.warning('该作者已存在！')
        } else {
          this.tempAuthorsInMediaDialog.push(inputValue)
        }
      } else {
        if (this.publicSentimentAuthors.includes(inputValue)) {
          this.$message.warning('该作者已存在！')
        } else {
          this.publicSentimentAuthors.push(inputValue)
        }
      }

      this.inputMainDialogAuthorVisible = false
      this.inputMediaDialogAuthorVisible = false
      this.inputPublicSentimentAuthorValue = ''
    },

    removePublicSentimentAuthor(index) {
      this.publicSentimentAuthors.splice(index, 1)
    },

    confirmAddMedia() {
      this.publicSentimentAuthors = [...this.tempAuthorsInMediaDialog]
      const selectedMediaTypes = this.selectedMedias.map(media => media.name)
      this.public_sentiment_prefix = this.public_sentiment_prefix.filter(item =>
        selectedMediaTypes.includes(item.type)
      )
      this.selectedMedias.forEach(media => {
        const mediaAuthors = [...media.authors]
        const existingIndex = this.public_sentiment_prefix.findIndex(item => item.type === media.name)
        const newMediaData = {
          type: media.name,
          author_id: mediaAuthors,
          inputSentimentVisible: false,
          inputSentimentValue: ''
        }
        if (existingIndex > -1) {
          this.$set(this.public_sentiment_prefix, existingIndex, newMediaData)
        } else {
          this.public_sentiment_prefix.push(newMediaData)
        }
      })
      this.closeMediaDialog()
    },

    waitForMediasAndMatch() {
      const checkMedias = () => {
        if (this.allMedias && Object.keys(this.allMedias).length > 0) {
          this.matchExistingMedias()
        } else {
          setTimeout(checkMedias, 100)
        }
      }
      checkMedias()
    },

    handleCurrentChange(page) {
      this.setCurrentPage(page)
    },
    handleSizeChange(size) {
      this.setPageSize(size)
    },

    handleAccountListScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target
      if (scrollHeight > clientHeight && scrollHeight - scrollTop - clientHeight < 50) {
        this.loadMoreAccountData()
      }
    },

    loadMoreAccountData() {
      if (this.accountListHasMoreData && !this.accountListIsLoadingMore) {
        this.getAllAccounts()
      }
    },

    matchExistingMedias() {
      this.selectedMedias = []
      const existingMediaTypes = this.public_sentiment_prefix.map(item => item.type)
      Object.entries(this.allMedias).forEach(([mediaId, media]) => {
        if (existingMediaTypes.includes(media.type)) {
          const existingData = this.public_sentiment_prefix.find(item => item.type === media.type)
          this.selectedMedias.push({
            id: mediaId,
            name: media.type,
            authors: existingData ? [...(existingData.author_id || [])] : [],
            inputAuthorVisible: false,
            inputAuthorValue: ''
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 省略样式，与之前相同 */
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

/* 添加预警词和排除预警词的样式 */
.warning-word-span {
  display: inline-block;
  padding: 0 10px;
  margin: 0 5px;
  height: 24px;
  line-height: 22px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap; /* 强制文本不换行 */
  width: 80px; /* 固定宽度 */
  text-align: center;
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号显示被隐藏的文本 */
  vertical-align: middle; /* 建议添加，使其与其他元素垂直对齐更好看 */
}

/* 用于控制文字截断的样式 */
.tag-text {
  display: inline-block; /* 必须，否则 max-width 无效 */
  vertical-align: middle; /* 优化垂直对齐 */
  max-width: 80px; /* 在这里限制文字的最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.eliminate-word-span {
  display: inline-block;
  padding: 0 10px;
  margin: 0 5px;
  height: 24px;
  line-height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
  white-space: nowrap;
  width: 80px; /* 固定宽度，确保5个字符+省略号能完整显示 */
  text-align: center;
  overflow: hidden;
}

.platform-word-span {
  display: inline-block;
  padding: 0 10px;
  margin: 0 5px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  white-space: nowrap;
  width: 80px; /* 固定宽度，确保5个字符+省略号能完整显示 */
  text-align: center;
  overflow: hidden;
}

.warning {
  padding: 20px;
  height: 100vh; /* 占满整个视口高度 */
  overflow: hidden; /* 防止出现滚动条 */
}

.warning-header {
  height: 60px;
  position: relative;
  margin-bottom: 10px;
  padding: 10px 0;
  flex-shrink: 0;
}

.add-warning-button {
  position: absolute;
  right: 0;
}

.warning-dialog-content {
  max-height: 50vh; /* 使用视口高度的百分比 */
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义对话框样式 */
::v-deep .el-dialog__body {
  max-height: 65vh;
  overflow: hidden;
  padding: 10px 20px;
}

::v-deep .el-dialog {
  margin-top: 5vh !important;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

.warning-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.section-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.section-header .section-title {
  margin-bottom: 0;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
}

.user-id-label {
  margin-right: 10px;
  font-weight: bold;
}

.el-tag {
  margin-right: 5px;
}

.tag-input {
  width: 100px;
  margin-right: 10px;
  vertical-align: bottom;
}

.country-tag {
  display: inline-block;
  margin-right: 5px;
}

.exclude-country {
  color: #f56c6c;
}

.warning-list {
  height: calc(100% - 80px); /* 减去头部高度和边距 */
  margin-top: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

/* 账户选择对话框样式 */
.account-select-container {
  display: flex;
  height: 500px;
  gap: 20px;
}

.account-select-left,
.account-select-right {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.account-list {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.selected-account-table {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.selected-account-table .el-table {
  flex: 1;
  overflow: hidden;
}

.selected-account-table .el-table__body-wrapper {
  overflow-y: auto;
}

.push-options {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.push-options .el-checkbox {
  margin-right: 0;
}

.account-item {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.account-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.account-item.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}


.account-email {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.account-info {
  font-size: 12px;
  color: #909399;
}

.account-info span {
  margin-right: 10px;
}

.no-selected {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-style: italic;
}

/* 媒体选择对话框样式 */
.media-select-container {
  display: flex;
  height: 500px;
  gap: 20px;
}

.media-select-left,
.media-select-right {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.media-list {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.media-item {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.media-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.media-item.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.media-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.media-info {
  font-size: 12px;
  color: #909399;
}

.media-info span {
  margin-right: 10px;
}

.author-section {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  flex-shrink: 0;
}

.section-subtitle {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.selected-media-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.selected-media-table {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.selected-media-table .el-table {
  flex: 1;
  overflow: hidden;
}

.selected-media-table .el-table__body-wrapper {
  overflow-y: auto;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.duplicate-email :deep(.el-input__inner) {
  border-color: #f56c6c !important;
  color: #f56c6c !important;
}

.duplicate-email :deep(.el-input__inner:focus) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

.no-selected {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-style: italic;
}

.media-author-input {
  width: 80px;
  margin: 2px;
  vertical-align: bottom;
}

/* 加载更多状态样式 */
.load-more-status {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.loading-more i {
  margin-right: 8px;
  font-size: 16px;
}

.no-more-data {
  color: #909399;
  font-size: 14px;
  font-style: italic;
}

/* 账户列表加载状态样式 */
.account-load-more-status {
  text-align: center;
  padding: 15px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 10px;
}

.account-load-more-status .loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}

.account-load-more-status .loading-more i {
  margin-right: 6px;
  font-size: 14px;
}

.account-load-more-status .no-more-data {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

/* 新增：用于高亮空输入框的样式 */
.input-error .el-input__inner {
  border-color: #f56c6c !important; /* Element UI 的危险色 */
}

/* （可选）让聚焦时的蓝色阴影也变成红色，效果更突出 */
.input-error .el-input__inner:focus {
  box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.5) !important;
}

/* 分页组件样式 */
.pagination-container {
  text-align: center;
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  margin-top: 0;
  height: 80px; /* 固定分页组件高度 */
  flex-shrink: 0;
  display: flex;
  //align-items: center;
  justify-content: center;
}

.pagination-container .el-pagination {
  margin: 0;
}
</style>
