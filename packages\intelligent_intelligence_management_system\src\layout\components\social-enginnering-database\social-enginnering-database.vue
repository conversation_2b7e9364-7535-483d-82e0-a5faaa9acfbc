<template>
  <div style="position: relative" class="search_view">
    <Spin size="large" fix :show="pathTreeLoad"></Spin>
    <Spin size="large" fix :show="searchPrefixLoad || searchLastLoad">
      <Icon type="ios-loading" size="20" class="demo-spin-icon-load"></Icon>
      <div v-if="!pathShow">
        搜索路径：{{ searchData.pathArr[prefixSearchPathIndex] }}
      </div>
      <div v-if="pathShow">
        搜索路径：{{ pathShow }}
      </div>
      <Button
        v-show="showCancelBtn"
        type="success"
        style="margin-top: 10px"
        @click="cancelSearch"
        title=""
      >
        取消搜索
      </Button>
    </Spin>
    <!-- 搜索头部 -->
    <div class="search_header">
      <div class="search_input">
        <div class="search_data_type">
          <b style="margin-right: 10px; font-size: 18px">数据类型:</b>
          <el-tree-select
            ref="selectPathTreeRef"
            v-model="selectPathValue"
            :data="allPathTreeData"
            placeholder="请选择路径, 默认搜索全部路径"
            style="width: 350px"
            multiple
            clearable
            :check-strictly="true"
            :default-expand-all="true"
            :show-checkbox="true"
            :collapse-tags="true"
            :collapse-tags-tooltip="true"
            node-key="value"
            value-key="value"
            :props="dataTypeProps"
            @check="dataTypeChange"
          />
        </div>
        <div class="input_btn">
          <Input
            ref="searchInputRef"
            search
            enter-button="搜索"
            clearable
            v-model="searchValue"
            placeholder="请输入查询关键字"
            @on-search="searchClick()"
            @on-focus="changeShowHisDom(true)"
            @on-blur="changeShowHisDom(false)"
          />
          <div
              class="search_his"
              v-if="hisValueData.length && showHisDom"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <div class="his_list">
                <List
                  border
                >
                  <ListItem
                    class="his_item"
                    v-for="item in hisValueData"
                    @click="searchHisValue(item)"
                    :key="item"
                  >
                    {{ item }}
                  </ListItem>
                </List>
              </div>
              <Button 
                style="float: right" 
                type="text" 
                icon="md-close" 
                @click="clearHisSearchArr"
              >
                清空历史搜索记录
              </Button>
            </div>
        </div>
      </div>
      <div class="print_box">
        <Icon
          type="ios-print-outline"
          class="print_icon"
          size="26"
          @click="printCurrentPage"
        ></Icon>
      </div>
    </div>
    <!-- 搜索内容 -->
    <div class="search_body">
      <div class="search_result">
        <div class="prefix_search">
          <div class="prefix_title">
            <b>结果分类</b>
          </div>
          <div
            class="prefix_list"
            @scroll="prefixListScroll"
            v-if="combinedSearchList.length"
          >
            <Card
              class="prefix_card"
              v-for="item in combinedSearchList"
              :key="item.row"
              @click.native="preciseCardClick(item)"
              :class="{ 'active-card': item.row === activeCardRow }"
            >
              <p :title="item.columnValues.title">
                <b>数据类型：</b>{{ allPathDesc[item.columnValues.path] }}
              </p>
              <p :title="item.columnValues.title">
                <b>命中字段：</b>
                <span v-html="highlightKeyword(item.columnValues.title, searchValue)"></span>
              </p>
            </Card>
            <p class="search_over_tip" v-if="isSearchCombined">数据已经到底了!</p>
          </div>
          <div class="no_prefix" v-else>
            <p>未查询到前缀数据。</p>
          </div>
        </div>
        <div class="data_search">
          <div class="data_title">
            <div class="data_title_left">
              <b style="margin-top: 0px; margin-right: 10px;font-size: 18px;">数据详情</b>
              <Button
                v-if="dataSearchListShow.length"
                size="small"
                @click="handleSelectAll"
                type="primary"
                >{{ isAllSelected ? '取消全选' : '全选' }}</Button
              >
              <div v-show="selectedItems.length > 0">
                <Button 
                  style="margin-left: 10px" 
                  size="small" 
                  @click="toCollect" 
                  type="success"
                >
                  收藏
                </Button>
                <Button 
                  style="margin-left: 10px" 
                  type="success" 
                  @click="exportToExcel" 
                  size="small"
                >
                  导出
                </Button>
                <Button 
                  style="margin-left: 10px" 
                  size="small" 
                  @click="delDatasSome" 
                  type="error"
                >
                  删除
                </Button>
              </div>
            </div>
            <div class="data_title_right" v-if="pathShow">
              <div class="search_tags">
                <el-tag
                  v-for="(tag, index) in searchTags"
                  :key="index"
                  :closable="true"
                  @close="handleTagClose(index)"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <Input
                search
                enter-button="内容搜索"
                clearable
                v-model="searchChildValue"
                placeholder="请输入查询关键字"
                @on-search="addSearchTag(searchChildValue)"
              />
            </div>
          </div>
          <div class="data_list" v-if="dataSearchListShow.length">
            <div class="twitterlistRow" v-for="(item, index) in dataSearchListShow" :key="index">
              <div class="twitterlistRowLeft">
                <label>
                  <input
                    style="cursor: pointer"
                    type="checkbox"
                    :value="item"
                    v-model="selectedItems"
                    @change="handleSingleSelect(item)"
                  />
                </label>
              </div>
              <div class="twitterlistRowMid">
                <div v-for="(value, key) in item" :key="key" class="twitterlistRowMidcon">
                  <div class="twitterlistRowMidconTit">
                    <b>{{ key }}</b>
                  </div>
                  <Tooltip 
                    placement="top"
                    theme="light"
                    max-width="200"
                  >
                    <div slot="content">
                      <div 
                        style="
                          display:flex;
                          flex-direction: row;
                          align-items: center;
                        "
                      >
                        <span>{{value.toString()}}</span>
                        <Button 
                          size="small" 
                          icon="ios-copy-outline" 
                          type="success"
                          style="margin-left:20px;min-width: 20px;max-width: 20px;"
                          title="点击复制"
                          @click="copyOne(value.toString())"
                        ></Button>
                      </div>
                    </div>
                    <div class="twitterlistRowMidconVal">
                      <span v-html="highlightKeyword(value.toString(), searchValue)"></span>
                    </div>
                  </Tooltip>
                </div>
              </div>
              <div class="twitterlistRowMidBtn">
                <Button 
                  size="small" 
                  type="success"
                  style="margin-top: 10px" 
                  @click="editFn(item)"
                >
                  编辑
                </Button>
                <Button 
                  size="small" 
                  type="success" 
                  style="margin-top: 10px" 
                  @click="toCopy(item)"
                >
                  复制
                </Button>
                <Button 
                  size="small" 
                  type="success" 
                  @click="toCollectSingBtn(item)" 
                  style="margin-top: 10px"
                >
                  收藏
                </Button>
                <Button 
                  size="small" 
                  type="error"
                  @click="delDatas(item)" 
                  style="margin-top: 10px"
                >
                  删除
                </Button>
              </div>
            </div>
            <p
              class="search_over_tip"
              v-if="
                dataSearchOver &&
                Math.ceil(dataSearchList.length / 20) === nowpage
              "
            >
              数据已经到底了!
            </p>
          </div>
          <div
            class="no_data"
            v-if="dataSearchListShow.length == 0 && pathShow.length > 0"
          >
            <p>未查询到数据。</p>
          </div>
          <div
            class="no_data"
            v-if="dataSearchListShow.length == 0 && pathShow.length == 0"
          >
            <p>请点击左侧菜单获取数据</p>
          </div>
          <div style="text-align: center; margin-top: 20px;">
            <Page
              v-if="dataSearchList.length > 20"
              :total="dataSearchList.length"
              size="small"
              :page-size="20"
              @on-change="pageChange"
            />
          </div>
        </div>
      </div>
    </div>
    <el-dialog
        :visible.sync="collectDialog"
        title="选取目录"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div style="width: 95%">
            <cellectTree :listType="'username'" :tableName="'favorites_data'" @getCollect="getCollect" />
        </div>
    </el-dialog>
    <el-dialog
        :visible.sync="editDialog"
        title="编辑"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div style="width: 95%">
            <el-form :model="editForm" label-width="auto" style="max-width: 600px">
                <el-form-item v-for="(value, key) in editForm" :key="key" :label="key">
                    <el-input
                        v-model="editForm[key]"
                        :disabled="key === 'row' || key === 'path' ? true : false"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit">保存</el-button>
                    <el-button @click="editDialog = false">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import cellectTree from '@/layout/components/social-enginnering-database/collectTree.vue';
// import Content from '@/layout/components/social-enginnering-database/content-view.vue';

export default {
  name: 'SearchView',
  components: {
    cellectTree
  },
  data() {
    return {
      editForm: {},
      nowpage: 1,
      searchTags: [],
      hisValueData: JSON.parse(localStorage.getItem('hisValueData')) || [],
      dataTypeProps: { children: 'children', label: 'title' },
      isAllSelected: false,
      searchChildValue: '',
      selectedItems: [],
      selectedItemsSing: [],
      activeCardRow: null,
      collectDialog: false,
      collectNum: 0,
      showCancelBtn: false,
      showHisDom: false,
      isMouseOver: false,
      isFoucs: false,
      historicalPathArr: ['/'],
      nowCheckPath: '/',
    };
  },
  computed: {
    ...mapState('socialEnginneringDatabase', [
      'pathShow', 'allPathTreeData', 'searchData', 'pathTreeLoad', 'searchPrefixLoad',
      'searchLastLoad', 'combinedSearchList', 'isSearchCombined', 'dataSearchList',
      'dataSearchListShow', 'allPathDesc', 'dataSearchOver', 'prefixSearchPathIndex',
    ]),
    searchValue: {
      get() { return this.$store.state.search.searchValue; },
      set(value) { this.$store.commit('search/SET_STATE', { key: 'searchValue', value }); }
    },
    selectPathValue: {
      get() { return this.$store.state.search.selectPathValue; },
      set(value) { this.$store.commit('search/SET_STATE', { key: 'selectPathValue', value }); }
    },
    editDialog: {
      get() { return this.$store.state.search.editDialog; },
      set(value) { this.$store.commit('search/SET_STATE', { key: 'editDialog', value }); }
    }
  },
  methods: {
    ...mapActions('search', [
      'getPathTreeData', 'clearPathTreeData', 'getCaseTreeData', 'clearCaseTreeData',
      'setSearchData', 'startInitialSearch', 'fetchMorePrefixResults', 'fetchSearchData',
      'delDataFn', 'editDataFn', 'clearData', 'clearDataSearchList',
    ]),
    highlightKeyword(text, keyword) {
      if (!keyword || !text) return text;
      const regex = new RegExp(keyword, 'gi');
      return text.replace(regex, match => `<span class="highlight-text">${match}</span>`);
    },
    dataTypeChange(data) {
      let node = this.$refs.selectPathTreeRef.getNode(data);
      let isChecked = node.checked;
      if (data.children && isChecked) {
        this.selectChildren(data, true);
      } else if (data.children && !isChecked) {
        this.selectChildren(data, false);
      }
    },
    selectChildren(node, checked) {
      node.children.forEach(child => {
        if (child.children) {
          this.selectChildren(child, checked);
        }
        if (checked) {
          if (!this.selectPathValue.includes(child.value)) {
            this.selectPathValue.push(child.value);
          }
        } else {
          const index = this.selectPathValue.indexOf(child.value);
          if (index > -1) {
            this.selectPathValue.splice(index, 1);
          }
        }
      });
      // Force update for Vue 2 reactivity with arrays
      this.selectPathValue = [...this.selectPathValue];
    },
    addSearchTag(v) {
      this.nowpage = 1;
      this.searchTags.push(v);
      this.clearDataSearchList();
      this.fetchSearchData({ v: this.nowpage, twoSearchTags: this.searchTags });
      this.searchChildValue = '';
    },
    handleTagClose(index) {
      this.searchTags.splice(index, 1);
      this.nowpage = 1;
      this.clearDataSearchList();
      this.fetchSearchData({ v: this.nowpage, twoSearchTags: this.searchTags });
    },
    copyOne(v) {
      this.$copyText(v).then(() => ElMessage.success('复制成功'), () => ElMessage.error('复制失败'));
    },
    toCopy(v) {
      this.$copyText(JSON.stringify(v, null, 2)).then(() => ElMessage.success('复制成功'), () => ElMessage.error('复制失败'));
    },
    pageChange(v) {
      this.nowpage = v;
      this.isAllSelected = false;
      this.selectedItems = [];
      if (v < 5) {
        this.$store.commit('search/SET_DATA_SEARCH_LIST_SHOW', this.dataSearchList.slice((v - 1) * 20, v * 20));
      } else {
        if (this.dataSearchList.length / 20 === v) {
          this.$store.commit('search/SET_DATA_SEARCH_LIST_SHOW', this.dataSearchList.slice((v - 1) * 20, v * 20));
          this.fetchSearchData({ v, twoSearchTags: this.searchTags });
        } else {
          this.$store.commit('search/SET_DATA_SEARCH_LIST_SHOW', this.dataSearchList.slice((v - 1) * 20, v * 20));
        }
      }
      this.$nextTick(() => {
        const dataListEl = this.$el.querySelector('.data_list');
        if (dataListEl) dataListEl.scrollTop = 0;
      });
    },
    getCollect(data) {
        // ... (Logic from original)
    },
    searchClick(type) {
      this.$refs.searchInputRef.blur();
      if (type !== 'router' && this.historicalPathArr.length > 1) {
        this.historicalPathArr = ['/'];
        this.nowCheckPath = '/';
      }
      this.activeCardRow = null;
      this.clearDataSearchList();
      this.clearData();
      this.setLoadingTimeout();
      this.setHisSearchValue();
      
      const searchData = {
        value: this.searchValue,
        database: 'public', // Example default
        caseId: '', // Example default
        pathArr: this.selectPathValue,
        relationArr: this.historicalPathArr,
      };
      this.selectedItems = [];
      this.setSearchData(searchData);
      this.startInitialSearch();
    },
    handleSelectAll() {
        this.isAllSelected = !this.isAllSelected;
        if (this.isAllSelected) {
            this.selectedItems = [...this.dataSearchListShow];
        } else {
            this.selectedItems = [];
        }
    },
    handleSingleSelect() {
        this.isAllSelected = this.selectedItems.length === this.dataSearchListShow.length;
    },
    editFn(v) {
        this.editForm = JSON.parse(JSON.stringify(v));
        this.editDialog = true;
    },
    onSubmit() {
        this.editDataFn({ editForm: this.editForm, nowpage: this.nowpage });
    },
    exportToExcel() {
        // ... (Logic from original)
    },
    toCollectSingBtn(v) {
        this.selectedItemsSing = [v];
        this.collectDialog = true;
    },
    toCollect() {
        this.selectedItemsSing = [];
        this.collectDialog = true;
    },
    delDatasSome() {
      MessageBox.confirm('确定要删除?', '提示', { type: 'warning' }).then(() => {
        this.isAllSelected = false;
        this.delDataFn(this.selectedItems);
        this.selectedItems = [];
      }).catch(() => ElMessage.info('取消'));
    },
    delDatas(v) {
      MessageBox.confirm('确定要删除?', '提示', { type: 'warning' }).then(() => {
        this.isAllSelected = false;
        this.selectedItems = [];
        this.delDataFn([v]);
      }).catch(() => ElMessage.info('取消'));
    },
    prefixListScroll(event) {
      if (this.isSearchCombined || this.searchPrefixLoad) return;
      const element = event.target;
      if (element.scrollTop + element.clientHeight >= element.scrollHeight - 10) {
        this.fetchMorePrefixResults();
      }
    },
    preciseCardClick(item) {
      this.isAllSelected = false;
      this.selectedItems = [];
      this.searchTags = [];
      this.searchChildValue = '';
      this.clearDataSearchList();
      const key = item.columnValues.r.type.name;
      const data = item.columnValues;
      this.activeCardRow = item.row;
      this.$store.commit('search/SET_STATE', { key: 'setNowsearchDataPath', value: data.path });
      this.$store.commit('search/SET_STATE', { key: 'setNowsearchDataRelation', value: `${data.title};${key}` });
      this.fetchSearchData({ v: 1, twoSearchTags: this.searchTags });
    },
    changeShowHisDom(bool) {
        this.isFoucs = bool;
        if (!this.isMouseOver) {
            this.showHisDom = bool;
        }
    },
    setHisSearchValue() {
        if (this.searchValue) {
            if (this.hisValueData.includes(this.searchValue)) {
                const index = this.hisValueData.indexOf(this.searchValue);
                this.hisValueData.splice(index, 1);
            }
            this.hisValueData.unshift(this.searchValue);
            if (this.hisValueData.length > 10) this.hisValueData.pop();
            localStorage.setItem('hisValueData', JSON.stringify(this.hisValueData));
        }
    },
    searchHisValue(item) {
        this.searchValue = item;
        this.searchClick();
    },
    clearHisSearchArr() {
        this.hisValueData = [];
        localStorage.setItem('hisValueData', JSON.stringify(this.hisValueData));
    },
    printCurrentPage() {
        window.print();
    },
    cancelSearch() {
        this.$store.commit('search/SET_STATE', { key: 'searchPrefixLoad', value: false });
        this.$store.commit('search/SET_STATE', { key: 'searchLastLoad', value: false });
    },
    setLoadingTimeout() {
        this.showCancelBtn = false;
        setTimeout(() => {
            if (this.searchPrefixLoad || this.searchLastLoad) {
                this.showCancelBtn = true;
            }
        }, 15000);
    },
    handleMouseEnter() { this.isMouseOver = true; },
    handleMouseLeave() {
        this.isMouseOver = false;
        if (!this.isFoucs) this.showHisDom = false;
    }
  },
  created() {
    this.hisValueData = JSON.parse(localStorage.getItem('hisValueData')) || [];
    this.clearPathTreeData();
    const selectPathData = localStorage.getItem('selectPathData');
    if (selectPathData) {
      this.$store.commit('search/SET_STATE', { key: 'allPathTreeData', value: JSON.parse(selectPathData) });
      this.$store.commit('search/SET_STATE', { key: 'pathTreeLoad', value: false });
    } else {
      this.getPathTreeData();
    }
    const allPathArr = localStorage.getItem('allPathArr');
    if (allPathArr) this.$store.commit('search/SET_STATE', { key: 'allPathArr', value: JSON.parse(allPathArr) });
    const allPathDesc = localStorage.getItem('allPathDesc');
    if (allPathDesc) this.$store.commit('search/SET_STATE', { key: 'allPathDesc', value: JSON.parse(allPathDesc) });
  },
  mounted() {
    this.searchClick();
  },
};
</script>

<style scoped lang="scss">
.search_view {
  height: 90vh;
  width: 100%;
  background-color: #fff;
}
.search_header {
  height: 5%;
  width: 100%;
  margin-top: 5px;
  background-color: #fff;
  padding: 5px 10px 0px 0px;
  display: flex;
  align-items: center;
}
.search_input {
  width: 90%;
  display: flex;
  .search_data_type {
    width: auto;
    display: flex;
    align-items: center;
    margin-left: 10px;
    .tree_select {
      width: 250px;
    }
  }
  .input_btn {
    margin-left: 20px;
    width: 30%;
    position: relative;
    .search_his {
      position: absolute;
      width: 87.3%;
      background: #fff;
      border: 1px solid #e2e2e2;
      border-radius: 2px;
      z-index: 5;
      top: 40px;
      left: 0;
      .his_list {
        max-height: 260px;
        overflow: auto;
        .his_item {
          padding-left: 20px;
          cursor: pointer;
        }
        .his_item:hover {
          background-color: #eeecec;
        }
      }
    }
  }
}

.print_box {
  width: 10%;
  height: 32px;
  text-align: right;

  .print_icon {
    cursor: pointer;
    &:hover {
      color: #2d8cf0;
    }
  }
}
.search_body {
  margin-top: 5px;
  border-top: 1px solid #e8eaec;
  width: 100%;
  height: 95%;
  padding: 5px 0px 0px 10px;
  .search_criteria {
    height: 120px;
    background-color: #fff;
    .search_database {
      padding: 0px 20px;
      height: 60px;
      border-bottom: 1px dashed #adadad;
      display: flex;
      align-items: center;
      .radio_group {
        margin: 0 10px;
      }
    }
    .search_path {
      padding: 0px 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .data_num {
        display: flex;
        p {
          font-weight: bold;
          margin-left: 20px;
          font-size: 15px;
          span {
            color: #2b85e4;
            padding: 0 5px;
            font-size: 19px;
          }
        }
      }
    }
  }
  /deep/ .highlight-text {
    color: #ff0000;
    font-weight: bold;
    background-color: transparent;
  }
  .search_result {
    display: flex;
    height: 100%;
    .prefix_search {
      width: 15%;
      height: 100%;
      position: relative;
      .prefix_title {
        height: 36px;
        line-height: 36px;
        font-size: 18px;
        
      }
      .prefix_list {
        margin-top:10px;
        position: relative;
        overflow: auto;
        height: 95%;
        .prefix_card {
          background-color: #fff;
          margin-bottom: 5px;
          cursor: pointer;
          /deep/ .ivu-card-body {
            padding: 5px;
          }
          /deep/ .highlight-text {
            color: #ff0000;
            font-weight: bold;
            background-color: transparent;
          }
        }
        .prefix_card:hover {
          background-color: #e9e9e9;
        }
        .active-card {
          border: 1px solid #2d8cf0;
          background-color: #f0faff;
          box-shadow: 0 0 5px rgba(45, 140, 240, 0.5);
        }
      }
      .no_prefix {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .data_search {
      width: 85%;
      height: 100%;
      .data_title {
        padding-left:10px;
        display: flex;
        justify-content: space-between;
        height: 36px;
        flex-direction: row;
        align-items: center;
        .data_title_left {
          display: flex;
        }
      }
      .data_list {
        height: 90%;
        overflow: auto;
        .twitterlistRow {
          display: flex;
          border-radius: 10px;
          box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
          padding-bottom: 10px;
          padding-top: 10px;
          margin: 10px;
          .twitterlistRowLeft {
            text-align: center;
            min-width: 30px;
            display: flex;
            align-content: center;
            flex-wrap: wrap;
            justify-content: center;
          }
          .twitterlistRowMid {
            display: flex;
            flex-wrap: wrap;
            width: 90%;
            .twitterlistRowMidcon {
              padding: 10px;
              width:14%;
              .twitterlistRowMidconTit {
                min-width: 150px;
                text-align: center;
              }
              .twitterlistRowMidconVal {
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 150px;
                display: inline-block;
                color: #13CE66;
              }
            }
          }
          .twitterlistRowMidBtn {
            padding-right: 5px;
            text-align: right;
            min-width: 200px;
            flex-wrap: wrap;
            display: flex;
            align-content: center;
            gap: 5px
          }
        }
      }
      .no_data {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .search_over_tip {
      color: #2b85e4;
      text-align: center;
      background-color: #fff;
      padding: 10px 0;
    }
  }
}
/deep/ .ivu-select-dropdown {
  max-height: 300px;
}
/deep/ .ivu-select-default.ivu-select-multiple .ivu-select-selection {
  max-height: 32px;
  overflow: hidden;
}
/deep/ .ivu-input-wrapper .ivu-input-search {
  height: 32px;
}
/deep/ .ivu-input-wrapper .ivu-input-search .ivu-input-group-append .ivu-input-search-enter-button {
  height: 32px;
}
/deep/ .ivu-input {
  height: 32px;
  line-height: 32px;
}
/deep/ .ivu-input-search-enter-button {
  height: 32px;
}
/deep/ .ivu-input-search-enter-button .ivu-input {
  height: 32px;
  line-height: 32px;
}
/deep/ .ivu-input-search-enter-button .ivu-btn {
  height: 32px;
  line-height: 30px;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
  to { transform: rotate(360deg); }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.data_title_right {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.search_tags {
  margin-left: 10px;
  display: flex;
  gap: 5px;
  margin-right: 5px;
}
</style>