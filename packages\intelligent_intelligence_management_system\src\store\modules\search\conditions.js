export default {
  namespaced: true,
  state: {
    conditionsData: {
      queryMode: "content_article",
      customTime: [new Date(), new Date()],
      timeRange: "24h",
      time_range_begin: 0,
      time_range_end: 0,
      queryString: "",
      ppqueryMode: "match_phrase",
    },
  },
  mutations: {
    setConditionsData(state, v) {
      if (v) {
        state.conditionsData.queryString = v;
      } else {
        state.conditionsData.queryString = "";
      }
      state.conditionsData.time_range_begin = 0;
      state.conditionsData.time_range_end = 0;
      state.conditionsData.timeRange = "";
      state.conditionsData.queryMode = "content_article";
      state.conditionsData.ppqueryMode = "match";
      state.conditionsData.customTime = [new Date(), new Date()];
    },
    setCustomTime(state, v) {
      state.conditionsData.customTime = v;
    },
    setSearchTaskTimeRange(state, v) {
      console.log("setSearchTaskTimeRange", v);
      state.conditionsData.time_range_end = v.time_range_end;
      state.conditionsData.time_range_begin = v.time_range_begin;
      let day = getTimeDiff(v.time_range_end, v.time_range_begin);
      function getTimeDiff(timestamp1, timestamp2) {
        const diffMs = Math.abs(timestamp2 - timestamp1);
        const days = Math.floor(diffMs / (60 * 60 * 24));
        const hours = Math.floor(diffMs / (60 * 60));

        if (days >= 1) {
          return `${days} 天`;
        } else {
          return `24小时`;
        }
      }
      state.conditionsData.timeRange = day;
      console.log("setSearchTaskTimeRange2", state.conditionsData.timeRange);
    },
    setTimeRange(state, v) {
      state.conditionsData.timeRange = v;
      console.log("setTimeRange", v);
      state.conditionsData.time_range_end = 0;
      state.conditionsData.time_range_begin = 0;
      let nowTimestamp = Date.parse(new Date()) / 1000;
      switch (v) {
        case "":
          state.conditionsData.time_range_end = 0;
          state.conditionsData.time_range_begin = 0;
          break;
        case "今天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = new Date().setHours(
            0,
            0,
            0,
            0
          );
          break;
        case "本月":
          state.conditionsData.time_range_end = nowTimestamp;
          const date = new Date();
          date.setDate(1);
          date.setHours(0, 0, 0, 0);
          state.conditionsData.time_range_begin = date.getTime();
          break;
        case "24h":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 24 * 3600
          );
          break;
        case "2天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 2 * 24 * 3600
          );
          break;
        case "3天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 3 * 24 * 3600
          );
          break;
        case "7天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 7 * 24 * 3600
          );
          break;
        case "10天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 10 * 24 * 3600
          );
          break;
        case "30天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 30 * 24 * 3600
          );
          break;
        case "60天":
          state.conditionsData.time_range_end = nowTimestamp;
          state.conditionsData.time_range_begin = parseInt(
            nowTimestamp - 60 * 24 * 3600
          );
          break;
        case "自定义时间":
          state.conditionsData.time_range_end =
            state.conditionsData.customTime[0] / 1000;
          state.conditionsData.time_range_begin =
            state.conditionsData.customTime[1] / 1000;
          break;
      }
    },
    setQueryMode(state, v) {
      state.conditionsData.queryMode = v;
    },
    setQueryString(state, v) {
      state.conditionsData.queryString = v;
    },
    setPPQueryMode(state, v) {
      state.conditionsData.ppqueryMode = v;
    },
    setSearchTask(state, v) {
      console.log("setSearchTask", v);
    },
  },
  actions: {
    sendSearchTask({ state, dispatch, commit }, v) {
      console.log("sendSearchTask", v);
      let nowTimestamp = Date.parse(new Date()) / 1000;
      let tmpCondition = {};
      let begin_timestamp = new Date().getTime();
      let end_timestamp;
      tmpCondition["simple_query_string"] = {
        query: v.task.queryString,
        fields: ["content"],
        default_operator: "and",
      };

      switch (v.task.timeRange) {
        case "无":
          begin_timestamp = 0;
          end_timestamp = 0;
          break;
        case "今天":
          end_timestamp = nowTimestamp;
          begin_timestamp = new Date().setHours(0, 0, 0, 0);
          break;
        case "本月":
          end_timestamp = nowTimestamp;
          const date = new Date();
          date.setDate(1);
          date.setHours(0, 0, 0, 0);
          begin_timestamp = date.getTime();
          break;
        case "24h":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 24 * 3600);
          break;
        case "2天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 2 * 24 * 3600);
          break;
        case "3天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 3 * 24 * 3600);
          break;
        case "7天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 7 * 24 * 3600);
          break;
        case "10天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 10 * 24 * 3600);
          break;
        case "30天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 30 * 24 * 3600);
          break;
        case "60天":
          end_timestamp = nowTimestamp;
          begin_timestamp = parseInt(nowTimestamp - 60 * 24 * 3600);
          break;
      }

      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.AddSimpleTask",
        [
          {
            head: {},
            msg: {
              task_authority: "username",
              task_type: "ai_workflow_task",
              method: "data_analysis_search_task",
              title: v.task.name,
              parms: {
                /* inputs: {
                  task_id: "",
                  query_string_list: v.task.queryString,
                  time_range: v.task.timeRange,
                  hbase_table_name: "favorites_data",
                  authority:
                    window.main.$store.state.userInfo.userinfo.authority,
                  username: window.main.$store.state.userInfo.userinfo.username,
                  sqlite_table_name: "TEST",
                }, */
                add_es_query_conditions: {
                  bool: {
                    must: [tmpCondition],
                  },
                },
                start_timestamp: begin_timestamp,
                end_timestamp: end_timestamp,
              },
            },
          },
        ],
        "search/conditions/setSearchTask"
      );
    },
  },
};
