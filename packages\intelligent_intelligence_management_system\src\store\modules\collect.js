export default {
  namespaced: true,
  state: {
    checkedCities: [],
    activeName: "username",
    dataList: [],
    nowCollect: null,
    showComponent: false,
    collectTabsActiveName: "",
    searchTabs: [],
    collCache: null,
    collectLoading: false,
    from: 0,
    size: 200,
  },
  mutations: {
    setCheckedCities(state, v) {
      console.log("setCheckedCities", v);

      state.checkedCities = v;
    },
    setCollCache(state, v) {
      state.collCache = v;
    },
    setCollectLoading(state, data) {
      state.collectLoading = data;
    },
    clearDataList(state) {
      state.dataList = [];
    },
    // 删除收藏数据
    sendDelFileData(state, v) {
      console.log("nowCollect", state.nowCollect);

      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DelData",
        [
          {
            head: {
              row_key: v.row,
            },
            msg: {
              table: "favorites_data",
              type: "username",
              relation: state.nowCollect
                ? state.nowCollect.id + ";" + state.collectTabsActiveName
                : "",
            },
          },
        ],
        "collect/isDelFileData"
      );
    },
    // 是否删除收藏成功
    isDelFileData(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除成功");
        window.main.$store.commit("collect/clearDataList");
        if (state.activeName === "case") {
          window.main.$store.dispatch("collect/sendCaseFileDataList");
        } else {
          window.main.$store.commit("collect/getOpinionCollect");
        }
      } else {
        window.main.$message.warning("删除失败！");
      }
    },
    getOpinionCollect(state) {
      state.collectLoading = true;
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: state.size,
              from: state.from,
            },
            msg: {
              table: "favorites_data",
              prefix: "",
              type: "username",
              relation: state.nowCollect.id + ";" + state.collectTabsActiveName,
            },
          },
        ],
        "collect/setCollectData"
      );
    },
    setTabs(state, data) {
      state.searchTabs =
        data[
          "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
        ].tabs;
      state.collectTabsActiveName =
        data[
          "/etc/web/intelligent_intelligence_management_system/search_list_tabs"
        ].tabs[0].value;
      window.main.$store.commit("collect/setShowComponent", true);
    },
    setShowComponent(state, data) {
      state.showComponent = data;
    },
    setCollectTabsActiveName(state, data) {
      console.log("collectTabsActiveName", data);
      state.collectTabsActiveName = data;
    },
    setNowCollect(state, data) {
      state.nowCollect = data;
    },
    setCollectData(state, data) {
      console.log("setCollectData", data);
      state.collectLoading = false;

      if (data?.length) {
        let dataArr = [];
        data.forEach((item) => {
          let data = item.columnValues.d.file_data;
          data["row"] = item.row;
          Array.isArray(data) ? dataArr.push(data[0]) : dataArr.push(data);
        });
        state.dataList = dataArr.concat(state.dataList);
        state.from += state.size;
      } else {
        window.main.$message.warning("没有数据！");
        // state.dataList = [];
      }
    },
    // 删除文件节点
    delFilePath(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.DelData",
        [
          {
            head: {
              row_key: [v.row],
            },
            msg: {
              type: "username",
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              path: "/network_media/public_opinion/bulletin",
              relation: v.fatherId ? `${v.fatherId};${v.type}` : "",
            },
          },
        ],
        "collect/isdelFilePath"
      );
    },
    isdelFilePath(state, data) {
      if (data?.status === "ok") {
        window.main.$message.success("删除成功!");
      } else {
        window.main.$message.warning("删除失败!");
      }
    },
    //添加文件路径
    addFilePath(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {},
            msg: {
              type: "username",
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              path: "/network_media/public_opinion/bulletin",
              prefix: v.random,
              relation: v.data.id ? `${v.data.id};${v.type}` : "",
              data: {
                data: {
                  name: {
                    name: v.name,
                  },
                  type: {
                    type: v.type,
                  },
                  _: {
                    _: v.random,
                  },
                },
              },
            },
          },
        ],
        "collect/isAddFilePath"
      );
    },
    // 是否成功添加文件路径
    isAddFilePath(state, data) {
      if (data?.status === "ok") {
        window.main.$message.success("添加成功!");
      } else {
        window.main.$message.warning("添加失败!");
      }
    },
    // 获取目录下的session列表
    sendFileSession(state, v) {
      if (v) {
        state.nowFileId = v;
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              size: 200,
            },
            msg: {
              path: "/network_media/public_opinion/bulletin",
              prefix: "",
              type: "username",
              relation: state.nowFileId + ";bulletin",
            },
          },
        ],
        "collect/setSessionList"
      );
    },
    setSessionList(state, data) {
      state.bulletinList = data;
    },
    // 修改文件路径名
    amendFileName(state, v) {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.AddData",
        [
          {
            head: {},
            msg: {
              type: "username",
              authority: window.main.$store.state.userInfo.userinfo.authority,
              username: window.main.$store.state.userInfo.userinfo.username,
              path: "/network_media/public_opinion/bulletin",
              prefix: v.id,
              relation: v.fatherId ? `${v.fatherId};${v.type}` : "",
              data: {
                data: {
                  name: {
                    name: v.name,
                  },
                  _: {
                    _: v.id,
                  },
                },
              },
            },
          },
        ],
        "collect/isAmendFileName"
      );
    },
    isAmendFileName(state, data) {
      if (data?.status === "ok") {
        window.main.$message.success("修改成功!");
      } else {
        window.main.$message.warning("修改失败!");
      }
    },
  },
  actions: {
    //获取etc路径
    getTabs(state) {
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs":
                "",
            },
          },
        ],
        "collect/setTabs"
      );
    },
  },
};
