// import { image } from 'd3';
import parse from "emailjs-mime-parser";
export default {
  namespaced: true,
  state: {
    isRegist: false,
    getParm: false,
    loginSucc: false,
    passwd: "",
    mailboxList: [],
    inbox: {},
    curInbox: [],
    picture: [],
    attachment: [],
    sendMailInfo: {
      to: [],
      pictureInfo: [],
      attachmentInfo: [],
      htmlBody: ``,
      pictureBody: [],
      attachmentBody: [],
    },

    headersData: [], //邮件头部数据
    htmlData: "", //邮件内容html
    imgCid: "", //处理过后的content-id
    imgUrl: "", //处理后的图片blob链接
    fileUrl: "", //处理后的附件blob链接
    fileDataAll: [], //附件的数据
    filename: "",
    mhtmldata: [], //mhtml
    contactList: {},
    inboxList: [],
    curMailboxId: "",
    blobstrs: "",
    basestrs: "",
    urls: "",
    //我的变量
    mailBoxUrl: "", //获得邮件发过来的url
  },
  mutations: {
    moveMessage(state, arg) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.MoveMessage",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              from: arg.row,
            },
            msg: {
              password: state.passwd,
              mailbox: arg.src,
              dest_mailbox: arg.dest,
            },
          },
        ],
        "mailbox/afterMoveMessage"
      );
    },

    afterMoveMessage(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("移动成功!");
          this.commit("mailbox/detailMailbox", state.curMailboxId);
        } else {
          window.main.$message.error("移动失败!");
        }
      }
    },

    delMailbox(state, id) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.DelMailbox",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: state.passwd,
              mailbox: id,
            },
          },
        ],
        "mailbox/afterDelMailbox"
      );
    },

    afterDelMailbox(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("删除成功!");
          this.commit("mailbox/listMailbox", state.passwd);
        } else {
          window.main.$message.error("删除失败!");
        }
      }
    },

    createMailbox(state, id) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.CreateMailbox",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: state.passwd,
              mailbox: id,
            },
          },
        ],
        "mailbox/afterCreateMailbox"
      );
    },

    afterCreateMailbox(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("添加成功!");
          this.commit("mailbox/listMailbox", state.passwd);
        } else {
          window.main.$message.error("添加失败!");
        }
      }
    },

    changePasswd(state, passwd) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.SetUser",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: passwd,
            },
          },
        ],
        "mailbox/afterChangePasswd"
      );
    },

    afterChangePasswd(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("密码修改成功!");
          // state.isRegist = true
        } else {
          window.main.$message.error("密码修改失败!");
          // state.isRegist = false
        }
        // state.getParm = true
      }
    },

    // 删除邮件
    delMail(state, v) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.DelMessage",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              from: v,
            },
            msg: {
              password: state.passwd,
              mailbox: state.curMailboxId,
            },
          },
        ],
        "mailbox/isDelMail"
      );
    },
    isDelMail(state, res) {
      if (res?.status === "ok") {
        window.main.$message.success("删除成功！");
        window.main.$store.commit("mailbox/detailMailbox", state.curMailboxId);
      }
    },
    clearFileData(state, v) {
      state.fileDataAll = [];
    },
    setPictureInfo(state, mail) {
      let htmlBodyEnd = `
				</body>
			</html>`;

      state.sendMailInfo.pictureInfo = [];
      state.sendMailInfo.getPictureInfo = false;

      for (let i = 0; i < mail.picture.length; i++) {
        state.sendMailInfo.pictureInfo[i] = {};
        state.sendMailInfo.pictureInfo[i].name = mail.picture[i].name;
        state.sendMailInfo.pictureInfo[i].type = mail.picture[i].raw.type;
        let img = new Image();
        img.src = mail.picture[i].url;
        img.onload = () => {
          state.sendMailInfo.pictureInfo[i].height = img.height;
          state.sendMailInfo.pictureInfo[i].width = img.width;
          state.sendMailInfo.pictureInfo[
            i
          ].img = `<img moz-do-not-send="false" src="cid:${state.sendMailInfo.pictureInfo[i].name}" width="${state.sendMailInfo.pictureInfo[i].width}" height="${state.sendMailInfo.pictureInfo[i].height}">`;
          state.sendMailInfo.htmlBody += state.sendMailInfo.pictureInfo[i].img;
          if (i == mail.picture.length - 1) {
            state.sendMailInfo.htmlBody += htmlBodyEnd;
          }
        };

        let reader = new FileReader();
        reader.readAsDataURL(mail.picture[i].raw);

        reader.onload = (e) => {
          let tmpPos = e.target.result.indexOf(",");
          state.sendMailInfo.pictureInfo[i].base64 = e.target.result.slice(
            tmpPos + 1
          );
          state.sendMailInfo.pictureBody[i].body =
            state.sendMailInfo.pictureInfo[i].base64;
          if (i == mail.picture.length - 1) {
            state.sendMailInfo.getPictureInfo = true;
          }
        };
      }
      // let interval = window.setInterval( function(){
      // 	if (state.sendMailInfo.getPictureInfo == true) {
      // 		window.clearInterval (interval)
      //
      // 	}
      // }, 100 )
      // while (1){
      // 	if (state.sendMailInfo.getPictureInfo) {
      // 		return
      // 	}
      // }
    },
    setAttachmentInfo(state, mail) {
      state.sendMailInfo.attachmentInfo = [];
      state.sendMailInfo.getAttachmentInfo = false;

      for (let i = 0; i < mail.attachment.length; i++) {
        state.sendMailInfo.attachmentInfo[i] = {};
        state.sendMailInfo.attachmentInfo[i].name = mail.attachment[i].name;
        state.sendMailInfo.attachmentInfo[i].type = mail.attachment[i].raw.type;
        if (mail.attachment[i].hash) {
          state.sendMailInfo.attachmentInfo[i].hash = mail.attachment[i].hash;
        }
        let reader = new FileReader();
        reader.readAsDataURL(mail.attachment[i].raw);

        reader.onload = (e) => {
          let tmpPos = e.target.result.indexOf(",");
          state.sendMailInfo.attachmentInfo[i].base64 = e.target.result.slice(
            tmpPos + 1
          );
          state.sendMailInfo.attachmentBody[i].body =
            state.sendMailInfo.attachmentInfo[i].base64;
          if (i == mail.attachment.length - 1) {
            state.sendMailInfo.getAttachmentInfo = true;
          }
        };
      }
    },

    sendMail(state, mail) {
      state.sendMailInfo = {};

      state.sendMailInfo.getPictureInfo = true;
      state.sendMailInfo.getAttachmentInfo = true;

      state.sendMailInfo.to = [];

      for (let i = 0; i < mail.to.length; i++) {
        let tmp_pos1 = mail.to[i].indexOf("@");
        let tmp_pos2 = mail.to[i].indexOf(".");
        if (tmp_pos1 == -1 || tmp_pos2 == -1) {
          alert("收件人格式错误！");
          return;
        }

        state.sendMailInfo.to[i] = {};
        state.sendMailInfo.to[i].username = mail.to[i].slice(0, tmp_pos1);
        state.sendMailInfo.to[i].authority = mail.to[i].slice(
          tmp_pos1 + 1,
          tmp_pos2
        );
      }

      let htmlBodyBeg = `<html>
					<head>
						<meta http-equiv="content-type" content="text/html; charset=GBK">
					</head>
					<body>

				`;
      let htmlBodyEnd = `

					</body>
				</html>

				`;
      // <a :download="${state.filename.name}" :href='/filesystem/api/rest/v1/big_file/put_sha512_file${state.filename.hash}?session_id=${window.main.$store.state.userInfo.session_id}'>${state.filename.name}</a>
      state.sendMailInfo.htmlBody = ``;
      state.sendMailInfo.htmlBody += htmlBodyBeg;

      if (mail?.text) {
        state.sendMailInfo.htmlBody += mail.text;
      }

      if (mail?.picture?.length) {
        state.sendMailInfo.pictureBody = [];

        this.commit("mailbox/setPictureInfo", mail);

        for (let i = 0; i < state.sendMailInfo.pictureInfo.length; i++) {
          state.sendMailInfo.pictureBody[i] = {};
          state.sendMailInfo.pictureBody[i].content_type =
            state.sendMailInfo.pictureInfo[i].type;
          state.sendMailInfo.pictureBody[i].filename =
            state.sendMailInfo.pictureInfo[i].name;
          state.sendMailInfo.pictureBody[i].transfer_encoding = "base64";
          // state.sendMailInfo.pictureBody[i].body = state.sendMailInfo.pictureInfo[i].base64
        }
      } else {
        state.sendMailInfo.htmlBody += htmlBodyEnd;
      }

      if (mail.attachment.length) {
        state.sendMailInfo.attachmentBody = [];

        this.commit("mailbox/setAttachmentInfo", mail);

        for (let i = 0; i < state.sendMailInfo.attachmentInfo.length; i++) {
          state.sendMailInfo.attachmentBody[i] = {};
          state.sendMailInfo.attachmentBody[i].content_type =
            state.sendMailInfo.attachmentInfo[i].type;
          state.sendMailInfo.attachmentBody[i].filename =
            state.sendMailInfo.attachmentInfo[i].name;
          if (state.sendMailInfo.attachmentInfo[i].hash) {
            state.sendMailInfo.attachmentBody[i].hash =
              state.sendMailInfo.attachmentInfo[i].hash;
          }
          state.sendMailInfo.attachmentBody[i].transfer_encoding = "base64";
          // state.sendMailInfo.attachmentBody[i].body = state.sendMailInfo.attachmentInfo[i].base64
        }
      }

      let interval = window.setInterval(function () {
        if (
          state.sendMailInfo.getPictureInfo &&
          state.sendMailInfo.getAttachmentInfo
        ) {
          window.clearInterval(interval);

          for (let i = 0; i < state.sendMailInfo.to.length; i++) {
            window.main.$emlmsg_socket.sendData(
              "Api.Eml.SendMessage",
              [
                {
                  head: {
                    session_id: window.main.$store.state.userInfo.session_id,
                  },
                  msg: {
                    password: state.passwd,
                    to: [
                      {
                        authority: state.sendMailInfo.to[i].authority,
                        username: state.sendMailInfo.to[i].username,
                      },
                    ],
                    subject: mail.subject,
                    // "text_body": {
                    // 	"charset": "utf-8",
                    // 	"transfer_encoding": "7bit",
                    // 	"body": mail.text,
                    // },
                    html_body: {
                      charset: "utf-8",
                      transfer_encoding: "7bit",
                      body: state.sendMailInfo.htmlBody,
                      attachment_body: state.sendMailInfo.pictureBody,
                    },
                    attachment_body: state.sendMailInfo.attachmentBody,
                  },
                },
              ],
              "mailbox/afterSendMail"
            );
          }
        } else {
        }
      }, 100);
    },
    afterSendMail(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("发送成功");
        } else {
          alert(data);
        }
      }
    },
    setUser(state, passwd) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.SetUser",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: passwd,
            },
          },
        ],
        "mailbox/afterSetUser"
      );
    },
    afterSetUser(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("账号注册成功!");
          state.isRegist = true;
        } else {
          window.main.$message.error("账号注册失败!");
          state.isRegist = false;
        }
        state.getParm = true;
      }
    },
    checkUser() {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.CheckUser",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
          },
        ],
        "mailbox/afterCheckUser"
      );
    },
    afterCheckUser(state, data) {
      if (data) {
        if (data.status === "ok") {
          window.main.$message.success("账号已注册");
          state.isRegist = true;

          if (state.passwd) {
            this.commit("mailbox/listMailbox", state.passwd);
          }
        } else {
          window.main.$message.error("账号未注册");
          state.isRegist = false;
        }
        state.getParm = true;
      }
    },

    listMailbox(state, passwd) {
      state.passwd = passwd;
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.ListMailbox",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: passwd,
            },
          },
        ],
        "mailbox/afterListMailbox",
        (error) => {
          //登录失败回调
          window.main.$message({
            type: "error",
            message: "登录失败，用户名或密码错误，请重新输入并登录",
            onClose: function () {
              state.passwd = "";
            },
            duration: 3000,
          });
        }
      );
    },
    afterListMailbox(state, data) {
      let tmpInboxName = [];
      let tmpInboxLayer = 0;
      if (data && data.length) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].Name.indexOf("INBOX") != -1) {
            //
            let tmpLayer = data[i].Name.split(".").length;
            if (!tmpInboxName[tmpLayer - 1]) {
              tmpInboxName[tmpLayer - 1] = [];
            }
            tmpInboxName[tmpLayer - 1].push(data[i].Name);
          }
        }
        tmpInboxLayer = tmpInboxName.length - 1;
        let tmpInboxList = [];
        for (let i = tmpInboxLayer - 1; i >= 0; i--) {
          tmpInboxList[i] = [];
          if (!tmpInboxList[i + 1]) {
            for (let j = 0; j < tmpInboxName[i].length; j++) {
              tmpInboxList[i][j] = {};
              tmpInboxList[i][j].id = tmpInboxName[i][j];
              if (tmpInboxName[i][j].lastIndexOf(".") != -1) {
                tmpInboxList[i][j].label = tmpInboxName[i][j].slice(
                  tmpInboxName[i][j].lastIndexOf(".") + 1
                );
              } else {
                tmpInboxList[i][j].label = "收件箱";
              }
              tmpInboxList[i][j].children = [];

              for (let k = 0; k < tmpInboxName[i + 1].length; k++) {
                let tmpChild = {};
                if (tmpInboxName[i + 1][k].indexOf(tmpInboxName[i][j]) != -1) {
                  tmpChild.id = tmpInboxName[i + 1][k];
                  tmpChild.label = tmpInboxName[i + 1][k].slice(
                    tmpInboxName[i + 1][k].lastIndexOf(".") + 1
                  );
                  tmpChild.children = [];
                  tmpInboxList[i][j].children.push(tmpChild);
                }
              }
            }
          } else {
            for (let j = 0; j < tmpInboxName[i].length; j++) {
              tmpInboxList[i][j] = {};
              tmpInboxList[i][j].id = tmpInboxName[i][j];
              if (tmpInboxName[i][j].lastIndexOf(".") != -1) {
                tmpInboxList[i][j].label = tmpInboxName[i][j].slice(
                  tmpInboxName[i][j].lastIndexOf(".") + 1
                );
              } else {
                tmpInboxList[i][j].label = "收件箱";
              }
              tmpInboxList[i][j].children = [];
              for (let k = 0; k < tmpInboxList[i + 1].length; k++) {
                //
                if (
                  tmpInboxList[i + 1][k].id.indexOf(tmpInboxList[i][j].id) != -1
                ) {
                  tmpInboxList[i][j].children.push(tmpInboxList[i + 1][k]);
                }
              }
            }
          }
        }

        if (tmpInboxLayer == 0) {
          state.inboxList = [];
          state.inboxList[0] = {};
          state.inboxList[0].id = "INBOX";
          state.inboxList[0].label = "收件箱";
          state.inboxList[0].children = [];
        } else {
          state.inboxList = [];
          state.inboxList.push(tmpInboxList[0][0]);
        }
console.log('inboxList',state.inboxList);
        window.main.$message.success("登录成功");
        state.loginSucc = true;
        this.commit("mailbox/getContact");
      }
    },

    getContact() {
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefix.Query",
        [
          {
            head: {
              size: 100,
              row_key: [],
            },
            msg: {
              type: "username",
              path: "/instant_msg/system",
              relation: `${window.main.$store.state.userInfo.userinfo.authority};username`,
              prefix: "",
            },
          },
        ],
        "mailbox/afterGetContact"
      );
    },

    afterGetContact(state, data) {
      state.contactChange = false;
      state.contactList = {};

      if (data && data.length) {
        for (let i = 0; i < data.length; i++) {
          let tmpContact = {};
          tmpContact.account = data[i].columnValues.d.account.account;
          tmpContact.authority = data[i].columnValues.d.authority.authority;
          tmpContact.username = data[i].columnValues.d.username.username;
          tmpContact.privilege = "username";
          tmpContact.remark = data[i].columnValues.d.remark.remark;
          tmpContact.rowKey = data[i].row;

          //
          state.contactList[tmpContact.account] = {};
          state.contactList[tmpContact.account] = tmpContact;
        }
        state.contactChange = true;
      } else {
        state.contactChange = true;
        window.main.$message.warning("暂无联系人！");
      }
    },

    detailMailbox(state, boxName) {
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.DetailMailbox",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
            msg: {
              password: state.passwd,
              mailbox: boxName,
            },
          },
        ],
        "mailbox/afterDetailMailbox"
      );
    },

    afterDetailMailbox(state, data) {
      // switch (data.Name) {
      // 	case 'INBOX':
      // 		state.inbox = {}
      // 		state.inbox.recent = data.Recent
      // 		state.inbox.messages = data.Messages
      // 		this.commit ('mailbox/getMail', 1)
      // 		break;
      // 	default:
      //
      // 		break;
      // }
      //

      // this.commit ('mailbox/getMail', {"name":data.Name, "beg:": 1})

      state.curMailboxId = data.Name;
      if (data) {
        if (data.Messages > 0) {
          this.commit("mailbox/getMail", {
            name: data.Name,
            beg: 0,
            messageNum: data.Messages,
          });
          // state.curMailboxId = data.Name
        } else {
          window.main.$message.warning("该目录下无邮件");
          state.curInbox = [];
        }
      }
    },

    getMail(state, arg) {
      state.inbox.curPage = arg.beg;
      window.main.$emlmsg_socket.sendData(
        "Api.Eml.ListMessage",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
              From: arg.beg,
              // "From": 0,
              Size: arg.messageNum,
            },
            msg: {
              password: state.passwd,
              mailbox: arg.name,
            },
          },
        ],
        "mailbox/afterGetMail"
      );
    },

    afterGetMail(state, res) {
      // let keyArr = Object.keys(res)
      // let tmpArr = keyArr.slice(keyArr.length - 10, keyArr.length)
      let tmpArr = Object.keys(res);

      let tmpMessage = [];
      // state.curInboxMail = []
      for (let i = 0; i < tmpArr.length; i++) {
        tmpMessage[i] = {};
        tmpMessage[i].date = res[tmpArr[i]].Date;
        tmpMessage[i].from =
          res[tmpArr[i]].From[0].PersonalName +
          "<" +
          res[tmpArr[i]].From[0].MailboxName +
          "@" +
          res[tmpArr[i]].From[0].HostName +
          ">";
        tmpMessage[i].subject = res[tmpArr[i]].Subject;
        tmpMessage[i].curPage = state.inbox.curPage;
        // tmpMessage[i].rowId = (state.inbox.curPage - 1) * 10 + i + 1
        tmpMessage[i].rowId = Number(i + 1);
      }
      tmpMessage = tmpMessage.reverse();
      tmpMessage = tmpMessage.slice(0, 1000);
      state.inbox.mail = [];
      state.inbox.mail = tmpMessage;
      state.curInbox = tmpMessage;
    },

    //去除content-id字符首位的尖括号
    removeBlock(state, str) {
      if (str) {
        let reg = /^\</gi;
        let reg2 = /\>$/gi;
        str = str.replace(reg, "");
        str = str.replace(reg2, "");
        state.imgCid = "cid:" + str;
      }
    },
    // 提取邮件头的数据
    getheaderData(state, data) {
      let header = {};
      const d = new Date(data.headers?.date[0]?.initial);
      // 修改时间戳
      const resDate =
        d.getFullYear() +
        "-" +
        (d.getMonth() + 1) +
        "-" +
        d.getDate() +
        " " +
        d.getHours() +
        ":" +
        d.getMinutes();
      header.time = resDate;
      if (data.headers?.subject) {
        header.test = data.headers?.subject[0]?.value;
      }
      if (data.headers?.from) {
        header.from = data.headers?.from[0]?.value[0]?.address;
      }
      if (data.headers?.to) {
        header.to = data.headers?.to[0]?.value[0]?.address;
      }
      //头部数据存到state中
      state.headersData = header;
    },

    // 提取邮件内容数据并处理图片链接
    getmeailBody(state, data) {
      state.htmlData = "";

      if (data.childNodes[0].childNodes[1]) {
        var imgdata = data.childNodes[0].childNodes[1].childNodes;
        // 提取邮件内容html
        var mimeHtml = imgdata[0].raw;
        // 切割字符串，去掉多余头部
        var index = mimeHtml.indexOf("<html>");
        //保存html字符串
        mimeHtml = mimeHtml.substring(index, mimeHtml.length);

        // 循环获取图片的content-id
        if (imgdata.length > 1) {
          for (let i = 1; i < imgdata.length; i++) {
            //获取图片的unit8array数组
            const imgUnit8 = imgdata[i].content;
            // 转bolb使用的参数
            let imgArg = [];
            // 获取图片的类型
            const imgType = imgdata[i].headers["content-type"][0].value;
            // 指定存放state里的键名
            const stateUrl = "imgUrl";
            // 把数据存入imgArg数组
            imgArg.unit8 = imgUnit8;
            imgArg.Type = imgType;
            imgArg.stateUrl = stateUrl;
            // 获取图片的blob链接
            window.main.$store.commit("mailbox/dataURLtoBlob", imgArg);
            // 获取并处理content-id
            const imgCid = imgdata[i].headers["content-id"][0].value;
            window.main.$store.commit("mailbox/removeBlock", imgCid);
            //将字符串data中的mimeHtml替换
            mimeHtml = mimeHtml.replace(state.imgCid, state.imgUrl);
          }
          // 存储处理好的html字符串
          state.htmlData = mimeHtml;
        }
      } else if (data.childNodes[1].childNodes) {
        var imgdata = data.childNodes[1].childNodes;
        var mimeHtml = imgdata[0].raw;
        // 切割字符串，去掉多余头部
        var index = mimeHtml.indexOf("<html>");
        //保存html字符串
        mimeHtml = mimeHtml.substring(index, mimeHtml.length);

        if (imgdata.length > 1) {
          for (let i = 1; i < imgdata.length; i++) {
            //获取图片的unit8array数组
            const imgUnit8 = imgdata[i].content;
            // 转bolb使用的参数
            let imgArg = [];
            // 获取图片的类型
            const imgType = imgdata[i].headers["content-type"][0].value;
            // 指定存放state里的键名
            const stateUrl = "imgUrl";
            // 把数据存入imgArg数组
            imgArg.unit8 = imgUnit8;
            imgArg.Type = imgType;
            imgArg.stateUrl = stateUrl;
            // 获取图片的blob链接
            window.main.$store.commit("mailbox/dataURLtoBlob", imgArg);
            // 获取并处理content-id
            const imgCid = imgdata[i].headers["content-type"][0].params.name;
            window.main.$store.commit("mailbox/removeBlock", imgCid);
            //将字符串data中的mimeHtml替换
            mimeHtml = mimeHtml.replace(state.imgCid, state.imgUrl);
            state.htmlData = mimeHtml;
          }
        } else {
          let fileData = data.childNodes[0].content;
          let dataString = "";
          for (let i = 0; i < fileData?.length; i++) {
            dataString += String.fromCharCode(fileData[i]);
          }

          let start = mimeHtml.indexOf("session_id=") + 11;

          if (start > 20) {
            let end = mimeHtml.indexOf(`"/>`);
            let sessionStr = mimeHtml.substring(start, end);

            let newHTml = mimeHtml
              .split(sessionStr)
              .join(window.main.$store.state.userInfo.session_id);
            state.htmlData = newHTml;

            for (let i = 0; i < newHTml.length; i++) {
              if (
                `${
                  newHTml[i] +
                  newHTml[i + 1] +
                  newHTml[i + 2] +
                  newHTml[i + 3] +
                  newHTml[i + 4]
                }` == "data:"
              ) {
                let ends = newHTml.indexOf('"/>', i);
                state.basestrs = newHTml.substring(i, ends);
                var arr = state.basestrs.split(",");
                let mime = arr[0].match(/:(.*?);/)[1],
                  bstr = atob(arr[1]),
                  n = bstr.length,
                  u8arr = new Uint8Array(n);
                while (n--) {
                  u8arr[n] = bstr.charCodeAt(n);
                }
                state.urls = new Blob([u8arr], { type: mime });
                // state.blobstr = basestr
                // var bb = URL.createObjectURL(state.blobstr)
                var bb = URL.createObjectURL(state.urls);
                newHTml = newHTml.split(state.basestrs).join(bb);
                state.htmlData = newHTml;
              }
            }
            state.htmlData = newHTml;
          } else {
            //小文件
            for (let i = 0; i < mimeHtml.length; i++) {
              if (
                `${
                  mimeHtml[i] +
                  mimeHtml[i + 1] +
                  mimeHtml[i + 2] +
                  mimeHtml[i + 3] +
                  mimeHtml[i + 4]
                }` == "data:"
              ) {
                let ends = mimeHtml.indexOf('"/>', i);
                state.basestrs = mimeHtml.substring(i, ends);
                var arr = state.basestrs.split(",");
                let mime = arr[0].match(/:(.*?);/)[1],
                  bstr = atob(arr[1]),
                  n = bstr.length,
                  u8arr = new Uint8Array(n);
                while (n--) {
                  u8arr[n] = bstr.charCodeAt(n);
                }
                state.urls = new Blob([u8arr], { type: mime });
                // state.blobstr = basestr
                // var bb = URL.createObjectURL(state.blobstr)

                var bb = URL.createObjectURL(state.urls);
                mimeHtml = mimeHtml.split(state.basestrs).join(bb);
                state.htmlData = mimeHtml;
              }
            }
            // state.htmlData = newHTml
          }
          // state.htmlData = mimeHtml
        }
        // 存储处理好的html字符串
      }
    },
    //转换为blob链接并存储到state
    dataURLtoBlob(state, Arg) {
      let b = new Blob([Arg.unit8], { type: Arg.Type });
      let url = URL.createObjectURL(b);
      state[Arg.stateUrl] = url;
    },
    //提取附件数据参数
    getfilData(state, data) {
      const fileData = data.childNodes;
      // 存数据之前先清空
      function recursion(count) {
        if (fileData.length > 1) {
          for (let i = count; i < fileData.length; i++) {
            const fileUnit8 = fileData[i].content;
            let fileArg = [];
            const fileType = fileData[i].headers["content-type"][0].value;
            const stateUrl = "fileUrl";
            fileArg.unit8 = fileUnit8;
            fileArg.Type = fileType;
            fileArg.stateUrl = stateUrl;
            // 获取附件的blob链接
            window.main.$store.commit("mailbox/dataURLtoBlob", fileArg);
            // 获取附件名字
            const fileName = fileData[i].headers["content-type"][0].params.name;
            // 存储附件的数据
            let fileDatas = [];
            fileDatas.fileblob = state.fileUrl;
            fileDatas.filename = fileName;
            state.fileDataAll.push(fileDatas);
          }
        }
      }
      recursion(1);
      state.fileDataAll.forEach((item) => {
        if (item.filename === undefined) {
          state.fileDataAll = [];
          recursion(2);
        }
      });
    },
    // 纯文本html的解析
    getmeailtext(state, data) {
      let fileData = data.raw;
      let index = fileData.indexOf("<html>");
      //保存html字符串
      let dataString = fileData.substring(index, fileData.length);
      state.htmlData = dataString;
    },
  },
  actions: {
    async detailMessage({ state, dispatch, commit }, rowId) {
      let formData = {
        head: {
          session_id: window.main.$store.state.userInfo.session_id,
          from: rowId,
        },
        msg: {
          password: state.passwd,
          mailbox: state.curMailboxId,
        },
      };
      await window.main.$axios
        .post("/eml_msg/api/rest/v1/eml/detail_message", formData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          //解析mime邮件
          const data = parse(res.data[0]);

          //提取邮件头数据
          commit("getheaderData", data);

          // 让serveice work处理片段，并返回一个url
          fetch("/sw/parse_eml", {
            method: "POST",
            headers: {
              "Content-Type": "text/plain",
            },
            body: `${data.raw}"
`,
          }).then((res) => {
            if (res.status == 200 && res.statusText == "OK") {
              state.mailBoxUrl = `/sw/msgs/${data["headers"]["message-id"][0]["value"]}/index.html`;
            }
          });

          // if (data.childNodes.length > 0) {
          //   //提取邮件内容数据
          //   commit("getmeailBody", data);
          //   //提取附件内容数据
          //   // commit('getfilData',data)
          // } else {
          //   commit("getmeailtext", data);
          // }
        });
    },
  },
};
