<template>
  <div class="intellManage">
    <div class="container">
      <div
        class="id-card"
        v-for="(item, index) in organizationList"
        :key="index"
        @click="clickCard(item)"
      >
        <!-- 组织卡片正面 -->
        <div class="id-card-front">
          <div class="id-card-content">
            <div class="id-card-photo">
              <el-avatar
                :size="120"
                :src="getAvatarSrc(item._source.params.basic.avatar)"
              >
              </el-avatar>
            </div>
            <div class="id-card-info">
              <div class="info-row">
                <span class="label">组织名称:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.name"
                  @click.stop
                  >{{ item._source.params.basic.name || "未知" }}</span
                >
              </div>
              <div class="info-row remark">
                <span class="label">备注:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.remark || '暂无备注'"
                  @click.stop
                  >{{ item._source.params.basic.remark || "暂无备注" }}</span
                >
              </div>
              <div class="info-row">
                <span class="label">所属:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.belong"
                  @click.stop
                  >{{ item._source.params.basic.belong || "未知" }}</span
                >
              </div>
              <div class="info-row">
                <span class="label">创建时间:</span>
                <span
                  class="value"
                  :title="item._source.params.basic.createTime"
                  @click.stop
                  >{{ item._source.params.basic.createTime || "未知" }}</span
                >
              </div>
            </div>
          </div>
        </div>
        <!-- 卡片悬停效果 -->
        <div class="id-card-hover"></div>
      </div>
      <!-- 空状态展示 -->
      <el-empty
        v-if="organizationList.length === 0"
        description="暂无组织数据"
        class="empty-state"
      >
        <el-button type="primary" @click="refreshData">重新加载</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

export default {
  name: "RelevantOriganization",
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapState({
      organizationList: (state) =>
        state.relevantOriganization.relevantOriganizationList,
    }),
  },

  created() {
    this.resetData();
    this.getOrganization();
  },

  methods: {
    ...mapMutations({
      getOrganization: "relevantOriganization/getOrganization",
      resetData: "relevantOriganization/resetData",
    }),

    refreshData() {
      this.resetData();
      this.getOrganization();
    },

    clickCard(data) {
      let organi = JSON.stringify(data);
      const routeData = this.$router.resolve({
        name: 'oriDetails',
        query: {
          data: organi,
        },
      });
      window.open(routeData.href, "_blank");
    },

    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require("@/assets/images/user.png");
    },

    getOrganizationType(type) {
      const typeMap = {
        key_organization: "目标组织",
        secondary_organization: "次要组织",
      };
      return typeMap[type] || "未知类型";
    }
  },
};
</script>

<style lang="scss" scoped>
.intellManage {
  padding: 10px;
  height: 100%;
  .container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    padding: 8px;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
    height: 90%;
    .id-card {
      position: relative;
      width: 80%;
      height: 170px;
      perspective: 1000px;
      cursor: pointer;
      
      .id-card-front {
        position: relative;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
        transition: all 0.3s ease;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 6px;
          background: linear-gradient(90deg, #1890ff, #36cfc9);
        }
        
        .id-card-content {
          display: flex;
          gap: 20px;
          
          .id-card-photo {
            width: 100px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            :deep(.el-avatar) {
              width: 100% !important;
              height: 100% !important;
              border-radius: 12px !important;
              object-fit: cover;
              box-shadow: 0 2px 8px rgba(0,0,0,0.12);
            }
          }
          
          .id-card-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .info-row {
              display: flex;
              margin-bottom: 8px;
              height: 25px;
              line-height: 25px;
              .label {
                width: auto;
                color: #666;
                font-size: 14px;
              }
              
              .value {
                margin-left: 5px;
                flex: 1;
                color: #333;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: default;
              }
              
              &.remark {
                .value {
                  color: #666;
                  font-size: 13px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }
            }
          }
        }
      }
      .id-card-hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 12px;
        i {
          font-size: 32px;
          margin-bottom: 8px;
        }
        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
      &:hover {
        .id-card-front {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        .id-card-hover {
          opacity: 1;
        }
      }
    }
    .empty-state {
      grid-column: 1 / -1;
      margin-top: 120px;
    }
  }
}
</style>
